# rax-materials-basic-app

## Getting Started

### `npm run start`

Runs the app in development mode.

Open [http://localhost:3333](http://localhost:3333) to view it in the browser.

The page will reload if you make edits.

### `npm run build`

Builds the app for production to the `build` folder.

## 约定
### 首屏数据
1. 在`pages/index/first-data-api.ts`文件中`getFirstData`方法中调用接口获取首屏数据。
2. 通过getUserInfo获取用户信息，node端渲染时只有kps（需要客户端的kp公参）。
3. 首屏数据遵循最小够用原则，避免首屏无关的接口调用。
4. node端单接口调用超时时间最大为800ms（暂定）

### 模块封装
对于只在Web端执行的模块，使用withUniversal进行包装。`@/lib/render-utils/with-universal`

### 异步组件
1. 首屏相关组件 不要使用异步组件。
2. 异步组件可使用`@/components/AsyncComponent.tsx`;