{"name": "@ali/uc-pegasus-source-project-uc-lite-welfare-new", "version": "1.0.0", "description": "uc-pegasus-source-project-uc-lite-welfare-new", "author": "rax", "appType": "UC", "scripts": {"start": "rax-app start --disable-open", "start:prod": "export BUILD_ENV=prod && rax-app start", "build": "rax-app build", "build:daily": "rax-app build --mode daily", "build:inspect": "node --inspect-brk ./node_modules/rax-app/bin/rax-cli build", "publish:daily": "def p -d", "publish:pre": "def p -d -- --def_publish_env=pre", "publish:prod": "def p -o", "eslint": "eslint --ext .js,.jsx,.tsx,.ts src", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "prettier": "prettier **/* --write", "lint": "npm run eslint && npm run stylelint", "compressFont": "tsx ./src/components/font-compress/script.ts", "previewFont": "tsx ./src/components/font-compress/preview.ts"}, "dependencies": {"@ali/act-account": "^2.3.2", "@ali/cms-res": "^1.3.0-alpha.2", "@ali/fact-stat": "^4.7.0", "@ali/itrace-browser": "^2.3.30", "@ali/itrace-fluency": "^1.0.3", "@ali/itrace-interface": "^2.1.12", "@ali/pcom-driver": "^1.1.4", "@ali/pegasus-document": "^3.0.6", "@ali/silly-toast": "^2.0.2", "@ali/uc-embed-core": "^1.0.21", "@ali/uc-iflow-tools": "^0.4.9", "@ali/uc-toolkit": "^3.21.0", "@ali/universal-event-tracking": "^1.0.0", "@ali/weex-rax-components": "^1.2.0-beta.6", "@ali/weex-toolkit": "^2.14.0-beta.8", "@ali/wormhole-context": "^0.0.1", "@ali/wpk-reporter": "^1.0.7", "@alifd/meet": "^2.8.1", "@rematch/core": "^2.0.1", "classnames": "^2.3.1", "dayjs": "^1.11.3", "eventemitter3": "^4.0.7", "events": "^3.3.0", "history": "^5.0.0", "idx": "^2.5.6", "jsdom": "^19.0.0", "lottie-web": "^5.7.14", "mock-browser": "^0.92.14", "qs": "^6.9.4", "rax": "^1.0.8", "rax-children": "^1.0.0", "rax-clone-element": "^1.0.0", "rax-countdown": "^1.2.0", "rax-document": "^0.1.0", "rax-find-dom-node": "^1.0.1", "rax-image": "^2.0.0", "rax-link": "^1.0.1", "rax-qrcode": "^2.0.2", "rax-redux": "^1.0.0", "rax-scrollview": "^3.7.1", "rax-swiper": "^0.2.0", "rax-text": "^2.0.0", "rax-textinput": "^1.4.4", "rax-use-mounted": "^1.0.0", "rax-view": "^2.0.0", "redux": "^4.0.5", "semver": "^7.3.5", "universal-element": "^0.0.6", "universal-env": "^3.3.0", "universal-toast": "^1.2.3", "universal-transition": "^1.1.1", "uuid": "^3.3.2"}, "devDependencies": {"@ali/broccoli-simulate-browser-env": "^1.0.14", "@ali/build-plugin-event-tracking-register": "^1.0.0", "@ali/build-plugin-pegasus-base": "^1.17.1", "@ali/build-plugin-pegasus-project": "^1.5.10", "@ali/build-plugin-pegasus-uc-project": "^1.6.6", "@ali/build-plugin-rax-app-def": "^3.0.0", "@ali/logonline": "^1.7.6", "@ali/pegasus-document": "^3.0.6", "@ali/wormhole-sdk": "^1.1.47", "@builder/swc": "^0.2.1", "@iceworks/spec": "^1.0.0", "@types/fontmin": "^0.9.5", "@types/rax": "^1.0.0", "@types/semver": "^7.3.9", "babel-plugin-transform-require-default": "^0.1.6", "build-plugin-fusion-mobile": "^1.0.0", "chalk": "^3.0.0", "console-clear": "^1.1.1", "cross-fetch": "^3.1.4", "eslint": "^6.8.0", "fontmin": "^1.1.0", "fs-extra": "^10.0.0", "husky": "^7.0.1", "node-fetch": "^2.6.1", "prettier": "^2.1.2", "rax-app": "^3.8.6", "sass": "^1.78.0", "sass-loader": "^16.0.5", "stylelint": "^13.7.2", "tsx": "^4.19.2", "typescript": "^4.9.5", "webpack-bundle-analyzer": "^4.4.2", "yargs": "^12.0.5", "yargs-parser": "^16.1.0"}, "originTemplate": "@rax-materials/scaffolds-app-ts", "publishConfig": {"registry": "https://registry.npm.alibaba-inc.com"}, "browser": {"crypto": false}, "pegasus": {"project": {"templateEngine": "javascript", "bizGroup": {"vipserver": "http.fc.vipserver", "host": "page.suikong-test"}}}, "repository": "**************************:uc-pegasus-source-project/uc-lite-welfare-new.git"}