/* eslint-disable @typescript-eslint/no-require-imports */
const process = require('process');
const buildArgv = require('yargs-parser')(process.env.BUILD_ARGV_STR || '');
const webpack = require('webpack');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const branch = process.env.BUILD_GIT_BRANCH || '/';


const isProd = buildArgv.def_publish_env === 'prod';
const isPre = buildArgv.def_publish_env === 'pre';
const isDaily = buildArgv.def_publish_env === 'daily';

module.exports = ({ onGetWebpackConfig }, options) => {

    onGetWebpackConfig((config) => {
        // 1. 清除旧规则避免冲突
        config.module.rules.delete('img');
        // 2. 添加新规则
        config.module
            .rule('img')
            .test(/\.(png|jpg|jpeg|gif)$/i)
            .use('url-loader')
            .loader('url-loader')
            .options({
                limit: 5120,
                name: '[name].[contenthash:8].[ext]',
            });
    });
};
