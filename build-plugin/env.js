const { BrowserEnv } = require('@ali/broccoli-simulate-browser-env');

function isObject(obj) {
  return obj && Object.prototype.toString.call(obj) === '[object Object]'
}

// function notSupport(key: string, defaultValue: number | string) {
//   console.warn(`property ${key} is not support, default value ${defaultValue}`);
// }

function noop() { }

const blackKeys = ['pegasus_data', '__propsNodeId'];

function createBrowserEnv(injectCtx) {
  class CustomBrowserEnv {
    console;
    Image;
    navigator;
    document;
    global;
    window;
    location;
    localStorage;
    sessionStorage;
    screen;
    fetch;
    __wormhole__;
    __setTimeout;

    constructor() {
      /** TODO 强制写死了https，因为faas网关没透传给faas框架，@卢令 网关同学，@张挺 框架同学 */
      const { headers = {}, href } = injectCtx;

      /**
       * 主文档url
       * 并行ssr时，minjs会传custom-doc-url
       * 串行ssr和csr时，直接获取href
       */
      headers['custom-doc-url'] = headers['custom-doc-url'] || href;
      /** href表示请求url */
      headers['custom-req-url'] = href;
      const docUrl = headers['custom-doc-url'];
      const baseBrowserEnv = new BrowserEnv({
        env: 'dev',
        url: docUrl,
        cookie: headers['cookie'],
        userAgent: headers['user-agent'], // TODO，需要请求侧提供userAgent
        headers,
        /** 以下部分配置依赖客户端补全参数，暂时写默认值，建议端不要使用 */
        /**
         * 设备的像素大小的比率
         * 示例：2
         */
        // devicePixelRatio?: number;
        /**
         * 屏幕的宽度
         * 示例：750
         */
        get screenWidth() {
          // notSupport('screenWidth', 750);
          return 750;
        },
        /**
         * 屏幕的宽度
         * 示例：1280
         */
        get screenHeight() {
          // notSupport('screenHeight', 1280);
          return 1280;
        },
        /**
         * 页面地址
         * 示例："https://developer.mozilla.org/zh-CN/docs/Web/API/URL"
         */
      });
      // 默认屏蔽 console，开发下不要屏蔽输出
      // this.console = baseBrowserEnv.console;
      this.console = console;
      this.__setTimeout = setTimeout;
      this.Image = class { };
      this.navigator = baseBrowserEnv.navigator;
      this.location = baseBrowserEnv.location;
      this.localStorage = baseBrowserEnv.localStorage;
      this.sessionStorage = baseBrowserEnv.sessionStorage;
      this.screen = baseBrowserEnv.screen;
      this.document = baseBrowserEnv.document;
      this.fetch = baseBrowserEnv.fetch;

      // console.log('============111', this.document.clientWidth);
      // this.document.head.appendChild('');
      // console.log('============222');
      /** 不能用Object.assign({}, baseBrowserEnv.window) ，会导致缺失window方法 */
      // eslint-disable-next-line
      this.global = this.window = Object.assign(baseBrowserEnv.window, {
        document: this.document,
        __isSSR: true,
        // addEventListener(name, fn) {},
      });
      // this.window.Date.now();
    }

    getGlobalVariableNameList() {
      return [
        'console',
        'Image',
        'navigator',
        'document',
        'global',
        'top',
        'self',
        'window',
        'location',
        'localStorage',
        'sessionStorage',
        'screen',
        'fetch',
        '__wh_data__',
        'require', // 提供node内建模块
        // '__ctx__',  // 正常不应该注入，但以防万一，先留个口子

        'setTimeout',
        'clearTimeout',
        'setInterval',
        'clearInterval',
        'setImmediate',
        'CustomEvent',
        'WebSocket', // 先注入，后续再看是否合适
        'requestAnimationFrame',
        'alert',
        '__setTimeout',
      ];
    }

    getGlobalVariableList() {
      return [
        this.console,
        this.Image,
        this.navigator,
        this.document,
        this.global,
        this.global,
        this.global,
        this.window,
        this.location,
        this.localStorage,
        this.sessionStorage,
        this.screen,
        this.fetch,
        this.getWhData(),
        require, // 提供node内建模块
        // this.getCtx(),

        noop,
        noop,
        noop,
        noop,
        noop,
        noop,
        noop,
        noop,
        noop,
        this.__setTimeout,
      ];
    }

    formatWhPageData(data) {
      let result = data;
      if (isObject(data)) {
        result = {};
        if (typeof data.__data !== 'undefined') {
          /** 表示此层级为声明类型，真实数据在__data中 */
          if (isObject(data.__data) && data.__data.__propsNodeId) {
            delete data.__data.__propsNodeId;
          }
          return data.__data;
        }
        for (const key in data) {
          // eslint-disable-next-line
          if (data.hasOwnProperty(key) && (blackKeys.indexOf(key) === -1)) {
            result[key] = this.formatWhPageData(data[key]);
          }
        }
      } else if (Array.isArray(data)) {
        /** a、这个逻辑要关注下页面配置未来是否有变更，测试来看 */
        result = this.formatWhPageData(data[0]) || [];
      } else {
        result = data;
      }
      return result;
    }

    getWhData() {
      const {
        page,
        mock = undefined,
        packId,
        // pageInfo
      } = this.__wormhole__ ? this.__wormhole__.data : {};
      /** TODO 此处需要过滤非必要字段 */
      if (page || packId || mock) {
        return {
          page: this.formatWhPageData(page),
          packId,
          mock,
          /** whHistoryId不应该暴露到渲染过程中，以防到时前端同时传该字段 */
          // whHistoryId,
        };
      }
      return undefined;
    }
  }
  return CustomBrowserEnv;
}
module.exports = createBrowserEnv;
