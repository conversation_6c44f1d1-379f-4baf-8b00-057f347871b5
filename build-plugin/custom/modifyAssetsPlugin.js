/**
 * An entry plugin which will set loader for entry before compile.
 */

const { RawSource, ConcatSource } = require('webpack-sources');

const PLUGIN_NAME = 'ModifyAssetsPlugin';

class ModifyAssetsPlugin {
  constructor(options) {
    this.options = options;
  }

  /**
   * @param {Compiler} compiler the compiler instance
   * @returns {void}
   */
  apply(compiler) {
    compiler.hooks.shouldEmit.tap(PLUGIN_NAME, (compilation) => {
      const bootstrapReg = /^(\S*)_bootstrap$/;
      const asyncPageChunk = compilation.chunkGroups.filter((group) => bootstrapReg.test(group.name));

      if (asyncPageChunk.length > 0) {
        const CSS_REG = /\.css$|\.css\.map$/;
        const cssFileNeedClear = [];
        const cssMapFileNeedClear = [];
        asyncPageChunk.forEach((chunkGroup) => {
          const pageName = chunkGroup.name.match(bootstrapReg)[1];

          const assetsCSSNameRegExp = new RegExp(`pages/${pageName}/index(.\\S*)?.css$`);
          const assetsCSSName = Object.keys(compilation.assets).filter((filename) => assetsCSSNameRegExp.test(filename))[0];

          chunkGroup.chunks.forEach((chunk) => {
            const includeCssFiles = chunk.files.filter((file) => CSS_REG.test(file));
            if (includeCssFiles.length > 0) {
              let homeCss = compilation.assets[assetsCSSName];
              let homeCssMap = compilation.assets[`${assetsCSSName}.map`];

              includeCssFiles.forEach((file) => {
                const isCSS = /\.css$/;
                if (isCSS.test(file)) {
                  const concatSourceParams = [homeCss, '\n', compilation.assets[file]];
                  // vendor～的css前置
                  if (file.indexOf('vendor~') === 0) concatSourceParams.reverse();

                  compilation.assets[assetsCSSName] = new ConcatSource(...concatSourceParams);

                  if (cssFileNeedClear.indexOf(file) < 0) {
                    cssFileNeedClear.push(file);
                  }
                  // compilation.assets[file] = new RawSource('.remove-by-webpack{display:none;}');
                  homeCss = compilation.assets[assetsCSSName];
                } else if (homeCssMap) {
                  const concatSourceParams = [homeCssMap, '\n', compilation.assets[file]]
                  // vendor～的css前置
                  if (file.indexOf('vendor~') === 0) concatSourceParams.reverse();

                  compilation.assets[`${assetsCSSName}.map`] = new ConcatSource(...concatSourceParams);
                  if (cssMapFileNeedClear.indexOf(file) < 0) {
                    cssMapFileNeedClear.push(file);
                  }
                  // compilation.assets[file] = new RawSource('');
                  homeCssMap = compilation.assets[`${assetsCSSName}.map`];
                }
              });

              // includeCssFiles.forEach((file) => {
              //   const index = chunk.files.indexOf(file);
              //   if (index !== -1) chunk.files.splice(index, 1);
              // });
            }
          });
        });
        cssFileNeedClear.forEach(file => {
          compilation.assets[file] = new RawSource('.remove-by-webpack{display:none;}');
        });
        cssMapFileNeedClear.forEach(file => {
          compilation.assets[file] = new RawSource('');
        });
      }
      return true;
    });
  }
}

module.exports = ModifyAssetsPlugin;
