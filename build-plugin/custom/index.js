/* eslint-disable @typescript-eslint/no-require-imports */
const process = require('process');
const buildArgv = require('yargs-parser')(process.env.BUILD_ARGV_STR || '');
const ModifyAssetsPlugin = require('./modifyAssetsPlugin');
const webpack = require('webpack');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const branch = process.env.BUILD_GIT_BRANCH || '/';

function getVersion(beta = false) {
  const version = branch.split('/')[1] || '0.0.0';
  if (beta && !isProd) {
    return `${version}-beta.${Date.now()}`;
  }
  return version;
}

const isProd = buildArgv.def_publish_env === 'prod';
const isPre = buildArgv.def_publish_env === 'pre';
const isDaily = buildArgv.def_publish_env === 'daily';

module.exports = ({ onGetWebpackConfig }, options) => {
  const { defineEnv } = options;

  onGetWebpackConfig('web', (config) => {
    config.plugin('modifyAssetsPlugin').use(ModifyAssetsPlugin);
  });

  onGetWebpackConfig((config) => {
    const params = [
      defineEnv.reduce((prev, curr) => {
        prev[`process.env.${curr}`] = JSON.stringify(process.env[curr]);
        return prev;
      }, {
        // 'process.type': JSON.stringify('browser'),
        DEV: JSON.stringify(!isProd && !isDaily && !isPre),
        PUBLISH_ENV: JSON.stringify(buildArgv.def_publish_env),
        IS_WEEX: JSON.stringify(false),
        IS_WEB: JSON.stringify(true),
        MAIN_VERSION: JSON.stringify(getVersion(true)),
      }),
    ];
    config.plugin('RuntimeDefinePlugin').use(webpack.DefinePlugin, params);

    // if (config.toConfig().target === 'web') {
    //   config.plugin('BundleBundleAnalyzerPluginAnalyzer').use(BundleAnalyzerPlugin);
    // }

  });
};
