/* eslint-disable @typescript-eslint/no-require-imports */
const process = require('process');
const buildArgv = require('yargs-parser')(process.env.BUILD_ARGV_STR || '');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const branch = process?.env.BUILD_GIT_BRANCH || '/';
const isLocal = buildArgv.local || process.env.NODE_ENV === 'development'; // 新增本地判断

const isProd = buildArgv.def_publish_env === 'prod';
function getVersion(beta = false) {
  const version = branch.split('/')[1] || '0.0.0';
  if (beta && !isProd) {
    return `${version}-beta.${Date.now()}`;
  }
  return version;
}

module.exports = (api) => {
  if(!isProd){
    return;
  }
  api.onGetWebpackConfig((webpackConfig) => {
    let publicPath;
    if (isLocal) {
      // 本地环境，使用本地路径或自定义本地 URL
      publicPath = '/';
    } else {
      // 线上环境配置
      publicPath = `https://broccoli-static.uc.cn/${isProd ? 'prod' : 'daily'
        }/code/npm/@ali/uc-pegasus-source-project-uc-lite-welfare-new/${getVersion()}/`;
    }
    webpackConfig.output.publicPath(publicPath);
    return webpackConfig;
  });
};