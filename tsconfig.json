{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"baseUrl": ".", "outDir": "build", "module": "esnext", "target": "es6", "jsx": "preserve", "jsxFactory": "createElement", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "rootDir": "./", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": false, "noUnusedLocals": true, "skipLibCheck": true, "types": ["node", "jest"], "paths": {"@/*": ["./src/*"], "rax-app": [".rax/index.ts"]}}, "include": ["src", ".rax"], "exclude": ["node_modules", "build", "public"]}