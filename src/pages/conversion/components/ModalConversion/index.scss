.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0; /* 初始状态设置为透明 */
  visibility: hidden; /* 防止点击事件 */
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out; /* 动画过渡 */
  .modal {
    margin: 0 auto;
    width: 630rpx;
    background-image: linear-gradient(0deg, #fffdf9 0%, #f8fbff 100%);
    border-radius: 40rpx;
    padding: 96rpx 90rpx 80rpx 90rpx;
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 48rpx;
      color: #01255d;
      line-height: 66rpx;
      text-align: center;
      font-weight: 700;
    }
    .desc {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      margin-top: 40rpx;
      font-family: PingFang-SC-Regular;
      font-size: 32rpx;
      color: #7e93b7;
      line-height: 44rpx;
      text-align: center;
      margin-bottom: 50rpx;
      .attention {
        color: #f02920;
      }
    }
    .confirm {
      margin: 0 auto;
      width: 450rpx;
      height: 108rpx;
      font-family: PingFangSC-Semibold;
      font-size: 40rpx;
      line-height: 108rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
      background-image: url(../../../../assets/modal_btn_bg.png);
      background-size: cover;
    }
  }
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal-backdrop.hide {
  opacity: 0;
  visibility: hidden;
}
