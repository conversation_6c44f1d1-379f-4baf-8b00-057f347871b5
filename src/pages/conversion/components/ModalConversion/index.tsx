import { createElement, useImperativeHandle, forwardRef, useState} from 'rax';
import './index.scss';
import View from 'rax-view';
import { store } from '@/store';

const Modal = forwardRef((props, ref) => {
  const [show, setShow] = useState(false);
  useImperativeHandle(ref, () => ({
    open: () => setShow(true),
    close: () => setShow(false)
  }));
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      setShow(false)
    }
  }
  const handleBack = () => {
    store?.dispatch?.route?.back();
    setShow(false)
  };
  return (
    <View onClick={handleBackdropClick} className={`modal-backdrop ${show ? 'show' : 'hide'}`}>
      <View className="modal">
        <View className="title">兑换成功</View>
        <View className="desc">最小兑换单位为分，不足兑换<View className="attention">0.01元</View>的元宝会退回到你的帐上</View>
        <View onClick={handleBack} className="confirm">查看流水信息</View>
      </View>
    </View>
  );
});

export default Modal;