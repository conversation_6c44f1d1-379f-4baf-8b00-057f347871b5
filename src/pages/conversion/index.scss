.p-conversion {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  background: #ffffff;
  padding-top: 88rpx;
  .p-conversion-header {
    position: relative;
    width: 750rpx;
    height: 120rpx;
    flex-direction: row;
    padding: 0 16rpx;
    align-items: center;
    justify-content: center;
    .back-icon {
      position: absolute;
      top: 38rpx;
      left: 16rpx;
    }
    .header-title {
      font-size: 38rpx;
      color: #333333;
      font-weight: 700;
      text-align: center;
    }
  }
  .notic {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0 48rpx;
    background: #fffcf0;
    border: 1rpx solid #ffe1a1;
    border-radius: 28rpx;
    padding-left: 15rpx;
    .message-item {
      margin-left: 8rpx;
      display: flex;
      justify-content: flex-start;
      width: 530rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #901906;
      font-weight: 500;
      .message-item-text {
        height: 56rpx;
        line-height: 56rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  .coin-info {
    padding: 32rpx 48rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .coin-info-title {
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      color: #12161a;
      font-weight: 700;
    }
    .coin-info-right {
      display: flex;
      flex-direction: row;
      font-family: PingFangSC-Regular;
      font-size: 32rpx;
      color: #12161a;
      .coin-num {
        margin-left: 8rpx;
        font-family: D-DIN-Bold;
        color: #f7534f;
        font-weight: 700;
      }
    }
  }
  .conversion {
    padding: 0 48rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    .conversion-options {
      margin-bottom: 32rpx;
      width: 148rpx;
      height: 96rpx;
      font-family: D-DIN-Bold;
      font-size: 40rpx;
      color: #12161a;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
      background: #f6f6f6;
      border-radius: 32rpx;
      line-height: 96rpx;
    }
    .forbidden {
      background: #F6F6F6;
      color: #859199;
    }
    .checked {
      background: #F6F6F6;
      border: 4rpx solid #F7534F;
      border-radius: 16px;
    }
  }
  .deduct {
    padding: 20rpx 48rpx 44rpx 48rpx;
    .deduct-title {
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      line-height: 44rpx;
      color: #12161a;
      font-weight: 600;
    }
    .deduct-coin {
      height: 76rpx;
      margin-top: 12rpx;
      font-family: D-DIN-Bold;
      font-size: 64rpx;
      line-height: 76rpx;
      color: #f7534f;
      font-weight: 700;
    }
  }
  .explain {
    margin-top: 20rpx;
    margin-bottom: 38rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #859199;
    text-align: center;
    font-weight: 400;
    line-height: 34rpx;
  }
  .submit {
    margin: 0 auto;
    width: 450rpx;
    height: 108rpx;
    font-family: PingFangSC-Semibold;
    font-size: 40rpx;
    line-height: 108rpx;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 700;
    background-image: url('../../assets/modal_btn_bg.png');
    background-size: cover;
  }
}
