import { createElement, Component, createRef } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from '@/components/image';
import './index.scss';
import IconBack from './../flow/images/<EMAIL>';
import MessageScroll from '@/components/common/MessageScroll/index';
import MessageIcon from './images/message-icon.png';
import { StoreDispatch, StoreState, store } from '@/store';
import { withPageLifeCycle } from 'rax-app';
import { connect } from 'rax-redux';
import Toast from '@/lib/universal-toast';
import ModalConversion from './components/ModalConversion/index';
import fact from '@/lib/fact';
import { initPageLogParam } from '@/utils/log';
interface IState {
  currentIndex: number;
  exchangeAllocationList: number[]
}
// 兑换页面
class Index extends Component<IProps> {
  state: IState = {
    currentIndex: -1, // 选择兑换金额的标志位
    exchangeAllocationList: []
  };
  modalRef = createRef();
  componentDidMount = () => {
    initPageLogParam('exchange', true);
    const { exchangeAllocation } = this.props;
    const { btnValues = [] } = exchangeAllocation;
    const exchangeAllocationList = [...btnValues, -1];
    this.setState({exchangeAllocationList})
    exchangeAllocationList?.forEach(item => {
      fact?.exposure('cash_option_exposure', {
        c: 'list',
        d: 'withdraw',
        count: item === -1 ? 'all' : `${(item / 100)?.toFixed(1)}`,
      })
    })
  };
  // 返回
  handleBack = () => {
    store?.dispatch?.route?.back();
  };
  // 选择兑换金额
  selectConversionUnit = (index, item) => {
    return () => {
      fact.click('fuli_cash_click', {
        c: 'button',
        d: 'withdraw',
        count: item === -1 ? 'all' : `${(item / 100).toFixed(1)}`,
      });
      const flag = this.isDeductUnit(item);
      if (flag) {
        this.setState({ currentIndex: index });
      } else {
        Toast.show('你的元宝余额不足，可做任务赚更多元宝哦');
      }
    };
  };
  // 是否可以兑换该数额
  isDeductUnit = (item) => {
    const { coin, exchangeRate } = this.props;
    if (item === -1) {
      return coin - exchangeRate >= 0;
    }
    return coin - item * exchangeRate >= 0;
  };
  // 扣除数额
  deductUnit = () => {
    const { currentIndex } = this.state;
    const { coin, exchangeRate, exchangeAllocation } = this.props;
    if (currentIndex === -1) {
      return 0;
    }
    if (currentIndex === exchangeAllocation?.btnValues?.length) {
      return coin;
    }
    return exchangeAllocation?.btnValues?.[currentIndex] * exchangeRate;
  };
  // 点击兑换
  submitConversion = async () => {
    const { currentIndex } = this.state;
    const { exchangeRate } = this.props;
    if (currentIndex === -1) {
      Toast.show('请选择一个兑换金额');
      return;
    }
    fact.click('exchange_click', {
      c: 'button',
      d: 'withdraw',
      count: ((this?.deductUnit() / exchangeRate) / 100)?.toFixed(1),
    });
    const res = await store?.dispatch?.currency?.transferAsset(this?.deductUnit());
    if (res) {
      this?.modalRef?.current?.open();
      this.setState({ currentIndex: -1 });
    } else {
      Toast.show('兑换失败 请再试一次');
    }
  };
  render() {
    const { currentIndex, exchangeAllocationList } = this.state;
    const { coin, exchangeAllocation } = this.props;
    const { notice = [] } = exchangeAllocation;
    return (
      <View className="p-conversion">
        <View className="p-conversion-header">
          <Image
            onClick={this.handleBack}
            className="back-icon"
            source={IconBack}
            style={{ width: '44rpx', height: '44rpx' }}
          />
          <Text className="header-title">元宝兑换</Text>
        </View>
        {!!notice?.length && (
          <View className="notic">
            <Image style={{ width: '32rpx', height: '32rpx' }} source={MessageIcon} alt="" />
            <MessageScroll
              messages={notice}
              interval={5000}
              renderItem={(item, index) => {
                return (
                  <div key={index} className="message-item">
                    <div className="message-item-text">{item}</div>
                  </div>
                );
              }}
            />
          </View>
        )}
        <View className="coin-info">
          <View className="coin-info-title">选择兑换现金值</View>
          <View className="coin-info-right">
            可兑元宝<View className="coin-num">{coin}</View>
          </View>
        </View>
        <View className="conversion">
          {exchangeAllocationList?.map((item, index) => {
            return (
              <View
                key={index}
                onClick={this.selectConversionUnit(index, item)}
                className={`conversion-options ${this.isDeductUnit(item) ? '' : 'forbidden'} ${
                  index === currentIndex ? 'checked' : ''
                }`}
              >
                {item === -1 ? '全部' : `¥${(item / 100).toFixed(1)}`}
              </View>
            );
          })}
        </View>
        <View className="deduct">
          <View className="deduct-title">扣除元宝</View>
          <View className="deduct-coin">{this.deductUnit()}</View>
        </View>
        <View className="explain">
          <View>最小单位可兑换到分，</View>
          <View>不足兑换0.01元的元宝会退回到你的账上</View>
        </View>
        <View onClick={this.submitConversion} className="submit">
          立即兑换
        </View>
        <ModalConversion ref={this.modalRef} />
      </View>
    );
  }
}

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {};

const mapState = (state: StoreState) => {
  const { coin, exchangeWay, exchangeRate, exchangeAllocation } = state.currency;
  return {
    coin,
    exchangeWay,
    exchangeRate,
    exchangeAllocation,
  };
};

const mapDispatch = (dispatch: StoreDispatch) => ({});

export default connect(mapState, mapDispatch)(withPageLifeCycle(Index));
