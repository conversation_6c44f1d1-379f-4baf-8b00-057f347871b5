import { createElement, useEffect } from 'rax';
import Image from "@/components/image";
import View from 'rax-view';
import './index.scss';
import NetworkErrImg from './images/networkdisconnect.png';
import { dispatch } from '@/store';
import { debounce } from '@/lib/utils';
import event from '@/utils/event';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';

export default function RetryPage() {

  const handleRetry = debounce(()=>{
    event.emit('pageIndexDataInit', {
      isRetrySuccess: true
    });
    dispatch.app.init();
    tracker.log({
      category: WPK_CATEGORY_MAP.GET_FIRST_DATA,
      msg: '点击重试页面',
      sampleRate: 1,
    });
  }, 500);

  return (
    <View className="retry-page">
      <Image source={NetworkErrImg} className="network-err-icon" />
      <View className="desc-text">无法连接网络，请稍后重试</View>
      <View className="retry-btn" onClick={handleRetry}>点击重试</View>
    </View>
  )
}
