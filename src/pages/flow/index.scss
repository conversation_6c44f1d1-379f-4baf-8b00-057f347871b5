* {
  user-select: none;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
.p-flow {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  background-color: #F6F6F6;
  padding-top: 88rpx;
  .p-flow-header {
    position: relative;
    width: 750rpx;
    height: 120rpx;
    flex-direction: row;
    padding: 0 16rpx;
    align-items: center;
    justify-content: center;
    .back-icon {
      position: absolute;
      top: 38rpx;
      left: 16rpx;
    }
    .header-title {
      font-size: 38rpx;
      color: #333333;
      font-weight: 700;
      text-align: center;
    }
    .old-wallet {
      position: absolute;
      right: 30rpx;
      top: 24rpx;
      align-items: center;
      .other-cash-title {
        font-size: 22rpx;
        color: #666;
        letter-spacing: 0;
      }
      .other-cash-amount {
        font-family: D-DIN-Bold;
        font-size: 28rpx;
        color: #F7534F;
        letter-spacing: 0;
        font-weight: 700;
        line-height: 40rpx;
      }
      .wallet-tip {
        width: 232rpx;
        height: 57rpx;
        align-items: center;
        justify-content: center;
        background-image: url("./images/<EMAIL>");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        position: absolute;
        bottom: -60rpx;
        right: -4rpx;
        z-index: 99;
        .tip-desc {
          font-size: 24rpx;
          color: #FFF;
          margin-top: 8rpx;
        }
      }
    }
  }
  .total-income {
    flex-direction: row;
    width: 690rpx;
    height: 60rpx;
    color: #999;
    font-size: 24rpx;
    padding-left: 40rpx;
    padding-top: 20rpx;
    line-height: 44rpx;
    .total-income-amount {
      font-weight: 700;
      color: #F7534F;
      font-size: 28rpx;
    }
  }
  .my-assets-cash {
    z-index: 10;
  }
  .earnings-info {
    margin: 0 30rpx;
    display: flex;
    flex-direction: row;
    padding-bottom: 36rpx;
    border-bottom: 2rpx solid #E0E5E8;
    .my-assets-cash {
      padding-left: 40rpx;
    }
    .my-assets-ingot,
    .my-assets-cash {
      flex: 1;
      .assets-num {
        font-family: D-DIN-Bold;
        font-size: 64rpx;
        color: #F7534F;
        font-weight: 700;
        line-height: 76rpx;
      }
      .my-assets-bottom {
        display: flex;
        flex-direction: row;
        margin-top: 10rpx;
        .assets-title {
          font-family: PingFangSC-Regular;
          font-size: 32rpx;
          color: #12161A;
          font-weight: 400;
        }
        .assets-tag {
          display: flex;
          flex-direction: row;
          align-items: center;
          margin-left: 12rpx;
          font-family: PingFangSC-Semibold;
          font-size: 24rpx;
          color: #F7534F;
          font-weight: 600;
          border-radius: 8rpx;
          padding: 6rpx 6rpx 6rpx 14rpx;
          background-color: rgba(247,83,79,0.1);
        }
      }
    }
  }
  .conversion-mode {
    margin: 30rpx 0 30rpx 30rpx;
    display: flex;
    flex-direction: row;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #12161A;
    font-weight: 400;
    .cut {
      margin-left: 16rpx;
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      color: #5C88B0;
      font-weight: 600;
    }
  }
  // .my-assets-ingot, .my-assets-cash {
  //   justify-content: center;
  //   align-items: center;
  //   .assets-title {
  //     font-size: 32rpx;
  //     color: #333333;
  //   }
  //   .assets-num {
  //     position: relative;
  //     font-size: 100rpx;
  //     font-weight: 700;
  //     color: #F7534F;
  //     margin-bottom: 16rpx;
  //   }
  //   .assets-desc {
  //     margin-bottom: 10rpx;
  //     span {
  //       font-size: 24rpx;
  //       color: #999;
  //       line-height: 30rpx;
  //       text-align: center;
  //     }
  //   }
  //   .cash-unit {
  //     position: absolute;
  //     font-size: 34rpx;
  //     color: #F7534F;
  //     right: -38rpx;
  //     top: 48rpx;
  //   }
  // }
  .system-notice {
    width: 690rpx;
    margin: 0 auto;
    // height: 282rpx;
    padding: 24rpx 40rpx;
    background: #FFF9E1;
    border: #901906;
    border-radius: 36rpx;
    .title {
      font-size: 30rpx;
      color: #A27D6B;
      font-weight: 700;
      text-align: center;
      margin-bottom: 20rpx;
    }
    span {
      color: #A27D6B;
      font-size: 22rpx;
      line-height: 38rpx;
    }
  }
  .assets-withdraw-btn {
    padding: 0 30rpx;
    border-radius: 50rpx;
    background: #F7534F;
    line-height: 50rpx;
    text-align: center;
    color: #FFF;
    font-size: 24rpx;
    margin-bottom: 28rpx;
    z-index: 1;
  }
  .btn-disable {
    background: #EEE;
    color: #999;
  }
  .flow-wrapper {
    position: absolute;
    top: 462rpx;
    width: 690rpx;
    bottom: 68rpx;
    // height: 400rpx;
    border-radius: 32rpx 32rpx;
    // border: 1rpx solid #979797;
    box-shadow: 0 20px 40px 0 rgba(0,0,0,0.05);
    background: #fff;
    margin-left: 30rpx;
    --tab-active-color: #F7534F;
    --tab-normal-color: #999999;
    --tab-active-line-height: 6rpx;
    --tab-large-font-size: 32rpx;
    --tab-divider-height: 1rpx;
    --tab-divider-color: #EEE;
    --tab-large-padding: 34rpx;
    transition: all 0.5s;

    .mt-tab-item-active-line--narrow-large {
      width: 138rpx;
      margin-left: -66rpx;
    }

    .mt-tab-item-active-line{
      background: #F7534F;
    }
    .empty-list-tip {
      margin-top: 232rpx;
      text-align: center;
      font-size: 36rpx;
      color: #BBB;
    }
    .flow-list {
      width: 100%;
      height: 100%;
      overflow: auto;
      .flow-item {
        flex-direction: row;
        justify-content: space-between;
        width: 690rpx;
        height: 140rpx;
        padding: 30rpx 40rpx 0 40rpx;
      }

      .equity-item{
        display: flex;

        .flow-name{
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 320rpx;
        }
      }
      .flow-item-left {
        .flow-title {
          font-size: 30rpx;
          color: #333;
          margin-bottom: 6rpx;
        }
        .flow-date {
          font-family: PingFangSC-Semibold;
          font-size: 24rpx;
          color: #BBB;
          line-height: 34rpx;
        }
      }
      .flow-amount {
        font-size: 40rpx;
        color: #F7534F;
        font-weight: 700;
      }

      .flow-name{
        font-family: D-DIN-Bold;
        font-size: 30rpx;
        color: #F7534F;
        font-weight: 700;
      }
      .payout-amount {
        color: #999;
      }
    }
    .flow-tab-content {
      position: absolute;
      top: 110rpx;
      bottom: 0;
      left: 0;
      right: 0;
    }
    .upgrade-tip {
      margin: 232rpx 0 0 238rpx;
      span {
        font-size: 36rpx;
        color: #BBB;
      }
    }
  }
  .list-desc {
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    font-size: 24rpx;
    text-align: center;
    color: #BBB;
  }
}
