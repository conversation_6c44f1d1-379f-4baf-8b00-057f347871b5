import { createElement, Component, createRef } from 'rax';
import { connect } from 'rax-redux';
import View from 'rax-view';
import Text from 'rax-text';
import Image from "@/components/image";
// import { history } from 'rax-app';
import ScrollView from 'rax-scrollview';
import './index.scss';
import { StoreDispatch, StoreState, dispatch, store } from '@/store';
import { convertCash2Display } from '@/utils/amount';
import '@alifd/meet/es/core/index.css';
import { Tab } from '@alifd/meet';
import IconBack from './images/<EMAIL>'
import ArrowsRightImg from './images/arrows.png';
import { withPageLifeCycle } from 'rax-app';
import { openURL } from "@/pages/index/task/help";
import { inPeriod } from "@/utils/date";
import fact from '@/lib/fact'
import { initPageLogParam } from '@/utils/log'
import { IFeFlowItem, IEquityFlowItem, EQUITY_TAG_TITLE_MAP } from "@/store/models/currency/typings";
import { formatTimestamp } from '@/utils/date';
import ConversionWay,{event} from './components/ConversionWay/index';
import { PAGE_ID } from '@/store/models/route';
import eventer, { EventMap } from "@/lib/utils/event";

let defaultActiveKey = 1
interface IState {
  currentTabKey: number;
  hasGetData: boolean;
  coinPageNo: number;
  cashPageNo: number;
  noticeHeight: number;
  showOldWalletBubble: boolean;
  equityPageNo: number;
}
class PageFlow extends Component<IProps, IState> {
  lockTime: number;
  scrollView = createRef();
  noticeNode = createRef();
  state:IState = {
    currentTabKey: 1,
    hasGetData: false,
    cashPageNo: 1,
    coinPageNo: 1,
    equityPageNo: 1,
    noticeHeight: 0,
    showOldWalletBubble: false
  }

  checkOldWalletBubble = () => {
    const hasShowOldWalletBubble = localStorage.getItem('hasShowOldWalletBubble')
    if (!hasShowOldWalletBubble) {
      localStorage.setItem('hasShowOldWalletBubble', '1')
      this.setState({
        showOldWalletBubble: true
      })
      const timer = setTimeout(() => {
        this.setState({
          showOldWalletBubble: false
        })
        clearTimeout(timer)
      }, 3000);
    } else {
      this.setState({
        showOldWalletBubble: false
      })
    }
  }

  componentDidMount() {
    this.lockTime = Date.now();
    this.props.queryExchangeInfo({});
    this.getFlowList()
    this.props.fetchTotalCashIncome({})
    this.setState({
      currentTabKey: defaultActiveKey
    })
    this.listen()
    initPageLogParam('profit', true);
    this.checkOldWalletBubble();
    setTimeout(() => {
      this.setState({
        noticeHeight: this.noticeNode.current?.offsetHeight,
      })
    }, 500);
    this.exposureFlow()
  }
  // 页面曝光
  exposureFlow = () => {
    if (this?.props?.exchangeWay === 'manual') {
      fact.exposure('change_cash_exposure', {
        c: 'button',
        d: 'withdraw',
      })
    }
    fact.exposure('fuli_exposure', {
        c: 'button',
        d: 'withdraw',
    })
    fact.exposure('change_exposure', {
      c: 'button',
      d: 'withdraw',
    })
    if (store?.getState()?.currency?.flowPageTabKey === 1) {
      fact.exposure('coin_detailsi_exposure', {
        c: 'button',
        d: 'withdraw',
      })
    } else {
      fact.exposure('cash_detailsi_exposure', {
        c: 'button',
        d: 'withdraw',
      })
    }
  }

  getInitData = () => {
    this.props.queryAllCurrency({});
    this.getFlowList();
  }


  componentWillUnmount() {
    this.unlisten();
  }

  listen() {
    eventer.on(EventMap.PageVisible, this.appear);
  }

  unlisten() {
    eventer.off(EventMap.PageVisible, this.appear);
  }

  appear = () => {
    if (Date.now() - this.lockTime < 1000) return;
    this.getInitData()
  };

  getFlowList = async () => {
    if (!this.props.isLogin) {
      await this.props.setupUserInfo()
    }
    Promise.all([this.props.fetchFlowList({
      type: 'coin',
      pageNo: 1
    }), this.props.fetchFlowList({
      type: 'cash',
      pageNo: 1
    }),
    dispatch.currency.queryAwardList({pageNo: 1})
  ]).then(() => {
      this.setState({
        hasGetData: true
      })
    })
  }

  getMoreList = (type) => {
    if (type === 'coin') {
      this.props.fetchFlowList({
        type,
        pageNo: this.state.coinPageNo + 1
      })
      this.setState({
        coinPageNo: this.state.coinPageNo + 1
      })
    } else if(type === 'cash'){
      this.props.fetchFlowList({
        type,
        pageNo: this.state.cashPageNo + 1
      })
      this.setState({
        cashPageNo: this.state.cashPageNo + 1
      })
    } else {
      dispatch.currency.queryAwardList({pageNo: this.state.equityPageNo + 1});
      this.setState({
        equityPageNo: this.state.equityPageNo + 1
      })
    }
  }

  onShow = () => {
    defaultActiveKey = store.getState().currency.flowPageTabKey
  }

  handleBack = () => {
    // history.goBack();
    dispatch.route.back();
  }

  handleWithdraw = () => {
    fact.click('fuli_click', {
      c: 'button',
      d: 'withdraw'
    })
    const {withdrawLink } = this.props;
    openURL(withdrawLink);
  }

  // 去旧版钱包
  handleToOldWallet = () => {
    const { oldWithdrawlink } = this.props;
    fact.click('other_cash_click', {
      c: 'button',
      d: 'withdraw'
    })
    openURL(oldWithdrawlink)
  }

  handleTabChange = (key) => {

    fact.click('profit_tab_click', {
      c: 'list',
      d: 'withdraw',
      tab_status: +key === 1 ? 'coin' : 'cash'
    })
    this.setState({
      currentTabKey: key
    })
  }

  renderTotalIncome = () => {
    const { totalIncomeAmount, flowList } = this.props
    if (!flowList.length) {
      return null
    }
    return totalIncomeAmount ? <View className="total-income">
      累计收益<Text className="total-income-amount din-num"> {totalIncomeAmount / 100}元</Text>
    </View> : null
  }

  renderList = (list: IFeFlowItem[], type:'coin' | 'cash') => {
    if (!list.length && this.state.hasGetData) {
      return <View className="empty-list-tip">
        暂时没有{type === 'coin' ? '元宝' : '现金'}收益哦
      </View>
    }
    return <ScrollView ref={this.scrollView}
                       onEndReachedThreshold={200}
                       onEndReached={() => this.getMoreList(type)}
                       style={{height: '100%'}}>
      {
        list.map((item, index) => {
          const isPayout = item.state.toLowerCase().includes('payout')
          const amount = type === 'coin' ? item.amount : item.amount / 100
          return <View className="flow-item" key={index}>
            <View className="flow-item-left">
              <View className="flow-title">{item.title}</View>
              <View className="flow-date">{item.desc}</View>
            </View>
            <View className={`flow-amount din-num ${isPayout ? 'payout-amount' : ''}`}>{isPayout ? '-' : '+'}{amount}</View>
          </View>
        })
      }
      <View x-if={list.length > 30} className="list-desc">仅展示最近30天明细</View>
    </ScrollView>
  }

  renderEquityList = (list: IEquityFlowItem[]) => {
    if (!list.length && this.state.hasGetData) {
      return <View className="empty-list-tip">
        暂时没有权益收益哦
      </View>
    }
    return (
      <ScrollView
            ref={this.scrollView}
            onEndReachedThreshold={200}
            onEndReached={() => this.getMoreList('equity')}
            style={{height: '100%'}}>
        {
          list.map((item, index) => {
            const equityTitle = EQUITY_TAG_TITLE_MAP[item?.tag];
            return (
              <View className="flow-item equity-item" key={index}>
                <View className="flow-item-left">
                  <View className="flow-title">{equityTitle || ''}</View>
                  <View className="flow-date">{formatTimestamp(item?.createTime, 'YYYY.MM.DD hh:mm:ss')}</View>
                </View>
                <View className="flow-name">{item?.name}</View>
            </View>
            )
          })
        }
        <View x-if={list.length > 30} className="list-desc">仅展示最近30天明细</View>
    </ScrollView>
    )
  }
  // 元宝兑换方式切换
  cutConversion = () => {
    fact.click('change_click', {
      c: 'button',
      d: 'withdraw'
    })
    const type = store?.getState()?.currency?.exchangeWay || 'auto'
    event.emit('OpenConversionWayPanel',{type})
  }
  // to兑换页
  toConversionPage = () => {
    fact.click('change_cash_click', {
      c: 'button',
      d: 'withdraw'
    })
    dispatch.route.push(PAGE_ID.conversion);
  }
  render() {
    const { noticeHeight, showOldWalletBubble } = this.state
    const { showNotice, noticeList, flowList, cashFlowList, oldCashAmount = 0, clientType, exchangeWay, equityFlowList } = this.props
    return (
      <View className="p-flow">
        <View className="p-flow-header">
          <Image onClick={this.handleBack} className="back-icon" source={IconBack} style={{width: '44rpx', height: '44rpx'}} />
          <Text className="header-title">我的收益</Text>
          <View className="old-wallet" onClick={this.handleToOldWallet} x-if={clientType === 'UCMobile'} >
            <Text className="other-cash-title">其他现金</Text>
            <Text className="other-cash-amount">{oldCashAmount}元</Text>
            <View className="wallet-tip" x-if={showOldWalletBubble}>
              <Text className="tip-desc">以前的钱包在这里</Text>
            </View>
          </View>
        </View>
        <View className="earnings-info">
          <View className="my-assets-ingot">
            <View className="assets-num">{this.props.coin}</View>
            <View className="my-assets-bottom">
              <View className="assets-title">我的元宝</View>
              {exchangeWay === 'manual' && (
                <View onClick={this.toConversionPage} className="assets-tag">
                  换现金
                  <Image source={ArrowsRightImg} style={{ width: '24rpx', height: '24rpx' }} />
                </View>
              )}
            </View>
          </View>
          <View className="my-assets-cash">
            <View className="assets-num">¥{this.props.amount2Cash}</View>
            <View className="my-assets-bottom">
              <View className="assets-title">我的现金</View>
                <View onclick={this.handleWithdraw} className="assets-tag">
                  提现兑换
                  <Image source={ArrowsRightImg} style={{ width: '24rpx', height: '24rpx' }} />
                </View>
            </View>
          </View>
        </View>
        <View className="conversion-mode">
          元宝兑换方式: { exchangeWay === 'auto' ? '自动兑换' : '手动兑换'}
          <View onClick={this.cutConversion} className="cut">切换</View>
        </View>
        <View x-if={showNotice} className="system-notice" ref={this.noticeNode}>
          <Text className="title">重要通知</Text>
          {
            noticeList.map(notice => {
              return (
                <Text>{notice}</Text>
              );
            })
          }
        </View>
        <View className="flow-wrapper" style={{top: showNotice ? `${470 + (noticeHeight * 2 + (noticeHeight ? 20 : 0))}rpx` : '472rpx'}}>
          <Tab contentClassName="flow-tab-content"  size="large" activeLineType="narrow" defaultActiveKey={defaultActiveKey} onChange={(key) => {
            this.handleTabChange(key)
          }}>
            <Tab.Item key={1} title="元宝收益">
              <View className="flow-list" >
                  {this.renderList(flowList, 'coin')}
              </View>
            </Tab.Item>
            <Tab.Item key={3} title="权益明细">
              <View className="flow-list">
                {this.renderEquityList(equityFlowList)}
              </View>
            </Tab.Item>
            <Tab.Item key={2} title="现金收益">
              <View className="flow-list" >
                  {this.renderTotalIncome()}
                  {this.renderList(cashFlowList, 'cash')}
              </View>
            </Tab.Item>
          </Tab>
        </View>
        <ConversionWay />
      </View>
    );
  }
}

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {};

const mapState = (state: StoreState) => {
  const { coin, amount = 0, oldCashAmount = 0, exchangeWay } = state.currency;
  const { noticeStartTime, noticeEndTime, noticeList } = state.cms.flowNoticeConfig || {}
  const showNotice = inPeriod(noticeStartTime, noticeEndTime, state.task.now)

  return {
    coin,
    amount2Cash: convertCash2Display(amount),
    status: state.user.status,
    flowList: state.currency.flowList || [],
    cashFlowList: state.currency.cashFlowList || [],
    totalIncomeAmount: state.currency.totalIncomeAmount,
    withdrawLink: state.app.withdrawLink,
    flowRateDesc: state.app?.flowRateDesc || '',
    showNotice,
    noticeList,
    rate: state.task.rate,
    isLogin: state.user?.isLogin,
    clientType: state.app.clientType,
    oldWithdrawlink: state.app.oldWithdrawlink,
    oldCashAmount: convertCash2Display(oldCashAmount),
    equityFlowList: state.currency.equityFlowList || [],
    exchangeWay,
  }
};

const mapDispatch = (dispatch: StoreDispatch) => ({
  init: dispatch.app.init,
  queryAllCurrency: dispatch.currency.queryAllCurrency,
  queryExchangeInfo: dispatch?.currency?.queryExchangeInfo,
  fetchFlowList: dispatch.currency.fetchFlowList,
  fetchTotalCashIncome: dispatch.currency.fetchTotalCashIncome,
  setupUserInfo: dispatch.user.setupUserInfo
});

export default connect(mapState, mapDispatch)(withPageLifeCycle(PageFlow));
