import { createElement, useEffect, useState } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import EventEmitter from 'eventemitter3';
import CheckboxImg from './images/checkbox.png';
import CheckedImg from './images/checked.png';
import './index.scss';
import { store } from '@/store';

export const event = new EventEmitter();

const Index = () => {
  const [showPanle, setShowPanle] = useState(false);
  const [type, setType] = useState<'manual' | 'auto'>('auto');
  useEffect(() => {
    event.on('OpenConversionWayPanel', openPanel);
    event.on('HideConversionWayPanel', closePanel);
    return () => {
      event.off('OpenConversionWayPanel', openPanel); 
      event.off('HideConversionWayPanel', closePanel);
    }
  }, []);
  const openPanel = (params: { type: 'manual' | 'auto' }) => {
    setType(params?.type);
    setShowPanle(true);
  };
  const closePanel = () => {
    setShowPanle(false);
  };
  // 选择选项
  const selectOption = (value) => {
    setType(value);
  };
  // 提交选项
  const CutConversionWay = async () => {
    await store?.dispatch?.currency?.cutExchangeWay(type);
    closePanel();
  };
  return (
    <View className="conversion-panel-comp" style={{ display: showPanle ? 'block' : 'none' }}>
      <View onClick={closePanel} className="panel-mask" />
      <View className={`conversion-content ${showPanle ? 'panel-appear' : ''}`}>
        <View onClick={() => selectOption('auto')} className="conversion-option">
          <View className="conversion-option-left">
            <View className="conversion-option-title">自动兑换</View>
            <View className="conversion-option-desc">每天0点自动兑换成现金</View>
          </View>
          <View clssName="conversion-option-right">
            <Image source={type === 'auto' ? CheckedImg : CheckboxImg} style={{ width: '32rpx', height: '32rpx' }} />
          </View>
        </View>
        <View onClick={() => selectOption('manual')} className="conversion-option">
          <View className="conversion-option-left">
            <View className="conversion-option-title">手动兑换</View>
            <View className="conversion-option-desc">手动兑换现金，不限次数</View>
          </View>
          <View clssName="conversion-option-right">
            <Image source={type === 'manual' ? CheckedImg : CheckboxImg} style={{ width: '32rpx', height: '32rpx' }} />
          </View>
        </View>
        <View className="operation">
          <View onClick={CutConversionWay} className="button">
            确认
          </View>
        </View>
      </View>
    </View>
  );
};

export default Index;
