.conversion-panel-comp {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 99999;
  .panel-mask {
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.5;
    position: absolute;
    top: 0;
    left: 0;
  }
  .conversion-content {
    width: 100%;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    will-change: transform;
    transform: translateY(0);
    background: #ffffff;
    border-radius: 48rpx 48rpx 0 0;
    z-index: 9999;
    padding: 64rpx;
    padding-bottom: 104rpx;
    .conversion-option {
      margin-bottom: 38rpx;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .conversion-option-left {
        .conversion-option-title {
          font-family: PingFangSC-Semibold;
          font-size: 34prx;
          line-height: 48rpx;
          color: #12161a;
          letter-spacing: 0;
          font-weight: 600;
        }
        .conversion-option-desc {
          margin-top: 6rpx;
          font-family: PingFangSC-Regular;
          font-size: 26rpx;
          line-height: 36rpx;
          color: #859199;
          letter-spacing: 0;
          font-weight: 400;
        }
      }
    }
    .operation {
      margin-top: 26rpx;
      .button {
        background: #F7534F;
        border-radius: 60rpx;
        font-family: PingFangSC-Medium;
        font-size: 40rpx;
        color: #FFFFFF;
        text-align: center;
        font-weight: 500;
        line-height: 128rpx;
      }
    }
  }
  .panel-appear {
    animation: panelOpen 0.3s ease-in forwards;
  }
}

@keyframes panelOpen {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}
