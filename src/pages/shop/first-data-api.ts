// TIPS: 该模块会引入到mini-js中，应尽可能减少没必要的引用，以达到mini-js体积最小
import { getUserInfo } from '@/lib/ucapi';
import { queryDiamondConf, queryTask, queryShopList } from '@/http';

// GUIDANCE: 获取首屏数据方法 (需保证此方法在node和web都可调用)
// 接口出错时建议返回null，或直接抛出错误，否则会影响SSR失败时的前端hydrate。
// 首屏页面不依赖数据接口的情况，可直接返回空对象 {} (注意，非null)
export interface IFirstData {
}

export async function getFirstData() {
  // TIPS:
  // Broccoli配置的运营数据 moduleData
  // console.log(getModuleData());
  let data
  try {
    const userInfo: any = await getUserInfo();
    console.log('userInfo:', userInfo)
    const kps = userInfo?.kps_wg
    if (!kps) return null
    const [diamondConf, taskRes, shopList] = await Promise.all([queryDiamondConf(kps), queryTask(kps), queryShopList()])
    data = {
      userInfo,
      diamondConf,
      queryTaskRes: taskRes,
      shopList: shopList.items,
      // multiBalance
    }
  } catch (err) {
    data = null
  }
  return data
}
