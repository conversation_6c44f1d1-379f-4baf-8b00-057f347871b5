import { render, createElement } from 'rax';
import DriverUniversal from 'driver-universal';
import Comp from './index';
import { setGlobalFirstData, getGlobalFirstData, isWithSkeleton } from '@/lib/render-utils/csr';
import { renderAction, perfMark } from '@/lib/render-utils/perf';

perfMark(renderAction.bootstrapExecute);

const handleCsrData = (data) => {
  console.log('CSR，更新FirstData');
  perfMark(renderAction.csrGotData);
  setGlobalFirstData(data);
}

(async function () {
  if (window.__CSRFirstDataPms__) {
    try {
      if (isWithSkeleton()) {
        const data = await window.__CSRFirstDataPms__;
        handleCsrData(data);
      } else {
        window.__CSRFirstDataPms__.then(handleCsrData);
      }
    } catch (err) {
      console.error(err);
    }
  }

  const firstData = getGlobalFirstData();
  perfMark(renderAction.hydrate);
  render(
    <Comp firstData={firstData} />,
    document.getElementById('root'),
    { driver: DriverUniversal, hydrate: true },
  );
})();



