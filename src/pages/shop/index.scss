* {
  user-select: none;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body,
#root,
.homeContainer,
.cardWrapper {
  height: 100%;
}

.homeContainer {
  position: relative;
  align-items: center;
  background-color: #fff;
}

.homeTitle {
  font-size: 45rpx;
  font-weight: bold;
  margin: 20rpx 0;
}

.homeInfo {
  font-size: 36rpx;
  margin: 8rpx 0;
  color: #555;
}

.row {
  display: flex;
  flex-direction: row;
}

.align-c {
  align-items: center;
}

.j-center {
  justify-content: center;
}

//.header-comp {
//  width: 100%;
//  height: 310rpx;
//  background-image: url("./assets/<EMAIL>");
//  background-size: 100% auto;
//  background-repeat: no-repeat;
//  .back-icon {
//    position: absolute;
//    top: 106rpx;
//    left: 24rpx;
//  }
//}
