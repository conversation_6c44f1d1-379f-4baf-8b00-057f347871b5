.shoping-index {
  height: 100%;
  width: 100%;
  position: relative;
  overflow-y: scroll;
  background-color: rgb(246, 249, 255);
  &::-webkit-scrollbar {
    display: none;
  }
  .shoping-header {
    position: relative;
    height: 310rpx;
    width: 100%;
    background-image: url("./assets/top.png");
    background-repeat: no-repeat;
    background-position: center top;
    background-size: contain;
    z-index: 9;
    .shoping-back {
      position: absolute;
      width: 64rpx;
      height: 64rpx;
      left: 24rpx;
      top: 106rpx;
    }
  }
  .shoping-progress-bar {
    position: sticky;
    top: 60rpx;
    padding: 50rpx 0 0rpx;
    z-index: 2;
    background-color: rgb(251, 235, 226);
  }
  .shoping-container {
    min-height: calc(100vh);
  }
}

.btn-to-top {
  position: fixed;
  right: 36rpx;
  bottom: 60rpx;
  width: 96rpx;
  height: 96rpx;
  border-radius: 96rpx;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-shadow: 0 3px 6px 0 rgba(0,0,0,0.15);
}
