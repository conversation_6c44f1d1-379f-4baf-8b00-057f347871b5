.skeleton-box {
  width: 100%;
  flex-wrap: wrap;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  .skeleton {
    height: 506rpx;
    width: 342rpx;
    border-radius: 24rpx;
    margin-bottom: 20rpx;
  }
}

.skeleton-animation {
  background: #aaa;
  animation: loading 2s ease infinite;
}

@keyframes loading {
  0% {
    background-size: 300% 100%;
    background-image: linear-gradient(100deg, #eee 40%, #fff 50%, #eee 60%);
    background-position: 100% 50%;
  }

  100% {
    background-size: 300% 100%;
    background-image: linear-gradient(100deg, #eee 40%, #fff 50%, #eee 60%);
    background-position: 0 50%;
  }
}
