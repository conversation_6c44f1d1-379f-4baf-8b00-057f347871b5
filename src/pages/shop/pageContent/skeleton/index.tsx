import { FC, createElement } from "rax";
import View from "rax-view";
import './index.scss';

const Skeleton: FC = () => {
  return (
    <View className="skeleton-box">
      <View className="skeleton skeleton-animation"></View>
      <View className="skeleton skeleton-animation"></View>
      <View className="skeleton skeleton-animation"></View>
      <View className="skeleton skeleton-animation"></View>
      <View className="skeleton skeleton-animation"></View>
      <View className="skeleton skeleton-animation"></View>
    </View>
  )
}

export default Skeleton