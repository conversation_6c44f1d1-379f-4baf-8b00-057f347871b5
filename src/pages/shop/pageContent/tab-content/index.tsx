import { FC, createElement } from "rax";
import View from "rax-view";
import './index.scss';
import { shopType } from "@/store/models/shop";

interface ITabContent {
  shopingType: shopType;
  children: JSX.Element | JSX.Element[];
}

const TabContent: FC<ITabContent> = ({
  shopingType,
  children,
}) => {
  // const translateX = shopingType === 'low_price' ? '0%' : (shopingType === 'you_like' ? '-100%' : '-200%')
  return (
    <View className="shoping-list-area">
      <View className="shoping-list-wrap" style={{'transform': `translateX(${'0%'})`}}>
        { children }
      </View>
    </View>
  )
}

export default TabContent