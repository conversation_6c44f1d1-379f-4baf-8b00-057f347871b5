import { FC, createElement, useState, useMemo, UIEvent, useCallback, useRef, useLayoutEffect } from "rax";
import View from "rax-view";
import Image from "@/components/image";
import { isIOS } from "@/lib/universal-ua";
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import IconToTop from "@/pages/index/shoppingList/assets/<EMAIL>";
import { useSelector, StoreState } from 'rax-redux';

import './index.scss';

import back from './assets/<EMAIL>';

import TaskProgress from './shoping-progress/index';
import TabControl from './tab-control/index';
import TabContent from "./tab-content";
import GoodsContainer from "./goods-container";
import { shopListResponse, shopType } from "@/store/models/shop";
import useCountDown from "./shoping-progress/useCountDown";
import { dispatch, store } from "@/store";

const Index: FC = () => {
  const indexRef = useRef<null | HTMLDivElement>(null)
  const [isLoad, setIsLoad] = useState(false);
  const [shopingType, setshopingType] = useState<shopType>('low_price');
  const [isStick, setStick] = useState(false)

  const { isDone, isEmpty } = useSelector((state: StoreState) => {
    return state?.shop[shopingType + 'GoodsList'] || { isDone: false, isEmpty: false }
  })

  const countDownTime  =  store.getState()?.app?.countDownTime || 0;
  useLayoutEffect(() => {
    const progressDom = document.querySelector('.shoping-progress-bar');
    if (progressDom) {
      const { top } = progressDom.getBoundingClientRect()
      if (top < 40) {
        indexRef.current?.scrollTo(0, 135)
      }
    }
  }, [shopingType])

  const [countDown, countDownPlayer] = useCountDown(countDownTime, 5);

  const handleScroll = (event: UIEvent<HTMLDivElement>) => {
    const isReachBottom = checkScrollBottom(event)
    if (isReachBottom && !isLoad && !isDone && !isEmpty) {
      addPageList(shopingType)
    }
    if (!isEmpty) {
      countDownPlayer()
    }
    const progressDom = document.querySelector('.shoping-progress-bar');
    if (progressDom) {
      const { top } = progressDom.getBoundingClientRect()
      if (top < 40) {
        setStick(true)
      } else {
        setStick(false)
      }
    }
  }

  const addPageList = (shopingType: shopType) => {
    setIsLoad(true)
    return dispatch.shop.addShopList(shopingType)
      .then((res: shopListResponse) => {
        if (res.goodsList.length) {
          dispatch.shop.preLoadingImage(res)
            .then(() => {
              setTimeout(() => setIsLoad(false), 100)
            })
        } else {
          dispatch.shop.updateTaobaoGoodsList(res)
          setTimeout(() => setIsLoad(false), 100)
        }
        return {length: res.goodsList.length}
      })
  }

  const checkScrollBottom = useCallback(
    (event: UIEvent<HTMLDivElement>) => {
      const { scrollHeight, scrollTop, clientHeight } = event.currentTarget;
      if ((scrollTop + clientHeight) > (scrollHeight - (clientHeight * 2))) {
        return true
      } else {
        return false
      }

    }, [])

  const header = useMemo(() => {
    const goBack = () => {
      if (isIOS) {
        jsbridge.exec('biz.openPageUrl', { url: 'ext:back' });
      } else {
        jsbridge.exec('biz.closeWebPage');
      }
    }
    return (
      <View className="shoping-header">
        <Image source={back} onClick={goBack} className="shoping-back" />
      </View>
    )
  }, []);

  const toTop = () => {
    indexRef.current?.scrollTo({
      top: 0,
      // behavior: "smooth",
    });
  }

  return (
    <>
      <View
        ref={indexRef}
        className="shoping-index"
        onScroll={handleScroll}
      >
        {header}
        <View className="shoping-container">
          <TaskProgress countDown={countDown} />
          <TabControl shopingType={shopingType} setType={setshopingType} />
          <TabContent shopingType={shopingType}>
            <GoodsContainer key={1} visible={shopingType === 'low_price'} requestLoad={isLoad && shopingType === 'low_price'} shopType="low_price" addPageList={addPageList} isEmpty={isEmpty} />
            <GoodsContainer key={2} visible={shopingType === 'you_like'} requestLoad={isLoad && shopingType === 'you_like'} shopType="you_like" addPageList={addPageList} isEmpty={isEmpty} />
            <GoodsContainer key={3} visible={shopingType === 'hot_sell'} requestLoad={isLoad && shopingType === 'hot_sell'} shopType="hot_sell" addPageList={addPageList} isEmpty={isEmpty} />
          </TabContent>
        </View>
      </View>
      <View className="btn-to-top" x-if={isStick} onClick={toTop}>
        <Image source={IconToTop} style={{ width: '48rpx', height: '48rpx' }} />
      </View>
    </>

  )
}

export default Index
