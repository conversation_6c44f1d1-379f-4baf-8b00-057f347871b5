import { shopType } from "@/store/models/shop";
import { FC, createElement } from "rax";
import View from "rax-view";
import Text from "rax-text";
import './index.scss';

interface ITabControl {
  shopingType: shopType;
  setType: (type: shopType) => void
}

const TabControl: FC<ITabControl> = ({
  shopingType,
  setType,
}) => {
  return (
    <View className="shoping-tab-control">
      <View className={`tab-item ${shopingType === 'low_price' ? 'active' : ''}`} onClick={() => setType('low_price')}>
        <Text className="tab-item-text">低价抢购</Text>
      </View>
      <View className={`tab-item ${shopingType === 'you_like' ? 'active' : ''}`} onClick={() => setType('you_like')}>
        <Text className="tab-item-text">猜你喜欢</Text>
      </View>
      <View className={`tab-item ${shopingType === 'hot_sell' ? 'active' : ''}`} onClick={() => setType('hot_sell')}>
        <Text className="tab-item-text">今日热销</Text>
      </View>
    </View>
  )
}

export default TabControl

