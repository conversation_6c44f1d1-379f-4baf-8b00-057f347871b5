.shoping-tab-control {
  position: sticky;
  top: 162rpx;
  z-index: 1;
  height: 108rpx;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  background-color: #fbebe2;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  .tab-item {
    position: relative;
    height: 100%;
    line-height: 108rpx;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      height: 6rpx;
      width: 0%;
      border-radius: 6rpx;
      background-color: #B84718;
      transition: all .2s;
    }
    &.active {
      &::after {
        width: 100%;
      }
      .tab-item-text {
        opacity: 1;
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color: #B84718;
        letter-spacing: 0;
        text-align: center;
        font-weight: 600;
      }
      // border-bottom: 6rpx solid #B84718;
    }
    .tab-item-text {
      opacity: 0.7;
      font-family: PingFangSC-Regular;
      font-size: 32rpx;
      color: #fa6425;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      transition: all .2s;
    }
  }
}