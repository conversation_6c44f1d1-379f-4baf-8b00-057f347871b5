import { useState, useRef ,TouchEvent } from 'rax';

type IuseTouch = [number, (e: TouchEvent<HTMLDivElement>) => void, (e: TouchEvent<HTMLDivElement>) => void, boolean]

const useTouch = ():IuseTouch => {
  const [toucMoveY, setToucMoveY] = useState(0);
  const [stick, setStick] = useState(false)
  const moveY = useRef(0);

  const touchstart = (event: TouchEvent<HTMLDivElement>) => {
    moveY.current = event.touches[0].clientY;
  }

  const touchmove = (event: TouchEvent<HTMLDivElement>) => {
    const clientY = event.touches[0].clientY;
    const move = (moveY.current - clientY) * 1.5;
    setToucMoveY(Math.ceil(move))
    moveY.current = clientY
    const { scrollHeight, scrollTop, clientHeight } = event.currentTarget;
    if ((scrollTop + clientHeight) >= scrollHeight) {
      setStick(true)
    } else {
      setStick(false)
    }
  }

  return [toucMoveY, touchstart, touchmove, stick]
}

export default useTouch


