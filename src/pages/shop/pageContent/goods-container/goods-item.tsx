import { FC, createElement } from "rax";
import View from "rax-view";
import Text from "rax-text";
import Image from "@/components/image";
import tracker from "@ali/weex-toolkit/lib/tracker";
import { WPK_CATEGORY_MAP } from "@/constants/tracker_category";
import ucapi from "@/utils/ucapi";
import { addParams } from "@/utils/url";
import { shopType } from "@/store/models/shop";
import { store } from "@/store";
import fact from "@/lib/stat";

interface IGoodsItem {
  item: any;
  type: shopType
}

const GoodsItem: FC<IGoodsItem> = ({
  item,
  type,
}) => {
  const toGoods = (Goods) => {
    const pageNo = store.getState()?.shop[type+'GoodsList']?.pageNo
    tracker.log({
      category: WPK_CATEGORY_MAP.CLICK_TAOBAO_GOODS,
      w_succ: 1,
      sampleRate: 1,
      msg: type,
      c1: type,
      c2: String(item.finalPromotionPrice),
      c3: String(item.title),
      c4: String(pageNo),
      bl1: item.clickUrl
    })
    fact.click('shoppingfeed_click', {
      a: 'uclitefuli_tblm',
      b: 'index',
      c: 'tbshopping',
      d: 'index'
    })
    ucapi.base.openURL({
      url: addParams('http:' + Goods.clickUrl, {
        uc_biz_str: 'S%3Acustom%7CC%3Atitlebar_hover_2%7CK%3Atrue%7CN%3Atrue'
      })
    })
  }
  return (
    <View className="goods-item" onClick={() => toGoods(item)}>
      <View className="item-content">
        <Image className="goods-item-image" source={item.pictUrl} />
        <Text className="goods-item-title">{item.title}</Text>
        <View className="goods-item-info">
          <Text className="goods-item-price price">¥</Text>
          <Text className="goods-item-price">{item.finalPromotionPrice}</Text>
        </View>
      </View>
    </View>
  )
}

export default GoodsItem
