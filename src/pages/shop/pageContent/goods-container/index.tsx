import { shopType } from "@/store/models/shop";
import { FC, createElement, useEffect, useState, useRef, useCallback } from "rax";
import View from "rax-view";
import './index.scss';
import { useSelector } from 'rax-redux';
import { dispatch, store, StoreState } from '@/store';
import Skeleton from '../skeleton/index';
import GoodsItem from './goods-item';
import PreLoadingImage from "@/store/models/shop/preLoadingImage.class";

interface IGoodsContainer {
  shopType: shopType;
  requestLoad: boolean;
  visible: boolean;
  addPageList: (shopType: shopType) => Promise<{length: number}>;
  isEmpty: boolean;
}

const GoodsContainer: FC<IGoodsContainer> = ({
  shopType,
  requestLoad,
  visible,
  addPageList,
  isEmpty: empty,
}) => {
  const [show, setShow] = useState(visible)
  const [firstLoading, setFirstLoading] = useState(false);
  const firstLoadRef = useRef(false);
  const [isEmpty, setIsEmpty] = useState(empty);
  const shopTypeListType = shopType === 'low_price' ? 'low_priceGoodsList' : (shopType === 'hot_sell' ? 'hot_sellGoodsList' : 'you_likeGoodsList');
  const { goodsList, pageNo, isDone } = useSelector((state: StoreState) => {
    return state?.shop[shopTypeListType] || { goodsList: [], pageNo: 0 }
  })
  const preGoodsList = useSelector((state: StoreState) => state.shop.preGoodsList)
  const leftList = goodsList.filter((item, index) => {
    const even = index % 2 === 0;
    if (!item.moveBy) return even
    if (item.moveBy === 'toLeft') return true
    if (item.moveBy === 'toRight') return false
    return even
  });
  const rightList = goodsList.filter((item, index) => {
    const odd = index % 2 === 1;
    if (!item.moveBy) return odd
    if (item.moveBy === 'toRight') return true
    if (item.moveBy === 'toLeft') return false
    return odd
  });

  const retry = () => {
    addPageList(shopType)
      .then(({length}) => {
        if (length === 0) {
          setIsEmpty(true)
        }
      })
    setIsEmpty(false)
  }

  useEffect(() => {
    if (visible) {
      setTimeout(() => {
        setShow(true)
      }, 50);
    } else {
      setShow(false)
    }
  }, [visible])

  useEffect(() => {
    if (!goodsList.length && shopTypeListType !== 'low_priceGoodsList') {
      addPageList(shopType)
    }
  }, [])

  useEffect(() => {
    if (!firstLoadRef.current && goodsList.length) {
      const preLoadImages = goodsList.slice(0, 4);
      const imagesLoadPromise = preLoadImages.map((item, index) => {
        return new Promise((resolve, reject) => {
          let img = new Image();
          img.onload = function () {
            resolve(index)
          }
          img.src = item.pictUrl
        })
      })
      Promise.allSettled(imagesLoadPromise).then((res) => {
        console.log(shopType, 'setFirstLoading')
        setFirstLoading(true)
      })
      firstLoadRef.current = true
    }
  }, [goodsList])

  useEffect(() => {
    if (empty) {
      setIsEmpty(true)
    }
  }, [empty])

  const checkLayoutAligning = useCallback((leftListDom: Element, rightListDom: Element) => {
    const leftHeight = leftListDom.clientHeight;
    const rightHeight = rightListDom.clientHeight;

    const lastList = leftHeight > rightHeight ? leftListDom : rightListDom;
    const lastItem = lastList.lastChild as Element;
    const lastHeight = lastItem.clientHeight;

    if ((leftHeight > rightHeight) && (leftHeight - lastHeight > rightHeight)) {
      return 'toRight'
    }

    if ((rightHeight > leftHeight) && (rightHeight - lastHeight > leftHeight)) {
      return 'toLeft'
    }
    return 'none'
  }, [])

  useEffect(() => {
    // 每次数据加载结束后重新布局 确保两边高度一致
    if (goodsList.length > 30 && visible) {
      setTimeout(() => {
        const leftListDom = document.querySelector('.show > .shopping-list-left');
        const rightListDom = document.querySelector('.show > .shopping-list-right');
        if (leftListDom && rightListDom) {
          const checkResult = checkLayoutAligning(leftListDom, rightListDom);
          if (checkResult !== 'none') {
            const newGoodList = [...goodsList]
            const length = newGoodList.length;

            const isEvent = length % 2 === 0;
            let index = 1;
            if (isEvent) {
              if (checkResult === 'toRight') {
                index = 2
              }
            } else {
              if (checkResult === 'toLeft') {
                index = 2
              }
            }

            const target = newGoodList[length - index];

            newGoodList.splice(length - index, 1, {
              ...target,
              moveBy: checkResult,
            });
            dispatch.shop.updateState({
              [shopTypeListType]: {
                goodsList: newGoodList,
                pageNo: pageNo,
              }
            })
          }
        }
      }, (shopType === 'low_price' && goodsList.length <= 30) ? 1000 : 500);
    }
  }, [firstLoading, goodsList, visible])

  useEffect(() => {
    if (shopType === 'low_price') {
      const proloadIamgeHandler = new PreLoadingImage((listChunk) => {
        dispatch.shop.updateTaobaoGoodsList({goodsList: listChunk, goodsType: shopType, pageNo: 1})
      })
      proloadIamgeHandler.runPreload(preGoodsList)
    }
  }, [shopType, preGoodsList])
  return (
    <View className={`shopping-list ${show ? 'show' : ''}`} style={{ display: visible ? 'flex' : 'none' }}>
      <View className="shopping-list-left" x-if={firstLoading}>
        {
          leftList.map((item, index) => {
            return <GoodsItem item={item} key={item.tbGoodLink?.slice(0, 10) + item.title?.slice(0, 10) + index + item.moveBy} type={shopType} />
          })
        }
      </View>
      <View className="shopping-list-right" x-if={firstLoading}>
        {
          rightList.map((item, index) => {
            return <GoodsItem item={item} key={item.tbGoodLink?.slice(0, 10) + item.title?.slice(0, 10) + index + item.moveBy} type={shopType} />
          })
        }
      </View>
      <Skeleton x-if={!firstLoading && !isEmpty} />
      <View x-if={isEmpty} className="empty" onClick={retry}>
        <View className="empty-container">
          <View className="title">暂时没有商品</View>
          <View className="desc">先去做其他任务 稍后再来</View>
          <View className="retry-btn">重试</View>
        </View>
      </View>
      <View className="list-loading row" x-if={requestLoad}>
        更多好物加载中...
      </View>
      <View className="list-loading row" x-if={isDone && !isEmpty}>
        没有更多商品了...
      </View>
    </View>
  )
}

export default GoodsContainer
