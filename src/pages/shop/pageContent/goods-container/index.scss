.shopping-list {
  min-height: calc(100vh - (72rpx + 30rpx + 108rpx));
  width: 100%;
  align-items: flex-start;
  padding-top: 40rpx;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  opacity: 0;
  transition: opacity .2s;
  &.show {
    opacity: 1;
  }
  // overflow-y: hidden;
  // background-color: rgb(246, 249, 255);
  &::-webkit-scrollbar {
    display: none;
  }
  .shopping-list-left,
  .shopping-list-right {
    width: 50vw;
    align-items: center;
  }
  .shopping-list-left {
    padding-left: 6rpx;
  }
  .shopping-list-right {
    padding-right: 6rpx;
  }
  .goods-item {
    margin-bottom: 20rpx;
    .item-content {
      width: 342rpx;
      background-color: #fff;
      border-radius: 24rpx;
      box-shadow: 0 0 15rpx rgba(0, 0, 0, 0.1);
      .goods-item-image {
        width: 100%;
        // height: 342rpx;
        border-top-left-radius: 24rpx;
        border-top-right-radius: 24rpx;
      }
      .goods-item-title {
        padding: 0 14rpx;
        margin: 14rpx 0 4rpx;
        font-family: PingFangSC-Regular;
        font-size: 28rpx;
        color: #01255d;
        letter-spacing: 0;
        font-weight: 400;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
      }
      .goods-item-info {
        flex-direction: row;
        margin-left: 14rpx;
        margin-bottom: 20rpx;
        align-items: baseline;
      }
      .goods-item-price {
        font-family: PingFangSC-Medium;
        font-size: 36rpx;
        color: #fa6425;
        letter-spacing: 0;
        font-weight: 500;
        &.price {
          font-size: 24rpx;
          margin-right: 4rpx;
        }
      }
    }
  }
  .empty {
    margin-top: 300rpx;
    // height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
    .empty-container {
      margin-bottom: 160rpx;
      align-items: center;
      .title {
        font-size: 36rpx;
        color: #7e93b7;
        margin-bottom: 12rpx;
      }
      .desc {
        font-size: 28rpx;
        color: #b4c1d6;
        margin-bottom: 28rpx;
      }
      .retry-btn {
        width: 218rpx;
        height: 88rpx;
        justify-content: center;
        align-items: center;
        background: #eef2f6;
        border-radius: 88rpx;
        font-size: 32rpx;
        color: #7e93b7;
        font-weight: 700;
        margin-bottom: 100rpx;
      }
    }
  }
  .list-loading {
    width: 100%;
    font-size: 24rpx;
    color: #B4C1D6;
    justify-content: center;
    margin: 20rpx;
    text-align: center;
  }
}
