import { FC, createElement, useEffect, useState, useMemo } from "rax";
import { connect } from 'rax-redux';
import { dispatch, store, StoreState } from '@/store';
import View from "rax-view";
import Text from "rax-text";
import Image from "@/components/image";
import { TaskInfo, TASK_EVENT_TYPE } from "@/store/models/task/types";

import './index.scss';
import { INGOT_ICON } from '@/constants/static_img';
import fact from "@/lib/fact";

interface IProgress {
  taobaoTask: TaskInfo;
  countDown: number;
}

const Progress: FC<IProgress> = ({
  taobaoTask,
  countDown,
}) => {
  const progress = taobaoTask?.dayTimes?.progress || 0;
  const target = taobaoTask?.dayTimes?.target || 0;
  const amount = taobaoTask?.rewardItems[0]?.amount || 0;
  const completedTask = taobaoTask && (progress === target);
  const countDownTime = store.getState().app.countDownTime;

  const [showAward, setShowAward] = useState(false)
  useEffect(() => {
    if (taobaoTask && progress !== target) {
      if (countDown === 0) {
        dispatch.task.complete({ id: taobaoTask.id, type: 'complete', params: { task: taobaoTask, toast: false } })
          .then(() => {
            setShowAward(true)
            fact.event('shoppingfeed_completed', {
              a: 'uclitefuli_tblm',
              b: 'index',
              c: 'tbshopping',
              d: 'index',
              page: 'page_fulitblm',
            })
            setTimeout(() => {
              setShowAward(false)
              dispatch.app.init()
            }, 1000);
          })
      }
    }
  }, [countDown])

  const girth = 2 * Math.PI * 14;
  const schedule = ((countDownTime - countDown) / countDownTime).toFixed(2);
  return (
    <View className="shoping-progress-bar">
      <View className="shoping-progress">
        <View className="shoping-progress-left">
          <Text className="progresss-left-title">浏览商品{countDownTime}s领{amount}元宝</Text>
          <Text className="progresss-left-number">{progress}/{target}次</Text>
        </View>
        <View className="shoping-progress-right" x-if={completedTask}>
          <Text className="right-text">已完成</Text>
        </View>
        <View className="shoping-progress-right" x-else>
          <Text className="progress-right-timer">{countDownTime - countDown}s</Text>
          <Image className="ingot-icon" source={INGOT_ICON} style={{ width: '48rpx', height: '48rpx' }} />
          <svg width="32" height="32">
            <circle className="circle-inner" fill="none" cx="16" cy="16" r="14" stroke-dashoffset={314} />
            <circle x-if={Number(schedule)} className="circle-outer" fill="none" cx="16" cy="16" r="14" stroke-dasharray={`${Number(schedule) * girth} ${girth}`} />
          </svg>
          <View className="award-tip" style={{ opacity: showAward ? 1 : 0, transform: showAward ? 'scale(1)' : 'scale(0.8)' }}>
            <Image className="award-icon" source={INGOT_ICON} style={{ width: '32rpx', height: '32rpx' }}></Image>
            <View className="coin-num">+{amount}</View>
          </View>
        </View>
      </View>
    </View>
  )
}

type props = {
  countDown: number;
}

const mapState = (state: StoreState, props: props) => {
  const taskList = state?.task?.taskList || [];
  const taobaoTask = taskList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_TAOBAO_LIANMENG);
  return {
    taobaoTask,
    countDown: props.countDown
  };
}

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(Progress);
