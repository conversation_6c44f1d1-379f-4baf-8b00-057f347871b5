.shoping-progress {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 0 24rpx;
  height: 72rpx;
  border-radius: 72rpx;
  background-color: #fff;
  &::after {
    content: "";
    position: absolute;
    top: -40px;
    left: 0;
    right: 0;
    bottom: 99%;
    background-color: #fbebe2;
  }
  .shoping-progress-left,
  .shoping-progress-right {
    position: relative;
    flex-direction: row;
    align-items: center;
  }
  .shoping-progress-left {
    padding-left: 30rpx;
  }
  .shoping-progress-right {
    padding-right: 4rpx;
    .right-text {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #7e93b7;
      letter-spacing: 0;
      font-weight: 400;
      margin-right: 30rpx;
    }
  }
  .progresss-left-title {
    font-family: PingFangSC-Semibold;
    font-size: 28rpx;
    color: #fa6425;
    letter-spacing: 0;
    font-weight: 600;
  }
  .progresss-left-number {
    margin-left: 10rpx;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #b4c1d6;
    letter-spacing: 0;
    font-weight: 400;
  }
  .progress-right-timer {
    font-family: D-DIN-Bold;
    font-size: 28rpx;
    color: #fa6425;
    letter-spacing: 0;
    font-weight: 700;
    margin-right: 10rpx;
  }
}

.circle-inner {
  fill: none;
  stroke: #eef2f6;
  stroke-width: 3;
  stroke-linecap: round;
}
.circle-outer {
  fill: none;
  stroke: #fa6425;
  stroke-width: 3;
  stroke-linecap: round;
  transition: all 1s linear;
  transform: rotate(-90deg);
  transform-origin: center;
  transform-box: fill-box;
  z-index: 1;
}

.ingot-icon {
  position: absolute;
  right: 14rpx;
  top: 10rpx;
}

.award-tip {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
  width: 152rpx;
  height: 70rpx;
  opacity: 0.8;
  transform: scale(0.8);
  border-radius: 56rpx;
  background: #FA6425;
  transition: all 0.5s ease-in-out;
  -webkit-transition: all 0.3s ease-out;
  -moz-transition: all 0.3s ease-out; /* Firefox 4 */
  -webkit-transition: all 0.3s ease-out; /* Safari 和 Chrome */
  -o-transition: all 0.3s ease-out; /* Opera */
  color: #fff;
  flex-direction: row;
  align-items: center;
  font-size: 28rpx;
  font-weight: 700;
  justify-content: center;
  overflow: hidden;
  .award-icon {
    margin-right: 2rpx;
  }
}
