import { useState, useRef, useEffect } from "rax";

const useCountDown = (countDownTime: number, successionTume: number): [number, () => void] => {
  const [countDown, setCountDown] = useState(countDownTime);
  const timeId = useRef<null | NodeJS.Timeout | number>(null);
  const clearTimeId = useRef<null | NodeJS.Timeout | number>(null);
  const refCount = useRef(countDownTime);

  useEffect(() => {
    setCountDown(countDownTime)
    refCount.current = countDownTime
  }, [countDownTime])

  const setDown = () => {
    refCount.current = refCount.current - 1
    if (refCount.current < 0) {
      refCount.current = countDownTime
    }
    setCountDown(refCount.current)
  }

  const reckonTime = () => {
    if (timeId.current) {
      clearTimeout(clearTimeId.current!)
      clearTimeId.current = setTimeout(() => {
        clearInterval(timeId.current!)
        timeId.current = null
      }, successionTume * 1000)
    } else {
      clearTimeId.current = setTimeout(() => {
        clearInterval(timeId.current!)
        timeId.current = null
      }, successionTume * 1000)

      timeId.current = setInterval(() => {
        setDown()
      }, 1000)

      setDown()
    }
  }

  return [countDown, reckonTime]
}

export default useCountDown