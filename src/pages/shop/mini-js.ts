import { getFirstData } from './first-data-api';
import net from '@/lib/network';
import {
  setGlobalFirstData,
  isGlobalFirstDataEmpty,
  isAsyncSsrDocument,
  isWithSkeleton,
  checkSkeleton
} from '@/lib/render-utils/csr';
import { getPkgIdfromWormholeData } from '@/lib/render-utils/normal';
import wormholeData from '@/lib/wormhole-data';
import { renderAction, perfMark, perfMarkNodeFirstDataTime, perfMarkAsyncSsrDemote } from '@/lib/render-utils/perf';
import './index.scss';

perfMark(renderAction.miniJsExecute);

// GUIDANCE：是否使用并行SSR的缓存。根据场景按需使用。packId维度缓存
const USE_ASYNCSSR_CACHE = false;

// 骨架屏检测开关
const NEED_CHECK_SKELETON = false;

(async function () {

  // 检测骨架是否有问题
  if (DEV && NEED_CHECK_SKELETON) {
    checkSkeleton();
  }

  // 并行SSR请求的Promise，与SSR接口首屏接口竞争关系。
  let asyncRenderPromise = new Promise(() => { });

  // 标记是否需要延迟加载主bundle，在SSR或者并行SSR有HTML片段返回的时候，需要预留时间上屏，主bundle加载会阻塞上屏。
  let bundleShouldDelay = false;

  // 【并行SSR场景】
  if (isAsyncSsrDocument()) {
    let pathname = '';
    // 从主文档获取 broPackId，用于获取对应 broPackId 的并行 SSR 渲染片段
    const broPackId = window?.__INITIAL_DATA__?.pageInitialProps?.__broPackId;
    if (!DEV) {
      const broccoliPathRegExp = /\/apps\/(\S*)\/routes\/(\S*)/;
      const [_, appCode, routeCode] = window.location.pathname.match(broccoliPathRegExp) || [];
      if (appCode && routeCode) {
        pathname = `/api/v1/ssr/async-fetch/${appCode}/${routeCode}`;
        if (broPackId) pathname += `?broPackId=${broPackId}`;
      } else {
        // 对于没有匹配到appCode和routeCode的情况。pathname置空。将不进行并行SSR渲染。
        pathname = ''
      }
    } else {
      pathname = `${window.location.pathname}?wh_page_only=true&broPackId=${broPackId}`;
    }

    // 无path时直接返回。
    if (!pathname || !broPackId) return;

    // 使用并行SSR缓存。
    const asyncSsrStoKey = `asyncSsr_${window?.__INITIAL_DATA__?.pageInitialProps?.__broPackId}`;
    if (USE_ASYNCSSR_CACHE) {
      const pageHTMLCache = localStorage.getItem(asyncSsrStoKey);
      const container = document.getElementById('root');
      if (pageHTMLCache && container) {
        container.innerHTML = pageHTMLCache;
        perfMark(renderAction.asyncSsrCacheOnScreen);
        console.log('并行SSR缓存上屏');
      }
    }

    // const version = MAIN_VERSION;

    asyncRenderPromise = new Promise((resolve, reject) => {
      const customDocUrl = window.location.href.replace(/skeletonMode/g, 'skeletonBak');
      perfMark(renderAction.asyncSsrApiStart);
      net.get(pathname, {}, {
        headers: {
          'custom-doc-url': customDocUrl,
        }
      })
        .then(res => {
          if (!isGlobalFirstDataEmpty()) return;
          let data = res?.data || res;
          const { initialProps, html: pageHTML } = data;

          perfMarkNodeFirstDataTime(initialProps?.__duration);

          if (!DEV
            && (
              initialProps?.__broPackId !== getPkgIdfromWormholeData(wormholeData) // broccoli的packId匹配
              // || initialProps?.__ver !== version  // 代码版本号匹配（先不匹配版本号，beta测试时版本号带时间戳，对不上）
            )
          ) reject();
          if (pageHTML) {
            if (USE_ASYNCSSR_CACHE) {
              localStorage.setItem(asyncSsrStoKey, pageHTML);
            }
            const container = document.getElementById('root');
            if (container) {
              container.innerHTML = pageHTML;
              // 并行SSR有HTML片段，需预留上屏时间
              bundleShouldDelay = true;
              perfMark(renderAction.asyncSsrOnScreen);
              console.log('并行SSR上屏');
            }
          }
          resolve(initialProps.firstData);
        })
        .catch(reject);
    })
  }


  try {
    // 判断全局是否有firstData，没有的情况下需要调用首屏数据接口。
    // 有数据的时候，为串行SSR成功。不需要前端获取数据。
    if (isGlobalFirstDataEmpty()) {
      if (isAsyncSsrDocument()) {
        // 并行SSR：需要await，因为bootstrap的bundle加载解析可能会影响HTML片段上屏
        // 并行SSR及数据预取能力较为稳定时，C端可以不用另外获取数据进行竞争关系
        // const data = await Promise.race([asyncRenderPromise, getFirstData()]);
        let data;
        try {
          // 并行SSR请求
          data = await asyncRenderPromise;
          perfMarkAsyncSsrDemote(false);
        } catch {
          perfMarkAsyncSsrDemote(true);
          // 并行SSR出错，前端请求
          data = await getFirstData();
        }
        setGlobalFirstData(data);
        console.log('并行SSR模式，更新FirstData');
      } else {
        // CSR：发起请求后，立刻加载bootstrap的bundle，在hydrate前，再拿数据
        window.__CSRFirstDataPms__ = getFirstData();
      }
    } else {
      // 串行SSR：bundle需要delay，预留上屏时间
      bundleShouldDelay = true;

      perfMarkNodeFirstDataTime();
    }
  } catch (err) {
    console.error(err);
  }

  // 需要预留给上屏的时间，150为经验值，根据实际情况调整。
  let bootstrapDelayTime = bundleShouldDelay ? 150 : 0;
  if (bootstrapDelayTime === 0 && isWithSkeleton()) {
    bootstrapDelayTime = 50;
  }

  // TODO: webpackChunkName的前缀需要与页面名一致。多页应用需考虑。（后续用工具实现）
  // 异步加载web bundle
  // 使用setTimeout,让接口调用先发出去
  if ((window.__WaitPms__ || []).length > 0) {
    await Promise.all(window.__WaitPms__ || []);
  }
  window.__WaitPms__ = [];

  setTimeout(() => {
    import(
      /* webpackChunkName: "shop_bootstrap" */
      './bootstrap'
      );
  }, bootstrapDelayTime)

})()
