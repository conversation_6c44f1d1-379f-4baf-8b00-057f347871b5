import { FC, createElement } from "rax";
import View from "rax-view";
import Image from '@/components/image';
import { isIOS } from "@/lib/universal-ua";
import jsbridge from '@ali/weex-toolkit/lib/ucapi';

import BackIcon from '../assets/<EMAIL>';
import './index.scss'

const Header: FC = () => {
  const handleBack = () => {
    if (isIOS) {
      jsbridge.exec('biz.openPageUrl', { url: 'ext:back' });
    } else {
      jsbridge.exec('biz.closeWebPage');
    }
  }
  return <View className="header-comp">
    <View className="back-icon" onClick={handleBack}>
      <Image source={BackIcon} style={{width: '64rpx', height: '64rpx'}}/>
    </View>
  </View>
}

export default Header
