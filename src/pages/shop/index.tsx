import { createElement } from 'rax';
import { Provider } from 'rax-redux';
import { dispatch, store } from '@/store';
import { isWeb } from 'universal-env';
import { ssrGetInitialProps } from '@/lib/render-utils/ssr';
import { getFirstData, IFirstData } from './first-data-api';

import View from 'rax-view';
import BasicPage from '@/components/BasicPage';
// import config from '@/config'

import Shop from './pageContent/index';

import stat from '@/lib/stat';
import initClient from '@/lib/tracker/init-client';
import { renderAction, perfMark, getPerfWpkLogParams } from '@/lib/render-utils/perf';

import './index.scss';
// GUIDANCE: 初始化啄木鸟 & 初始化fact埋点
initClient()
stat.init({
  ev_ct: 'uclite_fuli',
  ev_sub: 'uclite_fuli_index',
  spma: 'uclitefuli_tblm',
  spmb: 'index',
}, 'uclitefuli_tblm');

class Reward extends BasicPage<IFirstData> {

  constructor(props) {
    super(props);
    this.handleFirstData(this.useFirstData);
  }

  // GUIDANCE：使用首屏数据
  useFirstData = (firstData) => {
    console.log(firstData, 'firstData')
    if (firstData?.userInfo && firstData?.queryTaskRes && firstData?.diamondConf) {
      dispatch.user.updateUserInfo(firstData.userInfo);
      dispatch.app.updateState(firstData.diamondConf);
      dispatch.task.updateTaskList(firstData?.queryTaskRes?.values || []);
      // dispatch.shop.updateTaobaoGoodsList({
      //   goodsList: firstData?.shopList || [],
      //   goodsType: 'low_price',
      //   pageNo: 1
      // });
      dispatch.shop.updateState({
        isUpdateFirstData: true,
        preGoodsList: firstData?.shopList || [],
      });
    }
    // 更新数据到store
    // dispatch.app.updateInitialPageData(firstData);
  }

  async componentDidMount() {
    const isUpdateFristData = store.getState().shop.isUpdateFirstData;
    await dispatch.user.setupUserInfo()
    if (!isUpdateFristData) {
      // dispatch.shop.queryFeatureShopList({pageNo: 1, shopType: 'low_price'});
      dispatch.app.init()
    }
    // 页面pv埋点
    stat.pv('page_fulitblm', {
      b: 'index'
    });

    stat.exposure('shoppingfeed_expo', {
      a: 'uclitefuli_tblm',
      b: 'index',
      c: 'tbshopping',
      d: 'index',
      page: 'page_fulitblm',
  })

    setTimeout(() => {
      // 模拟业务标记T2
      perfMark(renderAction.t2);

      // 上报渲染流程性能监控参数
      const logParams = getPerfWpkLogParams();
      if (logParams?.wl_avgv1 && logParams.wl_avgv1 < 5000) {
        // 过滤无效数据
        // TODO 上报性能监控
        console.log('渲染流程性能监控参数 >> ', logParams);
      }
    }, 1000);
  }

  FirstScreen() {
    /* GUIDANCE: 首屏组件 */
    return <Shop />
  }

  OtherScreen() {
    /* GUIDANCE: 次屏组件 */
  }

  /**
   * GUIDANCE: 自定义骨架屏
   * 默认firstData为null的时候渲染的内容作为骨架。
   * 可通过此方法返回自定义骨架屏。
   */
    // customSkeleton = () => {
    //   return <div>自定义骨架</div>;
    // }
  renderContent = () => {
    return (
      <Provider store={store}>
        <View className="homeContainer">
          {this.FirstScreen()}
          {isWeb ? this.OtherScreen() : null}
        </View>
      </Provider>
    );
  }
}

Reward.getInitialProps = ssrGetInitialProps(getFirstData);

export default Reward;
