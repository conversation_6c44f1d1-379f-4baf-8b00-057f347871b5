import {dispatch, StoreDispatch, StoreState} from '@/store';
import {Component, createElement} from 'rax';
import View from 'rax-view';
import Countdown from 'rax-countdown';
import { connect } from 'rax-redux';

import './index.scss';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';
import dayjs from 'dayjs';
import modal from '@/components/modals/modal';
import fact from '@/lib/fact';
import Toast from '@/lib/universal-toast';
import ucapi from "@/utils/ucapi";
import Fact from "@/components/Fact";
import { execWithLock } from "@/utils/lock";

type BoxAniStatus = 'collect' | 'full' | '';

class Treasure extends Component<IProps, IState> {
  state = {
    lock: false,
    aniInit: false,
    aniStatus: '',
    boxStatus: {
      diff: 0,
      isSameDay: false
    }
  }
  collectAni
  fullAni
  initLottie = () => {
    const fullDom = document.querySelector('#fullTreasure')
    const collectDom = document.querySelector('#treasureCollect')
    const aniStatus = this.getAniStatus()
    this.setState({
      aniStatus
    })
    if (!aniStatus) return
    import('lottie-web').then((module) => {
      const lottie = module.default
      if (aniStatus === 'collect' && collectDom) {
        if (this.fullAni) {
          this.fullAni.destroy()
          this.fullAni = null
        }
        if (this.collectAni) {
          this.collectAni.destroy()
          this.collectAni = null
        }
        this.collectAni = lottie.loadAnimation({
          container: collectDom,
          renderer: 'canvas',
          loop: true,
          autoplay: true,
          animationData: require('./assets/collect.json'),
          assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202204/86ca85/images/',
        });
      } else if (fullDom) {
        if (this.collectAni) {
          this.collectAni.destroy()
          this.collectAni = null
        }
        if (this.fullAni) {
          this.fullAni.destroy()
          this.fullAni = null
        }
        this.fullAni = lottie.loadAnimation({
          container: fullDom,
          renderer: 'canvas',
          loop: true,
          autoplay: true,
          animationData: require('./assets/full.json'),
          assetsPath: 'https://image.uc.cn/s/uae/g/1y/animate/202204/faf1a5/images/',
        })
      }
    }).then(() => {
      this.setState({
        aniInit: true
      })
    })
  }
  getAniStatus (): 'collect' | 'full' | '' {
    const boxStatus = this.getBoxStatus();
    let aniStatus: BoxAniStatus;
    if (boxStatus.diff < 1) {
      aniStatus = 'full';
    } else if (!boxStatus.isSameDay) {
      aniStatus = '';
    } else {
      aniStatus = 'collect';
    }
    return aniStatus;
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any) {
    // 用任务的变化驱动宝箱UI变化
    if ((!prevProps.treasureTask && this.props.treasureTask) ||
      (prevProps.treasureTask !== this.props.treasureTask)
    ) {
      const aniStatus = this.getAniStatus();
      if (aniStatus !== this.state.aniStatus) {
        this.initLottie();
      }
    }
  }

  componentDidMount() {
    if (this.props.treasureTask) {
      this.initLottie();
    }
  }
  complete = async () => {
    if (this.state.lock) return;
    this.setState({
      lock: true
    })
    const { treasureTask } = this.props;
    fact.click('box_click', { 
      c: 'box', 
      d: 'click', 
      state: 0,
      task_id: treasureTask?.id || '',
      task_name: treasureTask?.name || '',
      taskclassify: treasureTask?.taskClassify || '',
      groupcode: treasureTask?.groupCode || '', 
      award_amount: treasureTask?.rewardItems[0]?.amount || ''
    });
    const prizes = await dispatch.task.complete({ id: this.props.treasureTask.id, type: 'complete', params: { toast: false, task: this.props.treasureTask } });
    if (prizes) {
      modal.openTreasure(prizes, 'treasure');
      ucapi.mission.updateBoxState();
      this.updateTaskStatus()
    }
    this.setState({
      lock: false
    })
  };
  handleClickBox = () => {
    execWithLock('click_treasure', async () => {
      const boxStatus = this.getAniStatus()
      if (boxStatus === 'full') {
        this.complete()
        return
      }
      if (boxStatus === 'collect') {
        Toast.show('倒计时结束后再来领取')
        return;
      }
      return Toast.show('明天继续领')
    }, 2000)
  }
  renderTomorrow = () => {
    return (
      <View key="getTomorrow" className={'container-treasure-item'}  onClick={this.handleClickBox}>
        <View className="get-tomorrow" onClick={() => {
          const { treasureTask } = this.props;
          fact.click('box_click', { 
            c: 'box', 
            d: 'click', 
            state: 2,
            task_id: treasureTask?.id || '',
            task_name: treasureTask?.name || '',
            taskclassify: treasureTask?.taskClassify || '',
            groupcode: treasureTask?.groupCode || '', 
            award_amount: treasureTask?.rewardItems[0]?.amount || '' 
          });
        }}
        >
          <View className="countdown-desc tomorrow-desc">明天再来</View>
        </View>
      </View>
    );
  };
  renderReceive = () => {
    return (
      [
        <View key="openbox" className={'container-treasure-item'} onClick={this.handleClickBox}>
          <View id="fullTreasure" className="lottie-area" />
          <View x-if={this.state.aniInit} className="countdown-desc">开宝箱得元宝</View>
        </View>
      ]
    );
  };
  updateTaskStatus = async () => {
    await dispatch.app.updateAll();
  }
  renderCountdown = diff => {
    return (
      <View key="countdown" className={'container-treasure-item'} onClick={this.handleClickBox}>
        <View id="treasureCollect" className="lottie-area" />
        <View className="countdown-desc countdown-num">
          <Countdown
            timeRemaining={diff * 1000}
            formatFunc={time => {
              const alls = Math.floor(time / 1000);
              let m: string | number = Math.floor(alls / 60);
              let s: string | number = Math.floor(alls % 60);
              s = s < 10 ? `0${s}` : s;
              m = m < 10 ? `0${m}` : m;
              return `${m}:${s}`;
            }}
            onComplete={async () => {
              await this.updateTaskStatus()
            }}
          />
        </View>
      </View>
    );
  };
  getBoxStatus () {
    const {now, treasureTask} = this.props
    const beginTimeD = dayjs(treasureTask?.beginTime);
    const nowD = dayjs(now);
    // 时间秒差
    const diff = beginTimeD.diff(nowD, 's');
    const isSameDay = beginTimeD.isSame(nowD, 'd');
    return {
      diff,
      isSameDay
    }
  }
  renderTreasure = () => {
    const boxStatus = this.getBoxStatus()
    // 开始抽奖
    if (boxStatus.diff <= 1) return this.renderReceive();
    // 明天领取
    if (!boxStatus.isSameDay) return this.renderTomorrow();
    // 倒计时
    return this.renderCountdown(boxStatus.diff);
  };
  render () {
    const {treasureTask, hiddenTreasure} = this.props
    return treasureTask && <Fact
      key="myTreasure"
      c="box"
      d="show"
      expoLogkey="box_show"
      expoExtra={{
        task_id: treasureTask?.id,
        task_name: treasureTask?.name,
        taskclassify: treasureTask?.taskClassify,
        groupcode: treasureTask?.groupCode,
        award_amount: treasureTask?.rewardItems[0]?.amount || ''
      }}
      noUseClick
      style={{visibility: hiddenTreasure ? 'hidden' : 'visible'}}
      className={'container-treasure'}
    > {this.renderTreasure()}
    </Fact>
  }
}

interface IState {

}

const mapState = (state: StoreState) => {
  const { taskList, now } = state.task;
  const treasureTask = taskList.find(it => it.event === TASK_EVENT_TYPE.TREASURE);
  return { treasureTask, now };
};
const mapDispatch = (dispatch: StoreDispatch) => ({
  getAward: dispatch.task.complete,
});

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
  hiddenTreasure: boolean;
};
export default connect(mapState, mapDispatch)(Treasure)
