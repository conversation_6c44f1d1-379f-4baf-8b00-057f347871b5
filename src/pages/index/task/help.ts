import modal from '@/components/modals/modal';
import {PRIZE_CONFIG} from '@/constants';
import {dispatch, store} from '@/store';
import {
  IWord,
  PRIZE_CODE,
  RewardItem,
  SignInfo,
  TASK_EVENT_TYPE,
  TASK_STATUS,
  TaskInfo
} from '@/store/models/task/types';
import {convertCash2Display} from '@/utils/amount';
import logoutCheck from '@/utils/logoutCheck';
import {getSignInfo, isCallAppTask, checkTaskTimeout} from '@/store/models/task/helper';
import {factToFinishTask, logToFinishTask, storeTaskHandleTimes} from '@/store/models/task/taskMonitor';
import ucapi from '@/utils/ucapi';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category';
import {isIOS} from "@/lib/universal-ua";
import {dealTaskExtra} from "@/store/models/task/taskCheck";
import dayjs from "dayjs";
import useSignData from '../SigninNew/useSingData';
import {AccomplishTask, changeTask, getChannelId, finishIncentiveAdTask, handleLinkTask } from './util'
import fact from "@/lib/fact/index";
import {dongfengMonitoring} from '@/lib/ucapi'
import {getParam} from "@/lib/qs";
import { getIncentiveAdSlotData } from '@/lib/utils/incentive_ad_help';
import {event} from '@/components/common/LoginProtocol';
import baseModal from '@/components/modals/modal';
import { MODAL_ID } from '@/components/modals';
import toast from '@/lib/universal-toast';
import eventer, {EventMap} from '@/lib/utils/event';
import stat from '@/lib/stat';
import { browseAdPlayerInstance } from "@/pages/index/task/ad_video_browse";
import {shouldShowPopup} from "@/components/modals/modalControl";
import { setLocalStorageWithExpiry } from '@/utils/localStorage';
import { STORAGE_APP_INSTALL_KEY } from '@/constants/storage';
import { getAppInstallWithExpiryMinutes, setAppInstallWithExpiryMinutes, updateAllAppInstallWithExpiryMinutes } from '@/lib/utils/app_install';
import { isWeb } from 'universal-env';

let appInstalCheckInitd = false;
const TIME_EVENT_FLAG = '_prt'

export function openURL(url?: string) {
  if (!url) return;
  // 信息流会打开首页关闭 webview 导致请求发不出去,延迟处理
  if (url.indexOf('ext') === 0) {
    setTimeout(() => {
      ucapi.base.openURL({ url });
    }, 300);
  } else {
    ucapi.base.openURL({ url });
  }
}

export const isTimeLinkTask = (taskEvent: string) => {
  return taskEvent.includes('link') && taskEvent.includes(TIME_EVENT_FLAG)
}

export const isTimeSearchTask = (taskEvent: string) => {
  return taskEvent.includes('search') && taskEvent.includes(TIME_EVENT_FLAG)
}

export const isPrtStoreTask = (event: string) => {
  return event.includes('store') && event.includes(TIME_EVENT_FLAG)
}

export const showTaskDesc = (event: string) => {
  return event.includes('_desc')
}
export const showDayTimesDesc = (event: TASK_EVENT_TYPE) => {
  return [TASK_EVENT_TYPE.ALIMAMA_SHOP, TASK_EVENT_TYPE.UCLITE_TAOBAO_LIANMENG].includes(event)
}

export const isSearchWordsTask = (taskEvent: TASK_EVENT_TYPE) => {
  const searchWordsTaskEvents = [
    TASK_EVENT_TYPE.UCLITE_SEARCH_WORD,
    TASK_EVENT_TYPE.UCLITE_READ_ONCE,
    TASK_EVENT_TYPE.UCLITE_READ_CLICK,
    TASK_EVENT_TYPE.BAIDU_READ_ONCE,
    TASK_EVENT_TYPE.BAIDU_READ_CLICK,
    TASK_EVENT_TYPE.UC_READ_CLICK,
    TASK_EVENT_TYPE.UC_READ_ONCE,
  ]
  return searchWordsTaskEvents.includes(taskEvent)
}

export const isClickTypeSearchWordsTask = (taskEvent: TASK_EVENT_TYPE) => {
  return [TASK_EVENT_TYPE.UCLITE_READ_CLICK, TASK_EVENT_TYPE.UC_READ_CLICK, TASK_EVENT_TYPE.SEARCH_READ_CLICK]?.includes(taskEvent);
}

export const isReadTypeSearchWordsTask = (taskEvent: TASK_EVENT_TYPE) => {
  return [TASK_EVENT_TYPE.UCLITE_READ_ONCE, TASK_EVENT_TYPE.UC_READ_ONCE, TASK_EVENT_TYPE.SEARCH_READ_ONCE]?.includes(taskEvent);
}
/**
 * 初始化APP安装数据
 * @param appInstallResult
 * @returns
 */
export const initAppInstallMap = async () => {
  if (appInstalCheckInitd) {
    return;
  }
  const startTime = Date.now();
  const checkResult = await window.__APP_INSTALL_CHECK_PMS__;
  tracker.log({
    msg: 'app初始化检测',
    category: WPK_CATEGORY_MAP.APP_INIT_CHECKER,
    wl_avgv1: Date.now() - startTime,
  });
  if (!checkResult) {
    return;
  }
  const appInstallMap: Map<
    string,
    {
      installed: boolean;
      res: Record<any, any>;
    }
  > = new Map();
  for (const result of checkResult) {
    // 只有成功的才放到缓存
    if (!result.error && result.installed) {
      appInstallMap.set(result.appPkg, {
        installed: result.installed,
        res: result.res,
      });
    }
    setAppInstallWithExpiryMinutes({
      pkg: result?.appPkg, 
      install: result?.installed, 
      updateLater: true
    });
  }

  appInstalCheckInitd = true;
  await dispatch.app.updateState({
    appInstallMap: appInstallMap,
  });
  setLocalStorageWithExpiry(STORAGE_APP_INSTALL_KEY, Object.fromEntries(appInstallMap ?? {}));
  updateAllAppInstallWithExpiryMinutes();
}

const getAppInstallMap =  async () => {
  await initAppInstallMap();
  const currentAppInstallMap = store.getState().app.appInstallMap;
  return currentAppInstallMap;
}
// 检测多个app安装
export const checkMultipleInstallApp = async (pkgs: string[], firstInit = false)=> {
  const currentAppInstallMap = await getAppInstallMap();
  const installAppMonitor = tracker.Monitor(WPK_CATEGORY_MAP.CHECK_INSTALLED_APP,  {
    sampleRate: 0.2
  });

  const needCheckList: string[] = [];
  const resultTaskList: Record<string, {
    install: boolean;
  }> = {};

  // 已经安装的检测，没有安装的再次检测
  for (let pkg of pkgs) {
   if(currentAppInstallMap[pkg]?.install){
      resultTaskList[pkg] = {
        install: true
      }
       // 补充打点
      stat.custom('installed_app_detect', {
        is_installed: 1,
        app_name: pkg,
        is_ios: isIOS ? 1 :  0,
        event_id: '19999'
      });
      continue;
    }
    // 首次进来看下缓存
    if(firstInit && isWeb) {
      const pkgLocalStorage = getAppInstallWithExpiryMinutes(pkg);
      if(typeof pkgLocalStorage === 'boolean'){
        resultTaskList[pkg] = {
          install: pkgLocalStorage
        }
        continue;
      }
    }
    needCheckList.push(pkg);
  }

  const checkPromise = (appPkg: string[]) => {
    return new Promise<Array<{
      appPkg: string;
      error: null,
      res: any,
      installed: boolean;
    }>>(async (resolve) => {
      try {
        const res = await ucapi.biz.queryApp({
          cache_first: '0',
          pkgs: appPkg,
        });
        const appInstallInfo = (res || {});

        const result = Object.keys(appInstallInfo).map((pkgName) => {

          const installed = isIOS
          ? Boolean(appInstallInfo[pkgName].appName)
          : appInstallInfo[pkgName]?.appSize > 0 || appInstallInfo[pkgName]?.canOpen;

          setAppInstallWithExpiryMinutes({
            pkg: pkgName, 
            install: installed, 
            updateLater: true
          });
          // 更新数据
          if(installed){
            currentAppInstallMap.set(pkgName, {
              installed,
              res: res[pkgName]
            });
          }

          // 补充打点
          stat.custom('installed_app_detect', {
            is_installed: installed ? 1 : 0,
            app_name: pkgName,
            is_ios: isIOS ? 1 :  0,
            event_id: '19999'
          });

          return {
            appPkg: pkgName,
            error: null,
            res: res[pkgName],
            installed
          }
        });

        installAppMonitor.success({
          msg: `批量查询结果`,
          c2: appPkg,
          bl1: JSON.stringify(appInstallInfo)
        });

        resolve(result);
      } catch (error) {
        resolve([]);
      }
    });
  };


  const chunkSize = 10;
  try {
    // 分片处理, 批量查询默认不超过十个,超过十个端侧抛异常
    for (let i = 0; i < needCheckList.length; i += chunkSize) {
      const batch = needCheckList.slice(i, i + chunkSize);
      const result = await checkPromise(batch);
      result.forEach(item => {
        resultTaskList[item.appPkg] = {
          install: item.installed
        }
      });
    }
    dispatch.app.updateState({appInstallMap: currentAppInstallMap});
    setLocalStorageWithExpiry(STORAGE_APP_INSTALL_KEY, Object.fromEntries(currentAppInstallMap ?? {}));
    updateAllAppInstallWithExpiryMinutes();
  return resultTaskList;
  } catch (error) {
    return resultTaskList;
  }
}

export const checkInstallApp = async (scheme, pkgName) => {
  const installAppMonitor = tracker.Monitor(WPK_CATEGORY_MAP.CHECK_INSTALLED_APP,  {
    sampleRate: 0.2
  });
  const appPkg = isIOS ? scheme : pkgName;

  const currentAppInstallMap = await getAppInstallMap();
  const currentResult = currentAppInstallMap.get(appPkg);
  // 已经检测安装过了,安装过的没必要二次检测安装，未安装需要二次
  if((currentResult !== undefined || currentResult !== null) && currentResult?.installed){
    // 是否已安装补充打点
    stat.custom('installed_app_detect', {
      is_installed: currentResult?.installed ? 1 : 0,
      app_name: appPkg,
      is_ios: isIOS ? 1 :  0,
      event_id: '19999'
    });
    installAppMonitor.success({
      msg: `【${appPkg}】查询成功-缓存`,
      c1: currentResult?.installed ? '已安装' : '未安装',
      c2: appPkg,
      bl1: JSON.stringify(currentResult?.res)
    });
    // console.info('store【checkInstallApp】scheme: ', currentResult?.installed, currentResult?.res);
    return [currentResult?.installed, currentResult?.res];
  }

  try {
    const res = await ucapi.biz.queryApp({
      cache_first: '0',
      pkgs: [appPkg],
    });
    const appInstallInfo = (res || {})[appPkg];
    const installed = isIOS ? Boolean(appInstallInfo.appName) : (appInstallInfo?.appSize > 0 || appInstallInfo?.canOpen);
    installAppMonitor.success({
      msg: `【${appPkg}】查询成功`,
      c1: installed ? '已安装' : '未安装',
      c2: appPkg,
      bl1: JSON.stringify(appInstallInfo)
    })
    // console.log('【checkInstallApp】scheme:', appInstallInfo, res);

    currentAppInstallMap.set(appPkg, {
      installed,
      res
    });
    // 是否已安装补充打点
    stat.custom('installed_app_detect', {
      is_installed: installed ? 1 : 0,
      app_name: appPkg,
      is_ios: isIOS ? 1 :  0,
      event_id: '19999'
    });
    dispatch.app.updateState({appInstallMap: currentAppInstallMap});
    setLocalStorageWithExpiry(STORAGE_APP_INSTALL_KEY, Object.fromEntries(currentAppInstallMap ?? {}));
    setAppInstallWithExpiryMinutes({
      pkg: appPkg, 
      install: installed
    });
    return [installed, res]

  } catch (err) {
    console.error('checkInstallApp', err);
  }
  return [false, {}]
}
export const handleRTACallTask = async (task: TaskInfo) => {
  dispatch.rta.rtaAdClick(task);
  dispatch.task.complete({
    id: task.id,
    type: 'complete',
    useUtCompleteTask: task?.useUtCompleteTask,
    publishId: task?.publishId,
    params: { task: task }
  });
}
export const handleRTANuCallTask = async (task: TaskInfo) => {
  dispatch.rta.rtaAdClick(task);
  dispatch.task.receive({
    id: task.id,
    publishId: task.publishId,
    params: { task: task  },
  });
}

export const handleCallAppTask = async (task: TaskInfo) => {
  const startAppMonitor = tracker.Monitor(WPK_CATEGORY_MAP.START_APP, {sampleRate: 1})
  let taskExtra;
  if (task.extra) {
    try {
      taskExtra = JSON.parse(dealTaskExtra(task.extra));
    } catch (e) {
      taskExtra = {};
    }
  } else {
    taskExtra = {};
  }
  const { scheme, pkgName } = taskExtra
  if (!scheme) {
    startAppMonitor.fail({
      msg: '未配置extra,或extra格式不正确',
      bl1: task.extra,
    })
    handleClickLinkTask(task)
    return
  }
  return handleStartApp(scheme, pkgName, task, startAppMonitor);
}

const handleStartApp = async(scheme: string, pkgName: string, task: TaskInfo, monitor) => {
  const taskStat = {
    task_id: task?.id,
    task_name: task?.name,
    taskclassify: task?.taskClassify || '',
    groupcode: task?.groupCode || '',
    award_amount: task?.rewardItems[0]?.amount || '',
    task_count: task?.dayTimes?.progress || 0,
    isfinish: checkTaskFinished(task) ? 1 : 0,
  }
  try {
    const startRes = await ucapi.biz.startApp(scheme);
    console.log('[startApp res]', startRes);
    // 唤端成功
    if (startRes?.result?.toString() === 'true') {
      stat.custom('task_call_app', {
        call_app_result: 'success',
        ...taskStat
      })
      monitor.success({
        msg: '唤端成功',
        c1: 'true',
        c2: pkgName,
        c3: task?.id,
        c4: task?.name,
        c5: JSON.stringify(startRes?.result),
        bl1:  task.extra,
        bl2: JSON.stringify(startRes),
        bl3: scheme,
        bl4: JSON.stringify(task),
      })
      if (task.event === TASK_EVENT_TYPE.CALL_APP_LINK) {
        const delayAwardFlag = delayTaskCompleteByAwardTime(task)
        if (!delayAwardFlag) {
          setTimeout(() => {
            dispatch.task.complete({
              id: task.id,
              type: 'complete',
              useUtCompleteTask: task?.useUtCompleteTask,
              publishId: task?.publishId,
              params: { task: task }
            });
          }, 1000)
        }
      }
    } else {
      // 唤端失败则跳转到中间页
      stat.custom('task_call_app', {
        call_app_result: 'fail',
        ...taskStat,
      })
      monitor.fail({
        msg: '唤端失败',
        c1: 'false',
        c2: pkgName,
        c3: task?.id,
        c4: task?.name,
        c5: JSON.stringify(startRes?.result),
        bl1: JSON.stringify(task?.extra),
        bl2: JSON.stringify(startRes),
        bl3: scheme,
        bl4: JSON.stringify(task),
      })
      handleClickLinkTask(task)
    }
  } catch (error) {
    // 唤端失败则跳转到中间页
    stat.custom('task_call_app', {
      call_app_result: 'fail',
      ...taskStat,
    })
    monitor.fail({
      msg: '唤端失败-catch',
      c1: 'false',
      c2: pkgName,
      c3: task?.id,
      c4: task?.name,
      bl1: JSON.stringify(task?.extra),
      bl2: JSON.stringify(error),
      bl3: scheme,
      bl4: JSON.stringify(task),
    })
    handleClickLinkTask(task)
  }
}

export const handleClickLinkTask = (task: TaskInfo) => {
  if (task.event === TASK_EVENT_TYPE.CALL_APP_LINK) {
    let delayAwardFlag = delayTaskCompleteByAwardTime(task);
    if (!delayAwardFlag) {
      handleLinkTask(task);
      return
    }
  }
  openURL(task.url);
}

/** 登录绑定任务 */
export const handleLogin = () => {
  const user = store?.getState()?.user
  if (!user?.isLogin) {
    event.emit('OpenLoginProtocolPanel')
    return false
  }
  if (!user?.bindTaobao) {
    dispatch.user.toBindTaobao()
    return false
  }
  return true
}


// 点击前确认是否已经安装
export const clickCheckAppInstall =(task: TaskInfo)=> {
  const extraObj = getExtraInfo(task)
  const { scheme, pkgName } = extraObj;
  if(!scheme || !pkgName){
    return false;
  }

  const appPkg = isIOS ? scheme : pkgName;
  const currentAppInstallMap = store.getState().app.appInstallMap;
  const currentResult = currentAppInstallMap.get(appPkg);
  return !!currentResult?.installed
}

/**
 * 下载类任务领取并去下载页面
 */
export const handleCallAppDownload = (task: TaskInfo, requestId: string) => {
  const checkResult = clickCheckAppInstall(task);
  if(checkResult){
    toast.show('该任务已过期，快去做其它任务吧');
    tracker.log({
      category: WPK_CATEGORY_MAP.TO_FINISH_TASK,
      sampleRate: 1,
      msg: `${task.id}_${task.event}_${task.name}_已安装`,
      w_succ: 1,
      c1: task.event,
      c2: '' + task.id,
      c4: getParam('entry'),
      bl1: task.url || '',
      bl2: JSON.stringify(task)
    })
    dispatch.app.updateAll();
    return;
  }
  // 监听页面隐藏后在触发任务领领取
  dispatch.task.receive({
    id: task.id,
    publishId: task.publishId,
    useUtCompleteTask: task.useUtCompleteTask,
    params: { task: task  },
  });
  openURL(task.url);
}

// 处理高价值多步骤任务
export const handleHighValueDialog = (task: TaskInfo, requestId: string) => {
  baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
  baseModal.openHighValueTask();
}


export const taskActionHandler = async (task: TaskInfo, params?: {location?: string, word?: IWord}) => {
  // 搜索任务登录后才可以做
  const flagLogin = searchWordLogin(task)
  if (flagLogin) {
    return
  }

  // 检测任务是否已经超时
  const checkTaskTimeoutFlag = checkTaskTimeout(task)
  if (checkTaskTimeoutFlag) {
    // 更新任务列表
    toast.show('任务已过期')
    dispatch.task.queryTask(false)
    return
  }
  const tasKEvent = task.event;
  task = changeTask(task, params);
  // 设备投放任务不需要登录, 默认使用设备参数识别
  if (!task?.useUtCompleteTask && logoutCheck()) {
    return;
  }

  const needAddTagTaskIdList = store.getState()?.app?.needAddTagTaskIdList || [];
  // 添加实时标签
  if (needAddTagTaskIdList?.includes(`${task?.id}`)) {
    dealWithRealTimeTag(task);
  }

  // 当天首次去完成任务时记录一下，用于监控任务完成率
  await storeTaskHandleTimes(task)
  const searchTaskChannelId = params?.word?.from || getChannelId(task) || ''

  // 点击去完成任务监控
  logToFinishTask(task, searchTaskChannelId)

  // 补充自定义打点
  factToFinishTask(task)

  const workerRegisterTaskIds = store.getState().task.workerRegisterTaskIds
  if (workerRegisterTaskIds.includes(String(task.id))) {
    console.log('appWorker注册过的任务，只跳转')
    fact.event('worker_to_do_task', {
      action: 'todotask',
      task_id: task.id || '',
      task_name: task.name || '',
    })
    tracker.log({
      category: WPK_CATEGORY_MAP.TASK_WORKER,
      msg: `点击去完成任务`,
      sampleRate: 1,
      w_succ: 1,
      c1: `${task.id}`,
      c2: getParam('entry'),
    })
    openURL(task.url);
    return
  }

  // 点击搜索任务通知客户端清除[关闭悬浮球]标记
  if (isTimeSearchTask(tasKEvent)) {
    ucapi.mission.notifyClearWelfareBallCloseFlag()
  }

  // 二方任务，需要带taskToken,跳转到二方的落地页, 特殊处理点淘任务，带token
  if (tasKEvent.includes(TASK_EVENT_TYPE.LINK_TOKEN)) {
    genTaskSecretbyUt(task).then(tokenVal => {
      let params;
      if (tasKEvent === TASK_EVENT_TYPE.UCLITE_DT_LINK_TOKEN) {
        params = { token: tokenVal }
      } else {
        params = { taskToken: tokenVal }
      }
      thirdTaskOpenUrl(task, params)
    });
    return
  }

  /**去完成激励广告任务 */
  if (isIncentiveAdTask(task)) {
    const adData = getIncentiveAdSlotData(task)
    fact.event('incentive_ad_click', {
      task_id: task?.id || '',
      task_name: task?.name || '',
      taskclassify: task?.taskClassify || '',
      groupcode: task?.groupCode || '',
      award_amount: task?.rewardItems[0]?.amount || '',
      task_progress: task?.dayTimes?.progress || '',
      adapp_id: adData?.appId,
      slot_id: adData?.slotKey,
    });
    const adTaskPreloadResult = browseAdPlayerInstance.getAdTaskPreloadResult(task.id);
    if (checkShowTanxAdPop(task, !!adTaskPreloadResult?.isTanxAdvanced, params?.location)) {
      modal.openTanxAd({
        adTask: task,
        adTaskPreloadResult,
        countdownSeconds: store.getState().cms.tanxOrderTaskDialog.countdownSeconds || 5,
      })
      return;
    }
    return finishIncentiveAdTask(task);
  }

  AccomplishTask(task, params)
};
/*
* 是否显示tanx下单任务弹窗：下单任务可做 && 下一个是tanx进阶广告 && 点击任务列表 && 不在频控范围
* @param task: TaskInfo
* @param location: string
* return boolean
* */
const checkShowTanxAdPop = (task: TaskInfo, isTanxAd, location = '') => {
  const { taskList } = store.getState().task;
  // 激励视频，非激励浏览任务
  const isVideoAdNewTask = task.event.includes(TASK_EVENT_TYPE.INCENTIVE_AD_TAG) && task.event !== TASK_EVENT_TYPE.VIDEO_AD_BROWSE;
  // 预加载结果，判断是tanx进阶类型
  const progressiveOrderTask = taskList?.find(task => task.event === TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER)
  const isOrderTaskAvailable = progressiveOrderTask && !checkTaskFinished(progressiveOrderTask);
  const { showTimeGap, maxDailyDisplayCount } = store.getState().cms.tanxOrderTaskDialog;
  const showPopUp = shouldShowPopup(MODAL_ID.TANX_AD, showTimeGap, maxDailyDisplayCount);
  return isVideoAdNewTask && isTanxAd && isOrderTaskAvailable && location === 'list' && showPopUp
}

export function jumpTaskHander(task: TaskInfo) {
  openURL(task.url);
  if (task.state === TASK_STATUS.TASK_DOING) {
    dispatch.task.complete({
      id: task.id,
      type: 'complete',
      useUtCompleteTask: task?.useUtCompleteTask,
      publishId: task?.publishId,
      params: { task: task }
    });
  }
}

export const getPrizeConfig = (rewardItem: RewardItem | { amount: number, mark: string}) => {
  if (!rewardItem) return {
    amount: 0,
    mark: ''
  };
  const { mark = 'coin' } = rewardItem;
  // const config = PRIZE_CONFIG[mark];
  const config = mark.indexOf('cash') > -1 ? PRIZE_CONFIG.cash : PRIZE_CONFIG.coin;
  // 现金要转换
  // const amount = mark === PRIZE_CODE.CASH ? convertCash2Display(rewardItem.amount) : rewardItem.amount;
  const amount = mark.indexOf(PRIZE_CODE.CASH) > -1 ? convertCash2Display(rewardItem.amount) : rewardItem.amount;
  return { ...config, amount };
};

export const rewardDesc = (rewardItem: RewardItem) => {
  if (!rewardItem) return '';
  const { mark = 'coin', amount } = rewardItem;
  if (mark.includes('cash')) {
    return `${amount / 100}元`
  }
  return `${amount}元宝`
}

export const taskItemRewardDesc = (task: TaskInfo | undefined, showUnit=true) => {
  if (!task) return ''
  const rewardItems = task?.rewardItems || [];
  if (rewardItems.length > 1) {
    const coinRewardItems = rewardItems.filter(item => item.mark.includes('coin'))
    let amount = 0
    coinRewardItems.forEach(item => {
      if (item.amount) {
        amount += item.amount
      }
    })
    // return showUnit ? `+${amount}` : `+${amount}`
    return `+${amount}`
  }
  let { mark, amount } = task?.rewardItems[0] || { mark: '', amount: '' }
  if (!mark) mark = ''
  if (mark.includes('lottery')) {
    return task.rewardItems[0].name
  }
  if (mark.includes('draw_money') || mark.includes('draw_ingot')) {
    return task?.desc || '';
  }
  if (mark.includes('cash')) {
    return amount ? `+${amount / 100}元` : '';
  }
  if (amount) {
    // return showUnit ? `+${amount}` : `+${amount}`
    return `+${amount}`
  } else {
    return '';
  }
}


export const finishSignTaskAndOpenModal = async (openLocation = '自动签到', isVoluntarily = false) => {
  const signMonitor = tracker.Monitor(WPK_CATEGORY_MAP.SIGN_DIALOG, { sampleRate: 1 })
  const newSignList = store.getState().task.newNewSignList;
  const signTask: SignInfo | undefined = getSignInfo();
  const { isLogin } = store.getState()?.user;

  if (!signTask) {
    signMonitor.fail({
      msg: openLocation + '-获取签到数据失败-未开启弹窗',
      c1: '',
      c2: '',
      c3: newSignList.length + '',
      bl1: JSON.stringify(newSignList)
    })
    return
  }
  // 老用户已登陆
  // 老用户签到 先调签到接口再显示弹窗
  if (signTask.event === TASK_EVENT_TYPE.UCLITE_SIGN_NM && isLogin) {
    // 未完成
    if (signTask.state !== TASK_STATUS.TASK_CONFIRMED) {
      const prizes = await dispatch.task.signIn(signTask as SignInfo);
      if (prizes) {
        const winPrizes = prizes.filter(prize => prize.win);
        if (winPrizes.length > 0) {
          await dispatch.app.updateTaskAndCurrency()
          const [, toDayAward, today] = useSignData(store.getState().task.newNewSignList);
          signMonitor.success({
            msg: openLocation + '-' + newSignList.length + '天签到弹窗',
            c1: today + '',
            c2: toDayAward,
            c3: newSignList.length + '',
            c4: 'false',
            c5: isLogin,
            bl1: JSON.stringify(newSignList)
          })
          if (newSignList.length > 7) {
            modal.openSignInNew(prizes?.[0]?.rewardItem, isVoluntarily)
            return
          }
          modal.openSignIn(prizes?.[0]?.rewardItem, isVoluntarily);
          // postmessage页面广播通知信息流卡片签到状态变更
          // ucapi.base.postmessage({
          //   data: {
          //     id: 'signStatusChange'
          //   }
          // });
        }
      }
    } else if (newSignList.length > 7) {
      // 已完成，有奖励, 老用户签到任务统一取prizes字段的奖励
      modal.openSignInNew(signTask?.prizes?.[0]?.rewardItem || null, isVoluntarily)
    } else {
      // 已完成
      modal.openSignIn(signTask.state === TASK_STATUS.TASK_CONFIRMED ? signTask.rewardItems?.[0] : null);
    }
    return
  }
  // 未登录下, 新老用户都出签到弹框
  if (signTask.event === TASK_EVENT_TYPE.UCLITE_SIGN || signTask.event === TASK_EVENT_TYPE.UCLITE_SIGN_NM) {
    const isNewUser = signTask.event === TASK_EVENT_TYPE.UCLITE_SIGN;
    signMonitor.success({
      msg: `${ isNewUser ? '新用户' : '老用户'}` + openLocation + '-' + newSignList.length + '天签到弹窗',
      c1: '',
      c2: '',
      c3: '',
      c4: isNewUser,
      c5: isLogin,
      bl1: JSON.stringify(newSignList)
    })
    modal.openSignIn(signTask.state === TASK_STATUS.TASK_CONFIRMED ? signTask.rewardItems?.[0] : null);
  }
}

// 已绑定过支付宝的用户 过滤掉 绑定任务
export function filterAlipayTask (task: TaskInfo) {
  const hasBindedAlipay = store.getState().user.hasBindedAlipay
  const bindFromTask = localStorage.getItem('bindAlipayFromTask')
  return !(task.event === TASK_EVENT_TYPE.BIND_ALIPAY_ACCOUNT && hasBindedAlipay && !bindFromTask)
}

// 需要登录才能去做任务
export function searchWordLogin (task) {
  if ([TASK_EVENT_TYPE.UCLITE_READ_ONCE,
    TASK_EVENT_TYPE.UCLITE_READ_CLICK,
    TASK_EVENT_TYPE.UC_READ_CLICK,
    TASK_EVENT_TYPE.UC_READ_ONCE,
    TASK_EVENT_TYPE.BAIDU_READ_ONCE,
    TASK_EVENT_TYPE.BAIDU_READ_CLICK,
    TASK_EVENT_TYPE.SEARCH_READ_ONCE,
    TASK_EVENT_TYPE.SEARCH_READ_CLICK]?.includes(task?.event)) {
      return logoutCheck()
  }
  return false
}

export function hideShoppingTask (task: TaskInfo) {
  if (!task) return false
  const installTaobao = store.getState().app?.installedTaobao
  const eventFilterUninstalledTaobao = store.getState().app?.eventFilterUninstalledTaobao || []
  return eventFilterUninstalledTaobao.includes(task.event) && !installTaobao
}

/**
 * 激励广告类任务展示条件(以下条件需全部满足)
 * 1、扩展字段有配置slotKey
 * 2、扩展字段配置的不开启检测广告是否填充 或则 预加载成功的
 * @param task
 * @returns
 */
export const ifShowAdTask = (task: TaskInfo) => {
  if (!isIncentiveAdTask(task)) {
    return true
  }
  const adTaskPreloadSuccessList = store.getState().task.adTaskPreloadSuccessList;
  const showAdTask = adTaskPreloadSuccessList?.find((item) => item?.id === task?.id)
  const adData = getIncentiveAdSlotData(task)
  return adData?.slotKey && (!adData?.checkAdFill || showAdTask)
}

/**
 * 隐藏掉开启装机检测没有安装app的任务
 * @param task
 * @param filterCallApp
 * @param type: 'list' | 'resource'
 * @returns
 */
export const hideUninstallTask = (task: TaskInfo, filterCallApp = true, type = 'list') => {
  if (!filterCallApp && isCallAppTask(task)) {
    return false
  }
  const list = type === 'list' ? store.getState().task.dealWithUninstallTaskList : store.getState().resource.dealWithUninstallTaskList;
  const curTask = list?.find((item)=> item?.id === task?.id);
  if (curTask && !curTask?.display) {
    return true
  }
  return false
}

/**
 * 处理预加载成功的激励广告任务
 * @param task
 */
export const dealWithPreloadSuccessAdTask = (task: TaskInfo) => {
  const showAdList = store.getState().task.adTaskPreloadSuccessList;
  const isHas = showAdList?.find((item)=>item?.id === task?.id);
  if (!isHas) {
    dispatch.task.updateState({adTaskPreloadSuccessList: [...showAdList, task]})
  }
}

/**
 * 二方换量合作任务-UC内打开链接
 * @param task 任务链接
 * @param params 拼接参数
 */
export function thirdTaskOpenUrl(task: TaskInfo, params = {}) {
  const jumpMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TOKEN_TASK_JUMP)
  let kv = '';
  let linkUrl = task.url;
  if (!linkUrl) {
    jumpMonitor.fail({
      msg: '跳转失败-未配置链接',
      c1: task.event,
      c2: JSON.stringify(params)
    })
  };
  const keyArr = Object.keys(params);
  if (keyArr.length) {
    keyArr.forEach((item, idx) => {
      kv += item + '=' + encodeURIComponent(params[item]) + (idx === keyArr.length - 1 ? '' : '&');
    });
    linkUrl = linkUrl + (linkUrl.indexOf('?') > 0 ? '&' : '?') + kv;
  }
  jumpMonitor.success({
    msg: '跳转成功',
    c1: task.event,
    c2: JSON.stringify(params),
    bl1: linkUrl
  })
  openURL(linkUrl);
}
// 获取二方任务token
export async function genTaskSecretbyUt(task: TaskInfo) {
  const t = Math.round(Date.now() / 1000);
  const ucParams: { ut: string; dd: string } = await ucapi.biz.ucparams({params: 'utdd'}, true);
  const decryptedUt = ucParams.dd;
  const sText = t + ',' + task.event + ',' + decryptedUt;
  return await ucapi.spam.encrypt({
    text: sText,
  });
}

// 获取现在距离目标时间的时间差s
export const getTargetTimeDiff = (targetTime: number, now: number) => {
  if (!targetTime) {
    return {
      diff: 0,
      isSameDay: false
    }
  }
  const targetTimeD = dayjs(targetTime);
  const nowD = dayjs(now);
  // 时间秒差
  const diff = targetTimeD.diff(nowD, 's');
  const isSameDay = targetTimeD.isSame(nowD, 'd');
  return {
    diff,
    isSameDay
  }
}
/**
 *
 * @param timestamp 当前时间，取服务端时间
 * @param task 任务内容
 */
export const checkTaskCountDown = (task: TaskInfo, timestamp: number) => {
  const { dayTimes = {progress: 0, target: 0}} = task;
  const isNeedFreezeTask = task?.event?.includes(TASK_EVENT_TYPE.VIDEO_AD);
  const isFinish = dayTimes.progress === dayTimes.target
  const taskBeginTimeDiff = getTargetTimeDiff(task.beginTime, timestamp);
  return !isFinish && isNeedFreezeTask && taskBeginTimeDiff.diff > 1
}


// 获取视频任务前n次奖励元宝总数
export const previousNTimesReward = (times: number, taskInfo: TaskInfo | undefined) => {
  if (times < 1 || !taskInfo) return 0
  const { allTimesRewardMap } = taskInfo;
  const rewardPerTimes = allTimesRewardMap?.['coin'] || []
  const extraRewardPerTimes = allTimesRewardMap?.['coin_extra'] || []
  const previousNTimesNormalReward = rewardPerTimes.filter((item) => item.times <= times).reduce((pre, cur) => {
    return pre + cur.amount
  }, 0)
  const previousNTimesExtraReward = extraRewardPerTimes.filter(item => item.times <= times).reduce((pre, cur) => {
    return pre + cur.amount
  }, 0)
  return previousNTimesNormalReward + previousNTimesExtraReward
}

/**
 * 处理未安装app是否显示改任务
 * @param taskInfo
 */
export const showUninstallAppTask = async (taskInfo: TaskInfo) => {
  if (!taskInfo?.extra) {
    return true
  };
  const extraObj = getExtraInfo(taskInfo)
  const { scheme, pkgName, andCheckInstall, iosCheckInstall, showUninstallApp } = extraObj;
  const conditions = (isIOS ? iosCheckInstall : andCheckInstall) || showUninstallApp;

  if (conditions && (scheme || pkgName)) {
    const schemePrefix = scheme.replace(/[^:]+$/, '//');
    const [isInstall] = await checkInstallApp(schemePrefix, pkgName);
    // 未安装不展示补充打点
    if (!isInstall) {
      fact.event('task_hide_installed', {
        task_id: taskInfo?.id,
        task_name: taskInfo?.name,
        taskclassify: taskInfo?.taskClassify,
        groupcode: taskInfo?.groupCode,
      })
    }
    // 未安装检测, 未安装时展示任务
    if (showUninstallApp) {
     return !isInstall
    }

    return !!isInstall
  }

  return true
}

/**
 * 处理 app安装 需要的参数
 * @param taskInfo
 */
export const getTaskAppInstallMap = (taskInfo: TaskInfo, needConditions = true) => {
  const extraObj = getExtraInfo(taskInfo)
  const { scheme, pkgName, andCheckInstall, iosCheckInstall, showUninstallApp } = extraObj;
  const conditions = (isIOS ? iosCheckInstall : andCheckInstall) || showUninstallApp;

  // 不需需要开启检测条件，即: iosCheckInstall : andCheckInstall;
  if(!needConditions && (scheme || pkgName)){
    return isIOS ? scheme : pkgName;
  }

  // 默认需要开启检测条件
  if (conditions && (scheme || pkgName)) {
    return isIOS ? scheme : pkgName;
  }
  return null;
}


export function filterDongfengTask(task: TaskInfo) {
  const extra = task?.extra;
  if (!extra) {
    return false;
  }
  try {
    let json :any = extra
    if (Object.prototype.toString.call(extra) !== '[object Object]') {
      json = JSON.parse(dealTaskExtra(extra));
    }
    return Boolean(json.monitorClickUrl) || Boolean(json.monitorExposureUrl);
  } catch (error) {
    return false;
  }
}

export function dongfengTaskReport(task: TaskInfo, action: 'expose' | 'click') {
  let extra = task?.extra;
  if (!extra) {
    return;
  }
  try {
    let json :any = extra
    if (Object.prototype.toString.call(extra) !== '[object Object]') {
      json = JSON.parse(dealTaskExtra(extra));
    }
    if (!json.monitorClickUrl || !json.monitorExposureUrl) {
      return;
    }
    console.log('dongfeng', action, task);
    fact.event(action==='expose' ? 'task_exposure_monitoring': 'task_click_monitoring', {
      c: 'task',
      d:'dongfeng',
      task_id: task.id,
      task_name: task.name
    });
    const url = action === 'expose' ? json?.monitorExposureUrl : json?.monitorClickUrl
    dongfengMonitoring(url, action)
      ?.then((data) => {
        tracker.log({
          category: 168, // 系统自动生成，请勿修改
          msg: '东风检测成功', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 1, // 用于计算"成功率";可选值为0或1
          c1: String(task.id ?? ''), // 自定义字段c1 对应 任务ID
          c2: String(action), // 自定义字段c2 对应 行为action
          c3: String(url), // 自定义字段c3 对应 检测链接url
          bl1: '', // 自定义长文本bl1 对应 错误信息
        });
      })
      .catch((error) => {
        tracker.log({
          category: 168, // 系统自动生成，请勿修改
          msg: '东风检测失败', // 将根据msg字段聚合展示在平台的top上报内容中
          w_succ: 0, // 用于计算"成功率";可选值为0或1
          c1: String(task.id ?? ''), // 自定义字段c1 对应 任务ID
          c2: String(action), // 自定义字段c2 对应 行为action
          c3: String(url), // 自定义字段c3 对应 检测链接url
          bl1: JSON.stringify(error), // 自定义长文本bl1 对应 错误信息
        });
      });
  } catch (error) {
    console.log(error);
  }
}

export function findDongfengTask(taskList: TaskInfo[] = []){
  const elements = document.querySelectorAll('[id^="task-dongfeng-"]');
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const match = String(entry?.target?.id).match(/task-dongfeng-(\d+)/);
        const taskId = match ? match[1] : null;
        const taskInfo = taskList.filter(item => String(item.id) === String(taskId))[0];
        const dongfengTaskMap = store.getState().task.dongfengExposeTaskMap;
        const isHas = dongfengTaskMap.has(`${taskInfo?.id}`);
        if(taskInfo && !isHas){
          console.log('dongfeng');
          dongfengTaskMap.set(`${taskInfo?.id}`, taskInfo?.id )
          dongfengTaskReport(taskInfo, 'expose');
        }
      }
    });
  });
  // 监听每个元素
  elements.forEach(element => observer.observe(element));
}

/**
 * 获取任务扩展字段信息
 * @param taskInfo
 * @returns
 */
export const getExtraInfo = (taskInfo?: TaskInfo) => {
  if (!taskInfo?.extra) {
    return {}
  }
  if (typeof taskInfo.extra === 'object') {
    return taskInfo.extra;
  }
  let extraObj;
  try {
    const extra = dealTaskExtra(taskInfo?.extra)
    extraObj = JSON.parse(extra || '{}');
  } catch (e) {
    console.error('task extra parse err', e);
    extraObj = {}
  }
  return extraObj;
}

/**
* 次数任务，state=7时代表完成，其他任务state=2代表完成
* @param task
* @returns
*/
export const checkTaskFinished = (task: TaskInfo) => {
  switch (task?.taskType) {
    case 'everydayTimes': // 每日次数任务，根据dayTimes判断
      return task.dayTimes?.target === task.dayTimes?.progress;
    default: // 其他任务任务类型根据state判断
      return task?.state === TASK_STATUS.TASK_CONFIRMED || task?.state === TASK_STATUS.TASK_FINISH;
  }
}

/**
 * 激励广告类型的任务
 */
export const isIncentiveAdTask = (task: TaskInfo) => {
  return task?.event?.includes(TASK_EVENT_TYPE.INCENTIVE_AD_TAG) || task?.event === TASK_EVENT_TYPE.UCLITE_TAOBAO_SHOPPING;
}

/**
 * 将字符串格式化为js对象
 * @param str
 * @returns
 */
export const formatStrToObj = (str: string) => {
  let obj;
  try {
    obj = JSON.parse(str || '{}')
  } catch (error) {
    obj = {}
  }
  return obj
}

/**
 * 汇川二方效果广告任务
 */
export const isHuiChuangAdEffectTask = (task: TaskInfo) => {
  const eventList = [
    TASK_EVENT_TYPE.CORP_APP_TASK,
    TASK_EVENT_TYPE.CORP_APP_TASK_NEW,
    TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND,
  ]
  return eventList?.includes(task?.event)
}

/**
 * RTA任务任务
 */
export const isRtaAllTask = (task: TaskInfo) => {
  const eventList = [
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DAILY,
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK,
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU,
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD,
  ]
  return eventList?.includes(task?.event)
}


// 延迟发奖
export const delayTaskCompleteByAwardTime = (task: TaskInfo) => {
  const extraObj = getExtraInfo(task)
  // 没有awardTime走之前逻辑
  if (!extraObj?.awardTime) {
    return false
  }
  let startTime = 0;
  const PageVisible = async () => {
    if (Date.now() - startTime >= Number(extraObj?.awardTime)) {
      await dispatch.task.complete({
        id: task.id,
        type: 'complete',
        useUtCompleteTask: task?.useUtCompleteTask,
        publishId: task?.publishId,
        params: { task: task }
      });
      dispatch.app.init();
    } else {
      toast.show('任务未完成');
    }
    eventer.off(EventMap.PageHidden, PageHidden);
    eventer.off(EventMap.PageVisible, PageVisible);
  };
  const PageHidden = () => {
    startTime = Date.now();
  };
  eventer.on(EventMap.PageVisible, PageVisible);
  eventer.on(EventMap.PageHidden, PageHidden);
  return true
}

// 单位换算: 元宝
export const convertUnitWan = (amount: number) => {
  if (!amount){
    return 0
  }
  return amount >= 10000 ? `${(amount / 10000).toFixed(2)}万` : `${amount}`
};

/** 单位换算: 分 =》 元 */
export const convertUnitYuan = (amount: number) => {
  if (!amount){
    return 0
  }
  return amount / 100;
};

/**
 * 获取任务奖励
 * @param task
 * @param showRandomText 是否展示“最高”文案
 * @param showUnit 是否展示单位
 * @returns
 */
export const getTaskRewardItem = (task: TaskInfo, showRandomText = true, showUnit = true) => {
  /**
   * 1、随机奖励:任务未完成 奖励取task中rewardItems; 任务已完成取prize中的rewardItems;
   * 2、非随机奖励: 奖励都取task中rewardItems
   *  */
  const isTaskFinish = checkTaskFinished(task);
  const isRandom = task?.rewardItems?.[0]?.randomAmount;
  let detail = Object.assign({}, isTaskFinish && isRandom ? task?.prizes?.[0]?.rewardItem : task?.rewardItems?.[0]);
  switch (detail.mark) {
    case 'cash':
      detail.name =  `${detail.randomAmount && showRandomText ? '最高' : ''}${convertUnitYuan(Number(detail.amount))}${showUnit ? '元' : ''}`;
      break;
    case 'coin':
      detail.name = `${detail.randomAmount && showRandomText ? '最高' : ''}${convertUnitWan(Number(detail.amount))}${showUnit ? '元宝' : ''}`;
      break;
    case 'equity':
      detail.name = `${detail.name}`;
      break;
    default:
      detail.name = `${detail.randomAmount && showRandomText ? '最高' : ''}${convertUnitWan(Number(detail.amount))}${showUnit ? '元宝' : ''}`;
      break;
  }
  return detail
};

/** 汇川下载类广告信息存到客户端 */
export const saveAdInsideGroupTaskInfoToStore = (task: TaskInfo) => {
  let adStoreData = store.getState().task.adStoreDataList || [];
  const savedTask = adStoreData?.find((item)=> item?.taskId === task?.id);
  // 有缓存过就不需要再存了
  if (savedTask?.taskId) {
    return
  }
  adStoreData.push({
    accountId: `${task.accountId}`,
    taskId: task.id,
  })
  ucapi.biz.setStoreData({
    page: 'uc_fuli_index',
    id: 'ad-inside-group-id',
    storeType: 'memory',
    data: {
      adStoreData
    }
  })
};

/** 从客户端获取汇川下载类广告信息 */
export const getAdInsideGroupTaskInfoFromStore = async () => {
  const resData =  await ucapi.biz.getStoreData({page: 'uc_fuli_index', id: 'ad-inside-group-id'});
  dispatch.task.updateState({
    adStoreDataList: resData?.data?.adStoreData || []
  })

}

// 处理添加实时人群包标签
export const dealWithRealTimeTag = (task: TaskInfo) => {
  const PageHidden = () => {
    dispatch.task.addRealTimeTag(task);
    eventer.off(EventMap.PageHidden, PageHidden);
  };
  eventer.on(EventMap.PageHidden, PageHidden);
}


/**
 * 获取任务当前次数批次目标总进度
 * @param task 
 * @param now 
 */
export const getTaskCurDayTimeTarget = (task: TaskInfo) => {
  const {isCustomDaytimes = false } = getExtraInfo(task);
  const { dayTimes = {progress: 0, target: 0}, dayTimeIntervalMap = {}} = task;
  const targetArr = Object.keys(dayTimeIntervalMap).sort((pre, next) => Number(pre) - Number(next)).filter(item => Number(item ?? 0) <= Number(dayTimes.target ?? 0));
  // 非自定义完成次数间隔 或则 不是激励广告类任务
  if (!targetArr?.length || !isIncentiveAdTask(task)) {
    return dayTimes.target;
  }
  
  const curDayTimeTargetIndex = targetArr?.findIndex((item) => Number(item) > dayTimes?.progress); 
  const target = targetArr[curDayTimeTargetIndex]; 
  const lastTarget = targetArr[targetArr.length - 1];

  // 展示自定义次数
  if (isCustomDaytimes) {
    // 第一间隔总次 <= 任务总次数
    if (dayTimes?.target <= Number(targetArr[0])) {
      return dayTimes?.target
    }

    // 配置最大次数 > 任务总次数
    if (dayTimes.progress === dayTimes.target) {      
      const preTarget = Number(lastTarget) === Number(dayTimes?.target) ? Number(targetArr[(targetArr.length >= 2 ? targetArr.length - 2 : 0)]) : Number(lastTarget);
      return dayTimes.target - preTarget;
    }
    
    // 最后自定义间隔 < 任务总次数
    if (!target) {
      return dayTimes?.target - Number(targetArr[targetArr.length - 1]);
    }

    // 第一批次
    if (Number(dayTimes?.progress ?? 0) < Number(targetArr[0])) {
      return Number(targetArr[0])
    }

    const preTarget = Number(targetArr[curDayTimeTargetIndex - 1])
    return Number(target) - preTarget
  }

  if (!target || Number(target) > dayTimes.target) {
    return dayTimes.target
  }

  return target;
}

/**
 * 获取任务当前自定义进度次数
 * @param task 
 * @returns 
 */
export const getTaskCurDayTimeProcess = (task: TaskInfo) => {
  const {isCustomDaytimes = false } = getExtraInfo(task);
  const { dayTimes = {progress: 0, target: 0}, dayTimeIntervalMap = {}} = task;

  if (!dayTimes.progress || !isCustomDaytimes || !isIncentiveAdTask(task) || !Object.keys(dayTimeIntervalMap).length) {
    return dayTimes?.progress;
  }
  const targetArr = Object.keys(dayTimeIntervalMap).sort((pre, next) => Number(pre) - Number(next)).filter(item => Number(item) <= dayTimes.target);
  const curDayTimeTargetIndex = targetArr?.findIndex((item) => Number(item) > dayTimes?.progress); 
  const target = targetArr[curDayTimeTargetIndex];
  const lastTarget = targetArr[targetArr.length - 1];

  // 第一批次
  if (Number(dayTimes?.progress ?? 0) < Number(targetArr[0])) {
    return dayTimes?.progress
  }
  
  // 任务做满
  if (dayTimes.progress === dayTimes.target) {
    // 10: 2 
    const preTarget = Number(lastTarget) === dayTimes?.progress ? Number(targetArr[targetArr.length >= 2 ? targetArr.length - 2 : 0]) : Number(lastTarget);
    return dayTimes?.progress - preTarget;
  }

  // 最后自定义间隔 < 任务总次数
  if (!target) {
    return dayTimes?.progress - Number(targetArr[targetArr.length - 1]);
  }
  
  return dayTimes?.progress - Number(targetArr[curDayTimeTargetIndex - 1]);
}


export default {
  taskActionHandler,
};
