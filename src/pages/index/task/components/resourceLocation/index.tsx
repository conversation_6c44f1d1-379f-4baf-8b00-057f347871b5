import { createElement } from 'rax';
import Image from '@/components/image';
import View from 'rax-view';
import Fact from '@/components/Fact';
import { TaskInfo } from '@/store/models/task/types';
import './index.scss';

interface IProps {
  handleClick: (e, taskInfo) => void | Promise<void>;
  renderBtn?: () => JSX.Element;
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  resource_location: string;
  resource_location_title: string;
  /** 子任务 */
  subTaskList?: TaskInfo[];
}

const NovelLimitSignin = (props: IProps) => {
  const { name, id, preTaskList } = props.taskInfo;

  if (!preTaskList?.length) {
    return null
  }

  return (
    <Fact
      className="resource-welfare-comp fact-task-item"
      c="task"
      d={`task${props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
      }}
    >
      <View className="task-item resource-welfare-task">
        <View className="title-name">{name}</View>
        <View className="resource-task-list">
          {preTaskList?.map((task, idx)=>{
            return (
             <Fact
             className="fact-resource-task-wrap" 
             c="task"
             d={`task${idx + 1}`}
             expoLogkey="task_expo"
             noUseClick
             expoExtra={{
               task_id: task?.id,
               module: props.taskModule,
               timemodule: props.timemodule || '',
               task_name: task?.name,
               taskclassify: task?.taskClassify || '',
               groupcode: task?.groupCode || '',
               resource_location: props?.resource_location || '',
               resource_location_title: props.resource_location_title || '',
             }}
             >
               <View className="resource-task-wrap" onClick={(e)=> props?.handleClick(e, task)}>
                <Image className="task-icon" source={task?.icon} />
                <View className="task-name" >{task?.name}</View>
                <View className="task-desc">{task?.desc}</View>
              </View>
             </Fact>
            )
          })}
        </View>
      </View>
    </Fact>
  )
}

export default NovelLimitSignin;
