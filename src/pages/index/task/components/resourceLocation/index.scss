.resource-welfare-comp{
  .resource-welfare-task{
    display: flex;
    flex-direction: column;
    align-items: start;

    .title-name{
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      color: #01255D;
      font-weight: 600;
    }

    .resource-task-list{
      width: 100%;
      flex-wrap: wrap;
      display: flex;
      flex-direction: row;

      .fact-resource-task-wrap{
        min-width: 146rpx;
        flex: 1;
      }

      .resource-task-wrap{
        height: 174rpx;
        background-image: linear-gradient(180deg, #EEF2F6 0%, #F8FBFF 100%);
        border-radius: 20rpx;
        margin-top: 20rpx;
        margin-right: 8rpx;
        display: flex;
        align-items: center;
        flex-direction: column;

        .task-icon{
          margin-top: 20rpx;
          width: 64rpx;
          height: 64rpx;
          box-shadow: none;
          -webkit-box-shadow: none;
          uc-perf-stat-ignore: image;
        }

        .task-name{
          margin: 14rpx 0 2rpx 0;
          font-family: PingFangSC-Semibold;
          font-size: 22rpx;
          color: #01255D;
          font-weight: 600;
        }

        .task-desc{
          font-family: PingFangSC-Semibold;
          font-size: 20rpx;
          color: #7E93B7;
          font-weight: 400;
        }
      }

      .fact-resource-task-wrap:nth-of-type(4n + 4) .resource-task-wrap{
        margin-right: 0;
      }

    }
  }
}