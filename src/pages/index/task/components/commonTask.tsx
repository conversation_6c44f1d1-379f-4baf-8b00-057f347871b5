import { createElement, Fragment, useMemo } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Fact from '@/components/Fact';
import { TaskInfo } from '@/store/models/task/types';
import { taskItemRewardDesc, filterDongfengTask } from '../help';
import Image from '@/components/image';
import { INGOT_ICON } from '@/constants/static_img';


interface IProps {
  handleClick: (e, taskInfo) => void | Promise<void>;
  renderBtn: () => JSX.Element;
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  resource_location: string;
  resource_location_title: string;
}

const Index = (props: IProps) => {
  const { name, id, desc, icon, rewardItems } = props.taskInfo;
  const rewardDesc = useMemo(()=> {
    return taskItemRewardDesc(props.taskInfo)
  }, [props.taskInfo]);

  const showDetail = useMemo(() => {
    const rewardItemDetail = rewardItems?.[0] ?? {};
    const hasAward = !!rewardItemDetail?.amount;
    const icon = rewardItemDetail?.mark.includes('coin') ? INGOT_ICON : rewardItemDetail.icon;
    return {
      hasAward,
      icon
    }
  }, [props.taskInfo])
  return (
    <Fact
      className="fact-task-item"
      id={`${filterDongfengTask(props.taskInfo) ? `task-dongfeng-${id}` : ''}`}
      c="task"
      d={`task${props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props.resource_location || '',
        resource_location_title: props.resource_location_title || '',
      }}
    >
      <View className="task-item common-task" onClick={props.handleClick}>
        {!!icon && <Image className="task-icon" loading={'lazy'} source={icon} />}
        <View className="task-info addw">
          <Text className={`item-title`}>{name}</Text>

          <View className={`flex-row items-center task-desc flex-wrap`}>
            {desc}
            {showDetail.hasAward && (
              <Fragment>
                <Image
                  className="ingot-icon"
                  source={showDetail.icon ?? INGOT_ICON}
                  style={{ width: '34rpx', height: '34rpx', verticalAlign: 'text-bottom' }}
                />
                <Text className="task-award align-self-end">{rewardDesc}</Text>
              </Fragment>
            )}
          </View>
        </View>
        {props.renderBtn()}
      </View>
    </Fact>
  );
};

export default Index;
