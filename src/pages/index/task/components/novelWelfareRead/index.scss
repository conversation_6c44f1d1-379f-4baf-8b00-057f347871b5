.novel-welfare-read-comp{
  .read-progress-wrap{
    margin-top: 16rpx;
    width: 610rpx;
    height: 156rpx;
    background-image: linear-gradient(180deg, #EEF2F6 0%, #F8FBFF 100%);
    border-radius: 24rpx;
    padding: 0 16rpx;
    position: relative;

    .progress-bar-wrap{
      width: 100%;
      height: 20rpx;
      background: #DEE5EE;
      border-radius: 10rpx;
      position: absolute;
      left: 0;
      top: 76rpx;
      z-index: 1;
      overflow: hidden;

      .progress-bar{
        height: 100%;
        background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
        border-radius: 10rpx;
      }
    }

    .progress-wrap {
      overflow-x: auto;
      position: relative;
      &::-webkit-scrollbar {
        display: none;
      }

      .progress-item-wrap{
        height: 156rpx;
        position: relative;
        top: 0;
        left: 0;
        z-index: 1;
      }
    }

    .progress-item {
      display: flex;
      flex-direction: column;
      align-items: center; /* 垂直居中对齐 */
      text-align: center; /* 文本居中 */
      min-width: 126rpx;
      z-index: 2;
      padding: 20rpx 0;
      position: relative;

      .item-award{
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 5rpx 8rpx;

        .award-icon{
          width: 32rpx;
          height: 32rpx;
          uc-perf-stat-ignore: image;
        }

        .amount{
          font-family: PingFangSC-Semibold;
          font-size: 22rpx;
          color: #405A86;
          letter-spacing: 0;
          font-weight: 600;
        }
      }

      .can-draw{
        background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
        border-radius: 20rpx;
        .amount{
          color: #FFFFFF;
        }
      }

      .item-node{
        margin: 18rpx 0;
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background: #FFFFFF;
        position: relative;
        z-index: 2;
      }

      .item-text{
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #7E93B7;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
    }

    .item-mean {
      flex: 1;
    }
  }
}