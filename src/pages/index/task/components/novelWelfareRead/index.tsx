import { createElement } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Fact from '@/components/Fact';
import { TaskInfo, TASK_EVENT_TYPE, TASK_STATUS } from '@/store/models/task/types';
import Image from '@/components/image';
import { INGOT_ICON } from '@/constants/static_img';
import './index.scss';
import { useSelector } from 'rax-redux';
import { StoreState, dispatch } from '@/store';
import { checkTaskFinished, getTaskRewardItem } from '../../help';
import { execWithLock } from '@/utils/lock';
import fact from '@/lib/fact';
import { openURL } from '../../help';

const { TASK_COMPLETED } = TASK_STATUS;

interface IProps {
  handleClick: (e, taskInfo) => void | Promise<void>;
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  resource_location: string;
  resource_location_title: string;
}

const NovelRead = (props: IProps) => {
  const { name, id, progress = 0, btnName, url = '' } = props.taskInfo;
  const { taskList = [] } = useSelector((state: StoreState) => state.resource.uc_piggy_novel || {});

  // 时长节点列表
  let progressTaskList: TaskInfo[] = taskList?.filter((task) => task?.event === TASK_EVENT_TYPE.SUB_NOVEL_READ_MINS)

  // 判断是否有阅读时长子任务
  if (!progressTaskList?.length) {
    return null;
  }

  // 按照目标时长排序
  progressTaskList.sort((pre, next) => pre?.target - next?.target);

  // 已完成、 待领取 的时长节点列表
  const finishAndWaitReceiveTaskList = progressTaskList?.filter((item) => checkTaskFinished(item) || item.state === TASK_COMPLETED);
  const finishTaskList = progressTaskList?.filter((item) => checkTaskFinished(item));
  // 目标任务
  const targetTask = progressTaskList?.[progressTaskList?.length - 1];
  // 目标任务是否完成
  const isTargetFinish = checkTaskFinished(targetTask);
  // 累计任务是否完成
  const isTaskFinish = checkTaskFinished(props?.taskInfo);
  // 获取下一阶段所需的时长
  const nextStageTime = progressTaskList?.[finishAndWaitReceiveTaskList?.length]?.target;
  // 获取下一阶段的奖励
  const nextStageReward = getTaskRewardItem(progressTaskList?.[finishAndWaitReceiveTaskList?.length]).name
  // 距离下一阶段的时长
  const distanceTime = Math.ceil((nextStageTime - progress) / 60);
  // 计算总元宝数; 各个节点奖励相加
  const totalAmount = progressTaskList?.reduce((pre, item) => pre += item.rewardItems?.[0]?.amount, 0)
  // 把进度条平分为 progressTaskList?.length 份数; 每个阶段占一份
  const progressWidth = finishAndWaitReceiveTaskList?.length / progressTaskList?.length * 100;
  // const progressWidth = 2 / 7 * 100;
  const descText = targetTask?.state === TASK_COMPLETED ? '任务已完成，赶紧领奖吧～' : `再读${distanceTime}分钟可赚${nextStageReward}`;
  // 子任务有待领奖状态, 则累计任务为 待领奖状态
  const waitReceiveTaskList = progressTaskList?.filter((task) => task?.state === TASK_COMPLETED);
  // 节点是否有随机奖励
  const hasRandomAmount = progressTaskList?.some((item) => item?.rewardItems?.[0]?.randomAmount);

  const btnClick = (e) => {
    // 有待领取奖励, 则批量领取
    if (waitReceiveTaskList?.length) {
      const completeTaskIds = waitReceiveTaskList?.map((task) => task?.id)?.join(',');

      const publishList: Array<{
        tid: number;
        publishId: number;
      }> = [];
      waitReceiveTaskList.forEach(item => {
        return publishList.push({
          tid: item.id,
          publishId: item.publishId
        });
      });

      execWithLock('batch-award', async () => {
        fact.click('task_click', {
          c: 'task',
          d: `task${props.index + 1}`,
          clicl_action: 'getprize',
          task_id: id,
          module: props.taskModule,
          timemodule: props.timemodule || '',
          task_name: name,
          task_click_position: '待领取',
          taskclassify: props.taskInfo?.taskClassify || '',
          groupcode: props.taskInfo?.groupCode || '',
          resource_location: props?.resource_location || '',
          resource_location_title: props.resource_location_title || '',
          task_progress: finishTaskList?.length,
        });

        await dispatch.task.batchAward({
          tids: completeTaskIds,
          publishList: JSON.stringify(publishList)
        });
      }, 1200);
      return;
    };

    // 目标任务完成, 则累计任务已完成
    if (isTargetFinish || isTaskFinish) {
      fact.click('task_click', {
        c: 'task',
        d: `task${props.index + 1}`,
        clicl_action: 'gotask',
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        task_click_position: '已完成',
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
        task_progress: finishTaskList?.length,
      });
      openURL(url)
      return;
    };

    // 任务未完成, 去做任务
    props.handleClick(e, {
      ...props?.taskInfo,
      task_progress: finishTaskList?.length,
    });
  };

  const renderBtn = () => {
    if (waitReceiveTaskList?.length) {
      return (
        <View className="btn btn-wait-receive">
          <Text className="btn-receive-text">待领取</Text>
        </View>
      );
    }

    // 目标任务完成, 则累计任务已完成
    if (isTargetFinish || isTaskFinish) {
      return (
        <View className="btn btn-finish">
          <Text className="btn-finish-text">已完成</Text>
        </View>
      );
    }

    return (
      <View className="btn btn-doing">
        <Text className="btn-amount-text">{btnName?.length >= 4 ? '去完成' : btnName}</Text>
      </View>
    );
  };

  const getProgressItemText = (progressTask: TaskInfo) => {
    if (checkTaskFinished(progressTask)) {
      return '已完成';
    };
    if (progressTask.state === TASK_COMPLETED) {
      return '可领取';
    }
    return `${(progressTask?.target / 60).toFixed(0)}分钟`
  };

  const getProgressWidth = () => {
    if (progressTaskList?.length < 5) {
      return `100%`
    }
    return `${126 * (progressTaskList?.length || 8)}rpx`
  }

  return (
    <Fact
      className="novel-welfare-read-comp fact-task-item"
      c="task"
      d={`task${props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
        task_progress: finishTaskList?.length,
      }}
    >
      <View className="task-item novel-read-task" onClick={(e) => btnClick(e)}>
        <View className="task-info add">
          <Text className={`item-title`}>{name}</Text>
          <Image
            x-if={totalAmount}
            className="ingot-icon"
            source={INGOT_ICON}
            style={{ width: '34rpx', height: '34rpx', verticalAlign: 'text-bottom' }}
          />
          <Text x-if={totalAmount} className="task-award">
            {hasRandomAmount ? '最高' : ''}
            {totalAmount}
          </Text>
          <View>
            <Text className="task-desc">
              {isTargetFinish || isTaskFinish ? '今日元宝已赚完，请明天再来' : descText}
            </Text>
          </View>
        </View>
        {renderBtn()}
      </View>
      <View className="read-progress-wrap">
        <View className="progress-wrap">
          <View className="progress-item-wrap row">
            <View className="progress-bar-wrap" style={{ width: getProgressWidth() }}>
              <View className="progress-bar" style={{ width: `${progressWidth}%` }} />
            </View>
            {progressTaskList?.map((progressTask, index) => {
              return (
                <View className={`progress-item ${progressTaskList?.length < 5 ? 'item-mean' : ''}`} key={index}>
                  <View className={`item-award ${progressTask?.state === TASK_COMPLETED ? 'can-draw' : ''}`}>
                    <Image className="award-icon" source={INGOT_ICON} />
                    <View className="amount">
                      {getTaskRewardItem(progressTask, !checkTaskFinished(progressTask), false)?.name}
                    </View>
                  </View>
                  <View className="item-node" />
                  <View className="item-text">{getProgressItemText(progressTask)}</View>
                </View>
              );
            })}
          </View>
        </View>
      </View>
    </Fact>
  );
}

export default NovelRead;
