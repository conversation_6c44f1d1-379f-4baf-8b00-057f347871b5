import { createElement } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Fact from '@/components/Fact';
import { TaskInfo, TASK_STATUS } from '@/store/models/task/types';
import { getExtraInfo } from '../../help';
import Image from '@/components/image';
import './index.scss';
import { getDistanceTimeToDay, calculateDaysBetweenDates } from '@/utils/date';
import { checkTaskFinished, getTaskRewardItem } from '../../help';
import { useSelector } from 'rax-redux';
import { StoreState } from '@/store';
import CheckIcon from '../../images/<EMAIL>';
import { openURL } from '../../help';
import fact from '@/lib/fact';
import { isIOS } from '@/lib/universal-ua';

const { TASK_COMPLETED, TASK_DOING } = TASK_STATUS;

interface IProps {
  handleClick: (e, taskInfo) => void | Promise<void>;
  renderBtn: () => JSX.Element;
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  resource_location: string;
  resource_location_title: string;
}

const NovelLimitSignin = (props: IProps) => {
  const { name, id, rewardItems, target, beginTime = 0, endTime = 0, state, cycleTotalDay, url = '' } = props.taskInfo;
  const curTime = useSelector((state: StoreState) => state.task?.now || Date.now());
  const targetList = new Array(target).fill('');
  const rewardIcon = rewardItems?.[0]?.icon || '';
  const isFinish = checkTaskFinished(props.taskInfo);

  // 周期天数
  const dayRange = calculateDaysBetweenDates(beginTime, endTime);
  // 剩余周期天数: 周期天数 减去 当天---即减1
  const remainDay = calculateDaysBetweenDates(endTime, curTime) - 1;
  
  // 当前周期天数: 当天未签到要 加上 当天---即加1
  const curCycleDay =  state === TASK_DOING ? cycleTotalDay + 1 : cycleTotalDay;
  // 不满足完成条件: 任务失效
  const isUnableFinish = remainDay + curCycleDay < target;

  // 把进度条平分为 target * 2 - 1 份数; 每个节点占2份
  const taskProgressPercent = isFinish ? '100' : (cycleTotalDay * 2) / (target * 2 - 1) * 100;

  const renderBtn = () => {
    // 已领奖
    if (isFinish) {
      return (
        <View className="btn btn-finish">
          <Text className="btn-finish-text">已领取</Text>
        </View>
      )
    }
    // 今日已签到
    if ( state === TASK_COMPLETED && cycleTotalDay !== target ) {
      return (
        <View className="btn btn-finish">
          <Text  className="btn-finish-text">今日已到</Text>
        </View>
      );
    }
    // 不满足完成条件
    if (isUnableFinish) {
      return (
        <View className="btn btn-finish">
          <Text className="btn-finish-text">已失效</Text>
        </View>
      );
    }
    return props?.renderBtn();
  };

  const getDescText = () => {
    if (isFinish) {
      return '任务已完成'
    }
    // 待领奖
    if (state === TASK_COMPLETED && cycleTotalDay == target) {
      return '任务已完成，赶紧领奖吧～'
    }
    // 不满足完成条件
    if (isUnableFinish) {
      return '剩余时间不足完成任务'
    }

    return `再到访${target - cycleTotalDay}天即可领奖`
  }

  const btnClick = (e) => {
    // 今日已到、已完成、失效 都不可点击
    if (state === TASK_COMPLETED && cycleTotalDay !== target || isFinish || isUnableFinish) {
      fact.click('task_click', {
        c: 'task',
        d: `task${props.index + 1}`,
        clicl_action: 'gotask',
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        task_click_position: `${isFinish ? '已完成' : isUnableFinish ? '已失效' : '今日已到'}`,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
        task_progress: cycleTotalDay,
      });
      const openUrl = isIOS ? getExtraInfo(props?.taskInfo)?.iosUrl : getExtraInfo(props?.taskInfo)?.androidUrl;
      openURL(openUrl)
      return;
    }
    // 去做任务、领取奖励
    return props.handleClick(e, {
      ...props?.taskInfo,
      task_progress: cycleTotalDay,
    });
  };

  return (
    <Fact
      className="novel-welfare-signin-comp fact-task-item"
      c="task"
      d={`task${props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
        task_progress: cycleTotalDay,
      }}
    >
      <View className="task-item novel-signin-task" onClick={(e) => btnClick(e)}>
        <View className="task-info add">
          <Text className={`item-title`}>{dayRange}天内到访{target}天领大奖</Text>
          <View>
            <Text className="task-desc">{getDescText()}</Text>
          </View>
        </View>
        {renderBtn()}
      </View>
      <View className="signin-progress-wrap">
        <View className="progress-bar-wrap">
          <View className="progress-bar" style={{ width: `${taskProgressPercent}%` }} />
        </View>
        <View className="progress-item-wrap row">
          {targetList?.map((progress, index) => {
            return (
              <View className="progress-item" key={index}>
                <View x-if={index !== (targetList.length - 1)} className="item-node">
                  <Image x-if={cycleTotalDay >= index + 1} className="check-icon" source={CheckIcon} />
                  <View x-else>{index + 1}</View>
                </View>
                <View x-if={index !== (targetList.length - 1)} className="item-text">第{index + 1}天</View>
                <View x-else className="award-info row">
                  <View className="award-desc">{getTaskRewardItem(props.taskInfo, !isFinish)?.name}</View>
                  <View className="award-icon">
                    <Image className="award-img" source={rewardIcon} />
                    <View x-if={cycleTotalDay === target || isFinish} className="item-node can-draw-icon">
                      <Image className="check-icon" source={CheckIcon} />
                    </View>
                  </View>
                  <View className="item-text">第{index + 1}天</View>
                </View>
              </View>
            )
          })}
        </View>
      </View>
    </Fact>
  )
}

export default NovelLimitSignin;
