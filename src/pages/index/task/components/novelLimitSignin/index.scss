.novel-welfare-signin-comp{
  .signin-progress-wrap{
    margin-top: 20rpx;
    width: 610rpx;
    height: 128rpx;
    background-image: linear-gradient(180deg, #EEF2F6 0%, #F8FBFF 100%);
    border-radius: 24rpx;
    padding: 0 16rpx;
    position: relative;

    .progress-bar-wrap{
      width: 526rpx;
      height: 20rpx;
      background: #DEE5EE;
      border-radius: 10rpx;
      position: absolute;
      left: 16rpx;
      top: 34rpx;
      z-index: 1;
      overflow: hidden;

      .progress-bar{
        height: 100%;
        background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
        border-radius: 10rpx;
      }
    }

    .progress-item {
      display: flex;
      flex-direction: column;
      align-items: center; /* 垂直居中对齐 */
      text-align: center; /* 文本居中 */
      justify-content: flex-end;
      flex: 1; /* 每个子元素占据相同的空间 */
      min-width: 0; /* 防止内容溢出 */
      position: relative;
      z-index: 2;
      height: 100%;

      .item-node{
        width: 44rpx;
        height: 44rpx;
        border-radius: 50%;
        font-family: D-DIN-Bold;
        font-size: 28rpx;
        color: #FFFFFF;
        font-weight: 700;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-top: 22rpx;
        background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
        border: 2rpx solid #FFFFFF;
        box-shadow: inset 0 -4rpx 8rpx 0 rgba(255,255,255,0.50);

        .check-icon{
          width: 24rpx;
          height: 24rpx;
          uc-perf-stat-ignore: image;
        }
      }

      .award-info{
        position: absolute;
        top: -10rpx;
        flex-direction: column;
        align-items: center;

        .award-desc{
          background-image: linear-gradient(270deg, #FFE7C1 0%, #FFF6DE 100%);
          border: 2rpx solid #FFFFFF;
          border-radius: 20rpx;
          padding: 6rpx 8rpx;
          font-family: D-DIN-Bold;
          font-size: 10px;
          color: #F02920;
          font-weight: 400;
          margin-top: -6rpx;
          min-width: 88rpx;
        }
      }

      .award-icon{
        margin-top: -8rpx;
        margin-bottom: -8rpx;
        width: 48rpx;
        height: 59rpx;
        position: relative;
        uc-perf-stat-ignore: image;

        .can-draw-icon{
          width: 40rpx;
          height: 40rpx;
          margin-top: 0;
          position: absolute;
          left: 28rpx;
          bottom: 0;
        }
      }

      .item-text{
        margin-top: 10rpx;
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #7E93B7;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
      }
    }
  }
}