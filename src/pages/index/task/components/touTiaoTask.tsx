import { createElement, FC } from 'rax';

import Text from 'rax-text';
import View from 'rax-view';
import Image from '@/components/image';

import { INGOT_ICON } from '@/constants/static_img';


interface TouTiaoTaskProps {
  title: string;
  amount: string;
  target: number;
  progress: number;
  renderBtn: () => JSX.Element;
  btnClick: (e, taskInfo) => void | Promise<void>;
}

const TouTiaoTask: FC<TouTiaoTaskProps> = ({
  title,
  amount,
  target,
  progress,
  renderBtn,
  btnClick,
}) => {
  return (
    <View className="task-item" onClick={btnClick}>
      <View className="task-info">
        <Text className="item-title">{title}</Text>
        <View className="item-desc din-num">
          <Image className="ingot-icon" source={INGOT_ICON} style={{ width: '34rpx', height: '34rpx' }} />
          <Text className="task-award">{amount}</Text>
        </View>
        <View className="task-desc">今日已完成{progress}/{target}次</View>
      </View>
      {renderBtn()}
    </View>
  )
}

export default TouTiaoTask
