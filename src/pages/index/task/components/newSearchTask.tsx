import { createElement, Fragment, memo, useEffect, useState } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Fact from '@/components/Fact';
import fact from '@/lib/fact';
import { IWord, TASK_EVENT_TYPE, TaskInfo } from '@/store/models/task/types';
import { taskItemRewardDesc, filterDongfengTask } from '../help';
import Image from '@/components/image';
import IngotIcon from '@/assets/<EMAIL>';
import { checkTaskFinished } from '@/pages/index/task/help';
import { getTaskFromNewSearch } from '../util';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
const getTaskAward = (taskInfo) => taskInfo?.rewardItems[0]?.amount || '';

interface IProps {
  handleClickKeyword: (taskInfo: TaskInfo, word: IWord) => void | Promise<void>;
  renderBtn: () => JSX.Element;
  taskInfo: TaskInfo;
  index: number;
}

const NewSearchTask = memo(({ handleClickKeyword, renderBtn, taskInfo, index }: IProps) => {
  const { name, id, desc, icon, rewardItems } = taskInfo;
  const rewardDesc = taskItemRewardDesc(taskInfo);
  const hasAward = !!rewardItems?.[0]?.amount;
  const factId = filterDongfengTask(taskInfo) ? `task-dongfeng-${id}` : '';
  const word = taskInfo?.ext?.words?.[0] || { name: '', type: 0, from: '' };
  const [taskCurrentWord, setTaskCurrentWord]  = useState('');
  const [pageview, setPageview] = useState(false);

  usePageVisibilityListener((visible: boolean) => {
    setPageview(visible);
  });

  useEffect(()=> {
    if(!pageview){
      setTaskCurrentWord(word.name);
    }

    if(!taskCurrentWord){
      return;
    }
    if(pageview && taskCurrentWord !== word.name){
      fact.exposure('task_expo', {
        c: 'task',
        d: `task${index + 1}`,
        task_id: id,
        task_name: name,
        taskclassify: taskInfo?.taskClassify || '',
        award_amount: getTaskAward(taskInfo),
        task_count: `${taskInfo?.dayTimes?.progress}`,
        isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
        from: getTaskFromNewSearch(taskInfo),
        query: word?.name,
      });
    }
  },[pageview, word, taskCurrentWord]);

  // 点击任务的event替换
  const handleEventReplaceClick = (e) => {
    const task = JSON.parse(JSON.stringify(taskInfo));
    handleClickKeyword(task, word);
  };
  // 拼接任务名称
  const getJointTitle = (task) => {
    if (!word?.name) {
      return;
    }
    const title = `搜索`;
    const progress = `(${taskInfo?.dayTimes?.progress}/${taskInfo?.dayTimes?.target})`
    if (word?.name?.length <= 5) {
      return (
        <Fragment>
          <View className='item-title-desc'>
            {`${title}“${word?.name}”`}
          </View>
        <View className='title-times'>{progress}</View>
      </Fragment>
      )
    }
    return (
      <Fragment>
        <View className='item-title-desc'>
        {`${title}“${word?.name?.slice(0, 5) + '...'}”`}
        </View>
        <View className='title-times'>{progress}</View>
      </Fragment>
    )
  };
  return (
    <Fact
      className="fact-task-item"
      id={factId}
      c="task"
      d={`task${index + 1}`}
      expoLogkey="task_expo"
      expoExtra={{
        task_id: id,
        task_name: name,
        taskclassify: taskInfo?.taskClassify || '',
        award_amount: getTaskAward(taskInfo),
        task_count: `${taskInfo?.dayTimes?.progress}`,
        isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
        from: getTaskFromNewSearch(taskInfo),
        query: word?.name,
      }}
    >
      <View className="task-item common-task" onClick={(e) => handleEventReplaceClick(e)}>
        {!!icon && <Image className="task-icon" loading="lazy" source={icon} />}
        <View className="task-info addw">
          <View className="item-title flex flex-row items-center">{getJointTitle(taskInfo)}</View>
          <View>
            <View className="task-desc flex-row items-center flex-wrap">
            {taskInfo.desc}
            {hasAward && (
              <Image
              className="ingot-icon"
              source={IngotIcon}
              style={{
                width: '34rpx',
                height: '34rpx',
                verticalAlign: 'text-bottom',
              }}
              />
            )}
            <Text className="task-award align-self-end">{rewardDesc}</Text>
            </View>

          </View>
        </View>
        {renderBtn()}
      </View>
    </Fact>
  );
});

NewSearchTask.displayName = 'NewSearchTask';

export default NewSearchTask;
