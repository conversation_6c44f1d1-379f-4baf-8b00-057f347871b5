.video-read-task {
  img {
    uc-perf-stat-ignore: image;
  }

  .task-item-child {
    padding-bottom: 40rpx;
  }
  .flex {
    display: flex;
  }
  .flex-col {
    flex-direction: column;
  }
  .items-center {
    align-items: center;
  }
  .flex-row {
    flex-direction: row;
  }
  .video-card {
    margin-bottom: 16rpx;
    justify-content: center;
    .card-node {
      width: 112rpx;
      height: 128rpx;
      margin-left: auto;
      padding: 8rpx 0;
      background-image: radial-gradient(circle at 50% 0%, #ffedca 0%, #fff4e6 100%);
      box-shadow: inset 0 -6rpx 20rpx 0 rgba(255, 255, 255, 0.5);
      border-radius: 24rpx;
      &:first-child {
        margin-left: 0rpx;
      }
      .card-status {
        opacity: 0.5;
        font-family: PingFangSC-Semibold;
        font-size: 16rpx;
        color: #901906;
        letter-spacing: 0;
        text-align: center;
        font-weight: 700;
      }
      .card-node-confirmed-icon {
        opacity: 1;
        position: relative;
        .tip-text, 
        .text {
          color: #ffffff;
          font-family: PingFangSC-Semibold;
          font-size: 16rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 700;
        }
        .text {
          visibility: hidden;
        }
        .tool-tip {
          position: absolute;
          bottom: 0;
          left: -25%;
          z-index: 1;
        }
        .tip-text {
          display: inline-block;
          position: absolute;
          bottom: 12rpx;
          left: 0;
          z-index: 2;
        }

      }
      .card-text {
        font-family: D-DIN-Bold;
        font-size: 22rpx;
        color: #901906;
        letter-spacing: 0;
        text-align: center;
        font-weight: 700;
      }
    }
    .card-node-confirmed {
      background: #eef2f6;
      box-shadow: inset 0 -6rpx 20rpx 0 rgba(255, 255, 255, 0.5);
      border-radius: 24rpx;
      .card-status {
        color: #405a86;
      }
      .card-text {
        color: #405a86;
      }
    }
  }

  .progress-bar {
    justify-content: center;
    .progress-node {
      position: relative;
      justify-content: center;
      box-sizing: content-box;
      width: 112rpx;
      padding-left: 54rpx;
      &:first-child {
        padding-left: 0rpx;
      }
    }
    .progress-dot {
      width: 12rpx;
      height: 12rpx;
      background: transparent;
      border: 2rpx solid #dee5ee;
      border-radius: 50%;
    }
    .text-xs {
      font-family: PingFangSC-Regular;
      font-size: 22rpx;
      line-height: 32rpx;
      color: #7e93b7;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      margin-top: 14rpx;
      white-space: nowrap;
    }
    .progress-line {
      position: absolute;
      left: -52rpx;
      top: 4rpx;
      width: calc(100% - 12rpx);
      height: 0rpx;
      border: 2rpx solid #dee5ee;
    }
    .dash-line {
      border: none;
      height: 3px;
      background-image: url(./assets/dash.svg);
      background-repeat: repeat-x;
      uc-perf-stat-ignore: image;
    }
  }
  .progress-bar.node-3 {
    width: 100%;
    padding-left: 50rpx;
    padding-right: 50rpx;
    .progress-node {
      width: 50%;
      padding-left: 0;
    }
    .progress-line {
      left: calc(-50% + 6rpx);
    }
  }
  .progress-bar.node-2 {
    width: 100%;
    padding-left: 50rpx;
    padding-right: 50rpx;
    .progress-node {
      width: 100%;
      padding-left: 0;
    }
    .progress-line {
      left: calc(-50% + 6rpx);
    }
  }
}
