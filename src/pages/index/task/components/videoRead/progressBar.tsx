import { TaskInfo } from '@/store/models/task/types';
import { createElement } from 'rax';
import View from 'rax-view';
import { VideoProgressReward } from './type';

interface IProps {
  storeChildTaskList: TaskInfo[];
  needDashLine?: boolean;
  rewards: Array<VideoProgressReward>;
}

const ProgressBar = (props: IProps) => {
  const {storeChildTaskList, rewards = [], needDashLine = true } = props;

  if (storeChildTaskList && storeChildTaskList.length === 1) {
    return null;
  }

  return (
    <View className={`progress-bar flex flex-row items-center node-${rewards.length}`}>
      {rewards.map((reward, index) => (
        <View key={index} className="progress-node">
          <View
            className={`progress-line ${needDashLine && index === rewards?.length - 1 ? 'dash-line' : ''}`}
            style={index === 0 ? { visibility: 'hidden' } : {}}
          ></View>
          <View className="flex flex-col items-center">
            <View className={`progress-dot`}></View>
            <View className="text-xs">{reward.time}</View>
          </View>
        </View>
      ))}
    </View>
  );
};

export default ProgressBar;
