import { INGOT_ICON } from '@/constants/static_img';
import { VideoProgressReward } from './type';
import { TASK_STATUS, TaskInfo } from '@/store/models/task/types';
import bigSizeIcon from './assets/fudai.png';
import unknownRewardIcon from './assets/coin-mul.png';
import { checkTaskFinished } from '../../help';

const getTipText = (task: TaskInfo) => {
  if (checkTaskFinished(task)) {
    return '已领取';
  }
  if (task.state === TASK_STATUS.TASK_COMPLETED) {
    return '立即领取';
  }
  return '即将获得';
};

const getAmount = (task: TaskInfo) => {
  if (checkTaskFinished(task)) {
    return `${task.prizes?.[0]?.rewardItem?.amount ?? 0}元宝`;
  }
  return `${task.rewardItems?.[0].amount ?? 0}元宝`;
};

export const formatGapToMinutesAndSeconds = (gap: number) => {
  // 计算分钟数和秒数
  const minutes = Math.floor(gap / 60);
  const seconds = gap % 60;

  if (minutes === 0) {
    return `${seconds}秒`;
  }
  if (seconds > 0) {
    return `${minutes}分${seconds}秒`;
  }
  return `${minutes}分钟`;
};

/**
 * @param storeChildTaskList 蓄水子任务list
 * @param progress 当前任务进度，即当前浏览时长
 * @param hasAward 是否有待领取的奖励
 */
export const handleVideoStoreChildTaskList = (
  storeChildTaskList: TaskInfo[],
  progress: number,
  target: number,
  hasAward: boolean,
) => {
  let rewardInfoList: Array<VideoProgressReward> = [];
  let storeChildTask: TaskInfo;
  // 只有一个子任务的时候，不展示进度
  if (storeChildTaskList.length === 1) {
    return {
      needDashLine: false,
      rewardInfoList: [],
    };
  }
  const lastStoreChildTask = storeChildTaskList[storeChildTaskList.length - 1];

  // 都未完成,最后一个子节点
  const lastReward: VideoProgressReward = {
    value: getAmount(lastStoreChildTask),
    time: formatGapToMinutesAndSeconds(lastStoreChildTask?.target),
    status: TASK_STATUS.TASK_DOING,
    icon: bigSizeIcon,
    iconSize: 'big',
  };

  // 未知节点
  const unknownReward: VideoProgressReward = {
    value: '??元宝',
    time: '...',
    status: TASK_STATUS.TASK_DOING,
    icon: unknownRewardIcon,
    iconSize: 'big',
  };

  // 当前进度 >= 总时长
  if (progress >= storeChildTaskList[storeChildTaskList.length - 1]?.target) {
    return {
      needDashLine: false,
      rewardInfoList: [],
    };
  }

  // 只有两个子任务
  if (storeChildTaskList.length === 2) {
    rewardInfoList = []; // 先重置
    storeChildTask = storeChildTaskList[0];
    rewardInfoList.push({
      value: getAmount(storeChildTask),
      time: formatGapToMinutesAndSeconds(storeChildTask?.target),
      status: storeChildTask.state,
      icon: INGOT_ICON,
      tipText: getTipText(storeChildTask),
    });
    rewardInfoList.push({
      ...lastReward,
      iconSize: 'normal',
      time: `再看${formatGapToMinutesAndSeconds((lastStoreChildTask.target ?? 0) - progress)}`,
    });

    return {
      rewardInfoList,
      needDashLine: false,
    };
  }

  // 当前进度<= 第一个子任务
  if (progress <= storeChildTaskList[0]?.target) {
    rewardInfoList = [];
    storeChildTask = storeChildTaskList[0];
    switch (storeChildTask?.state) {
      case TASK_STATUS.TASK_COMPLETED: // 待领奖
        rewardInfoList.push({
          value: getAmount(storeChildTask),
          time: formatGapToMinutesAndSeconds(storeChildTask?.target ?? 0),
          status: TASK_STATUS.TASK_COMPLETED,
          icon: INGOT_ICON,
          tipText: '立即领取',
        });
        rewardInfoList.push({
          value: getAmount(storeChildTaskList[1]),
          time: `再看${formatGapToMinutesAndSeconds((storeChildTaskList[1].target ?? 0) - progress)}`,
          status: TASK_STATUS.TASK_DOING,
          icon: INGOT_ICON,
          tipText: '即将获得',
        });
        break;
      case TASK_STATUS.TASK_FINISH:
      case TASK_STATUS.TASK_CONFIRMED:
        // 已领取奖励
        rewardInfoList.push({
          value: getAmount(storeChildTask),
          time: formatGapToMinutesAndSeconds(storeChildTask?.target ?? 0),
          status: TASK_STATUS.TASK_CONFIRMED,
          icon: INGOT_ICON,
          tipText: '已领取',
        });
        rewardInfoList.push({
          value: getAmount(storeChildTaskList[1]),
          time: `再看${formatGapToMinutesAndSeconds((storeChildTaskList[1].target ?? 0) - progress)}`,
          status: TASK_STATUS.TASK_DOING,
          icon: INGOT_ICON,
          tipText: '即将获得',
        });
        break;
      default:
        rewardInfoList.push({
          value: getAmount(storeChildTaskList[1]),
          time: formatGapToMinutesAndSeconds(storeChildTask?.target ?? 0),
          status: TASK_STATUS.TASK_DOING,
          icon: INGOT_ICON,
          tipText: '即将获得',
        });
        break;
    }
    // 大于3个子任务, 添加未知
    // === 3，用户刚开始
    if (
      storeChildTaskList.length > 3 ||
      (storeChildTaskList[0].state !== TASK_STATUS.TASK_COMPLETED && storeChildTaskList.length === 3)
    ) {
      rewardInfoList.push(unknownReward);
    }
    rewardInfoList.push(lastReward);
    return {
      rewardInfoList,
      needDashLine: true,
    };
  }

  //  其他情况
  // 已经领取奖励的
  let hasReceiveAwardList: TaskInfo[] = [];
  // 待领取奖励的
  let hasNeedReceiveAwardList: TaskInfo[] = [];
  let needDoingTaskIdList: TaskInfo[] = [];
  let curNextStoreChildTask: TaskInfo | null = null;
  for (const item of storeChildTaskList) {
    if (checkTaskFinished(item)) {
      hasReceiveAwardList.push(item);
      continue;
    }
    if (item.state === TASK_STATUS.TASK_COMPLETED) {
      hasNeedReceiveAwardList.push(item);
      continue;
    }
    // 找到第一个相邻节点
    if (item.target >= progress && !curNextStoreChildTask) {
      curNextStoreChildTask = item;
    }
    if (item.state === TASK_STATUS.TASK_DOING) {
      needDoingTaskIdList.push(item);
    }
  }
  // 已经领取过的
  if (hasReceiveAwardList.length) {
    const amount = hasReceiveAwardList.reduce((pre, cur) => {
      return pre + Number(cur.prizes?.[0]?.rewardItem?.amount ?? 0);
    }, 0);
    rewardInfoList.push({
      value: `${amount}元宝`,
      time: formatGapToMinutesAndSeconds(hasReceiveAwardList[hasReceiveAwardList.length - 1].target),
      status: TASK_STATUS.TASK_CONFIRMED,
      icon: INGOT_ICON,
      tipText: '已领取',
    });
  }

  // 待领取奖励的
  if (hasNeedReceiveAwardList.length) {
    const amount = hasNeedReceiveAwardList.reduce((pre, cur) => {
      return pre + Number(cur.rewardItems?.[0].amount ?? 0);
    }, 0);
    rewardInfoList.push({
      value: `${amount}元宝`,
      time: formatGapToMinutesAndSeconds(hasNeedReceiveAwardList[hasNeedReceiveAwardList.length - 1].target),
      status: TASK_STATUS.TASK_COMPLETED,
      icon: INGOT_ICON,
      tipText: '立即领取',
    });
  }
  // 只有最后一个子任务需要做
  if (needDoingTaskIdList.length === 1) {
    rewardInfoList.push({
      value: getAmount(lastStoreChildTask),
      time: `再看${formatGapToMinutesAndSeconds(lastStoreChildTask.target - progress)}`,
      status: TASK_STATUS.TASK_DOING,
      icon: bigSizeIcon,
      tipText: '即将获得',
    });
    return {
      rewardInfoList,
      needDashLine: rewardInfoList.length >= 3 ? true : false,
    };
  }

  if (curNextStoreChildTask) {
    rewardInfoList.push({
      value: getAmount(curNextStoreChildTask),
      time: `再看${formatGapToMinutesAndSeconds(curNextStoreChildTask.target - progress)}`,
      status: TASK_STATUS.TASK_DOING,
      icon: INGOT_ICON,
      tipText: '即将获得',
    });
  }
  if (needDoingTaskIdList.length > 2 && rewardInfoList.length < 3) {
    rewardInfoList.push(unknownReward);
  }
  rewardInfoList.push(lastReward);

  return {
    rewardInfoList,
    needDashLine: rewardInfoList.length >= 3 ? true : false,
  };
};
