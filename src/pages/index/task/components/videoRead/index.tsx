import { createElement, Fragment, useEffect, useMemo, useState } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Fact from '@/components/Fact';
import { useSelector } from 'rax-redux';
import { TASK_STATUS, TaskInfo } from '@/store/models/task/types';
import Image from '@/components/image';
import { INGOT_ICON } from '@/constants/static_img';
import './index.scss';
import ProgressBar from './progressBar';
import VideoCard from './videoCard';
import { formatGapToMinutesAndSeconds, handleVideoStoreChildTaskList } from './utils';
import { dispatch, StoreState } from '@/store';
import { execWithLock } from '@/utils/lock';
import fact from '@/lib/fact';
import { checkTaskFinished, openURL } from '../../help';
import { STORAGE_VIDEO_READ_TASK_NOTIFY } from '@/constants/storage';
import { getLocalStorageWithExpiry, setLocalStorageWithExpiry } from '@/utils/localStorage';
import { videoNotifyChanged } from '@/lib/ucapi';

interface IProps {
  handleClick: (e, taskInfo) => void | Promise<void>;
  renderBtn: () => JSX.Element;
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  resource_location: string;
  resource_location_title: string;
}

const Index = (props: IProps) => {
  const { name, id, icon } = props.taskInfo;
  const storeChildTaskList = useSelector(
      (state: StoreState) =>
        state.resource?.uc_piggy_xssvideo_fuli?.taskList ?? (props.taskInfo?.storeChildTaskList ?? []),
    );
  // 没有子任务, 不展示
  if (!storeChildTaskList?.length) {
    return <Fragment />;
  }
  
  // 当前进度
  const progress = useMemo(() => {
    return props.taskInfo.progress || 0;
  }, [props.taskInfo]);
  // 总时长
  const target = useMemo(() => {
    return storeChildTaskList[storeChildTaskList.length - 1]?.target;
  }, [storeChildTaskList]);

  // 有待领奖励
  const hasAwardList = useMemo(() => {
    return storeChildTaskList.filter((item) => item.state === TASK_STATUS.TASK_COMPLETED);
  }, [storeChildTaskList]);
  // 待领取奖励
  const needReceiveAmount = useMemo(() => {
    return hasAwardList.reduce((pre, cur) => {
      return pre + Number(cur.rewardItems?.[0]?.amount ?? 0);
    }, 0);
  }, [hasAwardList]);
  const finishTaskList = useMemo(() => {
    return storeChildTaskList.filter((item) => checkTaskFinished(item));
  }, [storeChildTaskList]);

  // 所有奖励
  const taskAllRewardAmount = useMemo(() => {
    return storeChildTaskList.reduce((pre, cur) => {
      return pre + Number(cur.rewardItems?.[0]?.amount ?? 0);
    }, 0);
  }, [storeChildTaskList]);

  const videoStoreChildDetail = useMemo(() => {
    return handleVideoStoreChildTaskList(storeChildTaskList, progress, target, !!hasAwardList.length);
  }, [progress, storeChildTaskList]);


  useEffect(()=> {
    const storeKey = getLocalStorageWithExpiry(STORAGE_VIDEO_READ_TASK_NOTIFY);
    if(storeKey){
      return;
    }
    // 任务完成, 且没有待领取奖励
    if(progress >= target && !hasAwardList.length) {
      // 通知客户端
      videoNotifyChanged(String(props.taskInfo.id));
      setLocalStorageWithExpiry(STORAGE_VIDEO_READ_TASK_NOTIFY, true);
    }
  }, [props.taskInfo])

  const onBtnClick = async (e) => {
    // 有待领取奖励, 则批量领取
    if (hasAwardList.length) {
      const completeTaskIds = hasAwardList?.map((task) => task?.id)?.join(',');
      const publishList: Array<{
        tid: number;
        publishId: number;
      }> = [];
      hasAwardList.forEach((item) => {
        return publishList.push({
          tid: item.id,
          publishId: item?.publishId,
        });
      });

      execWithLock(
        'batch-award',
        async () => {
          fact.click('task_click', {
            c: 'task',
            d: `task${props.index + 1}`,
            clicl_action: 'getprize',
            task_id: id,
            module: props.taskModule,
            timemodule: props.timemodule || '',
            task_name: name,
            task_click_position: '待领取',
            taskclassify: props.taskInfo?.taskClassify || '',
            groupcode: props.taskInfo?.groupCode || '',
            resource_location: props?.resource_location || '',
            resource_location_title: props.resource_location_title || '',
            task_progress: finishTaskList?.length,
          });

          await dispatch.task.batchAward({
            tids: completeTaskIds,
            publishList: JSON.stringify(publishList),
          });
        },
        1200,
      );
      return;
    }

    // 目标任务完成, 则累计任务已完成
    if (progress >= target) {
      fact.click('task_click', {
        c: 'task',
        d: `task${props.index + 1}`,
        clicl_action: 'gotask',
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        task_click_position: '已完成',
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
        task_progress: finishTaskList?.length,
      });
      openURL(props.taskInfo.url);
      return;
    }
    // 任务未完成, 去做任务
    props.handleClick(e, {
      ...props?.taskInfo,
      task_progress: finishTaskList?.length,
    });
  };

  return (
    <Fact
      className="fact-task-item video-read-task"
      c="task"
      d={`task${props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props.resource_location || '',
        resource_location_title: props.resource_location_title || '',
        task_progress: finishTaskList?.length,
      }}
    >
      <View className="task-item common-task" onClick={onBtnClick}>
        {!!icon && <Image className="task-icon" loading={'lazy'} source={icon} />}
        <View className="task-info addw">
          <Text className={`item-title`}>{name}</Text>
          <View className={`flex-row items-center task-desc flex-wrap`}>
            {progress >= target && hasAwardList.length
              ? `已看完${formatGapToMinutesAndSeconds(target)} 可领取`
              : `${progress >= target ? '已': ''}看完${formatGapToMinutesAndSeconds(target)}${progress >= target ? '已': '必'}得`}
            <Image className="ingot-icon" source={INGOT_ICON} style={{ width: '34rpx', height: '34rpx' }} />
            <View className="title-award din-num">
              +{progress === target && hasAwardList.length ? needReceiveAmount : taskAllRewardAmount}
            </View>
          </View>
        </View>
        {hasAwardList.length ? (
          <View className="btn btn-wait-receive">
            <Text className="btn-receive-text">待领取</Text>
          </View>
        ) : (
          props.renderBtn()
        )}
      </View>
      {storeChildTaskList.length >= 2 && videoStoreChildDetail.rewardInfoList.length ? (
        <View className="task-item-child">
          <VideoCard
            storeChildTaskList={storeChildTaskList}
            rewards={videoStoreChildDetail.rewardInfoList}
            handleClick={onBtnClick}
          />
          <ProgressBar
            storeChildTaskList={storeChildTaskList}
            needDashLine={videoStoreChildDetail.needDashLine}
            rewards={videoStoreChildDetail.rewardInfoList}
          />
        </View>
      ) : (
        <Fragment></Fragment>
      )}
    </Fact>
  );
};

export default Index;
