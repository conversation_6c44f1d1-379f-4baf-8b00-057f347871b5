import { INGOT_ICON } from '@/constants/static_img';
import { VideoProgressReward } from './type';
import { TASK_STATUS, TaskInfo } from '@/store/models/task/types';
import Image from '@/components/image';
import ToolTipIcon from './assets/tool-tip.png';
import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';

interface IProps {
  storeChildTaskList: TaskInfo[];
  rewards?: Array<VideoProgressReward>;
  handleClick: (e) => void | Promise<void>;
}

const VideoCard = (props: IProps) => {
  const { storeChildTaskList, rewards = [], handleClick } = props;

  if (storeChildTaskList && storeChildTaskList.length === 1) {
    return null;
  }

  return (
    <View className="video-card flex flex-row items-center" onClick={handleClick}>
      {rewards.map((reward, index) => (
        <View
          key={index}
          className={`card-node ${reward.status === TASK_STATUS.TASK_CONFIRMED ? 'card-node-confirmed' : ''}`}
        >
          <View className="flex flex-col items-center">
            {reward.status === TASK_STATUS.TASK_COMPLETED ? (
              <View className="card-node-confirmed-icon card-status">
                {/* 用于位置占位 */}
                <Text className="text">{reward.tipText}</Text>
                <Text className="tip-text">{reward.tipText}</Text>
                <View className="tool-tip">
                  <Image
                    className="card-node-confirmed-icon-inner"
                    source={ToolTipIcon}
                    style={{ width: '96rpx', height: '40rpx' }}
                  ></Image>
                </View>
              </View>
            ) : (
              <View className="card-status">{reward.tipText}</View>
            )}
            <Image
              className="ingot-icon"
              source={reward.icon ?? INGOT_ICON}
              style={
                reward.iconSize === 'big' ? { width: '76rpx', height: '76rpx' } : { width: '56rpx', height: '56rpx' }
              }
            />
            <View className="card-text din-num">{reward.value}</View>
          </View>
        </View>
      ))}
    </View>
  );
};

export default VideoCard;
