import {Component, createElement, Fragment} from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import {
  DOWNLOAD_APP_EVENT,
  FINISH_CAN_JUMP_LINK_EVENT,
  IWord,
  IWordType,
  NEW_SEARCH_TASK_EVENT,
  RTA_TASK_TYPE,
  SHOW_WAIT_RECEIVE_BTN_EVENT,
  TASK_EVENT_TYPE,
  TASK_STATUS,
  TaskInfo
} from '@/store/models/task/types';
import {connect} from 'rax-redux';
import {dispatch, store, StoreDispatch, StoreState} from '@/store';
import Image from '@/components/image';
import Countdown from 'rax-countdown';
import {execWithLock} from '@/utils/lock';
import {
  checkTaskCountDown,
  checkTaskFinished,
  dongfengTaskReport,
  filterDongfengTask,
  getExtraInfo,
  getTargetTimeDiff,
  hideUninstallTask,
  isClickTypeSearchWordsTask,
  isHuiChuangAdEffectTask,
  isIncentiveAdTask,
  isPrtStoreTask,
  isReadTypeSearchWordsTask,
  isSearchWordsTask,
  openURL,
  showDayTimesDesc,
  showTaskDesc,
  taskActionHandler,
  taskItemRewardDesc,
  getTaskCurDayTimeTarget,
  getTaskCurDayTimeProcess,
} from './help';
import fact from '@/lib/fact';
import ucapi from "@/utils/ucapi";
import {INGOT_ICON} from '@/constants/static_img';
import RedPacketIcon from '@/assets/red_packet.png';
import RefreshIcon from './images/<EMAIL>'
import NextIcon from './images/<EMAIL>'
import WechatIcon from '@/assets/<EMAIL>';
import ScanIcon from '@/assets/<EMAIL>';
import UnfoldIcon from './images/<EMAIL>';
import {isWeb} from 'universal-env';
import {getTaskDesc, isCallAppTask, MINIGAME_TASK_TAG} from "@/store/models/task/helper";
import Fact from '@/components/Fact';
import modal from '@/components/modals/modal';
// import {Switch} from '@alifd/meet';
import AsyncComponent from "@/components/AsyncComponent";
import {imgResize} from '@/utils/image';
import logoutCheck from '@/utils/logoutCheck';
import {isAndroid, isIOS} from "@/lib/universal-ua";
import {parseQueryStr} from "@/utils/url";
import {
  deleteAllCalendarReminders,
  deleteCalendarReminders,
  setCalendarReminders,
  setCalendarReminderStoreData
} from '@/utils/calendar_helper';
import toast from '@/lib/universal-toast';
import storage from '@ali/weex-toolkit/lib/storage';
import {STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY} from '@/constants/storage';
import baseModal from '@ali/weex-rax-components/lib/base_modal';
import {MODAL_ID} from "@/components/modals";
import event from "@/utils/event";
// import TouTiaoTask from './components/touTiaoTask';
import {finishSignTaskAndOpenModal, previousNTimesReward} from "@/pages/index/task/help";
import {logToFinishTask} from "@/store/models/task/taskMonitor";
import {getIncentiveAdSlotData} from '@/lib/utils/incentive_ad_help';
import eventer, {EventMap} from '@/lib/utils/event';
import CommonTask from './components/commonTask';
import MeetGift from '../welfareCard/components/meetGift';
import NovelWelfareRead from './components/novelWelfareRead';
import NovelLimitSignin from './components/novelLimitSignin';
import ResourceLocation from './components/resourceLocation';
import VideoReadTime from './components/videoRead/index';
import {getEvSub} from '@/lib/qs';
import NewSearchTask from './components/newSearchTask';
import {getTaskFromNewSearch} from './util';
import { formatLeftTime } from '@/utils/date';
import dayjs from "dayjs";

const { TASK_CONFIRMED, TASK_COMPLETED, TASK_NOT_COMPLETED, TASK_DOING } = TASK_STATUS;

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  moduleType: 'BIG_PRIZE' | 'DAILY' | 'TIME';
  resourceCode: string;
  resource_location: string;
  titleText: string;
}

interface IState {
  gameTaskGroup: number;
  linkTokenTaskUnfold: boolean;
  showDoubleCountdown: boolean;
}

enum TASK_CLICK_POSTION {
  TO_DO = '去完成',
  WAIT_RECEIVE = '待领取',
  FINISHED = '已完成'
}


const renderCommonTaskEvent = [
  TASK_EVENT_TYPE.FULI_ANNOUNCE,
  TASK_EVENT_TYPE.FULI_DESKTOP_VISIT,
  TASK_EVENT_TYPE.FULI_ADD_DESKTOP,
  TASK_EVENT_TYPE.UC_FULI_DESKTOP,
  TASK_EVENT_TYPE.UC_FULI_DESKTOP2,
]

class Item extends Component<IProps, IState> {
  state: IState = {
    gameTaskGroup: 0,
    linkTokenTaskUnfold: false,
    showDoubleCountdown: true,
  }

  get resourceExtraStatInfo() {
    return {
      resource_location: this.props.resourceCode || this.props.resource_location,
      resource_location_title: this.props.titleText || '',
    }
  }

  getResourceExtraClickStatInfo(task: TaskInfo) {
    let clickPostion = TASK_CLICK_POSTION.TO_DO
    if (checkTaskFinished(task)) {
      clickPostion = TASK_CLICK_POSTION.FINISHED
    } else if (task.state === TASK_STATUS.TASK_COMPLETED) {
      clickPostion = TASK_CLICK_POSTION.WAIT_RECEIVE
    }

    return {
      ...this.resourceExtraStatInfo,
      resource_click_position: clickPostion,
    }
  }

  componentDidMount() {
    const gameTaskGroup = localStorage.getItem('gameTaskGroup')
    if (gameTaskGroup) {
      this.setState({
        gameTaskGroup: Number(gameTaskGroup)
      })
    }
    this.exposureAdTask();
    eventer.on(EventMap.PageVisible, () => {
      // 页面可见后,确保是拿到最新的任务数据, 再去曝光
      setTimeout(()=>{
        this.exposureAdTask();
      }, 2000)
    })
  }

  exposureAdTask = () => {
    if (isIncentiveAdTask(this.props.taskInfo)) {
      const adData = getIncentiveAdSlotData(this.props.taskInfo)
      fact.event('incentive_ad_show', {
        task_id: this.props.taskInfo?.id || '',
        task_name: this.props.taskInfo?.name || '',
        taskclassify: this.props.taskInfo?.taskClassify || '',
        groupcode: this.props.taskInfo?.groupCode || '',
        award_amount: this.props.taskInfo?.rewardItems[0]?.amount || '',
        task_progress: this.props.taskInfo?.dayTimes?.progress || '',
        adapp_id: adData?.appId,
        slot_id: adData?.slotKey,
      });
    }
  };

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>){
  }

  showCallAppTask = (task: TaskInfo) => {
    return isCallAppTask(task) && !this.props?.resourceCode && !checkTaskFinished(task)
  }
  finishTask = async (task: TaskInfo) => {
    const taskInfo = task || this.props.taskInfo;
    const { taobaoRtaInfo, rtaScene, taskModule } = this.props;
    const { id, state, event } = taskInfo;
    const isTaobaoRta = RTA_TASK_TYPE.includes(taskInfo?.event) && taobaoRtaInfo;
    fact.click('task_click', {
      c: 'task',
      d: `task${this.props.index + 1}`,
      clicl_action: 'gotask',
      task_id: id,
      module: isCallAppTask(taskInfo) ? '逛APP赚钱' : this.props.taskModule,
      timemodule: this.props.timemodule || '',
      task_name: taskInfo.name,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
      task_progress: taskInfo?.dayTimes?.progress || taskInfo?.task_progress || '',
      ...(isTaobaoRta ? {
        scene: rtaScene,
        taobao_rta_type: taobaoRtaInfo?.category,
        sid: taobaoRtaInfo?.adInfo?.sid,
        rta_price: taobaoRtaInfo.adInfo?.price,
      } : {}),
      ...this.resourceExtraStatInfo,
      task_click_position: '去完成',
    });

    /**
     * 福利专属任务在进入页面会自动触发完成 (首访和到访任务)
     * 但是有概率会导致并发无法自动完成
     * 所以要加点击完成兜底
     */
    const isNovelFuli = getEvSub() === 'novel_fuli' && taskModule === 'uc_piggy_novel';
    const isClouddriveFuli = getEvSub() === 'clouddrive_fuli' && taskModule === 'uc_piggy_clouddrive';
    const isIndexFuli = taskModule === 'uc_piggy_tag_person';
    const canFinishTask = event === TASK_EVENT_TYPE.FULI_LIMIT_SIGNIN || event === TASK_EVENT_TYPE.FULI_MEET_GIFT;
    if ((isNovelFuli || isClouddriveFuli || isIndexFuli) && state === TASK_DOING && canFinishTask) {
      execWithLock('fuli-complete', async () => {
        dispatch.task.complete({
          id,
          type: 'complete',
          publishId: taskInfo?.publishId,
          useUtCompleteTask: taskInfo?.useUtCompleteTask,
          params: { task: taskInfo, toast: true }
      });
      }, 1200);
      return
    }

    await execWithLock(
      'finish_task_lock',
      async () => {
        console.log('taskActionHandler', taskInfo);
        await taskActionHandler(taskInfo, {location: 'list'});
      },
      3000,
    );
  };

  finishKeywordTask = async (task: TaskInfo, word: IWord) => {
    const taskInfo = task || this.props.taskInfo;
    const { id } = taskInfo
    fact.click('task_click', {
      c: 'task',
      d: `task${this.props.index + 1}`,
      clicl_action: 'gotask',
      task_id: id,
      module: this.props.taskModule,
      timemodule: this.props.timemodule || '',
      task_name: taskInfo.name,
      query: word?.name || '',
      commercial: word?.type === IWordType.highbiz ? true : false,
      from: getTaskFromNewSearch(task),
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
      task_progress: taskInfo?.dayTimes?.progress || '',
      task_count: `${taskInfo?.dayTimes?.progress}`,
      isfinish: checkTaskFinished(taskInfo) ? 1 : 0,
    });
    await execWithLock(
      'finish_task_lock',
      async () => {
        console.log('taskActionHandler', taskInfo);
        await taskActionHandler(taskInfo, { word });
      },
      3000,
    );
  }

  award = async (task: TaskInfo) => {
    console.log('award task', task);
    const taskInfo = task || this.props.taskInfo;
    console.log('award taskInfo', taskInfo);
    const { id, name, event, taskClassify, groupCode } = taskInfo;
    fact.click('task_click', {
      c: 'task',
      d: `task${this.props.index + 1}`,
      clicl_action: 'getprize',
      task_id: id,
      module: isCallAppTask(taskInfo) ? '逛APP赚钱' : this.props.taskModule,
      timemodule: this.props.timemodule || '',
      task_name: name,
      task_progress: this.props.taskInfo?.dayTimes?.progress || taskInfo?.task_progress || '',
      taskclassify: taskClassify || this.props.taskInfo?.taskClassify || '',
      groupcode: groupCode || this.props.taskInfo?.groupCode || '',
      ...this.resourceExtraStatInfo,
      task_click_position: '待领取',
    });
    if (event === TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH || event === TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER || event === TASK_EVENT_TYPE.UCLITE_SIGN_MINDER) {
      dispatch.task.complete({ id, type: 'complete', publishId: taskInfo?.publishId, useUtCompleteTask: taskInfo?.useUtCompleteTask,  params: {task: taskInfo }});
    } else {
      if (isHuiChuangAdEffectTask(this.props.taskInfo)) {
        const prizes = await dispatch.task.completeCorpTask({ id, publishId: taskInfo?.publishId, type: 'award' ,params: {task: taskInfo }});
        if (prizes && prizes.length > 0) {
          const validPrizes = prizes.filter(prize => prize.win);
          if (validPrizes.length) {
            modal.openTreasure(validPrizes, (validPrizes[0]?.rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin')
          }
        }
      } else if (event === TASK_EVENT_TYPE.BRAND_TASK) {
        const prizes = await dispatch.task.completeBrandTask({ id, type: 'award', publishId: taskInfo?.publishId, params: { task: taskInfo} });
        if (prizes && prizes.length > 0) {
          const validPrizes = prizes.filter(prize => prize.win);
          if (validPrizes.length) {
            modal.openTreasure(validPrizes, (validPrizes[0]?.rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin')
          }
        }
      } else {
        dispatch.task.complete({ id, type: 'award', publishId: taskInfo?.publishId, params: {task: taskInfo, toast: true  } });
      }
    }
  };

  // 点击任务打点
  handleClickFact = (task: TaskInfo) => {
    // 逛app模块打点
    const { moduleType } = this.props
    if (isCallAppTask(task)) {
      fact.click('guangapptask_click', {
        c: 'mokuai',
        d: 'guangapp',
        task_id: task?.id || '',
        task_name: task?.name || '',
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        ...this.getResourceExtraClickStatInfo(task)
      })
      return
    }
    // 今日任务点击
    if (moduleType === 'DAILY') {
      fact.click('jinritask_click', {
        c: 'mokuai',
        d: 'jinritask',
        task_id: task?.id || '',
        task_name: task?.name || '',
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        ...this.getResourceExtraClickStatInfo(task)
      })
    }
    // 大奖任务点击
    if (moduleType === 'BIG_PRIZE') {
      fact.click('dajiang_click', {
        c: 'mokuai',
        d: 'dajiangtask',
        task_id: task?.id || '',
        task_name: task?.name || '',
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        ...this.getResourceExtraClickStatInfo(task)
      })
    }
  }

  // 点击任务加锁
  BtnClick = async (e, taskInfo: TaskInfo) => {
    execWithLock('click_task_item', () => {
      this.handleClickTaskItem(e, taskInfo);
    }, 1200)
  }

  handleClickTaskItem = async (e, taskInfo: TaskInfo) => {
    const clickTask = taskInfo || this.props.taskInfo;
    // return modal.openTreasure([], 'treasure', clickTask)
    const { state, event } = clickTask;
    const isMiniGame = event.includes(MINIGAME_TASK_TAG);
    dongfengTaskReport(taskInfo , 'click');

    if (isHuiChuangAdEffectTask(clickTask)) {
      fact.event('task_hcad_click', {
        event_id: '19999',
        c: 'task',
        d: 'click',
        task_id: clickTask.id,
        task_name: clickTask.name,
        ...this.getResourceExtraClickStatInfo(clickTask)
      });
    }

    // 素人任务单独处理，仅跳转
    if (event === TASK_EVENT_TYPE.USER_GROUP_LINK) {
      fact.click('amateur_click', {
        c: 'task',
        d: 'link',
        task_id: clickTask?.id || '',
        task_name: clickTask?.name || '',
        taskclassify: clickTask?.taskClassify || '',
        groupcode: clickTask?.groupCode || '',
        ...this.getResourceExtraClickStatInfo(clickTask)
      });
      return ucapi.base.openURL({ url: clickTask.url });
    }

    this.handleClickFact(clickTask)
    if (event === TASK_EVENT_TYPE.UCLITE_INVITE_TASK && !logoutCheck()) {
      fact.click('invite_click', {
        c: 'task',
        d: 'share_go',
        task_id: clickTask?.id || '',
        task_name: clickTask?.name || '',
        taskclassify: clickTask?.taskClassify || '',
        groupcode: clickTask?.groupCode || '',
        ...this.getResourceExtraClickStatInfo(clickTask)
      })
      logToFinishTask(clickTask, '')
      return ucapi.base.openURL({ url: this.props.invitePageLink });
    }
    if (event === TASK_EVENT_TYPE.UCLITE_INVITE_ENTRY) {
      fact.click('code_click', {
        c: 'task',
        d: 'code',
        task_id: clickTask?.id || '',
        task_name: clickTask?.name || '',
        taskclassify: clickTask?.taskClassify || '',
        groupcode: clickTask?.groupCode || '',
        ...this.getResourceExtraClickStatInfo(clickTask)
      })
      if (logoutCheck()){
        return;
      }
      const { shieldInviteModule } = store.getState().app;
      const logInfo = {
        shieldInviteModule
      }
      logToFinishTask(clickTask, '', JSON.stringify(logInfo))
      if (shieldInviteModule) {
        return toast.show('活动已下线');
      } else {
        return modal.openInviteCode();
      }
    }
    if(state === TASK_NOT_COMPLETED && DOWNLOAD_APP_EVENT.includes(event)){
      // 触发下载类任务完成
      const result = await dispatch.task.checkAppDownloadFinish({
        taskInfo: clickTask,
        showToast: false
      });
      if(result){
        return;
      }
    }
    if (state === TASK_COMPLETED) {
      return this.award(clickTask);
    }
    if (this.props.feedsTask?.state !== TASK_STATUS.TASK_DOING && isPrtStoreTask(event)) {
      return
    }

    if (!isMiniGame && checkTaskFinished(clickTask)) {
      fact.click('task_click', {
        c: 'task',
        d: `task${this.props.index + 1}`,
        clicl_action: 'gotask',
        task_id: clickTask?.id,
        module: isCallAppTask(clickTask) ? '逛APP赚钱' : this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: clickTask?.name,
        task_progress: clickTask?.dayTimes?.progress || clickTask?.task_progress || '',
        taskclassify: clickTask?.taskClassify || '',
        groupcode: clickTask?.groupCode || '',
        task_click_position: '已完成',
        ...this.resourceExtraStatInfo,
      });
      if (FINISH_CAN_JUMP_LINK_EVENT.includes(event)) {
        openURL(clickTask?.url || '')
      }
      return
    }

    if (checkTaskCountDown(this.props.taskInfo, this.props.now)) {
      return toast.show('倒计时结束解锁，先去做其它的吧')
    }

    return this.finishTask(taskInfo);
  };

  handleGetMiniGameAward = (completeTaskList: TaskInfo[]) => {
    const completeTaskIds = completeTaskList.map(task => task.id)
    this.props.batchAward({
      tids: completeTaskIds.join(',')
    })
  }

  handleChangeGame = () => {
    const taskNum = this.props.miniGameTaskList.length
    const groupNum = Math.ceil(taskNum / 4)
    const gameTaskGroup = this.state.gameTaskGroup < (groupNum - 1) ? this.state.gameTaskGroup + 1 : 0
    fact.click('task_click', {
      c: 'task',
      d: `task${this.props.index + 1}`,
      clicl_action: 'refresh',
    });
    this.setState({
      gameTaskGroup
    })
    localStorage.setItem('gameTaskGroup', gameTaskGroup + '')
  }

  handleClickSignTask = () => {
    logToFinishTask(this.props.taskInfo, '', JSON.stringify(this.props.taskInfo))
    finishSignTaskAndOpenModal('手动开启')
  }

  handleChangeSearchWord = async () => {
    const { id, name, event , taskClassify, groupCode, rewardItems} = this.props.taskInfo;
    fact.click('task_click', {
      c: 'task',
      d: `task${this.props.index + 1}`,
      clicl_action: 'refresh',
      task_id: id,
      module: this.props.taskModule,
      timemodule: this.props.timemodule || '',
      task_name: name,
      taskclassify: taskClassify || '',
      groupcode: groupCode || '',
      award_amount: rewardItems[0]?.amount,
      task_progress: this.props.taskInfo?.dayTimes?.progress || '',
    });
    await dispatch.task.resetSearchWordsCache(event);
    dispatch.task.queryTask();
  }

  handleClickKeyword = (task: TaskInfo, word: IWord) => {
    console.log('keyword: ', word);
    if (!word?.name) return;
    // 未完成时需要提醒用户点击
    let needShowToast = false
    if (isClickTypeSearchWordsTask(task.event) || isReadTypeSearchWordsTask(task?.event)) {
      needShowToast = true
    }
    if (needShowToast) {
      dispatch.task.setShowTaskUncompletedToast({flag: true, event: task?.event})
    }
    this.finishKeywordTask(task, word);
  }

  handleSwitchChange = async (checked: boolean) => {
    console.log('switch checked', checked)
    fact.click('task_click', {
      c: 'task',
      d: 'calendar',
      status: this.props.hadSetSignRemind ? 1 : 0
    })
    // 设置或删除签到提醒
    if (checked) {
      try {
        await deleteAllCalendarReminders();
        const data = await setCalendarReminders();
        console.log('设置日历', data);
        if (!data.errCode) {
          toast.show('已打开提醒');
          dispatch.app.updateState({
            hadSetSignRemind: true,
          });
          dispatch.app.updateAll();
          // 通过签到任务 switch 入口写入日历时，签到提醒任务不自动领取，需要手动领取
          storage.set(STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY, true);
          setCalendarReminderStoreData();
        } else {
          if (isIOS) {
            toast.show('开启失败，请前往设置-UC极速版-日历开启读写权限');
          } else {
            toast.show('开启失败，请前往系统设置-权限管理-UC极速版开启日历读写权限');
          }
        }
      } catch (err) {
        // 不支持api
        console.log(err);
      }
    } else {
      try {
        const data = await deleteCalendarReminders();
        console.log('删除日历', data);
        if (!data.errCode && data.suc) {
          toast.show('已关闭提醒');
          dispatch.app.updateState({
            hadSetSignRemind: false,
          });
          dispatch.app.updateAll();
        }
      } catch (err) {
        // 不支持api
        console.log(err);
      }
    }
  }

  getDoneBtnDesc = (taskEvent: TASK_EVENT_TYPE) => {
    if (taskEvent.includes(MINIGAME_TASK_TAG) || taskEvent === TASK_EVENT_TYPE.WUFU_BENEFITS_LINK) {
      return '打开'
    }
    if (taskEvent === TASK_EVENT_TYPE.UCLITE_SIGN || taskEvent === TASK_EVENT_TYPE.UCLITE_SIGN_NM) {
      return '今天已签'
    }
    return '已完成'
  }

  renderCountDownBtn = () => {
    const { now, taskInfo } = this.props
    const {isSameDay, diff} = getTargetTimeDiff(taskInfo.beginTime, now);
      const endOfD = dayjs(now).endOf('day');
      const isToday = isSameDay && endOfD.diff(taskInfo?.beginTime, 's') > 60;
    return <View className="btn btn-countdown">
       { isToday && (
         <View  className={`btn-countdown-num ${isIOS ? 'ios-btn-style': ''}`}>
          <Countdown
            timeRemaining={diff * 1000}
            formatFunc={time => {
              const {hour, min, second} = formatLeftTime(time)
              return `${hour}:${min}:${second}`;
            }}
            onComplete={async () => {
            await dispatch.task.queryTask(null)
            }}
          />
       </View>
       )}
        <Text className="btn-countdown-text">{isToday ? '待解锁' : '明日再来'}</Text>
    </View>
  }

  _renderBtnFinish = (event: TASK_EVENT_TYPE) => {
    return (
      <View className="btn btn-finish">
        <Text className="btn-finish-text">{this.getDoneBtnDesc(event)}</Text>
      </View>
    );
  }
 renderRewardTag = (task: TaskInfo) => {
  const { rewardTag = '' } = getExtraInfo(task);
  const displayRewardTag = rewardTag?.length > 5 ? `${rewardTag?.slice(0,5)}...` : rewardTag;
  return (
    displayRewardTag &&
      <View className='btn-extra-num'>
        {displayRewardTag}
      </View>
    )
  }

  renderBtn = (task?: TaskInfo) => {
    const { now, taskInfo } = this.props
    const curTask = task || taskInfo
    const { state, btnName, event, doubleAwardEndTime } = task || taskInfo;

    if (state === TASK_STATUS.TASK_NOT_COMPLETED && DOWNLOAD_APP_EVENT.includes(event)) {
      return (
        <View className="btn btn-receive">
          <Text className="btn-receive-text">待核销</Text>
        </View>
      );
    }
    if (event === TASK_EVENT_TYPE.UCLITE_INVITE_TASK) {
      return <View className="btn btn-doing">
        <Text className="btn-amount-text">{this.props.shieldInviteModule ? '去查看' : '去邀请'}</Text>
      </View>
    }
    if (event === TASK_EVENT_TYPE.UCLITE_INVITE_ENTRY) {
      return <View className="btn btn-doing">
        <Text className="btn-amount-text">去填写</Text>
      </View>
    }

    if (SHOW_WAIT_RECEIVE_BTN_EVENT.includes(event) && state === TASK_COMPLETED) {
      return (
        <View className="btn btn-wait-receive">
          <Text className="btn-receive-text">待领取</Text>
        </View>
      );
    }

    if (state === TASK_COMPLETED) {
      return (
        <View className="btn btn-receive">
          <Text className="btn-receive-text">领取</Text>
        </View>
      );
    }

    // 处在冷却中的任务，显示倒计时
    if (checkTaskCountDown(taskInfo, now)) {
      return this.renderCountDownBtn()
    }
    if (taskInfo?.dayTimes?.target) {
      if (state === TASK_STATUS.TASK_FINISH) {
        return this._renderBtnFinish(event)
      }
    } else if (state === TASK_CONFIRMED) {
      return this._renderBtnFinish(event)
    }
    // 信息流任务已完成 但时长蓄水任务未完成的处理
    if (this.props.feedsTask?.state !== TASK_STATUS.TASK_DOING && isPrtStoreTask(event) && state === TASK_STATUS.TASK_DOING) {
      return (
        <View className="btn btn-finish">
          <Text className="btn-finish-text">未完成</Text>
        </View>
      );
    }
    // 未完成的签到提醒任务
    if (event === TASK_EVENT_TYPE.UCLITE_SIGN_MINDER ) {
      return (
        <View className="switch-doing">
          {isWeb ? <AsyncComponent resolve={() => import('./switch')} /> : null}
        </View>
      );
    }

    const doubleAwardEndTimeDiff = getTargetTimeDiff(doubleAwardEndTime, now).diff
    return (
      <View className="btn-wrapper">
        <View className="btn btn-doing">
          {this.renderRewardTag(curTask)}
          <Text className="btn-amount-text">{ btnName?.length >= 4 ? '去完成': btnName || '去完成'}</Text>
        </View>
        <View x-if={event === TASK_EVENT_TYPE.VIDEO_AD_NEW && doubleAwardEndTimeDiff > 1 && this.state.showDoubleCountdown} className="task-double-time">
          翻倍中
          <Countdown
            timeRemaining={doubleAwardEndTimeDiff * 1000}
            formatFunc={time => {
              const alls = Math.floor(time / 1000);
              let m: string | number = Math.floor(alls / 60);
              let s: string | number = Math.floor(alls % 60);
              s = s < 10 ? `0${s}` : s;
              m = m < 10 ? `0${m}` : m;
              return `${m}:${s}`;
            }}
            onComplete={async () => {
              this.setState({
                showDoubleCountdown: false
                }
              )
              await dispatch.task.queryTask(null)
            }}/>
        </View>
      </View>
    );
  };

  renderADTask = (task: TaskInfo) => {
    const { rewardItems, desc = '看多点多赚得多，本次得', id, name = '看广告赚钱', taskClassify, groupCode, event, dayTimes = {progress: 0, target: 0}} = this.props.taskInfo;
    const { progressiveOrderTask, isOrderTaskAvailable, now } = this.props;
    // 下单任务奖励
    const orderTaskReward = taskItemRewardDesc(progressiveOrderTask, false);
    const awardIcon = (rewardItems?.[0]?.mark || '').includes('cash') ? RedPacketIcon : INGOT_ICON;
    const rewardStr = taskItemRewardDesc(this.props.taskInfo, false);
    const { rewardTip = '' } = getExtraInfo(task ?? this.props.taskInfo);

    const renderAwardIcon = () => {
     return <Image className="ingot-icon" source={awardIcon} style={{width: '34rpx', height: '34rpx'}} />
    }
    const renderCommonReward = () => {
      if (!rewardTip) {
        return (
          <Fragment>
            {rewardStr ? renderAwardIcon() : ''}
            <Text className="task-item-award-strong">{rewardStr}</Text>
          </Fragment>
        );
      }
      return (
        <View className="reward-tip">
          <Image className="ingot-icon" source={awardIcon} style={{ width: '34rpx', height: '34rpx' }} />
          <Text className={`tip-text ${ isAndroid ? 'font-bold' : ''}`}>{rewardTip}</Text>
        </View>
      );
    };
    const curTarget = getTaskCurDayTimeTarget(this.props.taskInfo);
    const curProcess = getTaskCurDayTimeProcess(this.props.taskInfo)

    return <Fact
      className="fact-task-item"
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        task_event: event,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        task_progress: this.props.taskInfo?.dayTimes?.progress || '',
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item" onClick={this.BtnClick}>
        <View className="task-info ad-task-info">
          <View className="item-title">
            <View className="item-title-desc">{name}</View>
            <View className="title-times">
              ({curProcess}/{curTarget})
            </View>
          </View>
          <View x-if={task.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE && isOrderTaskAvailable} className="task-item-award">
            浏览得{renderAwardIcon()}<Text className='task-item-award-strong'>{rewardStr}</Text>
            &nbsp;&nbsp;下单得{renderAwardIcon()}<Text className='task-item-award-strong'>{orderTaskReward}</Text>
          </View>
          <View x-elseif={task.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE && !isOrderTaskAvailable} className="task-item-award">
            浏览并点击广告，本次得
            {renderCommonReward()}
          </View>
          <View x-else={rewardStr} className="task-item-award">
            {desc}
            {renderCommonReward()}
          </View>
        </View>
        {this.renderBtn()}
      </View>
    </Fact>
  }

  // 构造激励视频任务进度条的周期数组
  generateNodeArr = () => {
    const { newAdTaskNodes } = this.props
    if (!newAdTaskNodes.length) {
      return [
        [1, 3, 5, 30],
        [6, 10, 15, 30],
        [16, 20, 25, 30]
      ]
    }
    const numNodes = newAdTaskNodes.map(node => +node).sort((a, b) => a - b)
    const lastNode = numNodes[numNodes.length - 1]
    const nodeArr: any = []
    while (numNodes.length >= 3) {
      const partArr = numNodes.splice(0, 3)
      partArr.push(lastNode)
      nodeArr.push(partArr)
    }
    return nodeArr
  }

  renderNewADTask = () => {
    const progressNodesArr = this.generateNodeArr()
    const { rewardItems, id, name, allTimesRewardMap, dayTimes, taskClassify, groupCode, event } = this.props.taskInfo;
    const awardIcon = (rewardItems?.[0]?.mark || '').includes('cash') ? RedPacketIcon : INGOT_ICON
    const rewardStr = taskItemRewardDesc(this.props.taskInfo)
    const rewardPerTimes = allTimesRewardMap?.['coin'] || []
    const extraRewardPerTimes = allTimesRewardMap?.['coin_extra'] || []
    const progress = dayTimes?.progress || 0
    let progressNodes = progressNodesArr[0]
    if (progressNodesArr[1] && progress > progressNodesArr[0][2]) {
      progressNodes = progressNodesArr[1]
    }
    if (progressNodesArr[2] && progress > progressNodesArr[1][2]) {
      progressNodes = progressNodesArr[2]
    }
    let width = 0
    // 每个节点在进度条上的宽度占比
    const fistNodeWidthPercent = 11
    const secondNodeWidthPercent = 38
    const thirdNodeWidthPercent = 65
    const lastNodeWidthPercent = 93
    if (progress <= progressNodes[2]) {
      // 在前75%
      if (progress === 0) {
        width = 0
      } else {
        if (progress >= progressNodes[0] && progress <= progressNodes[1]) {
          width = fistNodeWidthPercent
          const firstGapTimes = progressNodes[1] - progressNodes[0]
          width +=  ((progress - progressNodes[0]) / firstGapTimes) * (secondNodeWidthPercent - fistNodeWidthPercent)
        }
        if (progress > progressNodes[1] && progress <= progressNodes[2]) {
          width = secondNodeWidthPercent
          const secondGapTimes = progressNodes[2] - progressNodes[1]
          width += ((progress - progressNodes[1]) / secondGapTimes) * (thirdNodeWidthPercent - secondNodeWidthPercent)
        }
      }
    } else {
      width = thirdNodeWidthPercent
      if (progress >= progressNodes[3]) {
        width = 100
      } else {
        const lastGapTimes = progressNodes[3] - progressNodes[2]
        width += ((progress - progressNodes[2]) / lastGapTimes) * (lastNodeWidthPercent - thirdNodeWidthPercent)
      }
    }
    const rewardsNodes = rewardPerTimes.filter(rewardItem => progressNodes.includes(rewardItem.times))
    const extraRewardNodes = extraRewardPerTimes.filter(rewardItem => progressNodes.includes(rewardItem.times))
    const maxAwardStr = `今日最高可得${previousNTimesReward(dayTimes?.target || 0, this.props.taskInfo)}元宝`
    let newDesc = `每天可完成${dayTimes?.target || 0}次, ${maxAwardStr}`
    return <Fact
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      className="fact-task-item"
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        task_event: event,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        task_progress: this.props.taskInfo?.dayTimes?.progress || '',
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item ad-new-task-item" onClick={this.BtnClick}>
        <View className="ad-task-title">
          <View className="task-info ad-task-info">
            <View className="item-title">
              <View className="item-title-desc">{name}</View>
              <Image x-if={rewardStr} className="ingot-icon" source={awardIcon} style={{width: '34rpx', height: '34rpx'}} />
              <View x-if={rewardStr} className="title-award din-num">
                {rewardStr}
              </View>
            </View>
            <View className="item-desc">
              {newDesc}
            </View>
          </View>
          {this.renderBtn()}
        </View>
        <View className="video-progress-wrapper">
          <View className="triangle-symbol"></View>
          <View className="video-progress-content">
            <View className="video-reward-nodes row">
              {
                rewardsNodes.map(node => {
                  const extraReward = extraRewardNodes.find(item => item.times === node.times)
                  const nodeReward = previousNTimesReward(node.times, this.props.taskInfo)
                  return <View className="reward-node">
                    <View className="reward-ingot row din-num">
                      <Image className="ingot-icon" source={awardIcon} style={{width: '32rpx', height: '32rpx'}} />
                       +{nodeReward}
                    </View>
                    <View x-if={extraReward?.amount} className="extra-ingot">
                      额外+{extraReward?.amount}
                    </View>
                    <View className="reward-times">
                      {node.times}次
                    </View>
                  </View>
                })
              }
            </View>
            <View className="progress-bar-wrapper">
              <View className="progress-bar" />
              <View className="progress-bar-inner" style={{width: `${width}%`}} />
              <View className="big-dots-string row">
                <View className="white-circle" />
                <View className="white-circle" />
                <View className="white-circle" />
                <View className="white-circle" />
              </View>
              <View className="dots-string row">
                {
                  Array(3).fill(0).map((item, index) => {
                    return <View key={`part-${index}`} className="dots-string-part row">
                      {
                        Array(3).fill(0).map((item, index) => {
                          return <View key={`dot-${index}`} className="dot"></View>
                        })
                      }
                    </View>
                  })
                }
              </View>
            </View>
          </View>
        </View>
      </View>
    </Fact>
  }

  handleCopy = (e) => {
    e.stopPropagation()
    fact.click('invite_click', {
      c: 'task',
      d: 'paste'
    })
    ucapi.base.copyToClipboard({
      text: this.props.inviteCode || '',
      toast: '1'
    })
  }

  handleShare = (e) => {
    e.stopPropagation()
    fact.click('invite_click', {
      c: 'task',
      d: 'wx'
    })
    const copyText = (this.props.inviteDesc || 'inviteCode').replace('inviteCode', this.props.inviteCode)
    ucapi.base.copyToClipboard({
      text: copyText,
      toast: '0'
    })
    baseModal.open(MODAL_ID.WECHAT_SHARE)
  }

  // 面对面扫码
  handleScan = (e) => {
    e.stopPropagation()
    fact.click('invite_click', {
      c: 'task',
      d: 'qr'
    })
    event.emit('showInviteQrcodePanel');
  }

  renderInviteTask = () => {
    const { name, desc = '新朋友下载 你立得现金', rewardItems, id, taskClassify, groupCode, event } = this.props.taskInfo;
    const { inviteCode, shieldInviteModule } = this.props;
    const awardIcon = (rewardItems?.[0]?.mark || '').includes('cash') ? RedPacketIcon : INGOT_ICON;
    const rewardStr = taskItemRewardDesc(this.props.taskInfo)
    const taskDesc = getTaskDesc(TASK_EVENT_TYPE.UCLITE_INVITE_TASK)
    const isLogin = this.props.isLogin
    return <Fact
      className="fact-task-item"
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        task_event: event,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item invite-task-item" onClick={this.BtnClick}>
        <View className="invite-task-content">
          <View className="task-info ad-task-info">
            <View className="item-title">
              <View className="item-title-desc">{name}</View>
            </View>
            <View className="item-desc">
              {isLogin ? taskDesc || desc : desc}
              <Image x-if={rewardStr} className="ingot-icon" source={awardIcon} style={{width: '34rpx', height: '34rpx'}} />
              <Text className="task-award align-self-end">{rewardStr}</Text>
            </View>
          </View>
          {this.renderBtn()}
        </View>
        <View x-if={isLogin && inviteCode && !shieldInviteModule} className="my-invite-info">
          <View className="my-invite-code">
            <Text className="text-code">我的邀请码</Text>
            <Text className="code-num din-num">{inviteCode}</Text>
            <Text className="text-copy" onClick={this.handleCopy}>复制</Text>
          </View>
          <View className="operate-area">
            <View className="operate-item" onClick={this.handleShare}>
              <Image source={WechatIcon} style={{width: '32rpx', height: '32rpx'}} />
              <Text>分享到微信</Text>
            </View>
            <View className="operate-item" onClick={this.handleScan}>
              <Image source={ScanIcon} style={{width: '32rpx', height: '32rpx'}} />
              <Text>面对面扫码</Text>
            </View>
          </View>
        </View>
      </View>
    </Fact>
  }

  renderStandardLayoutTask = () => {
    const { name, desc, rewardItems, id, taskClassify, groupCode, event } = this.props.taskInfo;
    const awardIcon = (rewardItems?.[0]?.mark || '').includes('cash') ? RedPacketIcon : INGOT_ICON;
    const rewardStr = taskItemRewardDesc(this.props.taskInfo)
    return <Fact
      className="fact-task-item"
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        task_event: event,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        task_progress: this.props.taskInfo?.dayTimes?.progress || '',
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item invite-task-item" onClick={this.BtnClick}>
        <View className="invite-task-content">
          <View className="task-info ad-task-info">
            <View className="item-title">
              <View className="item-title-desc">{name}</View>
            </View>
            <View className="item-desc flex flex-row items-center flex-wrap">
              {desc}
              <Image x-if={rewardStr} className="ingot-icon" source={awardIcon} style={{width: '34rpx', height: '34rpx'}} />
              <Text className="task-award align-self-end">{rewardStr}</Text>
            </View>
          </View>
          {this.renderBtn()}
        </View>
      </View>
    </Fact>
  }

  getGameResizeImg = (url: string) => {
    if (url.includes('peco-img')) {
      return imgResize(url, 70, 100)
    }
    return url
  }

  renderMiniGameTask = () => {
    const { miniGameTaskList } = this.props
    const taskNum = miniGameTaskList.length
    const doneTaskList = miniGameTaskList.filter(task => task.state === TASK_STATUS.TASK_CONFIRMED)
    const completeTaskList = miniGameTaskList.filter(task => task.state === TASK_STATUS.TASK_COMPLETED)
    const doneTaskNum = doneTaskList.length
    const rewardStr = taskItemRewardDesc(this.props.taskInfo)
    return <View className="task-item fact-task-item">
      <View className="task-info game-task-info">
        <View className="item-title">
          <View className="item-title-desc">玩小游戏赚钱</View>
          <Image className="ingot-icon" source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}} />
          <View className="title-award din-num">
            {rewardStr}
          </View>
        </View>
        <View className="item-desc">
          点击任意游戏玩两分钟即可 已完成{doneTaskNum}/{taskNum}次
        </View>
        <View x-if={completeTaskList.length} className="btn btn-receive game-btn-receive" onClick={() => this.handleGetMiniGameAward(completeTaskList)}>
          <Text className="btn-receive-text">领取</Text>
        </View>
        <View className="game-task-list">
          <Image onClick={this.handleChangeGame} className="btn-refresh" source={RefreshIcon} style={{width: '48rpx', height: '48rpx'}} />
          <View className="list-bar">
            {
              miniGameTaskList.map((gameTask, index) => {
                const name = gameTask.name.replace('玩2分钟', '')
                return <Fact
                    id={`${filterDongfengTask(gameTask) ? `task-dongfeng-${gameTask.id}` : ''}`}
                    c="task"
                    d={`task${this.props.index + 1}`}
                    expoLogkey="task_expo"
                    noUseClick
                    key={index}
                    style={{'margin-left': index === 0 ? ` ${-610 * this.state.gameTaskGroup}rpx` : '0', width: '25%'}}
                    expoExtra={{
                      task_id: gameTask.id,
                      task_event: gameTask.event,
                      game: gameTask.name,
                      module: this.props.taskModule,
                      timemodule: this.props.timemodule || '',
                      task_name: gameTask.name,
                      taskclassify: gameTask?.taskClassify || '',
                      groupcode: gameTask?.groupCode || '',
                      award_amount: gameTask?.rewardItems[0]?.amount || '',
                      task_progress: gameTask?.dayTimes?.progress || '',
                      ...this.resourceExtraStatInfo
                    }}>
                  <View key={index} className="game-item" onClick={ (e) => this.BtnClick(e, gameTask)}>
                    <Image className="game-icon" loading={'lazy'} source={this.getGameResizeImg(gameTask.icon)} style={{width: '84rpx', height: '84rpx'}} />
                    <View className="game-name">
                      {name}
                      <Image source={NextIcon} loading={'lazy'} style={{width: '16rpx', height: '16rpx'}}/>
                    </View>
                  </View>
                </Fact>
              })
            }
          </View>
        </View>
      </View>
    </View>
  }

  // 逛app赚钱任务
  renderLinkTokenTask = () => {
    const {  callAppTaskList, showAppTaskNum, taobaoRtaInfo, rtaScene } = this.props;
    const { linkTokenTaskUnfold } = this.state
    const showList = linkTokenTaskUnfold ? callAppTaskList : callAppTaskList.slice(0, showAppTaskNum)
    const notShowNum = callAppTaskList.length - showList.length;
    // 新增x-if 避免首次渲染重复出现 逛APP赚钱
    return <Fact
      c="mokuai"
      d="guangapp"
      expoLogkey="guangapptask_expo"
      noUseClick
      className="link-token-task fact-task-item border-bottom-none"
      key={'callAppTaskModule'}
      x-if={callAppTaskList.length}
      expoExtra={{
        ...this.resourceExtraStatInfo
      }}
    >
      {/* 2025.1.2 产品要求调整隐藏标题 */}
      {/* <View className="item-title">逛APP赚钱</View> */}
      {
        showList.map((task: TaskInfo, index) => {
          const { name, rewardItems, icon, id, event, accountId, adId, slotId, state, desc } = task;
          const isTaobaoRta = [TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK].includes(task.event) && taobaoRtaInfo;
          return <View className='task-item-container' id={`tid-${task.id}`}>
            <Fact key={index}
            id={`${filterDongfengTask(task) ? `task-dongfeng-${id}` : ''}`}
            big-prize={isHuiChuangAdEffectTask(task) ? '1' : '0'}
            data-only-exposed-event={isHuiChuangAdEffectTask(task) ? '1' : '0'}
            className={`task-item ${index === showList.length - 1 ? 'border-bottom-none' : ''}`}
            c="task"
            d={`task${this.props.index + 1}`}
            expoLogkey="task_expo"
            expoExtra={{
              task_id: id,
              task_event: event,
              account_id: accountId,
              ad_id: adId,
              slot_id: slotId,
              task_state: state,
              module: '逛APP赚钱',
              timemodule: this.props.timemodule || '',
              task_name: name,
              taskclassify: task?.taskClassify || '',
              groupcode: task?.groupCode || '',
              award_amount: task?.rewardItems[0]?.amount || '',
              ...(isTaobaoRta ? {
                scene: rtaScene,
                taobao_rta_type: taobaoRtaInfo?.category,
                sid: taobaoRtaInfo?.adInfo?.sid,
                rta_price: taobaoRtaInfo?.adInfo?.price,
              } : {})
            }}
            noUseClick
            onClick={(e) => this.BtnClick(e, task)}
          >
            <Image className="task-icon" source={icon} style={{width: '80rpx', height: '80rpx'}}/>
            <View className="task-info">
              <Text className={`item-title`}>{name}</Text>
              <View className="task-item-award din-num">
                {desc}
                <Image x-if={rewardItems[0]?.mark?.indexOf('cash') > -1 || rewardItems[0]?.mark?.indexOf('draw_money') > -1} className="ingot-icon" source={RedPacketIcon} style={{width: '34rpx', height: '34rpx'}} />
                <Image x-else className="ingot-icon" source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}} />
                <Text className="task-item-award-strong">{taskItemRewardDesc(task)}</Text>
              </View>
            </View>
            {this.renderBtn(task)}
            </Fact>
          </View>
        })
      }
      <View className='more-token-task-container border-bottom'>
        <View x-if={!linkTokenTaskUnfold && notShowNum > 0} className="more-token-task"
              onClick={() => this.setState({linkTokenTaskUnfold: true})}>
          <Image source={UnfoldIcon} style={{width: '24rpx', height: '24rpx'}} />
          <View className="more-desc">更多类似任务 （{notShowNum}）</View>
        </View>
      </View>
    </Fact>
  }

  renderSearchWordsTask = (task: TaskInfo) => {
    const { searchWordsCache, clickTypeSearchWordsCache, baiduSearchWordsCache, searchWordSize, clickTypeSearchWordSize, baiduSearchWordSize } = this.props;
    const { id, dayTimes, name, desc, state, event } = task;
    let showWords = isClickTypeSearchWordsTask(event) ? clickTypeSearchWordsCache : searchWordsCache
    let showWordSize = isClickTypeSearchWordsTask(event) ? clickTypeSearchWordSize : searchWordSize
    if (event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
      showWords = baiduSearchWordsCache
      showWordSize = baiduSearchWordSize
    }

    const rewardStr = taskItemRewardDesc(task)
    const progressPercent = (dayTimes?.progress || 0) / (dayTimes?.target || 1);
    if (!showWords.length && task.state !== TASK_STATUS.TASK_FINISH && progressPercent < 1) return null;
    return <Fact
      key={task?.id}
      className="task-item fact-task-item task-small-padding"
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        task_event: event,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        ...this.resourceExtraStatInfo
      }}>
      <View className="task-info search-word-task-info">
        <View className={`item-title ${state === TASK_STATUS.TASK_FINISH ? 'item-title-finsh': ''}`}>
          <View className="item-title-desc">{name || '搜索赚钱'}</View>
          <View className="title-times">
            ({dayTimes?.progress || 0}/{dayTimes?.target || 0})
          </View>
        </View>
        <View className="item-desc flex-row items-center">
          {/* 点击任意搜索词浏览15秒 已完成{dayTimes?.progress || 0}/{dayTimes?.target || 0}次 */}
          {desc || '点击任意搜索词浏览15秒'}
          <Image className="ingot-icon" source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}} />
          <Text className="title-award din-num">
            {rewardStr}
          </Text>
        </View>
        <View x-if={state === TASK_STATUS.TASK_FINISH || progressPercent === 1} className="btn btn-finish search-task-doing-btn">
          <Text className="btn-amount-text">已完成</Text>
        </View>
        <View className="search-word-task-list" x-if={state <= TASK_STATUS.TASK_CONFIRMED && showWords?.length && progressPercent < 1}>
          <View className="triangle-symbol"></View>
          <Image onClick={this.handleChangeSearchWord} className="btn-refresh" source={RefreshIcon} style={{width: '48rpx', height: '48rpx'}} />
          <View className="list-bar">
            {
              showWords?.map((word, idx) => {
                if (idx >= showWordSize) return null;
                const queryObj = parseQueryStr(task.url)
                return <Fact
                  className="keyword"
                  key={word?.name || idx}
                  onClick={() => this.handleClickKeyword(task, word)}
                  c="task"
                  d="search"
                  expoLogkey="task_expo"
                  noUseClick
                  expoExtra={{
                    task_id: id,
                    task_event: event,
                    module: this.props.taskModule,
                    task_name: name,
                    query: word?.name,
                    from: word?.from || queryObj?.['from'],
                    commercial: word?.type === IWordType.highbiz ? true : false,
                    taskclassify: task?.taskClassify || '',
                    groupcode: task?.groupCode || '',
                  }}
                >
                  <Text className="keyword-text">{word?.name}</Text>
                </Fact>
              })
            }
          </View>
        </View>
      </View>
    </Fact>
  }

  renderDesktop = (props) => {
    const { name, id, desc, icon, rewardItems } = props.taskInfo;
    const rewardDesc = taskItemRewardDesc(props.taskInfo);
    const hasAward = !!rewardItems?.[0]?.amount;

    return (
      <Fact
        className="fact-task-item"
        id={`${filterDongfengTask(props.taskInfo) ? `task-dongfeng-${id}` : ''}`}
        c="task"
        d={`task${props.index + 1}`}
        expoLogkey="task_expo"
        noUseClick
        expoExtra={{
          task_id: id,
          module: props.taskModule,
          timemodule: props.timemodule || '',
          task_name: name,
          taskclassify: props.taskInfo?.taskClassify || '',
          groupcode: props.taskInfo?.groupCode || '',
          resource_location: props.resource_location || '',
          resource_location_title: props.resource_location_title || '',
        }}
      >
        <View className="task-item common-task" onClick={props.handleClick}>
          {!!icon && <Image className="task-icon" loading={'lazy'} source={icon} />}
          <View className="task-info addw">
            <Text className={`item-title`}>{name}</Text>

            <View>
              <View className="task-desc flex-row items-center flex-wrap">
                {desc}
                {hasAward && <Image
              className="ingot-icon"
              source={INGOT_ICON}
              style={{ width: '34rpx', height: '34rpx', verticalAlign: 'text-bottom' }}
              />}
              <Text className="task-award">{rewardDesc}</Text>
              </View>
            </View>
          </View>
          {props.renderBtn()}
        </View>
      </Fact>
    )
  };

  renderTaskContent = (event, task: TaskInfo) => {
    const { bigPrizeEvents } = this.props
    switch (event) {
      // case TASK_EVENT_TYPE.USER_GROUP_LINK:
      //   // 素人任务，先注释下线
      //   return this.renderAmateurTask();
      case TASK_EVENT_TYPE.UCLITE_VIDEO_AD:
        // 旧的激励视频任务，带进度条的
        return this.renderNewADTask();
      case TASK_EVENT_TYPE.UCLITE_SIGN:
      case TASK_EVENT_TYPE.UCLITE_SIGN_NM:
        // 签到任务
        return this.renderSignTask();
      case TASK_EVENT_TYPE.UCLITE_INVITE_TASK:
        // 邀新任务
        return this.renderInviteTask();
      case TASK_EVENT_TYPE.UCLITE_INVITE_ENTRY:
        // 邀请填写任务
        return this.renderStandardLayoutTask();
      case TASK_EVENT_TYPE.UCLITE_WITHDRAWAL:
        // 提现任务
        return this.renderWithdrawalTask();
      case TASK_EVENT_TYPE.FULI_MEET_GIFT:
        // 见面礼
        return <MeetGift
          handleClick={this.BtnClick}
          renderBtn={this.renderBtn}
          taskInfo={this.props.taskInfo}
          taskModule={this.props.taskModule}
          timemodule={this.props.timemodule}
          index={this.props.index}
          {...this.resourceExtraStatInfo}
        />
      case TASK_EVENT_TYPE.FULI_LIMIT_SIGNIN:
        // 小说福利 - 累计到访任务 
        return <NovelLimitSignin
          handleClick={this.BtnClick}
          renderBtn={this.renderBtn}
          taskInfo={this.props.taskInfo}
          taskModule={this.props.taskModule}
          timemodule={this.props.timemodule}
          index={this.props.index}
          {...this.resourceExtraStatInfo}
        />
      case TASK_EVENT_TYPE.NOVEL_READ_MINS:
        /** 看小说赚元宝 */
        return <NovelWelfareRead
          handleClick={this.BtnClick}
          taskInfo={this.props.taskInfo}
          taskModule={this.props.taskModule}
          timemodule={this.props.timemodule}
          index={this.props.index}
          {...this.resourceExtraStatInfo}
        />
      case TASK_EVENT_TYPE.FULI_STORE_WELFARE:
        // 资源位父任务
        return (
          <ResourceLocation
            key={this.props.index}
            handleClick={this.BtnClick}
            taskInfo={this.props.taskInfo}
            taskModule={this.props.taskModule}
            timemodule={this.props.timemodule}
            index={this.props.index}
            {...this.resourceExtraStatInfo}
          />
        )
      case TASK_EVENT_TYPE.STORE_UC_VIDEO_READ_TIME:
        return <VideoReadTime
          handleClick={this.BtnClick}
          renderBtn={this.renderBtn}
          taskInfo={this.props.taskInfo}
          taskModule={this.props.taskModule}
          timemodule={this.props.timemodule}
          index={this.props.index}
          {...this.resourceExtraStatInfo} />
      default:
        // 大奖任务, 任务event统一配置在diamond
        if (bigPrizeEvents.includes(event)) {
          return this.renderBigPrizeTask()
        }
        // 激励视频任务
        if (event?.includes(TASK_EVENT_TYPE.VIDEO_AD)) {
          return this.renderADTask(task)
        }
        // 小游戏任务
        if (event?.includes(MINIGAME_TASK_TAG)) {
          return this.renderMiniGameTask()
        }
        // 属于逛app任务类型 并且 不是 通过资源位投放的任务
        // 并且任务没有完成 (逛app任务类型完成需要沉底)
        if (this.showCallAppTask(task)) {
          return this.renderLinkTokenTask()
        }
        // 搜索任务
        if (isSearchWordsTask(event)) {
          return this.renderSearchWordsTask(task)
        }
        // 资源位相关任务渲染
        if (renderCommonTaskEvent?.includes(event) || isCallAppTask(task) && this.props?.resourceCode) {
          return <CommonTask
            handleClick={this.BtnClick}
            renderBtn={this.renderBtn}
            taskInfo={this.props.taskInfo}
            taskModule={this.props.taskModule}
            timemodule={this.props.timemodule}
            index={this.props.index}
            {...this.resourceExtraStatInfo}
          />
        }
        // 搜索词扩展任务
        if (NEW_SEARCH_TASK_EVENT.includes(event)) {
          return <NewSearchTask
            index={this.props.index}
            handleClickKeyword={this.handleClickKeyword}
            renderBtn={this.renderBtn}
            taskInfo={this.props.taskInfo}
          />
        }
        // 兜底
        return this.renderOtherTask();
    }

    // 头条相关任务，先注释
    // if (event?.startsWith(TASK_EVENT_TYPE.TOUTIAO_TASK)) {
    //   const { id, name, dayTimes, taskClassify, groupCode, rewardItems } = this.props.taskInfo;
    //   const amount = taskItemRewardDesc(this.props.taskInfo)
    //   const { target = 10 , progress = 0 } = dayTimes || {};
    //   return (
    //     <Fact
    //       id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
    //       className="fact-task-item"
    //       c="task"
    //       d={`task${this.props.index + 1}`}
    //       expoLogkey="task_expo"
    //       noUseClick
    //       expoExtra={{
    //         task_id: id,
    //         module: this.props.taskModule,
    //         timemodule: this.props.timemodule || '',
    //         task_name: name,
    //         taskclassify: taskClassify || '',
    //         groupcode: groupCode || '',
    //         award_amount: rewardItems[0]?.amount || '',
    //         ...this.resourceExtraStatInfo
    //       }}
    //     >
    //       <TouTiaoTask
    //         title={name}
    //         amount={amount}
    //         target={target}
    //         progress={progress}
    //         renderBtn={this.renderBtn}
    //         btnClick={this.BtnClick}
    //       />
    //     </Fact>
    //   )
    // }
  }

  renderBigPrizeTask = () => {
    const { taobaoRtaInfo, rtaScene } = this.props;
    const { name, rewardItems, state, icon, id, event, taskClassify, groupCode, accountId, slotId, adId, desc } = this.props.taskInfo;
    const isTaobaoRta = [TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU].includes(event) && taobaoRtaInfo;
    return <Fact
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      className="fact-task-item"
      c="task"
      big-prize={isHuiChuangAdEffectTask(this.props.taskInfo) ? '1' : '0'}
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      data-only-exposed-event={isHuiChuangAdEffectTask(this.props.taskInfo) ? '1' : '0'}
      noUseClick
      expoExtra={{
        task_id: id,
        task_event: event,
        account_id: accountId,
        ad_id: adId,
        slot_id: slotId,
        task_state: state,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        ...(isTaobaoRta ? {
          scene: rtaScene,
          taobao_rta_type: taobaoRtaInfo?.category,
          sid: taobaoRtaInfo?.adInfo?.sid,
          rta_price: taobaoRtaInfo?.adInfo?.price,
        } : {}),
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item" onClick={this.BtnClick}>
        <Image x-if={icon} loading={'lazy'} className="task-icon" aria-hidden source={icon} />
        <View className="task-info corp-task-info">
          <Text className="item-title" numberOfLines={2}>
            {name}
          </Text>
          <View className="item-desc corp-task-desc">
            {desc}
            <Image x-if={rewardItems[0]?.mark?.indexOf('cash') > -1} classNmae="ingot-icon" loading={'lazy'} source={RedPacketIcon} style={{width: '34rpx', height: '34rpx'}} />
            <Image x-else classNmae="ingot-icon" loading={'lazy'} source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}} />
            {taskItemRewardDesc(this.props.taskInfo)}
          </View>
        </View>
        {this.renderBtn()}
      </View>
    </Fact>
  }

  renderSignTask = () => {
    const { hadSetSignRemind, isSupportSignRemind, signList} = this.props;
    let { name, rewardItems, event, id, state, nextSign, taskClassify, groupCode, desc = '连续签到 奖励更高' } = this.props.taskInfo;
    if (nextSign && state === TASK_STATUS.TASK_CONFIRMED) {
      rewardItems = nextSign.rewardItems;
    }
    const nextSignTask = signList.find(sign => sign.state === TASK_STATUS.TASK_PRE_TASK_NOT_FINISH)
    return <View className="fact-task-item">
      <Fact
        id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
        className="task-item"
        c="task"
        d={`task${this.props.index + 1}`}
        expoLogkey="task_expo"
        expoExtra={{
          task_id: id,
          module: this.props.taskModule,
          timemodule: this.props.timemodule || '',
          task_name: name,
          taskclassify: taskClassify || '',
          groupcode: groupCode || '',
          award_amount: rewardItems[0]?.amount || '',
          ...this.resourceExtraStatInfo
        }}
        noUseClick
        onClick={() => this.handleClickSignTask()}
      >
        <View className="task-info">
          <View className="item-title flex flex-row items-center flex-wrap">
            <Text className='item-title-desc'>
              {state === TASK_STATUS.TASK_CONFIRMED ? '明天签到' : (event === TASK_EVENT_TYPE.UCLITE_SIGN_NM ? '今天签到' : name)}
            </Text>
          </View>
          <View className="sign-task-tips flex flex-row items-center flex-wrap" x-if={event === TASK_EVENT_TYPE.UCLITE_SIGN_NM}>
            {desc || ''}
            <View className="item-desc din-num pt-0 flex flex-row items-center " x-if={nextSignTask}>
              <Image x-if={(nextSignTask?.rewardItems[0]?.mark || '').includes('cash')} className="ingot-icon" source={RedPacketIcon} style={{width: '34rpx', height: '34rpx'}} />
              <Image x-else className="ingot-icon" source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}} />
              <Text className="task-award align-self-end">{taskItemRewardDesc(nextSignTask)}</Text>
            </View>
          </View>
          <View className="sign-task-tips flex flex-row items-center flex-wrap" x-else>
            {desc || ''}
          </View>
        </View>
        {this.renderBtn()}
      </Fact>
      <Fact
        className="sign-minder-container"
        x-if={isSupportSignRemind}
        c="task"
        d="calendar"
        expoLogkey="task_expo"
        noUseClick
        expoExtra={{
          status: hadSetSignRemind ? 1 : 0
        }}
      >
        <View className="triangle-symbol"></View>
        <View className="sign-minder-bar">
          <Text className="sign-minder-text">{hadSetSignRemind ? '已' : ''}打开签到提醒避免漏签</Text>
          <Switch className="sign-minder-switch" checked={hadSetSignRemind} size="small" onChange={this.handleSwitchChange} />
        </View>
      </Fact>
    </View>
  }

  renderWithdrawalTask = () => {
    const { name, desc, id, taskClassify, groupCode, rewardItems } = this.props.taskInfo;
    const { withdrawalTag = ''} = getExtraInfo(this.props.taskInfo)
    return <Fact
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      className="fact-task-item"
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item" onClick={this.BtnClick}>
        <View className="task-info">
          <Text className={`item-title`}>{name}</Text>
          { withdrawalTag && <View className="item-desc din-num withdrawal-item-desc">
            <Image  className="ingot-icon" source={RedPacketIcon} style={{width: '34rpx', height: '34rpx'}} />
            <Text className="task-award">{withdrawalTag}</Text>
          </View>}
          <View className="task-desc withdrawal-task-desc">{desc}</View>
        </View>
        {this.renderBtn()}
      </View>
    </Fact>
  }

  renderAmateurTask = () => {
   const { name, icon, id, desc, taskClassify, groupCode, rewardItems } = this.props.taskInfo;
    return <Fact
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      className="fact-task-item"
      c="task"
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        task_name: name,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item" onClick={this.BtnClick}>
        <Image x-if={icon} className="task-icon" aria-hidden source={icon} />
        <View className="task-info corp-task-info">
          <Text className="item-title" numberOfLines={2}>
            {name}
          </Text>
          <View className="item-desc corp-task-desc">
            {desc}
          </View>
        </View>
        {this.renderBtn()}
      </View>
    </Fact>
  }

  renderOtherTask = () => {
    const { name, rewardItems, event, id, target, progress, desc, dayTimes, taskClassify, groupCode, accountId, adId, slotId, state } = this.props.taskInfo;
    const { taobaoRtaInfo, rtaScene } = this.props;
    const rewardDesc = taskItemRewardDesc(this.props.taskInfo)
    let duration = Math.floor((progress / 60));
    const remainder = (progress % 60) / 60;
    if (remainder) {
      const decimals = (Math.floor(remainder * 10))/10
      duration += decimals
    }
    const isTaobaoRta = RTA_TASK_TYPE.includes(event) && taobaoRtaInfo;
    const showDescTaskEvent = [TASK_EVENT_TYPE.UCLITE_TAOBAO_SHOPPING,TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH,TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER,TASK_EVENT_TYPE.LINK,TASK_EVENT_TYPE.CALL_APP_TOKEN,TASK_EVENT_TYPE.BRAND_TASK]
    const renderReward = () => {
      return <View className="item-desc din-num pt-0 flex flex-row items-center">
      <Image x-if={rewardItems[0]?.mark?.indexOf('cash') > -1 || rewardItems[0]?.mark?.indexOf('draw_money') > -1} className="ingot-icon" source={RedPacketIcon} style={{width: '34rpx', height: '34rpx'}} loading={'lazy'} />
      <Image x-elseif={rewardDesc} className="ingot-icon" loading={'lazy'} source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}} />
      <Text className="task-award align-self-end">{rewardDesc}</Text>
    </View>
    }
    return <Fact
      id={`${filterDongfengTask(this.props.taskInfo) ? `task-dongfeng-${this.props.taskInfo.id}` : ''}`}
      className="fact-task-item"
      c="task"
      big-prize={isHuiChuangAdEffectTask(this.props.taskInfo) ? '1' : '0'}
      data-only-exposed-event={isHuiChuangAdEffectTask(this.props.taskInfo) ? '1' : '0'}
      d={`task${this.props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: this.props.taskModule,
        timemodule: this.props.timemodule || '',
        account_id: accountId,
        ad_id: adId,
        slot_id: slotId,
        task_name: name,
        task_event: event,
        task_state: state,
        taskclassify: taskClassify || '',
        groupcode: groupCode || '',
        award_amount: rewardItems[0]?.amount || '',
        task_progress: dayTimes?.progress || '',
        ...(isTaobaoRta ? {
          scene: rtaScene,
          taobao_rta_type: taobaoRtaInfo?.category,
          sid: taobaoRtaInfo?.adInfo?.sid,
          rta_price: taobaoRtaInfo?.adInfo?.price,
        } : {}),
        ...this.resourceExtraStatInfo
      }}
    >
      <View className="task-item" onClick={this.BtnClick}>
        <View className="task-info">
          <View className={`item-title flex flex-row items-center flex-wrap`}>
            <Text className='item-title-desc'>{name}</Text>
            <View x-if={event !== TASK_EVENT_TYPE.WUFU_CARD} className="item-desc din-num pt-0 flex flex-row items-center">
              <Text x-if={isHuiChuangAdEffectTask(this.props.taskInfo)} className="tips">广告</Text>
            </View>
          </View>
          <View className="task-desc" x-if={event === TASK_EVENT_TYPE.UCLITE_REAL_NAME}>
            完成后就可以玩小游戏赚元宝了
            {renderReward()}
          </View>
          <View className="task-desc" x-if={isPrtStoreTask(event)}>
            {`已使用${ duration }/${target / 60}分钟`}
            {renderReward()}
          </View>
          <View x-if={showDayTimesDesc(event)} className="task-desc show-times">
            今日已完成{dayTimes?.progress || 0}/{dayTimes?.target || 0}次
            {renderReward()}
          </View>
          <View x-if={showDescTaskEvent.includes(event) || event.includes('_desc')} className="task-desc flex flex-row items-center flex-wrap show-times">
            {desc}
            {renderReward()}
          </View>
        </View>
        {this.renderBtn()}
      </View>
    </Fact>
  }

  render() {
    const { taskInfo } = this.props;
    if(this.showCallAppTask(taskInfo)) {
      return  this.renderTaskContent(taskInfo.event, taskInfo)
    }
    return <View id={`tid-${taskInfo.id}`} className='task-item-container'>
      {this.renderTaskContent(taskInfo.event, taskInfo)}
    </View>
  }
}
const mapState = (state: StoreState) => {
  const { taskList, displayCallAppTask } = state.task;
  const feedsTask = taskList.find(task => task.event === TASK_EVENT_TYPE.STORE_READ_TIME);
  const progressiveOrderTask = taskList.find(task => task.event === TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER)
  const isOrderTaskAvailable = progressiveOrderTask && !checkTaskFinished(progressiveOrderTask);
  // 过滤掉 唤端任务开关为false
  const callAppTaskList = state.task?.callAppTaskList?.filter(task=>{
    const is_kuai_shou = task?.extra?.includes('kwai://') || task?.extra?.includes('ksnebula://');
    return (!is_kuai_shou || displayCallAppTask) && !hideUninstallTask(task);
  })

  return {
    miniGameTaskList: state.task.miniGameTaskList,
    callAppTaskList,
    progressiveOrderTask,
    isOrderTaskAvailable,
    status: state.user.status,
    isLogin: state.user.isLogin,
    hasBindedAlipay: state.user.hasBindedAlipay,
    now: state.task.now,
    sv: state.user?.sv,
    feedsTask,
    hadSetSignRemind: state.app.hadSetSignRemind,
    isSupportSignRemind: state.app.isSupportSignRemind,
    searchWordsCache: state.task.searchWordsCache,
    clickTypeSearchWordsCache: state.task.clickTypeSearchWordsCache,
    baiduSearchWordsCache: state.task?.baiduSearchWordsCache,
    inviteCode: state.invite.inviteCode,
    inviteDesc: state.app.inviteDesc,
    invitePageLink: state.app.invitePageLink,
    shieldInviteModule: state.app.shieldInviteModule,
    searchWordSize: state.app.searchWordSize,
    clickTypeSearchWordSize: state.app?.clickTypeSearchWordSize,
    baiduSearchWordSize: state.app?.baiduSearchWordSize,
    showAppTaskNum: state.app?.showAppTaskNum || 4,
    bigPrizeEvents: state.app?.bigPrizeEvents,
    signList: state.task.newNewSignList,
    newAdTaskNodes: state.app.newAdTaskNodes,
    amateur: state.app.amateur,
    taobaoRtaInfo: state.rta.taobaoRtaInfo,
    rtaScene: state.app?.taobaoRtaConfig?.sceneId,
  }
};

const mapDispatch = (dispatch: StoreDispatch) => ({
  batchAward: dispatch.task.batchAward
});
export default connect(mapState, mapDispatch)(Item);
