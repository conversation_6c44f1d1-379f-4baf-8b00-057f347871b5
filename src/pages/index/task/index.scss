.container-task {
  // padding: 0 24rpx 24rpx 40rpx;
  padding: 0 0 24rpx 0;
  // margin: -30rpx 30rpx 0;
  margin: 24rpx 30rpx 0;
  min-height: 600rpx;
  background-image: linear-gradient(0deg, #FFFFFF 0%, #F5F9FF 100%);
  border: 1rpx solid rgba(1,37,93,0.10);
  box-shadow: 0 0 15px 0 rgba(0,80,188,0.05), inset 0 0 30px 0 #FFFFFF;
  border-radius: 40rpx;
  transition: all 0.3s;
  .task-item-container {
    padding: 0 40rpx;
  }
  .more-token-task-container {
    margin: 0 40rpx;
  }
  .task-title-img {
    display: inline-block;
    padding: 0 40rpx;
    margin-top: 40rpx;
    height: 36rpx;
    img{
      height: 36rpx;
    }
  }
}
.border-bottom {
  border-bottom: 2rpx solid #eee !important;
}
.border-bottom-none {
  border-bottom: none !important;
}
.link-token-task {
  // padding-top: 50rpx;
  // 隐藏逛APP,调整间距
  // padding-top: 20rpx;
  // padding-bottom: 36rpx;

  .item-title {
    display: inline;
    font-family: PingFangSC-Medium;
    font-weight: bold;
    font-size: 32rpx;
    color: #01255D;
  }
  .task-item {
    // padding-bottom: 20rpx;
    border-bottom: 2rpx solid #eee;
    .task-info {
      display: flex;
      flex-direction: column;
      width: 366rpx;
      .item-desc {
        margin-top: 8rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        text-align: center;
      }
      .ingot-icon {
        uc-perf-stat-ignore: image;
      }
      .task-award {
        text-align: center;
      }
    }
  }
  .more-token-task {
    flex-direction: row;
    align-items: center;
    margin-top: 6rpx;
    margin-bottom: 36rpx;
    img {
      uc-perf-stat-ignore: image;
    }
    .more-desc {
      font-family: PingFangSC-Semibold;
      font-size: 26rpx;
      color: #7E93B7;
      font-weight: 700;
      margin-left: 20rpx;
    }
  }
}
.fact-task-item {
  border-bottom: 2rpx solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.task-item-container {
  .fact-task-item {
    border-bottom: 2rpx solid #eee !important;
  }

  &:last-child {
    .fact-task-item {
      border-bottom: none !important;
    }
  }
}

.common-task{
  .task-info{
    flex:1 ;
  }
  .title-times {
    font-size: 32rpx;
    color: #01255D;
    font-family: PingFangSC-Medium;
  }
  .gift-wrap{
    margin-top: 8rpx;
    display: flex;
    align-items: center;

    .gift-icon{
      width: 34rpx;
      height: 34rpx;
      object-fit: contain;
      margin: 0 1rpx;
      uc-perf-stat-ignore: image;
    }

    .gift-desc{
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #405A86;
    }

    .item-award{
      font-family: D-DIN-Bold;
      font-size: 24rpx;
      color: #405a86;
    }
  }
  .task-icon,
  .ingot-icon {
    uc-perf-stat-ignore: image;
  }
}
.task-item {
  flex-direction: row;
  justify-content: space-between;
  padding: 30rpx 0;
  align-items: center;
  .font-bold {
    font-weight: 700 !important;
  }
  .items-end {
    align-items: end !important;
  }
  .flex-row {
    display: flex !important;
    flex-direction: row !important;
  }
  .items-center {
    align-items: center !important;
  }
  .flex-wrap {
    flex-wrap: wrap !important;
  }
  .align-self-end {
    align-self: flex-end !important;
  }
  .pt-0 {
    padding-top: 0 !important;
  }
  .reward-tip {
    background: rgba(255, 21, 21, 0.05);
    height: 36rpx;
    border-radius: 10rpx;
    display: flex;
    flex-direction: row;
    justify-content: start;
    align-items: center;
    padding: 0 8rpx 0 2.5rpx;
    margin-left: 4rpx;
    .tip-text {
      opacity: 0.8;
      font-family: D-DIN-Bold,PingFangSC-Medium;
      font-size: 24rpx;
      color: #f02920;
      font-weight: 500;
      text-align: center;
    }
  }
  .task-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 16rpx;
    margin-right: 12rpx;
    box-shadow: 0 0 6rpx rgba(0, 0, 0, 0.2);
    uc-perf-stat-ignore: image;
  }

  .task-info {
    width: 450rpx;
    overflow : hidden;
    text-overflow: ellipsis;
    // display: -webkit-box;
    display: inline-block;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;

    .item-title {
      display: inline;
      font-family: PingFangSC-Medium;
      font-weight: bold;
      font-size: 32rpx;
      color: #01255D;
      flex-wrap: wrap;
    }
    .withdrawal-task {
      color: #FF1515;
    }
    .task-award {
      font-family: D-DIN-Bold;
      display: inline-block;
      color: #405A86;
      font-size: 26rpx;
      position: relative;
      top: -2rpx;
    }
    .task-item-award {
      font-family: PingFangSC-Regular;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      // font-weight: 700;
      color: #405A86;
      font-size: 24rpx;
      padding-top: 8rpx;
      align-items: center;
      .task-item-award-strong {
        font-family: D-DIN-Bold;
        font-size: 26rpx;
        color: #405A86;
        // font-weight: 700;
      }
    }
    .task-desc {
      font-family: PingFangSC-Regular;
      color: #405A86;
      font-size: 24rpx;
      margin-top: 8rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-wrap: wrap;
    }
    .item-desc {
      font-family: PingFangSC-Regular;
      color: #405A86;
      font-size: 24rpx;
      font-weight: 400;
      display: inline-block;
      padding-top: 8rpx;

      .ingot-icon {
        display: inline-block;
        vertical-align: text-bottom;
        uc-perf-stat-ignore: image;
      }
    }
    .withdrawal-item-desc {
      // background-color: #FCECEB;
      padding: 6rpx 8rpx 6rpx 2rpx;
      margin-left: 4rpx;
      border-radius: 4rpx;

      .task-award {
        // color: #C93951;
        font-size: 26rpx;
      }
    }
    .withdrawal-task-desc {
      margin-top: 0;
    }
    .corp-task-desc {
      display: flex;
      flex-direction: row;
      margin-top: 4rpx;
      font-weight: 700;
      align-items: center;
      img {
        uc-perf-stat-ignore: image;
      }
    }
    .tips {
      width: 52rpx;
      height: 28rpx;
      background: #EEF2F6;
      border-radius: 4rpx;
      font-family: PingFangSC-Regular;
      font-size: 18rpx;
      color: #B4C1D6;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      margin-top: 2rpx;
      margin-left: 12rpx;
      display: inline-block;
      vertical-align: text-bottom;
    }

    .sign-task-tips {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #405A86;
      margin-top: 8rpx;
    }
  }

  .corp-task-info {
    width: 350rpx;
    display: block;
    margin-top: -8rpx;
  }

  .ad-task-info,
  .game-task-info,
  .search-word-task-info,
  .invite-task-item {
    .item-title {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-right: 4rpx;
    }
    .item-title-desc {
      display: block;
      max-width: 420rpx;
      margin-right: 4rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .item-title-finsh {
      max-width: 400rpx;
      display: flex;
      flex-wrap: wrap;
      .item-title-desc {
        display: block;
        width: 420rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .title-award {
      font-size: 26rpx;
      color: #405A86;
    }
    .title-times {
      font-size: 32rpx;
      color: #01255D;
      font-family: PingFangSC-Medium;
    }
    .item-desc {
      color: #405A86;
    }
    img {
      uc-perf-stat-ignore: image;
    }
  }
  .game-task-info {
    width: 610rpx;
    overflow: visible;
    position: relative;
    .game-task-list {
      position: relative;
      width: 610rpx;
      height: 156rpx;
      padding: 24rpx 0 0 0;
      margin-top: 28rpx;
      background: #EEF2F6;
      border-radius: 24rpx;
      flex-direction: row;
      .list-bar {
        flex-direction: row;
        width: 610rpx;
        overflow: hidden;
      }
      .game-item {
        align-items: center;
        .game-icon {
          border-radius: 16rpx;
          margin-bottom: 10rpx;
        }
        .game-name {
          flex-direction: row;
          align-items: center;
          color: #01255D;
          font-size: 20rpx;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
    .btn-refresh {
      position: absolute;
      right: -10rpx;
      top: -10rpx;
      uc-perf-stat-ignore: image;
    }
  }

  .search-word-task-info {
    width: 610rpx;
    overflow: visible;
    position: relative;
    display: inline-flex;
    .search-word-task-list {
      height: auto;
      padding: 16rpx 14rpx;
      position: relative;
      width: 610rpx;
      margin-top: 28rpx;
      background: #EEF2F6;
      border-radius: 24rpx;
      flex-direction: row;

      .triangle-symbol {
        position: absolute;
        top: -12rpx;
        left: 84rpx;
        width: 0;
        height: 0;
        border-width: 0 12rpx 12rpx;
        border-style: solid;
        border-color: transparent transparent #EEF2F6;
      }

      .list-bar {
        width: 580rpx;
        flex-direction: row;
        flex-wrap: wrap;

        .keyword {
          width: 262rpx;
          margin: 8rpx 12rpx;
          .keyword-text {
            font-family: PingFangSC-Regular;
            font-size: 28rpx;
            color: #405A86;
            letter-spacing: 0;
            font-weight: 400;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
      .btn-refresh {
        position: absolute;
        right: -10rpx;
        top: -10rpx;
      }
    }
  }

  .btn {
    font-family: D-DIN-Bold;
    width: 144rpx;
    height: 56rpx;
    background-size: cover;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    border-radius: 56rpx;
    position: relative;
    font-weight: 600;
  }
  .btn-extra-num{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    text-wrap: nowrap;
    height: 30rpx;
    position: absolute;
    right: 0;
    top: -24rpx;
    background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
    border-radius: 16rpx 16rpx 16rpx 2rpx;
    font-family: PingFangSC-Semibold;
    font-size: 18rpx;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: nowrap;
    padding: 0 10rpx;
    padding-top: 2rpx !important;
  }
  .task-double-time {
    font-size: 20rpx;
    color: #7E93B7;
    letter-spacing: 0;
    text-align: center;
    line-height: 28rpx;
    font-weight: 400;
    margin-top: 4rpx;
  }
  .btn-receive {
    background-image: url("./images/<EMAIL>");
  }

  .btn-wait-receive{
    background-image: linear-gradient(90deg, #FF5F50 0%, #FF2A21 99%);
    box-shadow: inset 0 -6rpx 20rpx 0 rgba(255,255,255,0.50);
  }
  .game-btn-receive,
  .search-task-doing-btn {
    position: absolute;
    right: 0;
    top: 14rpx;
  }
  .btn-receive-text {
    // font-weight: bold;
    font-size: 26rpx;
    color: #fff;
    text-align: center;
  }
  .btn-doing {
    color: #FF1515;
    border-radius: 34rpx;
    border: 2rpx solid #FAC9C7;
  }

  .btn-finish {
    // background-image: url("./images/<EMAIL>");
    border-radius: 46rpx;
    border: 2rpx solid #E5E9ED;
    color: #01255D;
    opacity: 0.5;
  }
  .btn-finish-text {
    // font-weight: bold;
    font-size: 26rpx;
    color: #01255D;
    text-align: center;
  }
  .btn-countdown {
    border-radius: 46rpx;
    border: 2rpx solid #E5E9ED;

    position: relative;
    .btn-countdown-num{
      width: 100rpx;
      height: 30rpx;
      position: absolute;
      right: 0;
      top: -24rpx;
      background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
      border-radius: 16rpx 16rpx 16rpx 2rpx;
      font-family: D-DIN-Bold;
      font-size: 22rpx;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 2rpx;
    }

    .ios-btn-style{
      width: 106rpx;
      letter-spacing: -1rpx;
    }

    .btn-countdown-text{
      color: rgba(1,37,93,.5);
      font-size: 26rpx;
    }
  }
  .icon-amount {
    height: 60rpx;
    width: 60rpx;
  }
  .btn-amount-text {
    // font-weight: bold;
    font-size: 26rpx;
    text-align: center;
  }

  .switch-doing {
    .sign-minder-btn {
      height: 54rpx;
      border-radius: 27rpx;
      background: linear-gradient(180deg, #B4C1D6 0%, #D4DCE8 100%);
      box-shadow: inset 0 -6rpx 20rpx 0 rgba(255,255,255,0.50);

      .mt-switch-knob {
        width: 36rpx;
        height: 36rpx;
        margin: 8rpx;
      }
    }
  }
}
.invite-task-item {

  flex-direction: column;
  .invite-task-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  .my-invite-info {
    width: 610rpx;
    height: 156rpx;
    border-radius: 24rpx;
    margin-top: 28rpx;
    border: 2rpx solid #eee;
    overflow: hidden;
  }
  .my-invite-code {
    flex-direction: row;
    width: 100%;
    height: 72rpx;
    align-items: center;
    padding: 0 24rpx;
    background: #EEF2F6;
    .text-code {
      font-size: 24rpx;
      color: #405A86;
      margin-right: 14rpx;
    }
    .code-num {
      font-size: 26rpx;
      color: #01255D;
      font-weight: 700;
      letter-spacing: 2rpx;
      margin-right: 14rpx;
    }
    .text-copy {
      font-size: 24rpx;
      color: #2696FF;
    }
  }
  .operate-area {
    flex-direction: row;
    .operate-item {
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: 82rpx;
      flex: 50%;
      img {
        margin-right: 12rpx;
        uc-perf-stat-ignore: image;
      }
      span {
        font-size: 24rpx;
        color: #01255D;
      }
    }
  }
}

.sign-minder-container {
  position: relative;
  top: -16rpx;
  margin-bottom: 20rpx;

  .triangle-symbol {
    position: absolute;
    top: -12rpx;
    left: 80rpx;
    width: 0;
    height: 0;
    border-width: 0 12rpx 12rpx;
    border-style: solid;
    border-color: transparent transparent #EEF2F6;
  }
  .sign-minder-bar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: #EEF2F6;
    border-radius: 24rpx;
    height: 88rpx;
    width: 608rpx;

    .sign-minder-text {
      font-family: PingFangSC-Regular;
      font-size: 28rpx;
      color: #405A86;
      letter-spacing: 0;
      margin-left: 32rpx;
    }
    .mt-switch--small {
      background: linear-gradient(180deg, #B4C1D6 0%, #D4DCE8 100%);
    }
    .sign-minder-switch {
      margin-right: 32rpx;
      height: 44rpx;
      width: 76rpx;
      border-radius: 22rpx;
      box-shadow: inset 0 -6rpx 20rpx 0 rgba(255,255,255,0.50);
      border: none;

      .mt-switch-knob {
        width: 28rpx;
        height: 28rpx;
        margin: 8rpx;
      }

    }
    .mt-switch--checked {
      background: linear-gradient(179deg, #F02920 1%, #FFA29D 99%);
    }
  }
}

.container-corp-task {
  min-height: 0;
  padding: 0 40rpx 10rpx 40rpx;
  .task-item-container {
    padding: 0 !important;
  }
  .corp-task-header {
    display: inline-block;
    margin-bottom: 10rpx;

    .header-pic {
      uc-perf-stat-ignore: image;
      height: 36rpx;
      margin-top: 40rpx;
    }
  }
}

.ad-new-task-item {
  padding: 40rpx 0 25rpx;
  align-items: center;
  flex-direction: column;
  .ad-task-title {
    width: 610rpx;
    overflow: visible;
    position: relative;
    flex-direction: row;
    justify-content: space-between;
  }
}

.task-small-padding {
  padding-bottom: 18rpx;
}

.video-progress-wrapper {
  height: 156rpx;
  padding: 0 12rpx;
  position: relative;
  width: 610rpx;
  margin-top: 28rpx;
  background: #EEF2F6;
  border-radius: 24rpx;
  flex-direction: row;

  .triangle-symbol {
    position: absolute;
    top: -12rpx;
    left: 84rpx;
    width: 0;
    height: 0;
    border-width: 0 12rpx 12rpx;
    border-style: solid;
    border-color: transparent transparent #EEF2F6;
  }
}

.video-progress-content {
  width: 588rpx;
  position: relative;
  .video-reward-nodes {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    .reward-node {
      width: 146rpx;
      height: 156rpx;
      align-items: center;
      justify-content: center;
      position: relative;
      .reward-ingot {
        font-size: 22rpx;
        color: #405A86;
        letter-spacing: 0;
        font-weight: 700;
        line-height: 32rpx;
      }
      .reward-times {
        font-size: 22rpx;
        color: #7E93B7;
        letter-spacing: 0;
        margin-top: 44rpx;
      }
    }
    .extra-ingot {
      position: absolute;
      top: -10rpx;
      left: 30rpx;
      width: 106rpx;
      height: 30rpx;
      background-image: linear-gradient(270deg, #F02920 0%, #FF8B80 100%);
      border-radius: 16rpx;
      font-size: 18rpx;
      color: #FFFFFF;
      letter-spacing: 0;
      line-height: 22rpx;
      font-weight: 700;
      align-items: center;
      justify-content: center;
    }
  }
  .progress-bar-wrapper {
    position: relative;
    width: 532rpx;
    border-radius: 32rpx;
    overflow: hidden;
    margin: 70rpx auto 0;
  }

  .progress-bar {
    width: 100%;
    height: 20rpx;
    background: #e2e6ee;
    border-radius: 20rpx;
  }

  .progress-bar-inner {
    position: absolute;
    left: 0;
    top: 0;
    height: 20rpx;
    border-radius: 20rpx 20rpx;
    background-image: linear-gradient(270deg, #F02920 0%, #FFA29D 98%);
    align-items: center;
    justify-content: center;
    max-width: 100%;
  }
  .big-dots-string {
    width: 100%;
    height: 12rpx;
    position: absolute;
    justify-content: space-between;
    top: 4rpx;
    padding: 0 42rpx;
    .white-circle {
      width: 12rpx;
      height: 12rpx;
      border-radius: 12rpx;
      background: #fff;
    }
  }
  .dots-string {
    width: 100%;
    height: 8rpx;
    top: 7rpx;
    padding: 0px 86rpx;
    position: absolute;
    .dots-string-part {
      width: 60rpx;
      height: 8rpx;
      justify-content: space-between;
      margin-right: 86rpx;
    }
    .dot {
      width: 8rpx;
      height: 8rpx;
      border-radius: 8rpx;
      background: #fff;
      opacity: 0.6;
    }
  }
}
.hight-lighted-task {
  background: inherit;
  animation-name: highlight-animation;
  animation-duration: 2.67s;
  animation-timing-function: linear;
  animation-fill-mode: forwards; // 动画结束后保持最后一帧的状态
}


@keyframes highlight-animation {
  0% {
    background: rgb(255, 227, 217,0.6);
  }
  6% {
    background: rgb(255, 227, 217,1);
  }
  25% {
    background: rgb(255, 227, 217,0.3);
  }
  43% {
    background: rgb(255, 227, 217,1);
  }
  62% {
    background: rgb(255, 227, 217,0.6);
  }
  100% {
    background: rgb(255, 227, 217,0);
  }
}
