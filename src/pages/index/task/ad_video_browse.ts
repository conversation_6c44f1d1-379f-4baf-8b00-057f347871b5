import {AdPlayer} from '@ali/weex-toolkit/lib/adPlayer';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import config from '@/config';
import tracker from '@/lib/tracker';
import {TaskInfo} from "@/store/models/task/types";
import fact from "@/lib/fact/index";

export interface IRewardInfoItem {
  /** 广告来源id */
  adn_id: string;
  /** 广告产品ID */
  app_id: string;
  /** 广告pid */
  pid: string;
  /** 广告实际价格 */
  price: string;
  /** 广告返回的广告Session ID */
  sid: string;
  /** 广告位ID */
  slot_id: string;
}

export interface IPreloadRes {
  requestId: string;
  type: string;
  reward_info_list: IRewardInfoItem[];
  errCode?: string;
  error?: string;
  is_tanx_advanced?: boolean; 
  ext: any;
}

export interface IBrowseAdPlayerConfig {
  task: TaskInfo;
  slotKey: string;
  appId: string;
  /** coralAppId */
  coralAppId?:string;
  /** moduleCode */
  moduleCodeTask?: string;
  /** 是否开启异步查奖功能 */
  enableAsyncQueryReward: boolean;
  /** 初始化打点 */
  initFact?: ()=> void;
  /** 广告预加载完成 */
  finishPreload?: (value: IPreloadRes | null)=> void;
}
export class BrowseAdPlayer {
  public browseAdPlayerMap: Map<string, AdPlayer> = new Map();
  private configMap: Map<string, IBrowseAdPlayerConfig> = new Map();
  private adTaskPreloadResults = new Map<number, {slotKey: string; isTanxAdvanced: boolean, reward_info_list: IRewardInfoItem[]}>();
  constructor(params?: IBrowseAdPlayerConfig){
    if(params){
      this.configMap.set(String(params.task.id), params);
    }
  }

  init(params: IBrowseAdPlayerConfig){
    const taskId = params.task.id;
    if(this.browseAdPlayerMap.get(String(taskId))){
      return;
    }
    // console.log('激励视频浏览 sdk】初始化]', params);
    const browseAdPlayer = new AdPlayer();
    browseAdPlayer.setup({
      appId: params.appId,
      coralAppId: params.coralAppId ?? config.appId,
      moduleCode: params.moduleCodeTask ?? config.moduleCodeTask,
      /** 非聚合方式广告 aid ，必填 */
      backupAid: {
        ios: '946670931',
        android: '946670931',
      },
      /** 非聚合方式用的类型, 默认 tt(穿山甲)，可选 hc（汇川） */
      backupTypeMap: {
        ios: 'tt', // tt | hc
        android: 'tt', // tt | hc
      },
      /** 聚合sdk 的aid，建议传入 */
      mixedAidMap: {
        ios: '10000281',
        android: 'uclite_activity_ad_videoad',
      },
      // 具体的监控项id，必填
      wpkCategory: {
        preload: WPK_CATEGORY_MAP.AD_VIDEO_PRELOAD,
        invoke: WPK_CATEGORY_MAP.AD_VIDEO_INVOKE,
        play: WPK_CATEGORY_MAP.AD_VIDEO_PLAY,
      },
      /** 可选参数，对应声浪活动 */
      businessCode: 'uclite_activity_ad',
      // @ts-ignore
      slotKey: params.slotKey,
      rewardedVideoSlotKey: params.slotKey,
      enableAsyncQueryReward: !!params.enableAsyncQueryReward,
    });
    params?.initFact && params.initFact();
    this.configMap.set(String(taskId), params);
    this.browseAdPlayerMap.set(String(taskId), browseAdPlayer);
    this.adTaskPreload(params.task).then((res: IPreloadRes | null) => {
      params.finishPreload && params.finishPreload(res);
    })
  }
  adTaskPreload(adTask: TaskInfo) {
    const taskId = adTask.id;
    const browseAdPlayer = this.browseAdPlayerMap.get(String(taskId));
    const { slotKey, appId } = this.configMap.get(String(taskId)) || {
      slotKey: '',
      appId: '',
    };
    if (browseAdPlayer) {
      return browseAdPlayer.preload().then((res: IPreloadRes | null) => {
        console.log(`${adTask.name}${slotKey}${appId}${taskId}预加载返回`, res);
        tracker.log({
          category: 170, // 系统自动生成，请勿修改
          msg: res ? 'adPlayer预加载成功' : 'adPlayer预加载失败',
          w_succ: res ? 1 : 0, // 用于计算"成功率";可选值为0或1
          c1: String(taskId ?? ''), // 自定义字段c1 对应 任务ID
          c2: slotKey,
          c3: res?.is_tanx_advanced ? '1' : '0',
          c4: res?.reward_info_list?.[0]?.pid || '',
          c5: res?.reward_info_list?.[0]?.adn_id || '',
          bl1: JSON.stringify(res),
        })
        fact.event('incentive_ad_pre_request', {
          task_id: adTask?.id,
          task_name: adTask?.name,
          taskclassify: adTask?.taskClassify || '',
          groupcode: adTask?.groupCode || '',
          adapp_id: appId,
          slot_id: slotKey,
          pre_request_result: res ? 'success' : 'fail',
          adn_id: res?.reward_info_list?.[0]?.adn_id || '',
          sid: res?.reward_info_list?.[0]?.sid || '',
          price: res?.reward_info_list?.[0]?.price || '',
          pid: res?.reward_info_list?.[0]?.pid || '',
          award_amount: adTask?.rewardItems[0]?.amount || '',
          task_progress: adTask?.dayTimes?.progress || '',
          is_tanx_advanced: res?.is_tanx_advanced ? '1' : '0',
        })
        this.setAdTaskPreloadResult(taskId, slotKey, res);
        return res;
      })
    }
    return Promise.resolve(null)
  }
  
  // 广告位预加载结果
  setAdTaskPreloadResult(taskId: number, slotKey: string, preloadRes: IPreloadRes | null){
    this.adTaskPreloadResults.set(taskId, {
      slotKey, 
      isTanxAdvanced: !!preloadRes?.is_tanx_advanced,
      reward_info_list: preloadRes?.reward_info_list || [],
    });
  }
  getAdTaskPreloadResult(taskId: number){
    return this.adTaskPreloadResults.get(taskId);
  }
  removeAdTaskPreloadResult(taskId: number){
    this.adTaskPreloadResults.delete(taskId);
  }

  getAdPlayer(taskId: number | string){
    const browseAdPlayer = this.browseAdPlayerMap.get(String(taskId));
    const playerParams = this.configMap.get(String(taskId));
    if(browseAdPlayer){
      tracker.log({
        category: 169, // 系统自动生成，请勿修改
        msg: 'adPlayer实例化成功', // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 1, // 用于计算"成功率";可选值为0或1
        c1: String(taskId ?? ''), // 自定义字段c1 对应 任务ID
        c2: playerParams?.slotKey,
        bl1: JSON.stringify(playerParams), // 自定义长文本bl1 对应 错误信息
      });
      return browseAdPlayer;
    }else {
      console.error('当前任务ID未初始化', taskId);
      tracker.log({
        category: 169, // 系统自动生成，请勿修改
        msg: 'adPlayer实例化失败', // 将根据msg字段聚合展示在平台的top上报内容中
        w_succ: 0, // 用于计算"成功率";可选值为0或1
        c1: String(taskId ?? ''), // 自定义字段c1 对应 任务ID
        c2: playerParams?.slotKey,
        bl1: JSON.stringify(playerParams), // 自定义长文本bl1 对应 错误信息
      });
      return null
    }
  }
}
export const browseAdPlayerInstance = new BrowseAdPlayer();
