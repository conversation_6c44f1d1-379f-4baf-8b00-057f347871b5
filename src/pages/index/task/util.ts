import {DESKTOP_TASK_EVENT, TASK_EVENT_TYPE, TaskInfo} from '@/store/models/task/types';
import {dispatch, store} from '@/store';
import storage from '@ali/weex-toolkit/lib/storage';
import {setCalendarReminders, setCalendarReminderStoreData} from '@/utils/calendar_helper';
import ucapi from '@/utils/ucapi';
import {isAndroid, isIOS, isLatestVersion, appVersion} from "@/lib/universal-ua";
import Toast from '@/lib/universal-toast';
import modal from '@/components/modals/modal';
import { MODAL_ID } from '@/components/modals';
import baseModal from '@ali/weex-rax-components/lib/base_modal';
import global, {EGolbalInstance} from "@/utils/global";
import {
  STORAGE_DEFAULT_BROWSER_CLICKED_KEY,
  STORAGE_PUSH_SWITCH_CLICKED_KEY,
  STORAGE_SIGN_MINDER_TASK_SET_CALENDAR_KEY
} from '@/constants/storage';
import {
  clickCheckAppInstall,
  delayTaskCompleteByAwardTime,
  finishSignTaskAndOpenModal,
  formatStrToObj,
  getExtraInfo,
  handleCallAppDownload,
  handleCallAppTask,
  handleHighValueDialog,
  handleLogin,
  handleRTACallTask,
  handleRTANuCallTask,
  isSearchWordsTask,
  isTimeLinkTask,
  jumpTaskHander,
  openURL,
  saveAdInsideGroupTaskInfoToStore,
} from './help';
import {browseAdPlayerInstance, IRewardInfoItem} from './ad_video_browse';
import fact from '@/lib/fact';
import { getPageVisibility } from "@/hooks/useVisibilitychange";
import {installDesktopWidget, whetherWidget, widget1, widget2} from '@/lib/utils/desktop';
import {getIncentiveAdSlotData, notifyAdAwardSuccess} from '@/lib/utils/incentive_ad_help';
import eventer, {EventMap} from '@/lib/utils/event';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category';
import {getParam} from '@/lib/qs';
import adPlayerCache from './adPlayerCache';
import config from '@/config';
import {addParams, parseQueryStr} from '@/utils/url';
import {PlayRes, TanxRewardType} from "@/pages/index/task/typings";

// 二方任务
function accomplishCustomerTask(task: TaskInfo) {
  const tasKEvent = task.event
  if (tasKEvent.startsWith('call_app') && tasKEvent !== TASK_EVENT_TYPE.CALL_APP_DOWNLOAD) {
    handleCallAppTask(task)
    return
  }
  if (!tasKEvent.includes('token')) {
    // link类型如果配置awardTime 延迟发奖
    let delayAwardFlag = false;
    if ([ TASK_EVENT_TYPE.LINK, TASK_EVENT_TYPE.APP_LINK ].includes(tasKEvent)) {
      delayAwardFlag = delayTaskCompleteByAwardTime(task);
    }
    if (!delayAwardFlag) {
      handleLinkTask(task)
      return
    }
  }
  openURL(task.url);
}

// 兼容主端android，17.4.6 及 17.4.8 子版本uctrial64，激励视频会下单任务bug，
export const skipCompleteOrderTask = (task: TaskInfo) => {
  const isLite = store.getState().app.clientType === 'UCLite';
  const sv = store.getState().user.sv;
  return task.event !== TASK_EVENT_TYPE.VIDEO_AD_BROWSE && isAndroid && !isLite && (!isLatestVersion(appVersion, '17.4.8') || (appVersion.startsWith('17.4.8') && sv === 'uctrial64'));
}

/** 完成激励视频任务 */
export const finishIncentiveAdTask = (task: TaskInfo) => {
  const adData = getIncentiveAdSlotData(task);
  const browseAdPlayer = browseAdPlayerInstance.getAdPlayer(task.id);
  const recordInvokeInfo = () => {
    adPlayerCache.addInvokeTaskIds(task.id);
  };
  const adTaskPreloadResult = browseAdPlayerInstance.getAdTaskPreloadResult(task.id);
  browseAdPlayerInstance.removeAdTaskPreloadResult(task.id);
  browseAdPlayer?.play(
    (playRes: PlayRes) => {
      // 广告播放回调
      let playResObj = formatStrToObj(playRes.msg);
      const rewardInfoList: IRewardInfoItem[] = playResObj?.reward_info_list || [];
      console.log('playRes========2', task?.name,  playResObj);
      // tanx下单不需要触发任务完成, 需要通过queryRewards主动去查询
      if (playRes.success && task?.event !== TASK_EVENT_TYPE.UCLITE_TAOBAO_SHOPPING) {
        const tanxRewardTypes = [TanxRewardType.BROWSE, TanxRewardType.ORDER, TanxRewardType.BROWSE_ORDER];
        // 收到发奖回调，移除调起广告的记录
        adPlayerCache.removeInvokeTaskIds(task.id);
        if (!skipCompleteOrderTask(task) && tanxRewardTypes.includes(playResObj.reward_type)) {
          dispatch.task.completeTanxProgressiveTask({
            task,
            rewardType: playResObj.reward_type
          })
        } else {
          dispatch.task.complete({
            id: task.id,
            useUtCompleteTask: task?.useUtCompleteTask,
            publishId: task?.publishId,
            type: 'complete',
            params: { task }
          });
        }
        if (playResObj.reward_success_id) {
          notifyAdAwardSuccess(task, {
            slotKey: rewardInfoList?.[0]?.slot_id || playResObj.aid,
            rewardId: playResObj.reward_success_id,
            requestId: `${Date.now()}`,
            appId: rewardInfoList?.[0]?.app_id || adData.appId,
          }, 'playRes')
        }
      }
      if (!playRes.success) {
        // const res = JSON.parse(playRes.msg)
        // if (!res.is_ended && store.getState().app?.skipVideoGetAward) {
        //   //【测试】skipVideoGetAward仅用作测试视频任务，不需要看完就触发领奖，生产勿开 ！！
        //   console.warn('[[biz.ad]]测试使用:', '视频未观看完成，触发领奖')
        //   // dispatch.task.complete({ id: task.id, type: 'complete', params: { task: task } });
        //   dispatch.task.complete({
        //     id: task.id,
        //     useUtCompleteTask: task?.useUtCompleteTask,
        //     publishId: task?.publishId,
        //     type: 'complete',
        //     params: { task }
        //   });
        // }
      }

      fact.event('incentive_ad_play', {
        playad_result: playRes.success ? 'finish' : 'unfinish',
        task_id: task?.id,
        task_name: task?.name,
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        slot_id: rewardInfoList?.[0]?.slot_id || playResObj?.aid || '',
        adapp_id: rewardInfoList?.[0]?.app_id || adData?.appId,
        adn_id: rewardInfoList?.[0]?.adn_id || '',
        sid: rewardInfoList?.[0]?.sid || '',
        price: rewardInfoList?.[0]?.price || '',
        pid: rewardInfoList?.[0]?.pid || '',
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        is_tanx_advanced: adTaskPreloadResult?.isTanxAdvanced ? '1' : '0',
      });
      eventer.off(EventMap.PageHidden, recordInvokeInfo);
    },
    invokeRes => {
      let invokeResObj = formatStrToObj(invokeRes?.msg);
      const rewardInfoList: IRewardInfoItem[] = invokeResObj?.reward_info_list || [];
      const fail_reason = JSON.stringify(invokeRes?.msg).replace(/\s|\+/g, '').toLowerCase().includes('nofill') ? 'empty' : 'error'
      const adParams = invokeRes?.params;
      fact.event('incentive_ad_turnup', {
        turnup_result: invokeRes.success ? 'success' : 'fail',
        task_id: task?.id,
        task_name: task?.name,
        taskclassify: task?.taskClassify || '',
        groupcode: task?.groupCode || '',
        slot_id: rewardInfoList?.[0]?.slot_id || adParams?.slot_key || '',
        adapp_id: rewardInfoList?.[0]?.app_id || adParams?.appId || '',
        adn_id: rewardInfoList?.[0]?.adn_id || '',
        sid: rewardInfoList?.[0]?.sid || '',
        price: rewardInfoList?.[0]?.price || '',
        pid: rewardInfoList?.[0]?.pid || '',
        turnup_fail_reason: invokeRes.success ? '' : fail_reason,
        award_amount: task?.rewardItems[0]?.amount || '',
        task_progress: task?.dayTimes?.progress || '',
        is_tanx_advanced: adTaskPreloadResult?.isTanxAdvanced ? '1' : '0',
      });
      // 广告调起成功在监听
      if (invokeRes.success) {
        // 看视频回来不自动更新数据
        dispatch.app.updateState({
          needPageVisibleUpdate: false
        });
        console.log('invokeRes:', invokeRes);
        adPlayerCache.setLastShowAdTask(task);
        eventer.on(EventMap.PageHidden, recordInvokeInfo);
      } else {
        const pageVisible = getPageVisibility();
        // 页面不可见时说明已经调起了视频，不显示无填充弹窗
        if (pageVisible) {
          modal.openTreasure(null, 'treasure', task, true, true, {
            pop_source: 'novideo',
          })
        }
      }
    },
  );
}

// 检查是否支持组件链路优化的客户端版本
export const checkUpdateToastVersion = async () => {
  // ios 不支持
  if (isIOS) {
    return false;
  }
  const sv = store.getState().user.sv;
  const clientType = store.getState()?.app?.clientType;
  const isLite = clientType === 'UCLite';
  if (!isLite) {
    // 主端安卓, 17.4.6.1377
    return isLatestVersion(appVersion, '17.4.6.1377')
  }
  if(appVersion.startsWith('17.2.9.1360')) {
    return sv === 'ucliteplusrelease5'
  }
  // 极速版 安卓：> 17.2.9.1360 支持，= 17.2.9.1360 +子版本号： ucliteplusrelease5
  return isLatestVersion(appVersion, '17.2.9.1360')
}

/** 桌面小组件 */
const accomplishDesktopTask = async (task: TaskInfo, params: {location: string}) => {
  const { location = ''} = params || {};
  // 复访任务 ios 无法判断用户是否安装桌面小组件
  if (isIOS && task?.event === TASK_EVENT_TYPE.FULI_DESKTOP_VISIT) {
    Toast.show('添加桌面组件并访问才可领取奖励哦~')
    return
  }
  const widgetName = task?.event === TASK_EVENT_TYPE.UC_FULI_DESKTOP ? widget1 : widget2
  const checkIsLastVersion = await checkUpdateToastVersion();
  // 安卓端
  if (isAndroid) {
    const desktopMonitor = tracker.Monitor(WPK_CATEGORY_MAP.DESKTOP_INSTALL_INFO, {sampleRate: 1})
    try {
      const res = await whetherWidget(widgetName, {taskInfo: task, resource_location: location});
      desktopMonitor.success({
        msg: '获取组件是否安装-成功',
        c1: `${res?.isInstalled}`,
        bl1: JSON.stringify(res),
        bl2: JSON.stringify(widgetName)
      })
      if (res?.isInstalled) {
        if (checkIsLastVersion) return
        return Toast.show('从桌面组件访问才可领取奖励哦~');
      }
    } catch (error) {
      desktopMonitor.fail({
        msg: '获取组件是否安装-失败',
        bl1: JSON.stringify(error),
        bl2: JSON.stringify(widgetName)
      })
      console.log('whetherWidget===error', error);
    }
  }

  // 安装组件
  try {
    const timer = setTimeout(() => {
      !checkIsLastVersion && Toast.show('可从桌面添加组件，安装完成后点击组件进入，即可完成任务');
      clearTimeout(timer);
    }, 2000);
    fact.event('task_install_desktop', {
      event_id: '19999',
      task_id: task.id
    });
    const installRes = await installDesktopWidget(widgetName, { taskInfo: task, resource_location: location });
    if (checkIsLastVersion && installRes.fail) {
      Toast.show('可从桌面添加组件，安装完成后点击组件进入，即可完成任务');
    }
  } catch (error) {
    Toast.show('当前客户端版本不支持，请升级客户端版本 ~');
  }
}

const accomplishAdInsideGroupTask = async (task: TaskInfo) => {
  const canOpen = task.token && task.slotId && task.accountId;
  if(!canOpen){
    tracker.log({
      category: WPK_CATEGORY_MAP.TO_FINISH_TASK,
      sampleRate: 1,
      msg: `${task.id}_${task.event}_${task.name}_任务参数不对`,
      w_succ: 1,
      c1: task.event,
      c2: '' + task.id,
      c4: getParam('entry'),
      bl1: task.url || '',
      bl2: JSON.stringify(task)
    })
    dispatch.app.updateAll();
    return;
  }

  const extraObj  = getExtraInfo(task) || {};
  /**
   * 通过扩展字段判断是否为下载类任务
   * 1、taskType === "download" 为下载类
   * 2、taskType === "url" 为纯跳转
   */
  const isAppDownloadTask = extraObj?.taskType === 'download';

  // 非下载类的任务
  if (!isAppDownloadTask) {
    await dispatch.ad.corpTaskClick({
      taskToken: task.token!,
      slotId: task.slotId!,
      accountId: task.accountId!
    });
    // 延时发奖
    if (extraObj?.awardTime && extraObj?.isHttpFinish === true) {
      delayTaskCompleteByAwardTime(task)
    }
    return;
  }

   // 下载任务领取
   const checkResult = clickCheckAppInstall(task);
   if(isAppDownloadTask && !checkResult){
    await dispatch.ad.corpTaskClick({
      taskToken: task.token!,
      slotId: task.slotId!,
      accountId: task.accountId!
    });
     dispatch.task.receive({
       id: task.id,
       publishId: task.publishId,
       useUtCompleteTask: task.useUtCompleteTask,
       params: { task: task  },
     });
    saveAdInsideGroupTaskInfoToStore(task);
   }else {
      Toast.show('该任务已过期，快去做其它任务吧');
      tracker.log({
        category: WPK_CATEGORY_MAP.TO_FINISH_TASK,
        sampleRate: 1,
        msg: `${task.id}_${task.event}_${task.name}_已安装`,
        w_succ: 1,
        c1: task.event,
        c2: '' + task.id,
        c4: getParam('entry'),
        bl1: task.url || '',
        bl2: JSON.stringify(task)
      });
      dispatch.app.updateAll();
   }
}

// 新搜索from获取
export const getTaskFromNewSearch = (task: TaskInfo) => {
  let from = getParam('from', task?.url);
  if (!from) {
    let extObj = task?.ext?.words?.[0]
    if (extObj?.from) {
      from = extObj?.from
    } else {
      const cmsChannels = store.getState().cms.channelEventArray;
      if (Array.isArray(cmsChannels)) {
        const cmsChannelEvent = cmsChannels.find(channel => channel.eventName === task.event)
        if (cmsChannelEvent?.channelId) {
          from = cmsChannelEvent.channelId
        }
      }
    }
  }
  return from
}
// 福利任务
async function accomplishWelfareTask(task: TaskInfo, params: any) {
  if (isTimeLinkTask(task.event)) {
    openURL(task.url);
    dispatch.task.complete({
      id: task.id,
      useUtCompleteTask: task?.useUtCompleteTask,
      publishId: task?.publishId,
      type: 'complete',
      params: { task }
    });
    return
  }

  const tasKEvent = task.event;
  const openUrlEvent = [
    TASK_EVENT_TYPE.LINK,
    TASK_EVENT_TYPE.IFLOW_LINK,
    TASK_EVENT_TYPE.LINK_PRT,
    TASK_EVENT_TYPE.APP_LINK,
    TASK_EVENT_TYPE.UCLITE_SEARCH_WORD,
    TASK_EVENT_TYPE.BAIDU_READ_ONCE,
    TASK_EVENT_TYPE.BAIDU_READ_CLICK,
    TASK_EVENT_TYPE.UCLITE_TAOBAO_LIANMENG,
  ]

  const handleMap = {
    [TASK_EVENT_TYPE.BRAND_TASK]: () => {
      dispatch.ad.click('taskBrandAd');
      // dispatch.task.complete({ id: task.id, type: 'complete', params: { task } });
      dispatch.task.complete({
        id: task.id,
        useUtCompleteTask: task?.useUtCompleteTask,
        publishId: task?.publishId,
        type: 'complete',
        params: { task }
      });
    },
    [TASK_EVENT_TYPE.UCLITE_WITHDRAWAL]: () => {
      // dispatch.task.complete({ id: task.id, type: 'complete', params: { task } });
      dispatch.task.complete({
        id: task.id,
        useUtCompleteTask: task?.useUtCompleteTask,
        publishId: task?.publishId,
        type: 'complete',
        params: { task }
      });
    },
    [TASK_EVENT_TYPE.UCLITE_READ_DOUBLE]: () => {
      // dispatch.task.complete({ id: task.id, type: 'complete', params: { task } });
      dispatch.task.complete({
        id: task.id,
        useUtCompleteTask: task?.useUtCompleteTask,
        publishId: task?.publishId,
        type: 'complete',
        params: { task }
      });
    },
    [TASK_EVENT_TYPE.BIND_ALIPAY_ACCOUNT]: async () => {
      localStorage.setItem('bindAlipayFromTask', '1')
      const bindSuccess = await dispatch.user.bindAlipay();
      if (bindSuccess) {
        // dispatch.task.complete({ id: task.id, type: 'complete', params: { task } });
        dispatch.task.complete({
          id: task.id,
          useUtCompleteTask: task?.useUtCompleteTask,
          publishId: task?.publishId,
          type: 'complete',
          params: { task }
        });
      }
    },
    [TASK_EVENT_TYPE.WUFU_BENEFITS_LINK]: async () => {
      const hasBindedAlipay = store.getState().user?.hasBindedAlipay
      if (hasBindedAlipay) {
        jumpTaskHander(task)
      } else {
        const bindSuccess = await dispatch.user.bindAlipay();
        if (bindSuccess) {
          jumpTaskHander(task)
        }
      }
    },
    [TASK_EVENT_TYPE.ALIMAMA_SHOP]: async () => {
      const goodsList = store.getState().shop?.shoppingList || [];
      if (!goodsList.length) {
        await dispatch.shop.queryShoppingList({pageNo: 1})
      }
      const scrollViewDom = document.querySelector('#pageScrollView');
      const shoppingListHeaderDom = document.querySelector('#shoppingListHeader');
      if (shoppingListHeaderDom) {
        const scrollView = global.get(EGolbalInstance.SCROLL_VIEW);
        const scrollTop = scrollViewDom?.scrollTop || 0;
        const elementPosition = shoppingListHeaderDom.getBoundingClientRect().top
        const y = Math.floor(scrollTop + elementPosition) - 100
        scrollView.scrollTo({
          x: 0,
          y: `${y}px`,
          animated: true
        })
      }
    },
  }
  if (typeof handleMap[tasKEvent] === 'function') {
    handleMap[tasKEvent]()
  } else if ([TASK_EVENT_TYPE.UCLITE_SEARCH_WORD, TASK_EVENT_TYPE.BAIDU_READ_ONCE, TASK_EVENT_TYPE.BAIDU_READ_CLICK].includes(tasKEvent)) {
    dispatch.task.deleteSearchWordsCacheOne({
      event: tasKEvent,
      name: params?.word?.name
    });
    const extParams = { ext: `word:${params?.word?.name || ''}` };
    // dispatch.task.complete({ id: task.id, type: 'complete', params: { task: task, ...extParams } });
    dispatch.task.complete({
      id: task.id,
      useUtCompleteTask: task?.useUtCompleteTask,
      publishId: task?.publishId,
      type: 'complete',
      params: { task, ...extParams }
    });
  }

  if (openUrlEvent.includes(tasKEvent)) {
    openURL(task.url);
  } else if (tasKEvent === TASK_EVENT_TYPE.UCLITE_WITHDRAWAL) {
    openURL(store.getState().app.withdrawLink);
  }
}

export const handleLinkTask = (task: TaskInfo) => {
  if (!task?.url) {
    return;
  }
  // 监听页面隐藏后在触发任务完成
  const tofinishTaskfn = () => {
    dispatch.task.complete({
      id: task.id,
      useUtCompleteTask: task?.useUtCompleteTask,
      publishId: task?.publishId,
      type: 'complete',
      params: { task }
    });
    eventer.off(EventMap.PageHidden, tofinishTaskfn);
  };
  eventer.on(EventMap.PageHidden, tofinishTaskfn);
  const timer = setTimeout(() => {
    eventer.off(EventMap.PageHidden, tofinishTaskfn);
    clearTimeout(timer);
  }, 2000);
  openURL(task?.url);
};

// 端内任务
async function accomplishAppTask(task: TaskInfo, params: any) {

  const finishReadTask = () => {
    openURL(task.url);
    const searchWordClicked = {
      taskId: task.id,
      event: tasKEvent,
      name: '', // 兼容旧版本通知jsapi没有奖励字段问题，主要记录点击时的奖励作为此次搜索奖励兜底，name为空不会弹出搜索奖励弹窗
      prizes: [
        {
          rewardItem: task?.rewardItems?.[0],
        }
      ]
    }
    console.log('searchWordClicked', searchWordClicked);
    dispatch.task.updateState({
      searchWordCompleted: searchWordClicked,
    });
  }

  // 公告任务
  const finishAnnounceTask = () => {
    // 有配置跳转链接,跳转后完成,
    if (task?.url) {
      handleLinkTask(task);
      return
    }
    // 没有配置链接,点击即完成,
    dispatch.task.complete({
      id: task.id,
      useUtCompleteTask: task?.useUtCompleteTask,
      publishId: task?.publishId,
      type: 'complete',
      params: { task }
    });
  }

  const tasKEvent = task.event;
  const hanldeMap = {
    [TASK_EVENT_TYPE.UCLITE_READ_ONCE]: finishReadTask,
    [TASK_EVENT_TYPE.UCLITE_READ_CLICK]: finishReadTask,
    [TASK_EVENT_TYPE.BAIDU_READ_ONCE]: finishReadTask,
    [TASK_EVENT_TYPE.BAIDU_READ_CLICK]: finishReadTask,
    [TASK_EVENT_TYPE.UCLITE_SIGN]: finishSignTaskAndOpenModal,
    [TASK_EVENT_TYPE.UCLITE_SIGN_NM]: finishSignTaskAndOpenModal,
    [TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER]: () => {
      console.log('点击设置默认浏览器')
      storage.set(STORAGE_DEFAULT_BROWSER_CLICKED_KEY, true);
      ucapi.biz.gotoDefaultBrowserSetting();
    },
    [TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH]: () => {
      storage.set(STORAGE_PUSH_SWITCH_CLICKED_KEY, true);
      if (isIOS) {
        ucapi.biz.gotoPushSetting({
          AuthBoxCheckPointScene: 7,  // 用于IOS端打点
        });
      } else {
        ucapi.biz.gotoPushSetting();
      }
    },
    [TASK_EVENT_TYPE.UCLITE_SIGN_MINDER]: async () => {
      try {
        const data = await setCalendarReminders();
        console.log('设置日历', data);
        if (data.errCode) {
          if (isIOS) {
            Toast.show('开启失败，请前往设置-UC极速版-日历开启读写权限');
          } else {
            Toast.show('开启失败，请前往系统设置-权限管理-UC极速版开启日历读写权限');
          }
        } else {
          // 设置成功
          dispatch.app.updateState({
            hadSetSignRemind: true,
          });
          dispatch.app.updateAll();
          storage.set(STORAGE_SIGN_MINDER_TASK_SET_CALENDAR_KEY, true);
          setCalendarReminderStoreData();
        }
      } catch (e) {
        // 不支持 api
      };
    },
    [TASK_EVENT_TYPE.FULI_ANNOUNCE]: finishAnnounceTask
  };

  if (typeof hanldeMap[tasKEvent] === 'function') {
    hanldeMap[tasKEvent]()
  }

}

function changeTask(task: TaskInfo, params: any): TaskInfo {
  const app = store?.getState()?.app
  if ([TASK_EVENT_TYPE.SEARCH_READ_CLICK,TASK_EVENT_TYPE.SEARCH_READ_ONCE].includes(task.event)) {
    const [url, query] = task.url.split('?')
    const from = getTaskFromNewSearch(task);
    task.url = url + '?' + query.replace(/(from)=([^&#]*)/, `$1=${from}`)
  } else {
    // 旧逻辑
    if (task.url && task.url.includes('from')) {
      const cmsChannels = store.getState().cms.channelEventArray;
      if (Array.isArray(cmsChannels)) {
        const cmsChannelEvent = cmsChannels.find(channel => channel.eventName === task.event)
        if (cmsChannelEvent?.channelId) {
          const [url, query] = task.url.split('?')
          task.url = url + '?' + query.replace(/(from)=([^&#]*)/, `$1=${cmsChannelEvent.channelId}`)
        }
      }
    }
    // 使用搜索词的渠道替换任务链接上的渠道
    if (isSearchWordsTask(task.event) && params?.word?.from) {
      const [url, query] = task.url.split('?')
      const from = params?.word?.from
      // 如果链接上已经存在from参数，则不替换，已搜索上的from为第一优先级
      if (/(from)=([^&#]*)/.test(query)) {
        task.url = url + '?' + query.replace(/(from)=([^&#]*)/, `$1=${from}`)
      }
    }
  }
  // 新增的搜索event替换
  if ([TASK_EVENT_TYPE.SEARCH_READ_ONCE, TASK_EVENT_TYPE.SEARCH_READ_CLICK]?.includes(task?.event)) {
    const [url] = task.url.split('?');
    const clientType = app?.clientType === 'UCLite' ? 'UCLite' : 'UC';
    task.event = SEARCH_EVENT_MATCH?.[task?.event]?.[clientType]
    const urlQueryObj = parseQueryStr(task.url);
    let data = {
      ...urlQueryObj,
      event: task.event,
      tid: task?.id
    }
    task.url = addParams(url, data);
    console.log('1task url:', task.url)
  }

  if (TASK_EVENT_TYPE.UCLITE_SEARCH_WORD === task.event) {
    const word = encodeURIComponent(params?.word?.name || '');
    task.url = task.url + `&app_id=${config.appId}&word=${word}&q=${word}`
  }
  if ([TASK_EVENT_TYPE.UCLITE_READ_CLICK, TASK_EVENT_TYPE.UCLITE_READ_ONCE, TASK_EVENT_TYPE.BAIDU_READ_ONCE, TASK_EVENT_TYPE.BAIDU_READ_CLICK, TASK_EVENT_TYPE.UC_READ_CLICK, TASK_EVENT_TYPE.UC_READ_ONCE].includes(task.event)) {
    const word = encodeURIComponent(params?.word?.name || '');
    const ext_params = encodeURIComponent(JSON.stringify({ q: params?.word?.name || '' }))
    task.url = task.url + `&app_id=${config.appId}&word=${word}&q=${word}&ext_params=${ext_params}`
    console.log('task url:', task.url)
  }
  return task
}

function getChannelId(task: TaskInfo) {
  const reg = /(from)=([^&#]*)/;
  const channelId = task?.url.match(reg)
  return channelId && channelId[2]
}

const SEARCH_EVENT_MATCH = {
  [TASK_EVENT_TYPE.SEARCH_READ_ONCE] : {
    'UC': TASK_EVENT_TYPE.UC_READ_ONCE,
    'UCLite': TASK_EVENT_TYPE.UCLITE_READ_ONCE,
  },
  [TASK_EVENT_TYPE.SEARCH_READ_CLICK]: {
    'UC': TASK_EVENT_TYPE.UC_READ_CLICK,
    'UCLite': TASK_EVENT_TYPE.UCLITE_READ_CLICK,
  }
}

const adInsideGroupTaskList = [
  TASK_EVENT_TYPE.CORP_APP_TASK,
  TASK_EVENT_TYPE.CORP_APP_TASK_NEW,
  TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND
];

const welfareTaskList = [
  TASK_EVENT_TYPE.ALIMAMA_SHOP,
  TASK_EVENT_TYPE.IFLOW_LINK,
  TASK_EVENT_TYPE.LINK_PRT,
  TASK_EVENT_TYPE.UCLITE_SEARCH_WORD,
  TASK_EVENT_TYPE.BAIDU_READ_ONCE,
  TASK_EVENT_TYPE.BAIDU_READ_CLICK,
  TASK_EVENT_TYPE.UCLITE_READ_DOUBLE,
  TASK_EVENT_TYPE.BRAND_TASK,
  TASK_EVENT_TYPE.UCLITE_WITHDRAWAL,
  TASK_EVENT_TYPE.BIND_ALIPAY_ACCOUNT,
  TASK_EVENT_TYPE.WUFU_BENEFITS_LINK,
  TASK_EVENT_TYPE.UCLITE_TAOBAO_LIANMENG,
  TASK_EVENT_TYPE.UCLITE_TAOBAO_SHOPPING
]
const customerTaskList = [
  TASK_EVENT_TYPE.LINK,
  TASK_EVENT_TYPE.APP_LINK,
  TASK_EVENT_TYPE.CALL_APP_LINK,
  TASK_EVENT_TYPE.APP_TOKEN,
  TASK_EVENT_TYPE.CALL_APP_TOKEN,
  TASK_EVENT_TYPE.UCLITE_TMALL_LINK_TOKEN,
  TASK_EVENT_TYPE.UCLITE_DT_LINK_TOKEN,
  TASK_EVENT_TYPE.LINK_TOKEN
]
const appTaskList = [
  TASK_EVENT_TYPE.UCLITE_READ_ONCE,
  TASK_EVENT_TYPE.UCLITE_READ_CLICK,
  TASK_EVENT_TYPE.UCLITE_SIGN,
  TASK_EVENT_TYPE.UCLITE_SIGN_NM,
  TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER,
  TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH,
  TASK_EVENT_TYPE.UCLITE_SIGN_MINDER,
  TASK_EVENT_TYPE.FULI_ANNOUNCE,
]

const rtaActiveCallList = [
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DAILY,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK
];
const rtaNuCallList = [
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD
];

const appDownLoadList = [
  TASK_EVENT_TYPE.CALL_APP_DOWNLOAD
]

const ucLoginList = [
  TASK_EVENT_TYPE.UC_LOGIN
];

const highValueList = [
  TASK_EVENT_TYPE.HIGH_VALUE_TASK
];

const firstVisitList = [
  TASK_EVENT_TYPE.FULI_MEET_GIFT
];

// 处理固定入口到访任务
async function handleFirstVisitTask(task: TaskInfo) {
  const extraObj = getExtraInfo(task)
  const modalConfig = extraObj?.entryGuideConfig || {}
  console.log(extraObj,'extraObj')
  if (extraObj?.envSub === 'entry') {
    // 入口引导弹窗
    baseModal.open(MODAL_ID.ENTRY_GUIDE,{
      title: modalConfig?.title,
      desc: modalConfig?.desc,
      btnText: modalConfig.btnText,
      image: modalConfig.image,
    });
  }
  // 完成任务
  // dispatch.task.complete({
  //   id: task.id,
  //   useUtCompleteTask: task?.useUtCompleteTask,
  //   publishId: task?.publishId,
  //   type: 'complete',
  //   params: { task }
  // });
}

function checkTaskAction(event: TASK_EVENT_TYPE) {
  const taskActionHandlerList = [
    {
      eventList: rtaActiveCallList,
      action: handleRTACallTask,
    },
    {
      eventList: rtaNuCallList,
      action: handleRTANuCallTask,
    },
    {
      eventList: customerTaskList,
      action: accomplishCustomerTask,
    },
    {
      eventList: welfareTaskList,
      action: accomplishWelfareTask,
    },
    {
      eventList: appTaskList,
      action: accomplishAppTask,
    },
    {
      eventList: adInsideGroupTaskList,
      action: accomplishAdInsideGroupTask,
    },
    {
      eventList: appDownLoadList, // 下载类任务
      action: handleCallAppDownload,
    },
    {
      eventList: ucLoginList, // 登录任务
      action: handleLogin
    },
    {
      eventList: highValueList, // 多步骤任务
      action: handleHighValueDialog
    },
    {
      eventList: DESKTOP_TASK_EVENT, // 桌面组件复访
      action: accomplishDesktopTask
    },
    {
      eventList: firstVisitList, // 固定入口到访任务
      action: handleFirstVisitTask
    }
  ]
  const targetTaskAction  = taskActionHandlerList.find((taskHandler) => {
    return taskHandler.eventList.includes(event)
  })

  if (targetTaskAction) {
    return targetTaskAction.action
  }

  return function defaultAction (task: TaskInfo) {
    if (task?.event === TASK_EVENT_TYPE.FULI_LIMIT_SIGNIN) {
      const openUrl = isIOS ? getExtraInfo(task)?.iosUrl : getExtraInfo(task)?.androidUrl;
      return openURL(openUrl || '');
    }
    openURL(task.url);
  }
}

function AccomplishTask(task: TaskInfo, params: any) {
  const action = checkTaskAction(task.event);
  action(task, params)
}

export {
  AccomplishTask,
  changeTask,
  getChannelId,
}
