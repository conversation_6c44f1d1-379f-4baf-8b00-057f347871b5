import {TaskInfo} from "@/store/models/task/types";
export class AdPlayerCache {
  readonly invokeTaskIds: number[]; // 已调起但未领奖的任务id,
  private lastShowAdTask: TaskInfo | null; // 上次看的视频任务
  constructor() {
    this.invokeTaskIds = [];
  }
  
  setLastShowAdTask(task: TaskInfo) {
    this.lastShowAdTask = task;
  }
  
  getLastShowAdTask() {
    return this.lastShowAdTask;
  }

  getInvokeTaskIds() {
    console.log('invokeTaskIds:', this.invokeTaskIds)
    return this.invokeTaskIds;
  }

  addInvokeTaskIds(taskId: number) {
    if (this.invokeTaskIds.includes(taskId)) {
      return
    }
    this.invokeTaskIds.push(taskId);
  }

  removeInvokeTaskIds(taskId: number) {
    const index = this.invokeTaskIds.findIndex(item => item === taskId);
    if (index > -1) {
      this.invokeTaskIds.splice(index, 1);
    }
  }
}

const adPlayerCache = new AdPlayerCache()
export default adPlayerCache

