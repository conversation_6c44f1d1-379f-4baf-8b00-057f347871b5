import { createElement, useEffect } from 'rax';
import Image from "@/components/image";
import { useSelector } from 'rax-redux';
import useMounted from 'rax-use-mounted';
import View from 'rax-view';

import './index.scss';
import { StoreState, store } from '@/store';
import { FILTER_TASK_TYPE, TASK_EVENT_TYPE, TaskInfo } from '@/store/models/task/types';
import Item from './item';
import fact from '@/lib/fact';
import { doOnAttach } from "@/lib/prerender";
import { filterAlipayTask, hideShoppingTask, isIncentiveAdTask, ifShowAdTask, dealWithPreloadSuccessAdTask, hideUninstallTask } from "@/pages/index/task/help";
import Fact from '@/components/Fact';
import { rectify } from '@/lib/render-utils/perf';
import { browseAdPlayerInstance } from './ad_video_browse';
import config from '@/config';
import eventer, {EventMap} from '@/lib/utils/event';

import { usePrevious } from '@/lib/utils/hooks';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { getParam } from '@ali/uc-toolkit/lib/network/plugin/qs';
import { getRenderType } from '@/lib/render-utils/csr';
import { getIncentiveAdSlotData, isOpenQueryAward } from '@/lib/utils/incentive_ad_help';
import { usePageVisibilityListener } from '@/hooks/useVisibilitychange';
import { getEvSub } from '@/lib/qs';
import { TaskTitleIcon, TaskParvialTitle } from '@/constants/static_img';
import { isAndroid, appVersion } from '@/lib/universal-ua';
import adPlayerCache from "@/pages/index/task/adPlayerCache";

// 今日任务模块-任务列表

const Task: Rax.FC = () => {
  const taskList: TaskInfo[] = useSelector((state: StoreState) => {
    let { taskList: taskList } = state.task;
    let renderTask = [...taskList];
    const { bigPrizeEvents } = state.app;
    // 过滤不显示的任务
    renderTask = renderTask.filter(task => FILTER_TASK_TYPE.indexOf(task.event) < 0 && !bigPrizeEvents.includes(task.event) && filterAlipayTask(task) && !hideShoppingTask(task) && task.event !== TASK_EVENT_TYPE.USER_GROUP_LINK &&  ifShowAdTask(task) && !hideUninstallTask(task, false));
    return renderTask;
  });

  const newTaskList: TaskInfo[] = useSelector((state: StoreState) => state.task.taskList)
  const prevTaskListLength = usePrevious(taskList.length);
  const adTaskList: TaskInfo[] = newTaskList?.filter(task => isIncentiveAdTask(task));
  const resourceAdTaskList: TaskInfo[] = useSelector((state: StoreState) => state.resource.resourceAdTaskList)
  
  // 所有的激励广告任务列表
  const allAdTaskList = adTaskList.concat(resourceAdTaskList);
  
  useMounted(() => {
    doOnAttach(() => {
      fact.exposure('task_show', { c: 'task', d: 'show' });
    })
  });

  usePageVisibilityListener((visible: boolean) => {
    const appInit = store.getState().app.animationReady;
    // 页面未初始化不触发
    if (!appInit) {
      return;
    }
    if (visible) {
      const lastShowAdTask = adPlayerCache.getLastShowAdTask();
      console.log('lastShowAdTask:', lastShowAdTask);
      if (lastShowAdTask) {
        browseAdPlayerInstance.adTaskPreload(lastShowAdTask);
      };
      eventer.emit(EventMap.PageVisible)
      return;
    }
    eventer.emit(EventMap.PageHidden);
  });

  useEffect(() => {
    // console.log('[Task] taskList change | prevTaskListLength ', prevTaskListLength, ' taskList.length ', taskList.length);
    if (!prevTaskListLength && taskList.length > 0) {
      console.log('[Task] task really show ');
      try {
        tracker.log({
          category: WPK_CATEGORY_MAP.TASK_COMP_SHOW,
          msg: 'task_really_show',
          wl_avgv1: rectify(window?.performance?.now()),
          c1: getParam('entry'),
          c2: getRenderType(),
        })
      } catch (err) {
        console.error('WPK_CATEGORY_MAP.TASK_COMP_SHOW err -> ', err)
      }
      fact.exposure('task_list_show', { c: 'task', d: 'list' });
    }
  }, [prevTaskListLength, taskList]);

  useEffect(() => {
    if (allAdTaskList?.length) {
      const preloadAdTaskMap = store.getState().task.preloadAdTaskMap;
      const isLite = store.getState().app.clientType === 'UCLite';
      const isOpenQueryReward = isOpenQueryAward(appVersion, isLite);
      // eslint-disable-next-line array-callback-return
      allAdTaskList?.map((adTask) => {
        if (preloadAdTaskMap.has(`${adTask?.id}`)) {
          return
        }
        const adData = getIncentiveAdSlotData(adTask)
        if (adData?.slotKey) {
          preloadAdTaskMap.set(`${adTask?.id}`, adTask?.id)
          browseAdPlayerInstance.init({
            task: adTask,
            slotKey: adData.slotKey,
            appId: adData.appId,
            coralAppId: config.appId,
            enableAsyncQueryReward: isOpenQueryReward,
            finishPreload: (res)=>{
              // 加载成功
              if (res) {
                dealWithPreloadSuccessAdTask(adTask)
              }
            }
          })
        }
      })
    }
  }, [allAdTaskList])

  useEffect(()=>{
    const monitorWidgetInstall = (state) => {      
      if (state && state?.detail) {          
        const desktopMonitor = tracker.Monitor(WPK_CATEGORY_MAP.DESKTOP_INSTALL_INFO, {sampleRate: 1})
        desktopMonitor.success({
          msg: '组件安装成功回调',
          bl1: JSON.stringify(state),
          bl2: JSON.stringify(state.detail)
        })
      }
    }
   if (isAndroid) {
    document.addEventListener('UCEVT_Widget_Installed',monitorWidgetInstall);
   }

   return ()=> {
    if (isAndroid) {
      document.removeEventListener('UCEVT_Widget_Installed',monitorWidgetInstall);
    }
   }
  }, [])

  // 只有小说分场，才需要切换副标题
  const taskTitleImg = getEvSub() !== 'novel_fuli' ? TaskTitleIcon : TaskParvialTitle;
  
  return <Fact
    c="mokuai"
    d="jinritask"
    expoLogkey="jinritask_expo"
    noUseClick
    x-if={taskList.length} className={'container-task'}>
    <View className="task-title-img">
      <Image source={taskTitleImg} />
    </View>
    {/*<StoreTask />*/}
    {taskList.map((taskInfo: TaskInfo, index) => <Item key={taskInfo.id} taskInfo={taskInfo} index={index} taskModule={'每日任务'} moduleType="DAILY" resource_location={'list'} />)}
  </Fact>;
};

export default Task;
