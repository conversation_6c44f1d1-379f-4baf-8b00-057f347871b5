import { getFirstData, prefetchClientData } from './first-data-api';
import net from '@/lib/network';
import config from '@/config';
import {
  setGlobalFirstData,
  isGlobalFirstDataEmpty,
  isAsyncSsrDocument,
  isWithSkeleton,
  getRenderType,
  waitAndFetchInitialData,
  detectStreamFirstData,
  getInitialDataTimeoutFromDocument,
  preFetchInitialData,
} from '@/lib/render-utils/csr';
import { getPkgIdfromWormholeData } from '@/lib/render-utils/normal';
import wormholeData from '@/lib/wormhole-data';
import { renderAction, perfMark, perfMarkNodeFirstDataTime, perfMarkAsyncSsrDemote } from '@/lib/render-utils/perf';
import './index.scss';

perfMark(renderAction.miniJsExecute);

// GUIDANCE：是否使用并行SSR的缓存。根据场景按需使用。packId维度缓存
const USE_ASYNCSSR_CACHE = false;

(async function () {

  // 标记是否需要延迟加载主bundle，在SSR或者并行SSR有HTML片段返回的时候，需要预留时间上屏，主bundle加载会阻塞上屏。
  let bundleShouldDelay = false;

  const doDefaultLogic = async () => {
    // 并行SSR请求的Promise，与SSR接口首屏接口竞争关系。
    let asyncRenderPromise = new Promise(() => { });

    /**
     * 预加载客户端数据(APP是否安装等)
     */
    prefetchClientData();

    // 【并行SSR场景】
    if (isAsyncSsrDocument()) {
      let pathname = '';
      // 从主文档获取 broPackId，用于获取对应 broPackId 的并行 SSR 渲染片段
      const broPackId = window?.__INITIAL_DATA__?.pageInitialProps?.__broPackId;
      if (!DEV) {
        const broccoliPathRegExp = /\/apps\/(\S*)\/routes\/(\S*)/;
        const [_, appCode, routeCode] = window.location.pathname.match(broccoliPathRegExp) || [];
        if (appCode && routeCode) {
          pathname = `/api/v1/ssr/async-fetch/${appCode}/${routeCode}?compass_params=name%3Auclitewelfare$kps_info&uc_param_str=dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf`;
        if (broPackId) pathname += `&broPackId=${broPackId}`;
        } else {
          // 对于没有匹配到appCode和routeCode的情况。pathname置空。将不进行并行SSR渲染。
          pathname = ''
        }
      } else {
        pathname = `/api/v1/ssr/async-fetch/${window.location.pathname}?wh_page_only=true&broPackId=${broPackId}&compass_params=name%3Auclitewelfare&$kps_info&uc_param_str=dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf`;
      }

      // 使用并行SSR缓存。
      // const asyncSsrStoKey = `asyncSsr_${window?.__INITIAL_DATA__?.pageInitialProps?.__broPackId}`;
      // if (USE_ASYNCSSR_CACHE) {
      //   const pageHTMLCache = localStorage.getItem(asyncSsrStoKey);
      //   const container = document.getElementById('root');
      //   if (pageHTMLCache && container) {
      //     container.innerHTML = pageHTMLCache;
      //     perfMark(renderAction.asyncSsrCacheOnScreen);
      //     console.log('并行SSR缓存上屏');
      //   }
      // }

      // const version = MAIN_VERSION;

      asyncRenderPromise = new Promise((resolve, reject) => {
        const customDocUrl = window.location.href.replace(/skeletonMode/g, 'skeletonBak');
        perfMark(renderAction.asyncSsrApiStart);
        net.get(pathname, {}, {
          headers: {
            'custom-doc-url': customDocUrl,
          }
        })
          .then(res => {
            if (!isGlobalFirstDataEmpty()) return;
            let data = res?.data || res;
            const { initialProps, html: pageHTML } = data;

            try {
              perfMarkNodeFirstDataTime(initialProps?.__duration);
              perfMark(renderAction.asyncSsrApiEnd);
              perfMark(renderAction.asyncSsrApiCost);
            } catch(err) {
              console.error('[mini-js] 设置打点异常', err);
            }

            if (!DEV
              && (
                initialProps?.__broPackId !== getPkgIdfromWormholeData(wormholeData) // broccoli的packId匹配
                // || initialProps?.__ver !== version  // 代码版本号匹配（先不匹配版本号，beta测试时版本号带时间戳，对不上）
              )
            ) {
              console.error(`[mini-js]__broPackId不一致', ${initialProps?.__broPackId}, ${getPkgIdfromWormholeData(wormholeData)} 忽略校验：${config.ignoreBroPackIdCheck}`);
              // 重要！客户端Bundle版本和Manifest版本会出现不一致问题。比如Manifest版本v1，Bundle版本v2，预取请求packId=v1，主文档packId为v2，出现版本不一致,校验不通过。如果前后版本接口数据不兼容，则一定要进行校验
              if (!config.ignoreBroPackIdCheck) {
                // reject(new Error(msg));
                // 由前端发起请求重试
                resolve(null);
                return;
              }
            };
            if (pageHTML) {
              const container = document.getElementById('root');
              if (container) {
                container.innerHTML = pageHTML;
                // 并行SSR有HTML片段，需预留上屏时间
                bundleShouldDelay = true;
                perfMark(renderAction.asyncSsrOnScreen);
                console.log('并行SSR上屏');
              }
            }
            resolve(initialProps.firstData);
          })
          .catch(reject);
      })
    }

    try {
      // 判断全局是否有firstData，没有的情况下需要调用首屏数据接口。
      // 有数据的时候，为串行SSR成功。不需要前端获取数据。
      if (isGlobalFirstDataEmpty()) {
        if (isAsyncSsrDocument()) {
          // 并行SSR：需要await，因为bootstrap的bundle加载解析可能会影响HTML片段上屏
          // 并行SSR及数据预取能力较为稳定时，C端可以不用另外获取数据进行竞争关系
          // const data = await Promise.race([asyncRenderPromise, getFirstData()]);
          let data;
          try {
            // 并行SSR请求
            console.log('[mini-js] await asyncRenderPromise for async-ssr');
            // data = await asyncRenderPromise;
            // 并行CSR，不需要await
            console.log('[mini js] asyncRenderPromise data', data);
               // 并行SSR请求
            data = await asyncRenderPromise;
          } catch(err) {
            console.warn('[mini-js] 并行SSR请求失败, 自动降级成CSR', err);
            perfMarkAsyncSsrDemote(true);
            // 并行CSR出错，由前端重新发起接口请求
            console.log('[mini js] asyncRenderPromise fallback data', data);
            // TODO: 降级成CSR 不需要await 阻塞bootstrap执行
            // 并行SSR出错，前端请求
            data = await getFirstData();
          }
          setGlobalFirstData(data);
        } else {
          // CSR：发起请求后，立刻加载bootstrap的bundle，在hydrate前，再拿数据
          window.__CSRFirstDataPms__ = getFirstData();
        }
      } else {
        // 串行SSR：bundle需要delay，预留上屏时间
        bundleShouldDelay = true;

        perfMarkNodeFirstDataTime();
      }
    } catch (err) {
      console.error(err);
    }
  };

  console.log('[mini-js] execute ...');

  const renderType = getRenderType();
  console.log('[mini-js] renderType: ', renderType);

  switch (renderType) {
    case 'STREAM_CSR':
      // 流式CSR
      waitAndFetchInitialData(getFirstData);
      break;
    case 'STREAM_SSR':
      // 流式SSR
      try {
        // 这里要设置一个超时，避免chunk2没有回来但链接被服务端主端关闭而进入一直等待状态
        await detectStreamFirstData(getInitialDataTimeoutFromDocument());
        console.log('流式SSR 已上屏，设置首屏数据，即将进入hydrate')
      } catch (err) {
        console.warn('流式SSR兜底-走降级CSR渲染', err);
        // 做个标记，chunk2回来后不上屏，以及用于统计流式SSR 成功率
        (window as any).__STREAM_SSR_FALLBACK__ = 1;
        preFetchInitialData(getFirstData());
      }
      break;
    default:
      console.log('非流式渲染, 走原来逻辑 ')
      await doDefaultLogic();
  }

  // 需要预留给上屏的时间，150为经验值，根据实际情况调整。
  let bootstrapDelayTime = bundleShouldDelay ? 150 : 0;
  if (bootstrapDelayTime === 0 && isWithSkeleton()) {
    bootstrapDelayTime = 50;
  }

  // TODO: webpackChunkName的前缀需要与页面名一致。多页应用需考虑。（后续用工具实现）
  // 异步加载web bundle
  // 使用setTimeout,让接口调用先发出去
  setTimeout(() => {
    import(
      /* webpackChunkName: "index_bootstrap" */
      './bootstrap'
    );
  }, bootstrapDelayTime)

})()
