.modal-sign-in_new .pig-content::before {
  display: none !important;
}

.singin-new-wrap {
  width: 100%;
  height: 100%;
  align-items: center;
  .singin-new-title {
    flex-direction: row;
    justify-content: center;
    margin-bottom: 210rpx;
    .title-text {
      font-family: PingFangSC-Semibold;
      font-size: 48rpx;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
    }
    .singin-headline {
      color: #01255d;
    }
    .singin-money {
      color: #f02920;
    }
  }
  .singin-new-foot {
    align-items: center;
    margin-top: 50rpx;
    .video-btn {
      width: 450rpx;
      height: 108rpx;
      background-image: url('../../../assets/modal_btn_bg.png');
      background-size: cover;
      .btn-text {
        color: #fff;
        font-weight: 700;
        font-size: 40rpx;
        letter-spacing: 0;
        text-align: center;
        line-height: 108rpx;
      }
    }
  }
}

.singin-new-container {
  .singin-bg {
    position: relative;
    height: 270rpx;
    width: 580rpx;
    background: url("./assect/line1.png") no-repeat center/cover;
  }
  .singin-center {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    justify-content: space-between;
    width: 100%;
    height: 100%;
  }
  .singin-row {
    width: 100%;
    height: 20rpx;
    // padding: 0 16rpx;
    flex-direction: row;
    justify-content: space-between;
  }
  .singin-row2 {
    flex-direction: row-reverse;
  }
  .item-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate3D(-50%, -50%, 0);
    background-color: #fff;
    border-radius: 50%;
    width: 10rpx;
    height: 10rpx;
  }
  .row-item-center {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate3d(-50%, -120%, 0);
    width: 100%;
    align-items: center;
    .item-money {
      position: absolute;
      top: 0;
      left: 50%;
      width: 100%;
      height: 48rpx;
      transform: translate3d(-50%, -120%, 0);
      font-family: D-DIN-Bold;
      font-size: 22rpx;
      color: #405a86;
      letter-spacing: 0;
      font-weight: 700;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 24rpx;
      word-break: break-all;
      &.tomorrow {
        color: #f02920;
      }
    }
  }
  .item-text {
    position: absolute;
    bottom: 0;
    transform: translateY(120%);
    width: 100%;
    text-align: center;
    font-family: PingFangSC-Regular;
    font-size: 22rpx;
    color: #7e93b7;
    letter-spacing: 0;
    font-weight: 400;
    white-space: nowrap;
    &.today {
      color: #405a86;
    }
  }
  .row-item {
    position: relative;
    flex: 1;
    height: 100%;
    .top-img {
      position: absolute;
      left: 50%;
      transform: translate3D(-50%, -220%, 0);
    }
    .tomorrow-tips {
      position: absolute;
      left: 50%;
      top: 0;
      transform: translate3D(-50%, -100%, 0);
      padding: 5rpx 16rpx;
      background-image: linear-gradient(270deg, #f02920 0%, #ff8b80 100%);
      border-radius: 13px;
      .tips-desc {
        white-space: nowrap;
        font-family: PingFangSC-Semibold;
        font-size: 24rpx;
        color: #ffffff;
        font-weight: 700;
      }
      .arrows {
        position: absolute;
        width: 24rpx;
        height: 12rpx;
        bottom: 0;
        left: 50%;
        transform: translate3D(-50%, 8rpx, 0);
      }
    }
  }
  .singin-day {
    margin-top: 80rpx;
    text-align: center;
    .singin-day-text {
      font-family: PingFangSC-Regular;
      font-size: 26rpx;
      color: #7e93b7;
      letter-spacing: 0;
      font-weight: 400;
    }
  }
}
.singin-new-container.littele-container {
  .singin-bg {
    height: 20rpx;
    width: 544rpx;
    background: url("./assect/line2.png") no-repeat center;
    border-radius: 10rpx;
  }
}

.ellipsis-group {
  position: absolute;
  top: 50%;
  left: 0%;
  transform: translate3d(-50%, -50%, 0);
  width: 48rpx;
  height: 6rpx;
  flex-direction: row;
  justify-content: space-between;
  .ellipsis {
    background-color: #fff;
    border-radius: 50%;
    width: 6rpx;
    height: 6rpx;
  }
}

.progress-wrap {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-top-right-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  overflow: hidden;
  .singin-progress {
    position: absolute;
    left: 0;
    top: 0;
    height: 20rpx;
    border-radius: 20rpx;
    background: linear-gradient(to right, #ff8b80, #f02920);
    z-index: 1;
  }
  .progress-curve {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 80rpx;
    border: 20rpx solid #f02920;
    border-radius: 20rpx;
    border-left: none;
    z-index: 0;
  }
  .singin-progress-bottom {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 20rpx;
    .progress-bottom {
      position: absolute;
      right: 0;
      top: 0;
      height: 20rpx;
      border-radius: 20rpx;
      background: linear-gradient(to right, #f02920, #f02920);
      z-index: 1;
    }
  }
}
