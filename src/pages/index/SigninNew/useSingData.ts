import {TASK_STATUS, TaskInfo} from '@/store/models/task/types';
import {store} from '@/store';
import {getDayDiff} from '@/store/models/task/helper';

export type signDate = {
  day: number;
  isToday: boolean;
  isTomorrow: boolean;
  state: TASK_STATUS;
  amount: string | undefined;
  date: string;
  isCoinLucky: boolean; // 小福袋
  isLottery: boolean; // 大福袋
  isCrossing: boolean; // 展示省略号
}

type ISignData = signDate[]; // 签到任务数据列表

function useSignData(signList: TaskInfo[], isVoluntarily = false):[ISignData, string | undefined, number | undefined] {
  const now = store.getState().task.now;
  const newNewSignList = store.getState().task.newNewSignList;
  let signDate = signList.map((sign, index) => {
    const isToday = getDayDiff(sign.completeTime, now) === 0 && (sign.state === TASK_STATUS.TASK_CONFIRMED || sign.state === TASK_STATUS.TASK_COMPLETED);
    return {
      day: sign.sort,
      isToday,
      isTomorrow: getDayDiff(sign.beginTime, now) === 1,
      isCoinLucky: sign.rewardItems[0]?.mark?.includes('coin_lucky')  || sign.rewardItems[0]?.mark === 'cash',
      isLottery: sign.rewardItems[0]?.mark?.includes('lottery'),
      state: sign.state,
      amount: getAward(sign),
      date: isToday ? '今天已签' : sign.sort + '天',
      isCrossing: signList[index+1] ? sign.sort !== (signList[index+1].sort - 1) : false,
    }
  });
  const toDaySign = signDate.find(sign => sign.isToday);
  const toDayAward = toDaySign?.amount;
  const toTomorrowAward = signDate.find(sign => sign.isTomorrow)?.amount || getAward(newNewSignList[0]);
  return [signDate, isVoluntarily ? toDayAward : toTomorrowAward, toDaySign?.day]
}

export function getAward(signTask: TaskInfo | null) {
  if (!signTask) return
  const reward = signTask.prizes?.[0]?.rewardItem || signTask.rewardItems?.[0]
  const mark =  reward?.mark || ''
  const amount = reward?.amount || 0
  // 已领奖的抽奖类型任务，奖品会在prize字段中
  if (signTask.prizes?.length) {
    if (mark.includes('cash')) {
      return amount / 100 + '元'
    }
    return amount + '元宝'
  } else {
    if (mark.includes('lottery')) {
      return reward?.name || ''
    }
    if (mark === 'cash') {
      return amount / 100 + '元'
    }
    return amount + '元宝'
  }
}

export default useSignData
