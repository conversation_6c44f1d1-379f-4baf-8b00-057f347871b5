import { createElement, FC, useEffect } from "rax";
import { store } from '@/store';
import View from 'rax-view';
import fact from '@/lib/fact';

import './index.scss';

import calendarMaker from './getCalendarMaker';
import useSingData from './useSingData';
import useAdplay from './useAdplay';

import SingHead from "./component/sign-head";
import SingFoot from "./component/sing-foot";
import SingContainer from "./component/sign-container";

import { RewardItem } from "@/store/models/task/types";

const SignNew:FC<{rewardItem: RewardItem | null, isVoluntarily: boolean}> = (
  {
    rewardItem,
    isVoluntarily
}) => {
  const newSignList = store.getState().task.newNewSignList;
  const signList = calendarMaker(newSignList)

  const [signDate, toDayAward] = useSingData(signList, isVoluntarily)
  const [amount, adPalyAction, adPlayDone] = useAdplay();

  const totalSignDay = newSignList.length; // 需要签到的总数 不考虑7天情况
  const isLastSignTo30Day = totalSignDay === 30 && signDate.length === 6; // 30天签到的最后一种情况需要特殊处理布局用 其他情况都为false

  const today = signDate.find(task => task.isToday) || signDate[0];
  const handleClick = () => {

    const checkSite = () => {
      const is30Day = totalSignDay === 30;
      if (is30Day && isVoluntarily) return '30day_signpop'
      if (!is30Day && isVoluntarily) return '15day_signpop'
      if (is30Day && !isVoluntarily) return '30day_listsignpop'
      return '15day_listsignpop'
    }

    fact.click('video_click', {
      c: checkSite(),
      d: 'video',
      day: today.day
    })
  }

  useEffect(() => {
    fact.exposure('fuli_expo', {
      c: isVoluntarily ? 'pop' : 'listpop',
      d: totalSignDay > 15 ? 'oldsign_30day' : 'oldsign_15day',
      day: today.day,
      video: adPlayDone ? '0' : '1'
    })
  }, [])

  return (
    <View className="singin-new-wrap">
      <SingHead prize={rewardItem} isVoluntarily={isVoluntarily} award={toDayAward} />
      <SingContainer singList={signDate} totalSignDay={totalSignDay} isLastSignTo30Day={isLastSignTo30Day} />
      <SingFoot amount={amount} adPalyAction={adPalyAction} adPlayDone={adPlayDone} handleClick={handleClick}  />
    </View>
  )
}

export default SignNew
