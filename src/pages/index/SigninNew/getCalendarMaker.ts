import { TaskInfo } from '@/store/models/task/types';

function calendarMaker(signList: TaskInfo[]) {
  const totalSignDays = signList.length; // 总签到天数
  const cumulativeSign = getCumulativeSign(signList); // 已签到天数
  // 7天直接返回
  if (totalSignDays === 7) return signList
  // 15天两种情况
  if (totalSignDays === 15) {
    if (cumulativeSign <= 6) {
      return signList.filter(sing => sing.sort <= 7 || sing.sort === 15)
    } else {
      return signList.filter(sing => sing.sort >= 7)
    }
  }
  // 剩下都是30天的情况
  const signNumberDays = [6, 12, 18, 24, 30];
  const signClassifications = ['under6', 'under12', 'under18', 'under24', 'under30'];

  const signDaysRange = {
    under6: [1, 2, 3, 4, 5, 6, 7, 14, 21, 30],
    under12: [7, 8, 9, 10, 11, 12, 13, 14, 21, 30],
    under18: [13, 14, 15, 16, 17, 18, 19, 20, 21, 30],
    under24: [19, 20, 21, 22, 23, 24, 25, 26, 27, 30],
    under30: [25, 26, 27, 28, 29, 30]
  };

  const getDaysRange = (signNum: number) => {
    for (let i = 0; i < signNumberDays.length; i++) {
      if (signNum <= signNumberDays[i]) {
        return signClassifications[i];
      }
    }
    return signClassifications[0];
  };

  const daysRange: number[] = signDaysRange[getDaysRange(cumulativeSign)!];

  return signList.reduce((previousValue: TaskInfo[], currentValue: TaskInfo) => {
    if (daysRange.includes(currentValue.sort)) {
      previousValue.push(currentValue)
    }
    return previousValue
  }, [])
}

function getCumulativeSign(signList: TaskInfo[]) {
  return signList.filter(sing => sing.state === 2).length
}

export default calendarMaker