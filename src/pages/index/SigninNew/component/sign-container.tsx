import { createElement, FC } from "rax";
import View from 'rax-view';
import Text from "rax-text";
import Image from "@/components/image";

import shaped from '../assect/shoe-shaped.png';
import shapedComplete from '../assect/shoe-shaped-complete.png';
import bag from '../assect/bag.png';
import bagComplete from '../assect/bag-complete.png';
import arrows from '../assect/arrows.png'

import SingProgress from "./sign-progress";

import { signDate } from '../useSingData';
import { TASK_STATUS } from '@/store/models/task/types';

interface ISingContainer {
  singList: signDate[];
  totalSignDay: number;
  isLastSignTo30Day: boolean;
}

const SingContainer: FC<ISingContainer> = ({
  singList,
  totalSignDay,
  isLastSignTo30Day,
}) => {
  const continuousDay = singList.find(sing => sing.isToday)?.day!
  const sliceIndex = totalSignDay !== 30 ? (continuousDay <= 6 ? 4 : 5) : 6
  const firstRow = singList.slice(0, sliceIndex);
  const secondRow = singList.slice(sliceIndex);
  const topCurrentNumber = (firstRow.findIndex(task => task.isToday) + 1)  || firstRow.length;
  const totalNumber = singList.findIndex(sing => sing.isToday) + 1 || 1;
  return (
    <View className={`singin-new-container ${isLastSignTo30Day ? 'littele-container' : ''}`}>
      <View className="singin-bg">
        <SingProgress topCurrentNumber={topCurrentNumber} topNumber={firstRow.length} bottomNumber={secondRow.length} totalNumber={totalNumber} />
        <View className="singin-center">
          <View className="singin-row singin-row1">
            {
              firstRow.map((row, index)=> {
                return (
                  <View className="row-item" key={index}>
                    <View className="row-item-center">
                      <Text className={`item-money ${row.isTomorrow ? 'tomorrow' : ''}`}>{row.isLottery ? row.amount : row.amount?.replace('元宝', '')}</Text>
                      <View className="top-img">
                        <Image source={getWingImage(row)} style={{
                          width: '60rpx',
                          height: '60rpx',
                        }}
                        />
                        <View className="tomorrow-tips" x-if={row.isTomorrow}>
                          <Text className="tips-desc">明天领</Text>
                          <Image className="arrows" source={arrows} />
                        </View>
                      </View>
                    </View>
                    <View className="item-circle"></View>
                    <Text className={`item-text ${row.isToday ? 'today' : ''}`}>{row.date}</Text>
                    <View className="ellipsis-group" x-if={row.isCrossing}>
                      <View className="ellipsis"></View>
                      <View className="ellipsis"></View>
                      <View className="ellipsis"></View>
                    </View>
                  </View>
                )
              })
            }
          </View>
          <View className="singin-row singin-row2" x-if={secondRow.length !== 0}>
            {
              secondRow.map(row => {
                return (
                  <View className="row-item">
                    <View className="row-item-center">
                      <Text className={`item-money ${row.isTomorrow ? 'tomorrow' : ''}`}>{row.isLottery ? row.amount : row.amount?.replace('元宝', '')}</Text>
                      <View className="top-img">
                        <Image source={getWingImage(row)} style={{
                          width: '60rpx',
                          height: '60rpx',
                        }}
                        />
                        <View className="tomorrow-tips" x-if={row.isTomorrow}>
                          <Text className="tips-desc">明天领</Text>
                          <Image className="arrows" source={arrows} />
                        </View>
                      </View>
                    </View>
                    <View className="item-circle"></View>
                    <Text className={`item-text ${row.isToday ? 'today' : ''}`}>{row.date}</Text>
                    <View className="ellipsis-group" x-if={row.isCrossing}>
                      <View className="ellipsis"></View>
                      <View className="ellipsis"></View>
                      <View className="ellipsis"></View>
                    </View>
                  </View>
                )
              })
            }
          </View>
        </View>

      </View>
      <View className="singin-day">
        <Text className="singin-day-text">已连续签到{continuousDay}天</Text>
      </View>
    </View>
  )
}

function getWingImage(sign: signDate) {
  if (sign.state !== TASK_STATUS.TASK_CONFIRMED) {
    if (sign.isCoinLucky || sign.isLottery) return bag
    return shaped
  }
  if (sign.isCoinLucky || sign.isLottery) return bagComplete
  return shapedComplete
}

export default SingContainer
