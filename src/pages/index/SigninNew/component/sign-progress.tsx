import { store } from "@/store";
import { createElement, FC } from "rax";
import View from 'rax-view';

interface Iprogress {
  topCurrentNumber: number;
  topNumber: number;
  bottomNumber: number;
  totalNumber: number;
}

const SingProgress:FC<Iprogress> = ({
  topCurrentNumber,
  topNumber,
  bottomNumber,
  totalNumber,
}) => {

  const newSingList = store.getState().task.newNewSignList;
  const isShowCurve = newSingList.length === 15 && totalNumber > topNumber;

  const topProgress = (topCurrentNumber / topNumber) * 100;
  const bottomProgress = (totalNumber - topNumber) / bottomNumber * 100;

  return (
    <View className="progress-wrap">
      <View className="singin-progress" style={{
        width: (topProgress - (topNumber === 5 ? 8.5 : 6.5)) + '%'
      }}></View>
      <View className="progress-curve" x-if={isShowCurve}></View>
      <View className="singin-progress-bottom" x-if={isShowCurve}>
        <View className="progress-bottom" style={{
          width: (bottomProgress - (topNumber === 4 ? 10.5 : 8.5)) + '%' 
        }}></View>
      </View>
    </View>     
  )
}

export default SingProgress