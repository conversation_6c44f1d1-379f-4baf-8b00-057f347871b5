import { createElement, FC } from "rax";
import View from 'rax-view';
import Text from "rax-text";
import {RewardItem} from "@/store/models/task/types";
import { rewardDesc } from "@/pages/index/task/help";

const SingHead: FC<{ award: string | undefined, isVoluntarily: boolean, prize: RewardItem | null }> = ({
  award,
  isVoluntarily,
  prize
}) => {
  let titleAward = isVoluntarily ? award?.replace('最高', '') : award
  // 自动签到并且是抽奖 展示奖励
  if (isVoluntarily && prize) {
    titleAward = rewardDesc(prize)
  }
  return (
    <View className="singin-new-title">
      <Text className="title-text singin-headline">{isVoluntarily ? '签到成功' : '明日签到'}</Text>
      <Text className="title-text singin-money">+{titleAward}</Text>
    </View>
  )
}

export default SingHead
