import { createElement, FC } from "rax";
import View from 'rax-view';
import Text from "rax-text";

import baseModal from '@ali/weex-rax-components/lib/base_modal';
import { MODAL_ID } from '@/components/modals/index';

interface ISingFoot {
  amount: number;
  adPalyAction: () => void;
  adPlayDone: boolean;
  handleClick: () => void;
}


const SingFoot: FC<ISingFoot> = ({
  amount,
  adPalyAction,
  adPlayDone,
  handleClick,
}) => {
  return (
    <View className="singin-new-foot" onClick={() => {
      if (!adPlayDone) {
        adPalyAction()
        handleClick()
      }
      baseModal.close(MODAL_ID.SIGN_IN_NEW)
    }}>
      <View className="video-btn">
        <Text className="btn-text">{
          adPlayDone ? '好的' : `看视频再领${amount}元宝`}
        </Text>
      </View>
    </View>
  )
}

export default SingFoot