import { TASK_EVENT_TYPE } from '@/store/models/task/types';
import { store } from '@/store';
import { taskActionHandler } from '@/pages/index/task/help';

const useAdplay = ():[number, () => void, boolean] => {
  const taskList = store.getState().task.taskList;
  const videoTask = taskList.find(task => task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW || task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD)!;

  const adPlayAction = () => taskActionHandler(videoTask)
  const amount = videoTask?.rewardItems[0]?.amount || 0;
  const adPlayDone = videoTask?.target === videoTask?.progress;
  return [amount, adPlayAction, adPlayDone]
}

export default useAdplay

