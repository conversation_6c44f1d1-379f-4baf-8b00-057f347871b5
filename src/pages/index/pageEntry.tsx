import { createElement, Component } from 'rax';
import PageIndex from './dashboard';
import RetryPage from '../RetryPage';
// import PageFlow from '../flow';

import View from 'rax-view';
import Nav, { history } from '@/lib/nav';
import Page from '@/lib/page';
import { connect } from 'rax-redux';
import { dispatch, store, StoreDispatch, StoreState } from '@/store';
import { pages } from "@/store/models/route";
import landscape from '@ali/weex-toolkit/lib/landscape';
import modal from '@/components/modals/modal';
import baseModal from '@ali/weex-rax-components/lib/base_modal/index';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import AsyncComponent from "@/components/AsyncComponent";
import {isWeb} from "universal-env";
import {doOnAttach} from "@/lib/prerender";
import ucapi from '@/utils/ucapi';
import event from '@/utils/event';
import { isAndroid, isLatestVersion } from '@/lib/universal-ua';
import Modal from "@/components/modals/modal";
import { MODAL_ID } from '@/components/modals';
import { getAdInsideGroupTaskInfoFromStore } from './task/help';

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {};
interface IState {
  ucParams: null;
  isRetrySuccess: boolean;
}

class PageEntry extends Component<IProps, IState> {
  defaultPage = '/'

  constructor(props){
    super(props);
    this.state = {
      isRetrySuccess: props.isRetrySuccess,
      ucParams: props.ucParams
    }
  }

  listen() {
    // change方法不能在didMount执行，会导致首个change接收不到
    // history.event.on('change', ({ page }) => {
    //   dispatch.route.updateScene({ page });
    // })
    // 初始化调用
    landscape.ban();
    document.addEventListener('UCEVT_Global_AccountStateChange', async (state: any) => {
      tracker.log({ category: WPK_CATEGORY_MAP.LOGIN_STATUS, msg: `AccountStateChange_${state}`, c1: 'addEventListener' });
      console.log('[user] 登录状态变化~~ ', state);
      if (state && state.detail) {
        if (state.detail.status !== 1) {
          // 登出
          store.dispatch.user.afterUserLogout();
        } else {
          const app = store?.getState()?.app
          const user = store?.getState()?.user
          if (!user?.bindTaobao) {
            await dispatch.user.getBindTaobaoInfo()
          }
          // 判断是否有高价值绑定任务 并且完成任务
          if (store?.getState()?.user?.bindTaobao && app?.ucLoginTask?.hasUcLoginTask) {
            const task = app?.ucLoginTask?.taskInfo;
            dispatch.task.complete({
              id: task?.id as number,
              type: 'complete',
              useUtCompleteTask: !!task?.useUtCompleteTask,
              publishId: task?.publishId,
              params: { task }
            });
          }
          // 移除未登录下高价值奖励弹窗
          const baseCurrentOpenModal = baseModal?.getCurrentOpenModalObj() || {};
          if(Object.keys(baseCurrentOpenModal).length && baseCurrentOpenModal[MODAL_ID.HIGH_VALUE_AWARD]){
            baseModal.close(MODAL_ID.HIGH_VALUE_AWARD);
            baseModal.removeCacheModal(MODAL_ID.HIGH_VALUE_AWARD);
          }
          store.dispatch.app.init();
          modal.closeLogout();
        }
      }
    });
    (window as any).alipayLoginCB = () => {
      localStorage.setItem('loginFromAlipay', '1')
      dispatch.user.selecBindtInfo()
      console.log('支付宝登录成功!')
    }
  }

  listPageIndexDataInit()  {
    event.on('pageIndexDataInit', (data)=> {
      const curRoute = location?.hash?.split('#')?.[1];  
      if(curRoute === '/' && data && Object.keys(data).length){
        this.setState({
          isRetrySuccess: data?.isRetrySuccess
        })
      }
    });
  }

  componentWillMount() {
    // change方法不能在didMount执行，会导致首个change接收不到
    history.event.on('change', ({ page }) => {
      dispatch.route.updateScene({ page });
    });
    const currentPage = location.hash.replace('#/', '')
    this.defaultPage = pages.includes(currentPage) ? `/${currentPage}` : '/'
  }

  async componentDidMount() {
    // 时机问题
    this.listen();
    this.listPageIndexDataInit();
    doOnAttach(() => {
      getAdInsideGroupTaskInfoFromStore()
      localStorage.removeItem('iFlowDialogShow')
      localStorage.removeItem('searchModalShow')
      this.appInit()
    });
  }

  async appInit(){
    const { ucParams } = this.props;
    try {
      // 统计APP初始化提前时间（PS： 后续调整到index.js constructor）
      if (window.__first_app_init_time) {
        console.log('[APP INIT  LIFT TIME]', Date.now() - window.__first_app_init_time);
        tracker.log({
          category: WPK_CATEGORY_MAP.APP_INIT_LEFT_TIME,
          sampleRate: 1,
          wl_avgv1: Date.now() - window.__first_app_init_time
        });
      }

      const params = ucParams ? ucParams : await ucapi.biz.ucparams({ params: 'prvesv' }, true);
      const clientType = params?.pr?.toLowerCase()?.includes('uclite') ? 'UCLite' : 'UCMobile';
      dispatch.app.updateState({ clientType });
       // 是否为安卓合规版本
       const isHeguiVer = isAndroid && isLatestVersion(params?.ve, '16.0.5.0000');
       const isHeguiSv = ['ucrelease', 'uctrial', 'uctrailhegui', 'uctrial64', 'RC1', 'uctrialhegui'].includes(params.sv);
       // 通过本地缓存hegui_agree有无判断是否为第一次来福利猪,
       const hasAgree = localStorage.getItem('hegui_agree')
      if (clientType === 'UCMobile' && isHeguiVer && isHeguiSv && !hasAgree) {
          Modal.openHeguiModal();
      } else {
        store.dispatch.app.init(true);
      }
      // 延迟3S、采集APP安装情况
      setTimeout(() => {
        dispatch.app.checkAppInstall();
      }, 3000);
      tracker.log({
        category: WPK_CATEGORY_MAP.GET_CLIENT_TYPE,
        sampleRate: 1,
        w_succ: params?.pr ? 1 : 0,
        msg: params?.pr ? '获取成功' : '未获取到',
        c1: params?.pr,
        c2: params?.ve,
        c3: params?.sv,
        c4: isHeguiVer && isHeguiSv && !hasAgree,
        bl1: JSON.stringify(params),
      });
    } catch (error) {
      tracker.log({
        category: WPK_CATEGORY_MAP.DOM_PROCESSING,
        sampleRate: 1,
        w_succ: 0,
        msg: '未获取到',
        bl1: JSON.stringify(error),
      });
      store.dispatch.app.init(true);
    }
  }

  render() {
    const { isRetrySuccess } = this.state;
    return (
      <View>
        <Nav default={this.defaultPage}>
          <Page scene="/">
            {isRetrySuccess ? <PageIndex /> : <RetryPage />}
          </Page>
          <Page scene="/flow">
            {
              isWeb ? <AsyncComponent resolve={() => import('../flow')} /> : <view />
            }
          </Page>
          <Page scene="/conversion">
            {
              isWeb ? <AsyncComponent resolve={() => import('../conversion')} /> : <view />
            }
          </Page>
        </Nav>
      </View>
    );
  }
}

const mapState = (store: StoreState) => {
  return {
    ucParams: store.app.ucParams,
    isRetrySuccess: true
  };
};

const mapDispatch = (store: StoreDispatch) => ({});
export default connect(mapState, mapDispatch)(PageEntry);
