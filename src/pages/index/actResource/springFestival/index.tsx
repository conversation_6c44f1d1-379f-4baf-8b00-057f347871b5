import Bg from './assets/<EMAIL>';
import Back from './assets/<EMAIL>';
import Help from './assets/<EMAIL>';
import Setting from './assets/<EMAIL>';
import Next from './assets/<EMAIL>';
import { IResource } from "@/pages/index/actResource/daily";

const indexResource: IResource = {
  bg: Bg,
  back: Back,
  setting: Setting,
  stickyHeaderBg: '#F93C30',
  activeDaysColor: '#fff',
  nextIcon: Next,
  help: Help,
  bgColor: '#FFECD2',
  fontColor: {
    name: '#fff',
    money: '#FFE16C',
    moneyUnit: '#FFD7B7',
    exchangeDesc: 'rgba(255, 255, 255, 0.5)'
  }
}

export default indexResource
