import Bg from './assets/<EMAIL>';
import Back from './assets/icon_back.png';
import Help from './assets/<EMAIL>';
import Setting from './assets/<EMAIL>';
import Next from './assets/<EMAIL>';

export interface IResource {
  bg: string;
  back: string;
  setting: string;
  help: string;
  nextIcon: string;
  bgColor: string;
  stickyHeaderBg: string;
  activeDaysColor: string;
  fontColor: {
    name: string;
    money: string;
    moneyUnit: string;
    exchangeDesc: string;
  }
}

const indexResource: IResource = {
  bg: Bg,
  back: Back,
  setting: Setting,
  nextIcon: Next,
  stickyHeaderBg: '#F8FBFF',
  activeDaysColor: '#01255d',
  help: Help,
  bgColor: '#F8FBFF',
  fontColor: {
    name: '#01255d',
    money: '#F02920',
    moneyUnit: '#01255D',
    exchangeDesc: '#7E93B7'
  }
}

export default indexResource
