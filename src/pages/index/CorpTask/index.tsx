import Rax, { createElement, Fragment, useEffect } from 'rax';
import View from 'rax-view';
import Image from "@/components/image";
import { useSelector } from 'rax-redux';
import useMounted from 'rax-use-mounted';
import '../task/index.scss';
import { StoreState } from '@/store';
import { TaskInfo, TASK_EVENT_TYPE, TASK_STATUS } from '@/store/models/task/types';
import Item from '../task/item';
import fact from '@/lib/fact';
import { doOnAttach } from "@/lib/prerender";
import storage from '@ali/weex-toolkit/lib/storage';
import ucapi from '@/utils/ucapi';
import {isIOS} from "@/lib/universal-ua";
import event from '@/utils/event';
import Fact from '@/components/Fact';
import useArrayLengthChanged from '@/hooks/useArraylengthChange';
import { setAppInstallWithExpiryMinutes } from '@/lib/utils/app_install';

let corpLogFlag: boolean = false;
let timer: any = null;

const Task: Rax.FC = () => {
  const bigPrizeEvents = useSelector((state: StoreState) => {
    return state.app?.bigPrizeEvents;
  });

  if(!bigPrizeEvents?.length) {
    return <Fragment></Fragment>
  }

  const taskList = useSelector((state: StoreState) => {
    const { taskList: taskList } = state.task;
    // 过滤不显示的任务, 任务event统一配置在diamond
    let renderTask = taskList.filter(task => {
      return bigPrizeEvents.includes(task.event);
    });
    return renderTask;
  });

  if(!taskList.length){
    return <Fragment></Fragment>
  }

  const hasTaskLengthChange = useArrayLengthChanged(taskList);
  useEffect(() => {
    let observer;
    if (taskList.length > 0) {
      console.log('taskList update');
      const taskListElements =  document.querySelectorAll('div[big-prize="1"]');
      observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (!entry.isIntersecting) {
            entry.target.setAttribute('data-fact-exposed', "false");
          }
        });
      });
      taskListElements.forEach(element => observer?.observe(element));
    }
    return () => {
      observer?.disconnect();
      observer = null;
    }
  }, [hasTaskLengthChange]);


  // 在统计截止时间内、任务完成前、且点击时未安装app，才进行打点记录
  const corpTaskInstallLog = async () => {
    for (let task of taskList) {

      if (isIOS || !task?.packageName) { // ios需要用 schema 查询，暂无
        continue;
      }
      try {
        const queryRes = await ucapi.biz.queryApp({
          cache_first: '1',
          pkgs: [task?.packageName]
        })
        console.log('queryApp', queryRes);
        const installed = isIOS ? (queryRes[task?.packageName] ? 1 : 0) : (queryRes[task?.packageName]?.versionName ? 1 : 0);
        setAppInstallWithExpiryMinutes({
          pkg: task?.packageName, 
          install:installed === 1
        });

        // 展示前
        if (!task?.fromCache) {
          fact.event('dltask_installed', {
            task_id: task.id,
            task_name: task.name,
            module: '大奖专区',
            slot_id: task.slotId,
            account_id: task.accountId,
            sid: task.sid,
            ad_id: task.adId,
            is_ios: isIOS ? 1 : 0,
            installed: installed,
          });
          console.log('二方任务展示记录打点');
        }

        // 点击后
        const expireTime = await storage.get(`hc_ad_${task.slotId}_${task.accountId}_click_expire`);
        const now = Date.now();
        const clickInstallFlag = await storage.get(`hc_ad_${task.slotId}_${task.accountId}_click_install_status`);
        if (now < expireTime && task.state === TASK_STATUS.TASK_DOING && !clickInstallFlag) {
          fact.event('dltask_installed2', {
            task_id: task.id,
            task_name: task.name,
            module: '大奖专区',
            slot_id: task.slotId,
            account_id: task.accountId,
            sid: task.sid,
            ad_id: task.adId,
            is_ios: isIOS ? 1 : 0,
            installed: isIOS ? (queryRes[task?.packageName] ? 1 : 0) : (queryRes[task?.packageName]?.versionName ? 1 : 0),
          });
          console.log('二方任务点击后记录打点');
        }

      } catch (e) {
        console.log('调用queryApp报错：', JSON.stringify(e));
      }
    }
  }
  const dealLog = () => {
    if (taskList.length > 0 && !corpLogFlag) {
      if (timer) {
        clearTimeout(timer);
        timer = null;
      }
      timer = setTimeout(() => {
        corpTaskInstallLog();
        corpLogFlag = true;
      }, 500);
    }
  }
  const renderItems = taskList.map((taskInfo: TaskInfo, index) => <Item key={taskInfo.id} taskInfo={taskInfo} index={index} moduleType="BIG_PRIZE" taskModule={'大奖专区'}/>);
  useMounted(() => {
    doOnAttach(() => {
      fact.exposure('task_show', { c: 'task', d: 'show' });
      event.on('appearInitData', () => {
        corpLogFlag = false;
      });
    })
  });
  useEffect(() => {
    dealLog();
  });
  const style = {
    // marginTop: '100rpx'
  }
  return <Fact
    c="mokuai"
    d="dajiangtask"
    expoLogkey="dajiang_expo"
    noUseClick
    style={style} className={'container-task container-corp-task'} x-if={renderItems.length > 0}>
    <View className="corp-task-header">
      <Image className="header-pic" source="https://yes-file.uc.cn/file/1738913190532_1724145226_6864.png" />
    </View>
    {renderItems}
  </Fact>;
};

export default Task;
