import {Component, createElement} from 'rax';
import { connect } from 'rax-redux';
import View from 'rax-view';
import { StoreDispatch, StoreState} from '@/store';
import Image from '@/components/image';
import Text from "rax-text";
import { openURL } from '@/pages/index/task/help';
import modal from '@/components/modals/modal';
import { isIOS } from "@/lib/universal-ua";
import './index.scss';
import {ITransformer} from "@/store/models/app/typings";
import fact from '@/lib/fact';
import Fact from '@/components/Fact';
import logoutCheck from "@/utils/logoutCheck";

import BubbleImg from './assets/<EMAIL>';

interface IState {
  isToolbar: boolean;
  disableBack: boolean;
}

class Transformers extends Component<IProps, IState> {

  handleClick = (e, transformer: ITransformer) => {
    fact.click('top_oper_click', {
      c: 'top_oper',
      d: 'click',
      opername: transformer.businessName || ''
    })
    if (logoutCheck()){
      return;
    }
    if (transformer.type === 'link') {
      openURL(transformer.link)
      return
    }
    if (transformer.type === 'searchWords') {
      modal.openSearchModal("search-words", 'top_icon')
      return;
    }
  }

  getShowItems = () => {
    const { transformers, welfareBallState } = this.props
    return transformers.filter(item => {
      if (item.businessName === 'video' && !welfareBallState) {
        return false
      }
      return item.platform?.includes(isIOS ? 'ios' : 'android')
    })
  }

  render () {
    const showItems = this.getShowItems()
    return (
      <View x-if={showItems.length} className="comp-transformers" style={{'justify-content': showItems.length < 3 ? 'space-around' : 'space-between'}}>
        {
          showItems.map((item, index) => {
            return <Fact key={index} c="top_oper" d="show" expoLogkey="top_oper_show" expoExtra={{
              opername: item.businessName || ''
            }}>
              <View key={index} className="transformer-item" onClick={(e) => this.handleClick(e, item)}>
                <View x-if={item.imgStyle === 'bubble'} className="item-bubble">
                  <Image source={BubbleImg} style={{width: '96rpx', height: '37rpx'}} />
                  <Text className="bubble-text">大额福利</Text>
                </View>
                <Image className="transformer-img" source={item.img} style={{width: '88rpx', height: '88rpx'}} />
                <Text className="transformer-title">{item.title}</Text>
              </View>
            </Fact>
          })
        }
      </View>
    );
  }
};

const mapState = (state: StoreState) => {
  return {
    transformers: state.app?.transformers || [],
    welfareBallState: state.app.welfareBallState
  };
};

const mapDispatch = (dispatch: StoreDispatch) => ({
});


type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
};
export default connect(mapState, mapDispatch)(Transformers)

