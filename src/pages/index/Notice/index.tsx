import {createElement, Component} from 'rax';
import './index.scss';
import View from 'rax-view';
import Image from 'rax-image';
import NoticeIcon from './assets/<EMAIL>';

interface IProps {
  className?: string;
  animationReady: boolean;
  noticeList: Array<string>;
}

class Notice extends Component<IProps> {
  box: HTMLElement | null;
  text: HTMLElement | null;
  boxWidth: number;
  textWidth: number;
  scrollTimer: any;
  timeoutTimer: any;
  perFontSize: number;
  lastLeft: number = 0;

  componentDidMount = () => {
    this.initData();
    this.initAnimation();
  }

  componentDidUpdate = (prevProps: IProps) => {
    if (!prevProps.animationReady && this.props.animationReady) {
      this.initData();
      this.initAnimation();
    }
  }

  initData = () => {
    this.box = document.querySelector(".notice-marquee");
    this.text = document.querySelector(".notice-content");
    this.boxWidth = this.box?.offsetWidth as number; // offsetWidth = width + padding + margin
    this.perFontSize = this.boxWidth / 24;
  }

  initAnimation() {
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
      this.scrollTimer = null;
    }
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
    if (this.props.animationReady) {
      (this.text as HTMLElement).style.transform = '';
      this.textWidth = this.text?.textContent?.length as number * this.perFontSize;
      if (this.textWidth > this.boxWidth) {
        this.timeoutTimer = setTimeout(() => {
          this.startAnimation();
        }, 500);
      }
    }
  }

  startAnimation = () => {
    this.scrollTimer = setInterval(this.step, 50);
  }

  step = () => {
    let left = this.lastLeft;
    this.setLeft(this.text, --left);
    if (left < -this.textWidth) {
      this.setLeft(this.text, this.boxWidth);
    }
  }

  setLeft = (box, left = 0) => {
    this.lastLeft = left;
    box.style.transform = `translateX(${left}px)`;
  }

  renderNoticeContent = () => {
    const { noticeList } = this.props;
    let notice = '';

    // 判断是否配置了公告
    if (noticeList && noticeList.length > 0) {
      const len = noticeList.length;
      if (len === 1) {
        notice = noticeList[0];
      } else {
        notice = '';
        noticeList.forEach((item, idx) => {
          notice += item;
          if (idx < (len - 1)) {
            notice += '&emsp;&emsp;&emsp;&emsp;';
          }
        })
      }
    }
    return notice;
  }

  render() {
    return (
      <View className="notice" key="notice">
        <Image className="notice-icon" source={{uri: NoticeIcon}}></Image>
        <View className="notice-marquee">
          <View className="notice-content"
            dangerouslySetInnerHTML = {{ __html: this.renderNoticeContent() }}
          ></View>
        </View>
      </View>
    );
  }
}

export default Notice;
