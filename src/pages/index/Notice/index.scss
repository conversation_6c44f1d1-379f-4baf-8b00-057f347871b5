.notice {
  position: relative;
  width: 690rpx;
  height: 56rpx;
  padding: 0 20rpx;
  margin: 20rpx auto;
  background: #FFFCF0;
  border: 1rpx solid #FFE1A1;
  border-radius: 28rpx;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.notice-icon {
  width: 32rpx;
  height: 32rpx;
  margin: auto 10rpx auto 0;
  padding-top: 1rpx;
  uc-perf-stat-ignore: image;
}

.notice-marquee {
  width: 586rpx;
  color: #A27D6B;
  height: 34rpx;
  line-height: 34rpx;
  overflow: hidden;
  font-family: PingFangSC-Regular;;

  .notice-content {
    display: block;
    white-space: nowrap;
    position: relative;
    height: 34rpx;
    line-height: 34rpx;
    font-size: 24rpx;
    font-weight: 400;
    padding-top: 2rpx;
  }
}
