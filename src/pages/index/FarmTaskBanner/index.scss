.comp-farm-task-banner {
  position: relative;
  width: 690rpx;
  height: 186rpx;
  // background-image: url("./images/<EMAIL>");
  background-size: 100% auto;
  background-repeat: no-repeat;
  padding: 0 40rpx 0 32rpx;
  margin: 40rpx 30rpx 0;
  transition: all 0.2s ease-in-out;
  border-radius: 40rpx;
  overflow: hidden;
  background-color: #fff;
  .banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.5;
    pointer-events: none;
    animation: opacityAni 2s infinite;
    img {
      uc-perf-stat-ignore: image;
    }
  }
  .uc-farm-task {
    width: 100%;
    padding-top: 50rpx;
    align-items: center;
    .task-content {
      flex: 1;
      margin-left: 16rpx;
      .task-title {
        font-size: 32rpx;
        color: #12161A;
        line-height: 40rpx;
        font-weight: 700;
        margin-bottom: 12rpx;
      }
      .task-desc {
        font-size: 24rpx;
        color: #405A86;
        font-weight: 700;
        align-items: center;
      }
      .max-num {
        font-size: 24rpx;
        color: #405A86;
        font-weight: 700;
      }
    }
    img {
      uc-perf-stat-ignore: image;
    }
  }
  .task-btn-wrap {
    .task-btn {
      width: 144rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: center;
      color: #fff;
      background-image: url('../../../assets/button_bg_red.png');
      background-size: cover;
      font-size: 26rpx;
    }
  }
  @keyframes opacityAni {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }
}
