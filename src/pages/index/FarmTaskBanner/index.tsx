import Rax, { createElement } from 'rax';
import Image from '@/components/image';
import View from 'rax-view';
import Text from 'rax-text';
import { useSelector } from 'rax-redux';
import './index.scss';
import { StoreState } from '@/store';
import TaskIcon from './images/<EMAIL>';
import BannerBg from './images/<EMAIL>';
import { INGOT_ICON } from '@/constants/static_img';
import { execWithLock } from '@/utils/lock';
import { taskActionHandler } from '@/pages/index/task/help';
import fact from '@/lib/fact/index';
import Fact from '@/components/Fact';

// 去uc芭芭农场的任务Banner
function FarmTaskBanner(): JSX.Element {
  // 去uc芭芭农场的任务
  const ucFarmTask = useSelector((state: StoreState) => state?.task?.toppingTask);
  const handleToFarm = async () => {
    if (ucFarmTask) {
      fact.click('button_click', {
        c: 'task',
        d: 'button',
        task_id: ucFarmTask.id,
        task_name: ucFarmTask.name,
        taskclassify: ucFarmTask?.taskClassify || '',
        groupcode: ucFarmTask?.groupCode || '',
        award_amount: ucFarmTask?.rewardItems[0]?.amount || '',
      });
      await execWithLock(
        'finish_task_lock',
        async () => {
          await taskActionHandler(ucFarmTask);
        },
        3000,
      );
    }
  };
  return (
    <Fact
      x-if={ucFarmTask}
      c="task"
      d="top"
      expoLogkey="top_expo"
      expoExtra={{
        task_id: ucFarmTask?.id,
        task_name: ucFarmTask?.name,
        taskclassify: ucFarmTask?.taskClassify || '',
        groupcode: ucFarmTask?.groupCode || '',
        award_amount: ucFarmTask?.rewardItems[0]?.amount || '',
      }}
      noUseClick
    >
      <View className="comp-farm-task-banner">
        <View className="banner-bg">
          <Image source={BannerBg} style={{ width: '690rpx' }} />
        </View>
        <View className="uc-farm-task row">
          <Image source={ucFarmTask?.icon || TaskIcon} style={{ width: '96rpx', height: '96rpx' }} />
          <View className="task-content">
            <View className="task-title">{ucFarmTask?.name}</View>
            <View className="task-desc din-num row">
              最高可领
              <Image source={INGOT_ICON} style={{ width: '34rpx', height: '34rpx' }} />
              <Text className="max-num din-num">{ucFarmTask?.rewardItems[0]?.amount || 1300}</Text>
            </View>
          </View>
          <View className="task-btn-wrap">
            <View onClick={handleToFarm} className="task-btn">
              {ucFarmTask?.btnName || '去完成'}
            </View>
          </View>
        </View>
      </View>
    </Fact>
  );
}

export default FarmTaskBanner;
