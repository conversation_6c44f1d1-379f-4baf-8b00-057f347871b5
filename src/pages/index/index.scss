* {
  user-select: none;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

#root {
  height: 100vh;
}

@font-face {
  font-family: D-DIN-Bold;
  // src: url("https://image.uc.cn/s/uae/g/01/yuanbaosign2020/DIN-Bold.ttf");
  src: url("https://image.uc.cn/s/uae/g/42/uc-interactive/D-DIN-Bold.otf");
}
.close-btn-bg, .close-btn {
  uc-perf-stat-ignore: image;
}
.row {
  display: flex;
  flex-direction: row;
}

.align-c {
  align-items: center;
}

.j-center {
  justify-content: center;
}

.din-num {
  font-family: D-DIN-Bold;
}

.homeContainer {
  height: 100%;
  align-items: center;
}
.page-root {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  position: relative;
  .wrap-rule {
    flex-direction: row;
    justify-content: space-between;
    width: 360rpx;
    margin-left: 68rpx;
    text-align: center;
    padding-top: 30rpx;
    padding-bottom: 30rpx;
    // padding-bottom: 150rpx;
    font-size: 20rpx;
    color: #01255d;
    &.wrap-rule-margin {
      padding-bottom: 150rpx;
    }
  }
  .wrap-task-list {
    position: relative;
    min-height: 1400rpx;
  }
}

.homeTitle {
  font-size: 45rpx;
  font-weight: bold;
  margin: 20rpx 0;
}

.homeInfo {
  font-size: 36rpx;
  margin: 8rpx 0;
  color: #555;
}
.btn-to-top {
  position: fixed;
  right: 36rpx;
  bottom: 60rpx;
  width: 96rpx;
  height: 96rpx;
  border-radius: 96rpx;
  align-items: center;
  justify-content: center;
  background: #FFFFFF;
  box-shadow: 0 3px 6px 0 rgba(0,0,0,0.15);
}
