.comp-double-award-banner {
  position: relative;
  width: 690rpx;
  height: 186rpx;
  margin: 0 auto;
  margin-top: 24rpx;
  padding: 44rpx 40rpx 36rpx 36rpx;
  border-radius: 40rpx;
  background-size: 100% auto;
  background-repeat: no-repeat;
  overflow: hidden;
  img {
    uc-perf-stat-ignore: image;
  }
  .banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;
  }
  .task-icon {
    border-radius: 16rpx;
  }
  .banner-tag {
    position: absolute;
    top: 4rpx;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    margin: auto;
  }

  .tag-text {
    position: absolute;
    top: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    width: fit-content;
    margin: auto;
    color: #ffffff;

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    .text {
      display: inline-block;
      font-weight: 400;
      font-size: 22rpx;
      font-family: FZLanTingYuanS-Bold;
    }
    .rax-countdown-main {
      margin-left: 12rpx;
      .rax-text-v2 {
        font-size: 24rpx;
        font-family: D-DIN-Bold;
      }
    }
  }

  .double-award-task {
    z-index: 2;
    width: 100%;
    height: 100%;
    align-items: center;

    .task-content {
      flex: 1;
      margin-left: 16rpx;
      font-family: PingFangSC-Semibold;
      .task-title {
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color:#01255D;
        line-height: 40rpx;
        font-weight: 700;
        margin-bottom: 12rpx;
      }
      .rax-text-v2 {
        white-space: nowrap;
      }
      .task-desc {
        font-size: 24rpx;
        color: #405A86 ;
        font-weight: 700;
        align-items: center;
        .din-num {
          font-family: DIN-Bold;
          font-size: 24rpx;
          color: #405A86;
        }
      }
      .max-num {
        font-size: 24rpx;
        color: #405a86;
      }
    }
    .task-btn-wrap {
      width: 144rpx;
      border-radius: 28rpx;
      text-align: center;
      &.task-btn-wrap-double {
        height: 106rpx;
        background-image: linear-gradient(180deg, rgba(240, 41, 32, 0) 0%, rgba(240, 41, 32, 0.1) 98%);
      }
      &.task-btn-wrap-normal {
        display: flex;
        justify-self: center;
        align-items: center;
      }
      .task-btn {
        width: 144rpx;
        height: 56rpx;
        line-height: 56rpx;
        background-image: url('../../../assets/button_bg_red.png');
        background-size: 100% auto;
        font-size: 26rpx;
        color: #fff;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        uc-perf-stat-ignore: image;
      }
      .task-double-time {
        color: #f02920;
        letter-spacing: 0;
        text-align: center;
        font-weight: 700;
        margin-top: 6rpx;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .rax-text-v2 {
          font-size: 22rpx;
          font-family: PingFangSC-Medium;
          display: inline-block;
        }
        .text-double {
          font-size: 20rpx;
          font-weight: 700;
        }
        .text {
          font-size: 22rpx;
          font-weight: 700;
        }
        .task-double-time-icon {
          margin-left: -4rpx;
        }
      }
    }
  }

  @keyframes opacityAni {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }
}
