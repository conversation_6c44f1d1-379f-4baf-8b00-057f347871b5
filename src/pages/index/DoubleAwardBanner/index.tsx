import Rax, { createElement, Fragment, useEffect, useMemo, useState } from 'rax';
import Image from '@/components/image';
import View from 'rax-view';
import Text from 'rax-text';
import { useSelector } from 'rax-redux';
import './index.scss';
import { dispatch, store, StoreState } from '@/store';
import { DOWNLOAD_APP_EVENT, IPrize, TASK_EVENT_TYPE, TASK_STATUS, TaskInfo } from '@/store/models/task/types';

import RedPacketIcon from '@/assets/red_packet.png';
import BannerBg from './images/banner-bg.png';
import ShortTag from './images/tag-short.png';
import LongTag from './images/tag-long.png';
import { INGOT_ICON } from '@/constants/static_img';
import { execWithLock } from '@/utils/lock';
import {
  getTargetTimeDiff,
  getTaskCurDayTimeProcess,
  getTaskCurDayTimeTarget,
  hideUninstallTask,
  ifShowAdTask,
  isHuiChuangAdEffectTask,
  taskActionHandler,
  taskItemRewardDesc,
} from '@/pages/index/task/help';
import fact from '@/lib/fact/index';
import Fact from '@/components/Fact';
import modal from '@/components/modals/modal';
import Countdown from 'rax-countdown';
import { logToFinishTask } from '@/store/models/task/taskMonitor';
import { combineBrandAdTask, combineCorpAdTask } from '@/store/models/task/task_util';
import { getParam } from '@/lib/qs';

const hcAdEvent = [TASK_EVENT_TYPE.BRAND_TASK, TASK_EVENT_TYPE.CORP_APP_TASK, TASK_EVENT_TYPE.CORP_APP_TASK_NEW, TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND];
// 翻倍任务
function DoubleAwardBanner(): JSX.Element {
  const limitTaskResource = useSelector(
    (state: StoreState) =>
      state.resource.uc_piggy_limit_time || {
        taskList: [],
        attributes: {
          limitTimeTaskConfig: []
        },
      },
  );
  const hcAdTaskBrandAd = useSelector((state: StoreState) => state.ad.taskBrandAd);
  const hcAdTaskCorp = useSelector((state: StoreState) => state.ad.taskCorpAd);
  const hcAdStoreDataList = useSelector((state: StoreState) => state.task.adStoreDataList);
  const clientType = useSelector((state: StoreState) => state.app.clientType);

  const filterTaskEvent = (task: TaskInfo, needFilterHcAdEvent = true) => {
    // 待核销，待领奖励的不过滤
    if (task.state !== TASK_STATUS.TASK_DOING) {
      return true;
    }
    if (needFilterHcAdEvent) {
      return ifShowAdTask(task) && !hideUninstallTask(task, true, 'resource') && !hcAdEvent.includes(task.event);
    }
    return ifShowAdTask(task) && !hideUninstallTask(task, true, 'resource');
  }

  // 过滤可做的翻倍任务
  const [doubleAwardTask, setDoubleAwardTask] = useState<TaskInfo[]>(() => {
    return (limitTaskResource.taskList ?? []).filter((task) => filterTaskEvent(task, true));
  });

  // 汇川相关的广告，汇川信息更新后再重新做过滤
  useEffect(() => {
    let taskInfoList = (limitTaskResource.taskList ?? []).filter((task) => filterTaskEvent(task, false));
    // 品牌
    if (hcAdTaskBrandAd) {
      taskInfoList = combineBrandAdTask(taskInfoList, hcAdTaskBrandAd);
    } else {
      taskInfoList = taskInfoList.filter(task => TASK_EVENT_TYPE.BRAND_TASK !== task.event);
    }
    // 效果
    if (hcAdTaskCorp) {
      taskInfoList = combineCorpAdTask(taskInfoList, hcAdTaskCorp, clientType, hcAdStoreDataList);
    } else {
      taskInfoList = taskInfoList.filter(task => !isHuiChuangAdEffectTask(task.event));
    }
    getParam('debug') && console.log('double list', taskInfoList);
    setDoubleAwardTask(taskInfoList);
  }, [hcAdTaskBrandAd, hcAdTaskCorp, limitTaskResource.taskList]);

  if (!doubleAwardTask.length) {
    return <Fragment></Fragment>
  }

  // 素材内容
  const limitTimeTaskConfigMap = useMemo(() => {
    const map: Map<string, string> = (limitTaskResource?.attributes?.limitTimeTaskConfig ?? []).reduce((acc, item) => {
      acc.set(String(item.taskId), item.abbreviation);
      return acc;
    }, new Map<string, string>());
    return map;
  }, [limitTaskResource]);

  // 当前在做的任务
  const curTask = useMemo(() => {
    return doubleAwardTask[0];
  }, [doubleAwardTask]);


  const taskShowDetail = useMemo(() => {
    const { name = '', desc = '', btnName = '去完成', rewardItems = [] } = curTask ?? {};
    let taskNameDefaultLength = curTask?.icon ? 10 : 12;
    const taskDescDefaultLength = curTask?.icon ? 11 : 15;
    if (!curTask?.dayTimes) {
      taskNameDefaultLength = taskNameDefaultLength + 4
    }
    return {
      rewardDesc: taskItemRewardDesc(curTask)?.replace('+', ''),
      awardIcon: (rewardItems?.[0]?.mark || '').includes('cash') ? RedPacketIcon : INGOT_ICON,
      taskName: name?.length >= taskNameDefaultLength ? name.substring(0, taskNameDefaultLength - 2) + '..' : name,
      tasDesc: desc?.length >= taskDescDefaultLength ? desc.substring(0, taskDescDefaultLength - 2) + '..' : desc,
      btnName: curTask?.state === TASK_STATUS.TASK_COMPLETED ? '待领取' : btnName.length ? btnName : '去完成',
      multiple: rewardItems?.[0]?.multiple > 1 ? rewardItems?.[0]?.multiple ?? 1 : 1
    }
  }, [curTask]);

  const doubleAwardEndTimeDiff = useMemo(() => {
    if (!curTask) {
      return 0;
    }
    return getTargetTimeDiff(curTask?.endTime, curTask?.beginTime).diff;
  }, [curTask]);


  const isLimitTimeTask = useMemo(() => {
    return curTask?.timeLimitType === 'MINUTE';
  }, []);

  const cardMaterialConfig = useMemo(() => {
    return {
      tagText: limitTimeTaskConfigMap?.get(String(curTask.id)) ?? '惊喜奖励',
      tagImage: isLimitTimeTask ? LongTag : ShortTag,
      isLongTag: isLimitTimeTask,
    };
  }, [curTask]);

  const handleClick = async () => {
    fact.click('task_click', {
      c: 'task',
      d: 'multiple',
      task_id: curTask.id,
      task_name: curTask.name,
      taskclassify: curTask?.taskClassify || '',
      groupcode: curTask?.groupCode || '',
      award_amount: curTask?.rewardItems[0]?.amount || '',
      task_progress: curTask?.dayTimes?.progress || curTask?.task_progress || '',
      task_click_position: '去完成',
      isfinish: 0,
      multiple: taskShowDetail.multiple,
      resource_location: 'uc_piggy_limited_task',
    });

    fact.click('resource_click', {
      c: 'task',
      d: 'multiple',
      resource_location: 'uc_piggy_limited_task',
    });

    // 领取奖励
    if (curTask.state === TASK_STATUS.TASK_COMPLETED) {
      if (!hcAdEvent.includes(curTask.event)) {
        dispatch.task.complete({ id: curTask.id, type: 'award', publishId: curTask?.publishId, params: { task: curTask, toast: true } });
        return;
      }
      let prizes;
      // 汇川效果
      if (isHuiChuangAdEffectTask(curTask)) {
        prizes = await dispatch.task.completeCorpTask({ id: curTask.id, publishId: curTask?.publishId, type: 'award', params: { task: curTask } });
      }
      // 汇川品牌
      if (curTask.event === TASK_EVENT_TYPE.BRAND_TASK) {
        prizes = await dispatch.task.completeBrandTask({ id: curTask.id, type: 'award', publishId: curTask?.publishId, params: { task: curTask } });
      }
      if (prizes && prizes.length > 0) {
        const validPrizes = prizes.filter(prize => prize.win);
        if (validPrizes.length) {
          modal.openTreasure(validPrizes, (validPrizes[0]?.rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin')
        }
      }
      // 更新资源位
      dispatch.resource.getResourceAllDate({ firstInit: false, resData: null });
      return;
    }

    // 汇川效果下载类
    if (curTask.state === TASK_STATUS.TASK_NOT_COMPLETED && DOWNLOAD_APP_EVENT.includes(curTask.event)) {
      // 触发下载类任务完成
      const result = await dispatch.task.checkAppDownloadFinish({
        taskInfo: curTask,
        showToast: false
      });
      if (result) {
        return;
      }
    }

    await execWithLock(
      'finish_task_lock',
      async () => {
        logToFinishTask(curTask, 'multiple');
        await taskActionHandler(curTask, { location: 'multiple' });
      },
      2000,
    );
  };

  // 倒计时结束，更新资源位列表
  const timeComplete = () => {
    dispatch.resource.getResourceAllDate({ firstInit: false, resData: null });
  };

  return (
    <Fact
      x-if={curTask}
      className="comp-double-award-banner"
      c="task"
      d="multiple"
      expoLogkey="resource_exposure"
      noUseClick
      expoExtra={{
        resource_location: 'uc_piggy_limited_task',
      }}
    >
      <View className="banner-bg">
        <Image source={BannerBg} style={{ width: '690rpx' }} />
      </View>
      <View className="banner-tag">
        <Image
          source={cardMaterialConfig.tagImage}
          style={{ width: cardMaterialConfig.isLongTag ? '272rpx' : '192rpx' }}
        />
      </View>
      <View className="tag-text">
        <Text className="text">{cardMaterialConfig.tagText}</Text>
        {cardMaterialConfig.isLongTag ? (
          <Countdown
            timeRemaining={doubleAwardEndTimeDiff * 1000}
            interval={1000}
            tpl="{h}:{m}:{s}"
            onComplete={timeComplete}
          />
        ) : (
          <></>
        )}
      </View>
      <Fact
        c="task"
        d="multiple"
        expoLogkey="task_expo"
        noUseClick
        expoExtra={{
          task_id: curTask?.id,
          task_name: curTask?.name,
          task_event: curTask.event,
          task_state: curTask.state,
          taskclassify: curTask.taskClassify || '',
          groupcode: curTask.groupCode || '',
          award_amount: curTask?.rewardItems[0]?.amount || '',
          task_progress: curTask?.dayTimes?.progress || '',
          isfinish: 0,
          resource_location: 'uc_piggy_limited_task',
          multiple: taskShowDetail.multiple,
        }}
        className="double-award-task row"
        key={curTask.id}
      >
        {curTask?.icon && <Image className="task-icon" source={curTask?.icon} style={{ width: '96rpx', height: '96rpx' }} />}
        <View className="task-content">
          <View className="task-title">
            <Text style={{ whileSpace: 'nowrap' }}>
              {taskShowDetail.taskName}
              {curTask?.dayTimes?.target
                ? `(${getTaskCurDayTimeProcess(curTask)}/${getTaskCurDayTimeTarget(curTask)})`
                : ''}
            </Text>
          </View>
          <View className="task-desc row">
            {taskShowDetail.tasDesc ?? '本次可领'}
            {taskShowDetail.rewardDesc && <Image source={taskShowDetail.awardIcon} style={{ width: '34rpx', height: '34rpx' }} />}
            <Text className="din-num">{taskShowDetail.rewardDesc}</Text>
          </View>
        </View>
        <View className={`task-btn-wrap ${taskShowDetail.multiple > 1 ? 'task-btn-wrap-double' : 'task-btn-wrap-normal'}`} onClick={handleClick}>
          <View className="task-btn">{taskShowDetail.btnName}</View>
          {taskShowDetail.multiple > 1 ? (
            <View className="task-double-time">
              <Image className="task-double-time-icon" source={taskShowDetail.awardIcon} style={{ width: '40rpx', height: '40rpx' }} />
              <Text className="text-double">x</Text>
              <Text className="din-num">{taskShowDetail.multiple}</Text>
              <Text className="text">倍</Text>
            </View>
          ) : (
            <></>
          )}
        </View>
      </Fact>
    </Fact>
  );
}

export default DoubleAwardBanner;
