@import './variables.scss';

.sign-comp {
  width: 690rpx;
  height: 354rpx;
  background: #FFFFFF;
  border: 1rpx solid rgba(1,37,93,0.10);
  box-shadow: 0 0 30rpx 0 rgba(0,80,188,0.05);
  border-radius: 40rpx;
  margin: auto;
  @include set-theme {
    background: get-variable('background_color');
  }

  .sign-comp-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin: 50rpx 40rpx 10rpx;

    .sign-comp-header-title {
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      color: #01255D;
      line-height: 40rpx;
      font-weight: 700;
      @include set-theme {
        color: get-variable('title_color');
        font-weight: get-variable('title_font_weight');
      }
    }
    .sign-comp-header-desc {
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      text-align: right;
      font-weight: 400;
      // color: #7E93B7;
      @include set-theme {
        color: get-variable('desc_color');
      }
    }
  }

  .sign-comp-body {
    margin-bottom: 40rpx;

    .sign-comp-list {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .sign-comp-list-item {
        position: relative;

        .sign-reward-done-icon {
          position: absolute;
          top: 16rpx;
          right: 10rpx;
          height: 32rpx;
          width: 32rpx;
          z-index: 5;
        }

        .sign-reward-icon-wrap {
          position: relative;
          width: 64rpx;
          height: 64rpx;
          background-color: #FEF0D5;
          border-radius: 50%;
          overflow: hidden;
          margin: 20rpx 13rpx 6rpx;

          .sign-reward-icon {
            width: 64rpx;
            height: 64rpx;
            margin: auto;
          }
          .cash-icon {
            width: 46rpx;
            height: 56rpx;
            margin-top: 8rpx;
          }

          .sign-reward-text {
            position: absolute;
            width: 64rpx;
            bottom: 4rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;

            .sign-reward-symbol {
              font-family: D-DIN-Bold;
              font-size: 12rpx;
              color: #FFE66F;
              letter-spacing: 0;
              text-align: right;
              font-weight: 700;
              margin-right: 1rpx;
            }
            .sign-reward-value {
              font-family: D-DIN-Bold;
              font-size: 16rpx;
              color: #FFE66F;
              letter-spacing: 0;
              font-weight: 700;
            }
          }
        }
      }

      .sign-day {
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        color: #7E93B7;
        letter-spacing: 0;
        text-align: center;
        font-weight: 400;
        @include set-theme {
          color: get-variable('sign_day_color');
        }
      }
      .focus-day {
        font-weight: 700;
        @include set-theme {
          color: get-variable('title_color');
        }
      }
    }

    .sign-comp-minder-container {
      position: relative;
      margin: 24rpx 40rpx 0;

      .triangle-symbol {
        position: absolute;
        top: -12rpx;
        left: 24rpx;
        width: 0;
        height: 0;
        border-width: 0 12rpx 12rpx;
        border-style: solid;
        border-color: transparent transparent #EEF2F6;
        @include set-theme {
          border-color: transparent transparent get-variable('minder_bar_background_color');
        }
      }
      .sign-comp-minder-bar {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        background: #EEF2F6;
        border-radius: 24rpx;
        height: 72rpx;
        width: 610rpx;

        @include set-theme {
          background: get-variable('minder_bar_background_color');
        }
        .sign-comp-minder-text {
          font-family: PingFangSC-Regular;
          font-size: 24rpx;
          // color: #405A86;
          letter-spacing: 0;
          font-weight: 400;
          margin-left: 24rpx;
          @include set-theme {
            color: get-variable('minder_text_color');
          }
        }

        .sign-comp-minder-btn {
          height: 44rpx;
          line-height: 44rpx;
          border-radius: 22rpx;
          padding: 0 24rpx;
          margin-right: 24rpx;
          font-family: PingFangSC-Regular;
          font-size: 20rpx;
          letter-spacing: 0;
          text-align: center;
          font-weight: 400;
        }

        .sign-comp-btn-sign {
          color: rgba(1,37,93,0.5);
          background-color: #FFFFFF;
          @include set-theme {
            color: get-variable('btn_sign_color');
            background: get-variable('btn_bakground_color');
          }
        }
        .sign-comp-btn-receive {
          color: #FFFFFF;
          width: 114rpx;
          background-image: url('./assets/<EMAIL>');
          background-repeat: no-repeat;
          background-size: contain;
          @include set-theme {
            color: get-variable('receive_btn_color');
          }
        }
        .sign-comp-btn-remind0 {
          // color: #FF1515;
          border-radius: 34rpx;
          border: 2rpx solid #FAC9C7;
          // background-color: #FFFFFF;
          @include set-theme {
            color: get-variable('btn_remind0_color');
            background-color: get-variable('btn_bakground_color');
            border-color: get-variable('btn_border_color');
          }
        }
        .sign-comp-btn-remind1 {
          // color: rgba(1,37,93,0.5);
          // background-color: #FFFFFF;
          @include set-theme {
            color: get-variable('btn_remind1_color');
            background-color: get-variable('btn_bakground_color');
          }
        }
        .sign-comp-btn-more {
          // color: #FF1515;
          border-radius: 34rpx;
          border: 2rpx solid #FAC9C7;
          // background-color: #FFFFFF;
          @include set-theme {
            color: get-variable('more_btn_color');
            background-color: get-variable('btn_bakground_color');
            border-color: get-variable('btn_border_color');
          }
        }
      }
    }
  }
}
