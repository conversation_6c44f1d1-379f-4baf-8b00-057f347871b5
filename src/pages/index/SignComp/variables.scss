// 信息流浅色主题
$light-theme: (
  background_color: #FFFFFF,
  title_color: #333333,
  title_font_weight: 400,
  desc_color: #999999,
  sign_day_color: #999999,
  minder_bar_background_color: #F6F6F6,
  minder_text_color: #666666,
  btn_bakground_color: #FFFFFF,
  btn_border_color: #FAC9C7,
  receive_btn_color: #FFFFFF,
  btn_remind0_color: #FF1515,
  btn_remind1_color: rgba(1,37,93,0.5),
  btn_sign_color: rgba(1,37,93,0.5),
  more_btn_color: #FF1515,
);

// 信息流深色主题
$dark-theme: (
  background_color: #FFFFFF,
  title_color: #333333,
  title_font_weight: 400,
  desc_color: #999999,
  sign_day_color: #999999,
  minder_bar_background_color: #F6F6F6,
  minder_text_color: #666666,
  btn_bakground_color: #FFFFFF,
  btn_border_color: #FAC9C7,
  receive_btn_color: #FFFFFF,
  btn_remind0_color: #FF1515,
  btn_remind1_color: rgba(1,37,93,0.5),
  btn_sign_color: rgba(1,37,93,0.5),
  more_btn_color: #FF1515,
);

// 信息流透明主题
$transparent-theme: (
  background_color: #FFFFFF,
  title_color: #333333,
  title_font_weight: 400,
  desc_color: #999999,
  sign_day_color: #999999,
  minder_bar_background_color: #F6F6F6,
  minder_text_color: #666666,
  btn_bakground_color: #FFFFFF,
  btn_border_color: #FAC9C7,
  receive_btn_color: #FFFFFF,
  btn_remind0_color: #FF1515,
  btn_remind1_color: rgba(1,37,93,0.5),
  btn_sign_color: rgba(1,37,93,0.5),
  more_btn_color: #FF1515,
);

// 福利页浅色主题
$welfare-theme: (
  background_color: #FFFFFF,
  title_color: #01255D,
  title_font_weight: 700,
  desc_color: #7E93B7,
  sign_day_color: #7E93B7,
  minder_bar_background_color: #EEF2F6,
  minder_text_color: #405A86,
  btn_bakground_color: #FFFFFF,
  btn_border_color: #FAC9C7,
  receive_btn_color: #FFFFFF,
  btn_remind0_color: #FF1515,
  btn_remind1_color: rgba(1,37,93,0.5),
  btn_sign_color: rgba(1,37,93,0.5),
  more_btn_color: #FF1515,
);

//定义映射集合
$themes: (
  light-theme: $light-theme,
  dark-theme: $dark-theme,
  transparent-theme: $transparent-theme,
  welfare-theme: $welfare-theme,
);

//获取颜色并为当前主题色配置颜色
//字体颜色
@mixin base-color() {
  @each $theme-name, $theme in $themes {
    [data-theme="#{$theme-name}"] & {
      color: map-get($map: $theme, $key: base-color);
    }
  }
}

//背景色
@mixin base-background() {
  @each $theme-name, $theme in $themes {
    [data-theme="#{$theme-name}"] & {
      background: map-get($map: $theme, $key: background-color);
    }
  }
}

// 颜色较多的情况下，获取颜色和设置颜色封装成函数
// 获取背景色下的变量颜色值
@function get-variable($variable-name) {
  @return map-get($theme, $variable-name);
}

// 给当前主题色配置颜色变量
@mixin set-theme {
  @each $theme-name, $theme in $themes {
    // !global 把局部变量强升为全局变量
    // 供get-variable函数中：map-get里 key 的使用
    $theme: $theme !global;

    [data-theme = '#{$theme-name}'] & {
      @content;
    }
  }
}
