import {Component, createElement} from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Image from 'rax-image';

import './index.scss';

import { INGOT_ICON } from '@/constants/static_img';
import CashBgIcon from './assets/<EMAIL>';
import SignDoneIcon from './assets/<EMAIL>';

interface IState {

}

interface IProps {
  signList: any;
  currentDay: number;
  hadSetSignRemind?: boolean;
  isSupportSignRemind: boolean;
  title: string;
  desc?: string;
  showDesc?: boolean;
  triangleSymbolLeftMargin?: number;
  isFlowCard?: boolean;
  themeType?: string;
  clickRecieve: () => any;
  remindSign?: () => any;
  clickBody?: () => any;
  clickGetMore?: () => any;
}

export enum TASK_STATUS {
  TASK_DOING = 0,
  TASK_COMPLETED = 1,
  TASK_CONFIRMED = 2,
  TASK_REPEAT = 3,
  TASK_NOT_READY = 4,
  TASK_PRE_TASK_NOT_FINISH = 5,
  TASK_TIMES_LIMIT = 6,
  TASK_FINISH = 7,
  TASK_NOT_COMPLETED = 8,
  TASK_INVALID_TIME = 9
}

const themeTypes = [
  'light-theme',
  'dark-theme',
  'transparent-theme',
  'welfare-theme',
]

export default class SignComp extends Component<IProps, IState> {

  state: IState = {

  }
  componentDidMount() {
    if (this.props.themeType && themeTypes.includes(this.props.themeType)) {
      document.body.setAttribute('data-theme', this.props.themeType);
    } else {
      document.body.setAttribute('data-theme', 'welfare-theme');
    }
  }

  getSignRewardBg = (rewardItems) => {
    if (rewardItems[0]?.mark?.indexOf('cash') > -1) {
      return CashBgIcon;
    }
    return INGOT_ICON;
  }

  isCashReward = (rewardItems) => {
    if (rewardItems[0]?.mark?.indexOf('cash') > -1) return true;
    return false;
  }

  getRewardVal = (rewardItems) => {
    if (!rewardItems || !rewardItems?.length) return '';
    if (rewardItems[0]?.mark?.indexOf('cash') > -1) {
      return rewardItems[0]?.amount / 100;
    }
    return rewardItems[0]?.amount;
  }

  getRewardUnit = (rewardItems) => {
    if (rewardItems?.length && rewardItems[0]?.mark?.indexOf('cash') > -1) {
      return '元';
    }
    return '元宝';
  }

  getTriangleSymbolLeft = (idx: number) => {
    const triangleSymbolLeftUnit = this.props.triangleSymbolLeftMargin || 90;
    if (idx < 1) return `${24}rpx`;
    return (24 + triangleSymbolLeftUnit * (idx - 1)) + 'rpx';
  }

  getSignToday = () => {
    return this.props.signList[this.props.currentDay - 1];
  }

  clickRecieve = (e: Event) => {
    e.stopPropagation();
    return this.props.clickRecieve && this.props.clickRecieve();
  }

  remindSign = (e: Event) => {
    e.stopPropagation();
    if (this.props.isFlowCard) {
      return this.clickGetMore();
    }
    return this.props.remindSign && this.props.remindSign();
  }

  clickBody = () => {
    return this.props.clickBody && this.props.clickBody();
  }

  clickGetMore = () => {
    return this.props.clickGetMore && this.props.clickGetMore();
  }

  clickSignDoneBtn = (e: Event) => {
    e.stopPropagation();
    if (this.props.isFlowCard) {
      return this.clickGetMore();
    }
  }

  clickRemindDoneBtn = (e: Event) => {
    e.stopPropagation();
    if (this.props.isFlowCard) {
      return this.clickGetMore();
    }
  }

  getTriangleSymbolDay = (signToday: any, day: number) => {
    const signList = this.props.signList;
    if (signList.length === day) {
      return day;
    }
    if (signToday?.state === TASK_STATUS.TASK_CONFIRMED) {
      return day + 1;
    }
    return day;
  }

  getTriangleSymbolSign = (signToday: any, day: number) => {
    const signList = this.props.signList;
    if (signList.length === day) {
      return signToday;
    }
    if (signToday?.state === TASK_STATUS.TASK_CONFIRMED) {
      return signToday?.nextSign;
    }
    return signToday;
  }

  render () {
    const { signList, title, desc = '', showDesc = true, isFlowCard = false, hadSetSignRemind = false } = this.props;
    // console.log('signList:',signList.length, signList)
    const signToday = this.getSignToday();
    const triangleSymbolSign = this.getTriangleSymbolSign(signToday, this.props.currentDay);
    return (
      <View className="sign-comp" onClick={this.clickBody}>
        <View className="sign-comp-header">
          <Text className="sign-comp-header-title">{title}</Text>
          <Text className="sign-comp-header-desc" x-if={showDesc}>{desc}</Text>
        </View>
        <View className="sign-comp-body">
          <View className="sign-comp-list">
            {
              signList.map((item, idx) => {
                const rewardItems = item.rewardItems || []
                return (
                  <View className="sign-comp-list-item" key={`${idx}_${item.id}`}>
                    <Image x-if={item.state === TASK_STATUS.TASK_CONFIRMED} className="sign-reward-done-icon" source={{ uri: SignDoneIcon }}></Image>
                    <View className="sign-reward-icon-wrap">
                      <Image className={`sign-reward-icon ${this.isCashReward(rewardItems) ? 'cash-icon' : ''}`} source={{ uri: this.getSignRewardBg(rewardItems) }}></Image>
                      <View className="sign-reward-text" x-if={this.isCashReward(rewardItems)}>
                        <Text className="sign-reward-symbol">¥</Text>
                        <Text className="sign-reward-value">{this.getRewardVal(rewardItems)}</Text>
                      </View>
                    </View>
                    <Text className={`sign-day ${triangleSymbolSign?.id === item.id ? 'focus-day' : ''}`}>{signToday?.id === item.id ? '今天' : `第${idx+1}天`}</Text>
                  </View>
                );
              })
            }
          </View>
          <View className="sign-comp-minder-container">
            <View className="triangle-symbol" style={{ left: this.getTriangleSymbolLeft(this.getTriangleSymbolDay(signToday, this.props.currentDay)) }}></View>
            <View className="sign-comp-minder-bar">
              <Text className="sign-comp-minder-text">
                {
                  signList?.length === this.props.currentDay && signToday?.state === TASK_STATUS.TASK_CONFIRMED ?
                  '今天已签到' :
                  (
                    signToday?.state === TASK_STATUS.TASK_CONFIRMED ?
                    `明天签到领取 ${this.getRewardVal(signToday?.nextSign?.rewardItems)} ${this.getRewardUnit(signToday?.nextSign?.rewardItems)}` :
                    `签到领取 ${this.getRewardVal(signToday?.rewardItems)} ${this.getRewardUnit(signToday?.rewardItems)}`
                  )
                }
              </Text>
              {
                signList?.length === this.props.currentDay && signToday?.state === TASK_STATUS.TASK_CONFIRMED ?
                <Text className={`sign-comp-minder-btn sign-comp-btn-sign ${isFlowCard ? 'sign-comp-btn-more' : ''}`} onClick={this.clickSignDoneBtn}>{isFlowCard ? '赚更多' : '已签到'}</Text> :
                <>
                  {
                    signToday?.state !== TASK_STATUS.TASK_CONFIRMED ?
                    <View className="sign-comp-minder-btn sign-comp-btn-receive" onClick={this.clickRecieve}>领取</View> :
                    <>
                      <Text className="sign-comp-minder-btn sign-comp-btn-remind0" onClick={this.remindSign} x-if={this.props.isSupportSignRemind && !hadSetSignRemind}>{isFlowCard ? '赚更多' : '提醒我'}</Text>
                      <Text className={`sign-comp-minder-btn sign-comp-btn-remind1 ${isFlowCard ? 'sign-comp-btn-more' : ''}`} x-if={hadSetSignRemind} onClick={this.clickRemindDoneBtn}>{isFlowCard ? '赚更多' : '已设提醒'}</Text>
                    </>
                  }
                </>
              }
            </View>
          </View>
        </View>
      </View>
    );
  }
};
