import {createElement} from 'rax';
import Image from "@/components/image";
import './index.scss';
import { IBannerAd } from '@/store/models/app';
import Fact from '@/components/Fact';
import logoutCheck from '@/utils/logoutCheck';
import { openURL } from '@/pages/index/task/help';
import { appVersion, isLatestVersion, isIOS } from "@/lib/universal-ua";
import Toast from '@/lib/universal-toast';
import {IAd} from "@/utils/huichuan";
import openTaobao from "@/utils/ucapi/opentaobao";
import { useState, useEffect, useRef, useCallback } from 'rax';
import {ISlotAd} from '@/utils/huichuan';
import factStat from '@ali/fact-stat';

interface IProps {
  bannerBrandAd: ISlotAd | null;
  bannerAdLoaded: boolean;
  backupAd: IBannerAd | undefined;
  exposure: (key: string) => void;
  click: (key: string) => void;
  bannerBrandAdName: string;
  hiddenBackupAd?: boolean;
}

// function usePrevious(value: any) {
//   const ref = useRef();
//   useEffect(() => {
//       ref.current = value
//   }, [value]);
//   return ref.current;
// }

function Banner(props: IProps) {
  const [ adImgLoaded, setAdImgLoaded ] = useState(false);
  // const prevBannerBrandAd = usePrevious(props.bannerBrandAd);
  const cacheAdExposure = useRef<{[key: string]: boolean}>({});

  const hasValidAd = (bannerInfo?) => {
    const adInfo = bannerInfo === undefined ? props.bannerBrandAd : bannerInfo;
    return adInfo
      && adInfo.ad
      && adInfo.ad[0]
      && adInfo.ad[0].ad_content?.img_1;
  }

  const onAdLoaded = () => {
    setAdImgLoaded(true);
  }

  factStat.on('auto_exposure', () => {
    if (hasValidAd()) {
      if (props.bannerBrandAd?.ad?.[0]?.ad_id) {
        const exposureKey = `${props.bannerBrandAd.sid}-${props.bannerBrandAd?.ad?.[0]?.ad_id}`;
        if (!cacheAdExposure.current?.[exposureKey]) {
          cacheAdExposure.current[exposureKey] = true;
          console.log('banner广告真曝光');
          props.exposure(props.bannerBrandAdName);
        }
      }
    }
  });

  // useMount
  // useEffect(() => {
  //   onAutoExpose();
  // }, []);

  // useUpdate
  // useEffect(() => {
  //   console.log('bannerBrandAd update')
  //   if (
  //     // 无到有
  //     !hasValidAd(prevBannerBrandAd) && hasValidAd()
  //     ||
  //     // 更换广告
  //     // @ts-ignore
  //     (hasValidAd(prevBannerBrandAd) && hasValidAd() && prevBannerBrandAd?.sid !== props.bannerBrandAd?.sid)
  //   ) {
  //     // onAutoExpose();
  //   }
  // }, [props.bannerBrandAd]) // 监听的数组元素类型非基本类型，需要处理一下上一个值

  const handleClick = () => {
    if (hasValidAd()) {
      props.click(props.bannerBrandAdName);
    }
  }

  const handleBackupAdClick = () => {
    console.log('cms banner点击');
    const banner: IBannerAd | undefined = props.backupAd;
    // 登录态判断
    if (banner?.needLogin) {
      if (logoutCheck()) {
        return;
      }
    }
    // 双端版本判断
    if (banner?.android || banner?.ios) {
      let isNewVersion
      if (appVersion) {
        if (isIOS) {
          isNewVersion = isLatestVersion(appVersion, banner.ios as string);
        } else {
          isNewVersion = isLatestVersion(appVersion, banner.android as string);
        }
      }
      if (!isNewVersion) {
        Toast.show('请升级到最新版本后参与活动');
        return;
      }
    }
    if (banner?.isTaobao) {
      openTaobao(banner?.link);
    } else {
      openURL(banner?.link);
    }
  }

  if (hasValidAd()) {
    const adInfo: Partial<IAd> | undefined = props.bannerBrandAd?.ad?.[0];
    if (adInfo?.ad_content?.img_1) {
      const extras = {
        ad_id: adInfo.ad_id,
        // @ts-ignore
        sid: this.props.bannerBrandAd.sid,
        row: 1,
        lnk_type: 'instal',
        account: adInfo.ad_content.account_id,
        banner_name: adInfo.ad_id,
      };
      return <Fact
        className="fuli-top-banner"
        key={adInfo.ad_id}
        onClick={handleClick}
        logkey="banner_click"
        c="banner"
        d="banner"
        expoLogkey="banner_display"
        expoExtra={extras}
        ckExtra={extras}
        style={{
          display: adImgLoaded ? 'flex' : 'none'
        }}
      >
        <Image onLoad={onAdLoaded} source={(adInfo.ad_content?.img_1 || '').replace('http://', 'https://')}
                className="banner-image banner-ad"/>
      </Fact>;
    }
  } else if ((props.bannerAdLoaded) && props.backupAd && !props.hiddenBackupAd && appVersion) {
    const backupAd = props.backupAd;
    const extras = {
      ad_id: backupAd.id,
      row: 1,
      lnk_type: 'base',
      account: '0',
      banner_name: backupAd?.id,
    };
    return <Fact
      className="fuli-top-banner"
      key={backupAd.id}
      onClick={handleBackupAdClick}
      logkey="banner_click"
      c="banner"
      d="banner"
      expoLogkey="banner_display"
      expoExtra={extras}
      ckExtra={extras}
      style={{
        display: adImgLoaded ? 'flex' : 'none'
      }}
    >
      <Image onLoad={onAdLoaded} source={backupAd.img} className="banner-image"/>
    </Fact>;
  }
  return null;
}

export default Banner;

