import { createElement } from 'rax';
import { Provider } from 'rax-redux';
import { store, dispatch } from '@/store';
import { ssrGetInitialProps } from '@/lib/render-utils/ssr';
import { getFirstData } from './first-data-api';
import { renderAction, perfMark, getPerfWpkLogParams, getPerfDomProcessingParams } from '@/lib/render-utils/perf';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';
import { isNode, isWeb } from 'universal-env';
import View from 'rax-view';
import stat from '@/lib/stat';
import BasicPage from '@/components/BasicPage';
import './index.scss';
import PageEntry from './pageEntry';
import { initAppInstallMap } from '../index/task/help';
import initClient from '@/lib/tracker/init-client';
import { IDiamondConf, IMultiBalance, IQueryTaskRes, ClientInfo } from '@/pages/index/typings';
import { getUserInfo, getUCParams } from '@/lib/ucapi';
import { getParam } from '@/lib/qs';

// GUIDANCE：初始化监控
initClient();
// 初始化打点
stat.init({});


if (isWeb) {
  const logonline = require('@ali/logonline');
  logonline.init();
}
export interface IFirstData {
  userInfo: ClientInfo;
  diamondConf: IDiamondConf;
  multiBalance: IMultiBalance;
  queryTaskRes: IQueryTaskRes;
}


class Home extends BasicPage<IFirstData> {
  constructor(props) {
    super(props);
    this.handleFirstData(this.useFirstData);
  }

  initFrontendData(queryTaskRes, clientType) {
    if (!queryTaskRes?.frontData) {
      return;
    }
    // @ts-ignore
    let { taobaoRtaConfig, ucTaobaoRtaConfig, ...others } = queryTaskRes?.frontData;
    let frontData = {
      ...others,
      taobaoRtaConfig,
    };
    if (clientType === 'UCMobile') {
      frontData.taobaoRtaConfig = ucTaobaoRtaConfig;
    }
    dispatch.app.updateState({
      ...frontData,
    });
  }
  initAppConfig(clientType) {
    dispatch.app.updateState({
      clientType,
    });
  }
  initTaskList(queryTaskRes) {
    queryTaskRes &&
      dispatch.task.updateState({
        ...queryTaskRes,
        resTaskList: queryTaskRes?.values || [],
        now: queryTaskRes.timestamp,
      });
      // TODO
    queryTaskRes?.values && dispatch.task.updateSearchWordsCache(queryTaskRes.values);
    const values = queryTaskRes?.values || [];
    // 首屏任务初步处理
    const taskList = dispatch.task.firstScreenTaskListHandler(values ?? []);
    // 初始化任务数据
    dispatch.task.setTaskList({
      resList: taskList || [],
      extra: {
        taskBrandAd: {},
        taskCorpAd: {},
      },
    });

  }
  /**
   * 初始化首屏数据
   * @param ucParams
   * @param queryTaskRes
   * @param multiBalance
   * @param cmsResData
   */
  async initFirstScreenData(ucParams, userInfo, queryTaskRes, multiBalance, cmsResData, resourceCodesInfoRes, rightsGiftList) {
    if (ucParams) {
      const clientType = ucParams?.pr?.toLowerCase()?.includes('uclite') ? 'UCLite' : 'UCMobile';
      this.initAppConfig(clientType);
      if (queryTaskRes) {
        this.initFrontendData(queryTaskRes, clientType);
      }
    }
    if (userInfo) {
      dispatch.user.updateUserInfo(userInfo);
    }
    if (multiBalance) {
      // 初始化顶部用户资产数据
      multiBalance?.balanceInfo && dispatch.currency.updateMultiBalance(multiBalance.balanceInfo);
    }
    if (cmsResData) {
      // 初始化CMS数据
      dispatch.cms.setCmsData(cmsResData);
    }
    if (queryTaskRes) {
      // 初始化任务数据
      this.initTaskList(queryTaskRes);
    }
    initAppInstallMap();
    if (resourceCodesInfoRes) {
      // 处理安装检测, 前置处理
      const allResourceTasks = Object.values(resourceCodesInfoRes?.data)?.map((item)=> (item?.taskList ?? []))?.flat(Infinity);
      dispatch.resource.firstScreenResourceListHandler(allResourceTasks ?? []);
      // 资源位数据      
      dispatch.resource.getResourceAllDate({
        firstInit: true,
        resData: resourceCodesInfoRes
      });
    }

    if (rightsGiftList) {
      // 初始化权益兑换数据
      dispatch.rightsGift.update({ rightsGiftList:  rightsGiftList?.planList ?? [] });
    }

    // 更新push开关 & 设置浏览器
    dispatch.app.getJasApiByPush({ firstInit: true});
    dispatch.app.getJasApiByBrowser({ firstInit: true });
  }
  // GUIDANCE：使用首屏数据
  useFirstData = async firstData => {
    getParam('debug') && console.log('first data:', firstData);
    // 更新数据到store
    if (firstData) {
      try {
        let { queryTaskRes, ucParams, userInfo, multiBalance, cmsResData, resourceCodesInfoRes, rightsGiftList } = firstData;
        // CSR过程补齐公参&用户信息
         this.initFirstScreenData(ucParams, userInfo,  queryTaskRes, multiBalance, cmsResData, resourceCodesInfoRes, rightsGiftList);
         if (!isNode) {
          window.__first_app_init_time = Date.now();
          const [params, user] = await Promise.all([
            getUCParams(),
            getUserInfo()
          ]);
          ucParams = params;
          userInfo = user;
          dispatch.app.updateState({
            ucParams,
          });
          await dispatch.user.initTraceId({
            ucParams,
            userInfo
          });
          const kps = (userInfo as any)?.kps_wg;
          tracker.log({
            category: WPK_CATEGORY_MAP.GET_FIRST_DATA,
            msg: `获取成功`,
            sampleRate: 1,
            w_succ: 1,
            c1: '' + !!kps,
            c2: '' + !!queryTaskRes?.frontData,
            c3: '' + !!(queryTaskRes?.values && queryTaskRes?.values?.length),
            c4: '' + !!ucParams,
            c5: '' + !!userInfo,
            c6: '' + !!queryTaskRes,
            c7: '' + !!multiBalance,
            c8:  Object.keys(resourceCodesInfoRes?.data ?? {}).length,
            c9: getParam('entry') ?? 'unknown',
            bl3: JSON.stringify(firstData),
          });

        }
      } catch (err) {
        tracker.log({
          category: WPK_CATEGORY_MAP.GET_FIRST_DATA,
          msg: '数据解析异常',
          sampleRate: 1,
          w_succ: 0,
          bl2: err?.message,
          bl3: JSON.stringify(firstData),
        });
      }
    } else {
      tracker.log({
        category: WPK_CATEGORY_MAP.GET_FIRST_DATA,
        msg: '获取失败',
        sampleRate: 1,
        w_succ: 0,
      });
    }
  };

  async componentDidMount() {
    perfMark(renderAction.hydrated);
    setTimeout(() => {
      // 模拟业务标记T2
      perfMark(renderAction.t2);

      // 上报渲染流程性能监控参数
      const logParams = getPerfWpkLogParams();
      if (logParams?.wl_avgv1 && logParams.wl_avgv1 < 5000) {
        // 过滤无效数据
        tracker.log({
          category: WPK_CATEGORY_MAP.RENDER_MONITOR,
          ...logParams,
        });
        console.log('渲染流程性能监控参数 >> ', logParams);
      }
    }, 1000);

    // 延迟一定时间再上报, 因为非离线包模式下，页面加载耗时有可能超过3秒
    setTimeout(() => {
      tracker.log({
        category: WPK_CATEGORY_MAP.DOM_PROCESSING,
        ...getPerfDomProcessingParams(),
      });
    }, 3500);
  }

  FirstScreen() {
    return <PageEntry />;
  }

  render() {
    return (
      <Provider store={store}>
        <View className="homeContainer">{this.FirstScreen()}</View>
      </Provider>
    );
  }
}

Home.getInitialProps = ssrGetInitialProps(getFirstData);

export default Home;
