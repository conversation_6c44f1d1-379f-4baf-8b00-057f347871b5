// 广告组件，目前未使用
import { createElement, Component } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from 'rax-image';
import { connect } from 'rax-redux';
import { StoreState, store, dispatch } from '@/store';
import ucapi from '@/utils/ucapi';
import {TaskInfo} from '@/store/models/task/types';
// import Loading from './Loading';
// import toast from '@/lib/universal-toast';
import { STORAGE_HC_AD_CARD_CACHE_KEY } from '@/constants/storage';
import { isHuiChuangAdEffectTask } from '../task/help';

import './index.scss';

import AdBgImg from './assets/ad_bg.png';
import CompleteIcon from './assets/complete.png';
import { TASK_STATUS } from '@/store/models/task/types';
import fact from '@/lib/fact';
import Fact from '@/components/Fact';

interface IProps {
  className?: string;
}
type ITotalProps = ReturnType<typeof mapState> & IProps;

interface IState {
  loading: boolean;
}

/**
 * 广告组件
 */
class Ad extends Component<ITotalProps, IState> {

  state: IState = {
    loading: true,
  }

  setAdClickStatus = (currentCardAd) => {
    const adInfo = JSON.parse(localStorage.getItem(STORAGE_HC_AD_CARD_CACHE_KEY) as string);
    // 先判断本地卡片信息是否与当前实际一样，不一样则 false
    if (adInfo) {
      if (currentCardAd?.slotId !== adInfo.slotId || currentCardAd?.accountId !== adInfo.accountId) {
        return false
      }
      if (adInfo?.clicked) {
        return true;
      }
    }
    return false;
  }

  showAdPrize = (currentCardAd) => {
    const adInfo = JSON.parse(localStorage.getItem(STORAGE_HC_AD_CARD_CACHE_KEY) as string);
    // 先判断本地卡片信息是否与当前实际一样，不一样则 false
    if (adInfo) {
      if (currentCardAd?.slotId !== adInfo.slotId || currentCardAd?.accountId !== adInfo.accountId) {
        return false
      }
      if (adInfo?.prize && currentCardAd.state === TASK_STATUS.TASK_CONFIRMED) {
        return adInfo?.prize && (adInfo?.prize / 100).toFixed(2) || true;
      }
    }
    return false;
  }

  handleClickAd = async (task: TaskInfo | null) => {
    console.log('点击广告', task);
    if (!task) return;
    if (task.token && task.slotId && task.accountId) {
      fact.click('fuli_click', {
        c: 'card',
        d: 'app',
        account_id: task.accountId as string
      });
      const result = await dispatch.ad.corpTaskClick({
        taskToken: task.token,
        slotId: task.slotId,
        accountId: task.accountId
      });
      if (result) {
        this.changeCardAdStatus(task, {
          click: 1,
        });
      }
    }
  }

  handleClickSkipBtn = () => {
    dispatch.route.back();
    ucapi.base.openURL({
      url: 'uclink://www.uc.cn/19b64348381e629f44f43b8506f24e92?action=launch&module=toolbar&switch_tab=user_center&entry=uclink_switch_tab'
    })
  }

  handleClickCompletedBtn = () => {
    dispatch.route.back();
    ucapi.base.openURL({
      url: 'uclink://www.uc.cn/19b64348381e629f44f43b8506f24e92?action=launch&module=toolbar&switch_tab=user_center&entry=uclink_switch_tab'
    })
  }

  // 更改卡片点击信息到本地
  changeCardAdStatus = (ad: TaskInfo, params: any) => {
    // 读取当前的本地卡片广告信息
    const lastCardAd = localStorage.getItem(STORAGE_HC_AD_CARD_CACHE_KEY);
    if (lastCardAd) {
      const adInfo = JSON.parse(lastCardAd);
      if (adInfo.slotId === ad.slotId && adInfo.accountId === ad.accountId) {
        if (Object.keys(params).includes('click')) {
          adInfo.clicked = params['click'];
        } else if (Object.keys(params).includes('prize')) {
          adInfo.prize = params['prize'];
        }
        localStorage.setItem(STORAGE_HC_AD_CARD_CACHE_KEY, JSON.stringify(adInfo));
      } else {
        this.setCardAdToStorage(ad, true);
      }
    }
  }

  // 储存卡片信息到本地
  setCardAdToStorage = (ad, clicked) => {
    // 获取当前时间戳
    const ts = new Date().getTime();
    // 计算过期时间戳，如果有配置时间，则使用配置的过期时间，没有则默认当天 24 点过期
    let hcAdCacheEndTs = 0;
    // 不同广告类型不同缓存时间
    const { huichuanBanner2SlotId, huichuanBidAdCacheHr, huichuanBrandAdCacheHr, huichuanPopSlotId, huichuanBrandTaskSlotId  } = store.getState().cms;
    const brandAdArr = [ huichuanBanner2SlotId, huichuanPopSlotId, huichuanBrandTaskSlotId];
    const hcAdCacheHr = brandAdArr.includes(ad?.slotId) ? huichuanBrandAdCacheHr : huichuanBidAdCacheHr;
    if (hcAdCacheHr) {
      hcAdCacheEndTs = ts + (hcAdCacheHr || 0) * 60 * 60 * 1000;
    } else {
      hcAdCacheEndTs = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000).getTime();
    }
    const adInfo = {
      slotId: ad.slotId,
      accountId: ad.accountId,
      clicked: clicked ? 1 : 0,
      expireTime: hcAdCacheEndTs,
    }
    localStorage.setItem(STORAGE_HC_AD_CARD_CACHE_KEY, JSON.stringify(adInfo));
  }

  // 获取本地存的卡片广告信息
  getLastCardAd = () => {
    // 判断上次卡片广告标识，缓存时间内返回缓存广告信息
    const currentTs = new Date().getTime();
    const lastCardAd = localStorage.getItem(STORAGE_HC_AD_CARD_CACHE_KEY);
    if (lastCardAd) {
      const adInfo = JSON.parse(lastCardAd);
      if (adInfo.expireTime > currentTs) {
        return adInfo;
      } else {
        localStorage.removeItem(STORAGE_HC_AD_CARD_CACHE_KEY);
      }
    }
    return null;
  }

  getShowAd = () => {
    const ads = this.getAds();
    if (ads.length > 0) {

      // todo: 这里拿到的 ads 数组顺序可能会变，不能保证是最终的，
      // ads[0] 可能是最先返回的广告，并不是列表最靠前的
      // 临时处理方法是稍微延后 卡片 组件的加载 500ms

      // ⬆最新处理方法，使用 Promise.all 请求广告保证广告数据全返回，这里拿到的就是排序后正确的顺序

      // 读取本地是否有卡片广告信息缓存
      const cacheLastCardAdInfo = this.getLastCardAd();
      if (cacheLastCardAdInfo) {
        // 拿到上次卡片的广告信息，对比当前可显示的ads
        const ad = ads.find(ad => ad.slotId === cacheLastCardAdInfo.slotId && ad.accountId === cacheLastCardAdInfo.accountId);
        if (ad) {
          return ad;
        }
      } else {
        // 没有卡片广告信息缓存⬇
        this.setCardAdToStorage(ads[0], false);
      }

      return ads[0];
    }
    return null;
  }

  getAds = () => {
    const { taskList, huichuanGameTaskSlotList } = this.props;
    // 筛选可显示的广告
    const ads = taskList.filter(task => {
      if (isHuiChuangAdEffectTask(task)&& !task.toDelete) {
        if (task.state === TASK_STATUS.TASK_CONFIRMED) {
          const lastCardAd = this.getLastCardAd();
          if (lastCardAd && lastCardAd.slotId === task.slotId && lastCardAd.accountId === task.accountId) return true;
        } else {
          if (!huichuanGameTaskSlotList.includes(task?.slotId || '')) return true;
        }
      }
      return false;
    })
    console.log('Ad组件卡片可显示的ads：', ads)
    return ads;
  }

  convertCashAmount = (amount: number) => {
    if (amount <= 0) return 0;
    return (amount / 100).toFixed(2);
  }

  showAwardModal = async (task: TaskInfo | null) => {
    if (task) {
      fact.click('fuli_click', {
        c: 'card',
        d: 'appdone',
        account_id: task.accountId as string
      });
      const prizes = await dispatch.task.completeCorpTask({ id: task.id, type: 'award', params: { task } });
      if (prizes && prizes?.length > 0) {
        const validPrizes = prizes.filter(prize => prize.win);
        if (validPrizes.length) {
          // todo: 储存实际金额到本地
          this.changeCardAdStatus(task, {
            prize: validPrizes[0].rewardItem?.amount,
          });
          // modal.openCashAward(validPrizes[0].rewardItem);
        }
      }
    }
  }

  getCashFromDesc = (desc: string | undefined) => {
    if (desc === undefined) return 0;
    const matchs = desc.match(/(\d+)/g);
    if (matchs) {
      return parseInt(matchs[0], 10).toFixed(2);
    }
    return 0;
  }

  render() {
    const { className } = this.props;
    const ad = this.getShowAd();
    let adAward = 0;
    if (ad) {
      adAward = this.showAdPrize(ad);
    }
    return (
      <>
        {/* <Loading x-if={this.state.loading} style={{ height: 668 }} /> */}
        <Fact
          x-if={ad}
          className={`${className || ''} ad-comp`}
          c="card"
          d={ad?.state === TASK_STATUS.TASK_DOING ? 'app' : 'appdone'}
          expoLogkey="fuli_expo"
          noUseClick
          expoExtra={{
            account_id: ad?.accountId as string
          }}
        >
          <View className="a-wrap">
            <Text className="a-tips">广告</Text>
            <View className="award-amount">
              <Text className="money-signal">¥</Text>
              <Text className="money-amount" x-if={!adAward}>{this.getCashFromDesc(ad?.desc)}</Text>
              <Text className="money-amount" x-if={adAward}>{adAward}</Text>
              <Text className="range-text" x-if={!adAward}>最高</Text>
            </View>
            <Image
              className="a-img"
              source={{ uri: AdBgImg }}
            >
            </Image>
            <View x-if={ad?.state === TASK_STATUS.TASK_DOING} className={`a-download-btn-wrap ${this.setAdClickStatus(ad) ? 'btn-clicked' : ''}`} onClick={() => this.handleClickAd(ad)}>
              <Text className="btn-text">{ad?.rewardItems[0].name || '下载APP领取'}</Text>
            </View>
            <View x-if={ad?.state === TASK_STATUS.TASK_COMPLETED} className="a-download-btn-wrap" onClick={() => this.showAwardModal(ad)}>
              <Text className="btn-text">领取奖励</Text>
            </View>
            <View x-if={ad?.state === TASK_STATUS.TASK_CONFIRMED} className="a-download-btn-wrap task-confirmed">
              <Text className="btn-text">奖励已领取</Text>
            </View>
            <Image x-if={ad?.state === TASK_STATUS.TASK_CONFIRMED || ad?.state === TASK_STATUS.TASK_COMPLETED} className="complete-icon" source={{ uri: CompleteIcon }}></Image>
            <Text className="complete-tips" x-if={this.setAdClickStatus(ad) && ad?.state === TASK_STATUS.TASK_DOING}>奖励会在审核后到账 预计1～2个工作日</Text>
          </View>
        </Fact>
      </>
    );
  }
}

const mapState = (state: StoreState) => {
  return {
    taskList: state.task.taskList,
    huichuanGameTaskSlotList: state.cms.huichuanGameTaskSlotList,
  }
}

export default connect(mapState, null)(Ad);
