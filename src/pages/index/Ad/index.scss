.ad-comp {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // animation: adscale 0.6s ease;

  .ad-title {
    font-family: PingFangSC-Semibold;
    font-size: 40rpx;
    color: #01255D;
    letter-spacing: 0;
    text-align: center;
    line-height: 58rpx;
    font-weight: bold;
  }
  .a-wrap {
    position: relative;
    margin: 30rpx auto 10rpx auto;
    width: 690rpx;

    .a-tips {
      position: absolute;
      right: 50rpx;
      top: 20rpx;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      color: #FFC59B;
      letter-spacing: 0;
      text-align: right;
      font-weight: 400;
    }

    .award-amount {
      position: absolute;
      top: 80rpx;
      width: 100%;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: center;

      .money-signal {
        font-family: D-DIN-Bold;
        font-size: 40rpx;
        color: #F02920;
        text-align: center;
        margin-top: 10rpx;
        margin-right: 10rpx;
      }
      .money-amount {
        font-family: D-DIN-Bold;
        font-size: 80rpx;
        color: #F02920;
        text-align: center;
      }
      .range-text {
        opacity: 0.5;
        font-family: PingFangSC-Regular;
        font-size: 24rpx;
        color: #F02920;
        letter-spacing: 0;
        text-align: center;
        line-height: 28rpx;
        font-weight: 400;
        width: 24rpx;
        margin-top: 20rpx;
        margin-left: 4rpx;
      }
    }

    .a-img {
      width: 690rpx;
      height: 378rpx;
    }

    .a-download-btn-wrap {
      position: absolute;
      bottom: 60rpx;
      left: calc(50% - 240rpx);
      width: 480rpx;
      height: 92rpx;
      background-image: radial-gradient(#FFF0CD -55%, #FFFCF4 42%, #FFF0CD 100%);
      box-shadow: 0 10rpx 20rpx 0 rgba(219,0,0,0.25), inset 0 -6rpx 20rpx 0 rgba(255,255,255,0.50);
      border-radius: 53rpx;

      .btn-text {
        height: 92rpx;
        line-height: 92rpx;
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color: #901906;
        letter-spacing: 0;
        text-align: center;
        font-weight: bold;
      }
    }

    .btn-clicked {
      bottom: 66rpx;
    }

    .task-confirmed {
      background-image: none;
      border: 4rpx solid #FFFFFF;
      box-shadow: none;
      opacity: 0.5;
      bottom: 60rpx;

      .btn-text {
        color: #FFF5DA;
      }
    }

    .complete-icon {
      position: absolute;
      top: 90rpx;
      left: 40rpx;
      width: 128rpx;
      height: 128rpx;
    }

    .complete-tips {
      position: absolute;
      bottom: 20rpx;
      width: 100%;
      text-align: center;
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      color: #FFFFFF;
      letter-spacing: 0;
      text-align: center;
      font-weight: bold;
    }
  }
}

@keyframes adscale {
  0%{
    transform: scale(0);
  }
  30%{
    transform: scale(0);
  }
  80%{
    transform: scale(1.05);
  }
  100%{
    transform: scale(1);
  }
}
