import { createElement, Component, CSSProperties } from 'rax';
import View from 'rax-view';
import Image from 'rax-image';
import { connect } from 'rax-redux';
import { StoreState } from '@/store';
import LoadingIcon from '../assets/loading.png';

import './index.scss';

class Loading extends Component<IProps, IState> {

  render() {
    const { style = {}, className = '' } = this.props;
    return <View className={`loading-icon-wrap ${className}`} style={style}>
      <Image className="loading-icon" source={{ uri: LoadingIcon }}></Image>
    </View>
  }
}

interface IState {

}

const mapState = (state: StoreState) => ({
});
type IProps = ReturnType<typeof mapState> & {
  className?: string;
  style?: CSSProperties;
};
export default connect(mapState)(Loading)
