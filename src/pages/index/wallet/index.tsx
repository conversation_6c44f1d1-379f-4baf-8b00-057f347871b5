import { StoreState, StoreDispatch, dispatch } from '@/store';
import { Component, createElement } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import cz from 'classnames';
import Image from '@/components/image';
import { connect } from 'rax-redux';
// import { history } from 'rax-app';
import { PAGE_ID } from '@/store/models/route';
import { TaskInfo, TASK_STATUS, RewardItem } from '@/store/models/task/types';
import './index.scss';
import { convertCash2Display, convertCoin2CashDisplay } from '@/utils/amount';
import fact from '@/lib/fact';
import Fact from '@/components/Fact';
// import config from "@/config";
// import ucapi from "@/utils/ucapi";
import { getAward } from '@/pages/index/SigninNew/useSingData'
import {matrixing} from '@/store/models/currency/helper';
import shaped from '@/pages/index/SigninNew/assect/shoe-shaped.png';
import redPacket from '@/pages/index/SigninNew/assect/red-packet.png';
import IngotIcon from './images/<EMAIL>';
import {execWithLock} from "@/utils/lock";
import {taskActionHandler} from "@/pages/index/task/help";

class Wallet extends Component<IProps, IState> {
  topLottieAni;
  componentDidMount() {
    // history.push('/flow');
  }

  handleWithdraw = (type: 'coin' | 'cash') => {
    // const url = config.link.wallet;

    // execWithLock('to_withdraw', (unlockFn) => {
    //   ucapi.base.openURL({ url });
    //   setTimeout(() => {
    //     unlockFn()
    //   }, 1000)
    // })
    dispatch.currency.updateCurrency({
      flowPageTabKey: type === 'coin' ? 1 : 2
    })
    // fact.click('money_cash_click', { c: 'money', d: 'cash' });
    if (type === 'cash') {
      fact.click('fuli_click', {
        c: 'header',
        d: 'coin',
        count: this.props.coin
      });
    } else {
      fact.click('fuli_click', {
        c: 'header',
        d: 'cash',
        count: this.props.amount2Cash
      });
    }
    // history.push('/flow');
    dispatch.route.push(PAGE_ID.flow);
  };

  handleClickTopTask = async () => {
    const { taskDisplayOnTop } = this.props
    if (taskDisplayOnTop) {
      fact.click('top_task_click', {
        c: 'task',
        d: 'top',
        clicl_action: 'gotask',
        task_id: taskDisplayOnTop.id,
        module: 'top',
        task_name: taskDisplayOnTop.name,
        taskclassify: taskDisplayOnTop?.taskClassify || '',
        groupcode: taskDisplayOnTop?.groupCode || '',
        award_amount: taskDisplayOnTop?.rewardItems[0]?.amount || '',
      });
      await execWithLock(
        'finish_task_lock',
        async () => {
          await taskActionHandler(taskDisplayOnTop);
        },
        3000,
      );
    }
  }
  componentDidUpdate(prevProps): void {
    if (prevProps?.taskDisplayOnTop?.id !== this.props?.taskDisplayOnTop?.id) {
      let displayOnTopImage = this.props.taskDisplayOnTop?.extra?.displayOnTopImage
      if (displayOnTopImage?.includes('.json')) {
        this.topLottie(displayOnTopImage);
      }
    }
  }
  // 右上角lottie动画
  topLottie = (url) => {
    let root = document.getElementById('top-task-lottie') as HTMLElement;
    if (root) {
      // 非首屏核心元素、lottie动画延后1S在展示
      setTimeout(() => {
        import('lottie-web').then((module) => {
          const lottie = module.default;
          if (this.topLottieAni) {
            this?.topLottieAni?.destroy()
          }
          this.topLottieAni = lottie.loadAnimation({
            container: root,
            renderer: 'canvas',
            loop: true,
            autoplay: true,
            path: url,
          });
        });
      }, 1000);
    }
  };
  // 右上角如果是去农场任务展示配置的图片 如果非农场任务展示默认的样式入口
  getTopTaskRender = (taskDisplayOnTop, hasAward) => {
    const { displayOnTopImage } = taskDisplayOnTop?.extra || null;
    if (displayOnTopImage) {
      return (
        <View className="top-task-farm">
          <View id="top-task-lottie">
            {!displayOnTopImage?.includes('.json') && <Image className="top-task-img" source={displayOnTopImage} />}
          </View>
          <View className="top-task-text">
            <View className={`text-container ${hasAward ? 'text-container-animation' : ''}`}>
              <View className="text-container-item">{taskDisplayOnTop?.extra?.displayOnTopName || ''}</View>
              {hasAward && (
                <View className="text-container-item text-container-item-award">
                  领
                  <Image
                    className="item-award-icon"
                    source={IngotIcon}
                    style={{
                      width: '40rpx',
                      height: '40rpx',
                    }}
                  />
                  {getAward(taskDisplayOnTop)?.replace(/[^(\d|\.)]/g, '')}
                </View>
              )}
            </View>
          </View>
        </View>
      );
    } else {
      return (
        <>
          <View className="top-task-content" />
          <View className="top-task">
            <Text className="reward-des">{taskDisplayOnTop?.extra?.displayOnTopName || ''}</Text>
            <View className="reward-warp">
              {hasAward ? (
              <>
                <Image className="reward-icon" source={IngotIcon} style={{
                  width: '40rpx',
                  height: '40rpx',
                }} />
                <Text className="reward-money din-num">{
                getAward(taskDisplayOnTop)?.replace(/[^(\d|\.)]/g, '')
              }</Text>
              </>
              ) : <Text className="reward-money din-num reward-text">{taskDisplayOnTop?.btnName || '入口'}</Text>}
            </View>
          </View>
        </>
      );
    }
  };
  render() {
    const { coin, amount2Cash, indexResource, taskDisplayOnTop } = this.props
    // const rewardItems = getTomorrow(signList)
    // const icon = checkIcon(rewardItems)
    // const nextSignTask = signList.find(sign => sign.state === TASK_STATUS.TASK_PRE_TASK_NOT_FINISH)
    const showTopTask = taskDisplayOnTop && taskDisplayOnTop.state === TASK_STATUS.TASK_DOING;
    const hasAward = !!(taskDisplayOnTop?.rewardItems?.length && taskDisplayOnTop?.rewardItems[0]?.amount);
    return (
      <>
        <View className={cz('container-wallet')}>
          <Fact
            className="wrap-coin"
            onClick={() => this.handleWithdraw('coin')}
            c="header"
            d="coin"
            expoLogkey="fuli_expo"
            expoExtra={{
              count: coin
            }}
            noUseClick
          >
            <View className="coin-num-wrap">
              <View className="coin-num-wrap-money" style={{color: indexResource?.fontColor?.money}}>{coin}</View>
              <View className="coin-num-wrap-intro" style={{color: indexResource?.fontColor?.name}}>元宝</View>
              <Image className="icon-next" source={indexResource.nextIcon} style={{ width: '24rpx', height: '24rpx' }} />
            </View>
          </Fact>
          <Fact
            className="wrap-money"
            onClick={() => this.handleWithdraw('cash')}
            c="header"
            d="cash"
            expoLogkey="fuli_expo"
            expoExtra={{
              count: amount2Cash
            }}
            noUseClick
          >
             <Text className="money-intro" style={{color: indexResource?.fontColor?.name}}>现金余额</Text>
            <Text className="money-num din-num" style={{ color: indexResource.fontColor.money }}>¥{amount2Cash}</Text>
          </Fact>
        </View>
        <Fact
          x-if={showTopTask}
          className="top-task-wrapper"
          onClick={this.handleClickTopTask}
          c="task"
          d="top"
          expoLogkey="top_expo"
          expoExtra={{
            task_id: taskDisplayOnTop?.id,
            module: 'top',
            task_name: taskDisplayOnTop?.name,
            taskclassify: taskDisplayOnTop?.taskClassify || '',
            groupcode: taskDisplayOnTop?.groupCode || '',
            award_amount: taskDisplayOnTop?.rewardItems[0]?.amount || ''
          }}
          noUseClick
          >
          {this.getTopTaskRender(taskDisplayOnTop, hasAward)}
        </Fact>
        {/*<Fact x-else expoLogkey="fuli_expo" c="bubble" d="sign" className="sign-entrance" x-if={nextSignTask && isLogin}>*/}
        {/*  <Text className="reward-des">明日签到{rewardItems?.mark?.includes('lottery') ? '最高' : '可得'}</Text>*/}
        {/*  <View className="reward-warp">*/}
        {/*    <Image className="reward-icon" source={icon} style={{*/}
        {/*      width: '40rpx',*/}
        {/*      height: '40rpx',*/}
        {/*    }} />*/}
        {/*    <Text className="reward-money din-num">{*/}
        {/*      nextSignTask && getAward(nextSignTask)?.replace(/[^(\d|\.)]/g, '')*/}
        {/*    }</Text>*/}
        {/*  </View>*/}
        {/*</Fact>*/}
      </>
    );
  }
};

function getTomorrow(signList: TaskInfo[]): RewardItem | undefined {
  const sign = signList.find(sign => sign.state === TASK_STATUS.TASK_PRE_TASK_NOT_FINISH) || signList[0]
  const rewardItems = sign?.rewardItems[0];
  return rewardItems
}

function checkIcon(rewardItem: RewardItem | undefined) {
  if (!rewardItem) return shaped

  if (rewardItem.mark === 'cash') return redPacket
  if (rewardItem.mark?.includes('cash_lottery')) return redPacket

  return shaped
}

interface IState {

}

const mapState = (state: StoreState) => {
  const { coin, amount = 0 } = state.currency;
  const coin2Cash = convertCoin2CashDisplay(coin);
  const amount2Cash = convertCash2Display(amount);
  const taskDisplayOnTop = state.task.taskDisplayOnTop;
  return {
    coin,
    amount2Cash,
    coin2Cash,
    indexResource: state.app.indexResource,
    signList: state.task.newNewSignList,
    isLogin: state.user.isLogin,
    taskDisplayOnTop,
  };
};
const mapDispatch = (dispatch: StoreDispatch) => ({});

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {};
export default connect(mapState, mapDispatch)(Wallet)
