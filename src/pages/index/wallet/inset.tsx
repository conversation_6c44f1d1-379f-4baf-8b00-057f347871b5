import { StoreState, dispatch } from '@/store';
import { createElement, useCallback, memo } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Image from '@/components/image';
import { connect, ConnectedProps } from 'rax-redux';
import { PAGE_ID } from '@/store/models/route';
import './index.scss';
import { convertCash2Display } from '@/utils/amount';
import fact from '@/lib/fact';
import Fact from '@/components/Fact';
import { openURL } from '../task/help';

// 常量定义
const WALLET_TYPES = ['coin', 'cash', 'withdraw'] as const;
type WalletType = typeof WALLET_TYPES[number];

// 类型定义
interface FactClickParams {
  c: string;
  d: string;
  count: number;
}

const mapState = (state: StoreState) => {
  const { coin, amount = 0 } = state.currency;
  return {
    coin,
    amount2Cash: convertCash2Display(amount),
    isLogin: state.user.isLogin,
    taskDisplayOnTop: state.task.taskDisplayOnTop
  };
};

const connector = connect(mapState);
type Props = ConnectedProps<typeof connector>;

// 抽取常量
const FACT_COMMON_PROPS = {
  c: 'header',
  expoLogkey: 'fuli_expo',
  noUseClick: true,
} as const;

const InsetWallet = memo(({
  coin,
  amount2Cash,
}: Props) => {

  const logFactClick = useCallback((params: FactClickParams) => {
    fact.click('fuli_click', params);
  }, []);

  const handleWithdraw = useCallback((type: WalletType) => {
    dispatch.currency.updateCurrency({
      flowPageTabKey: type === WALLET_TYPES[0] ? 1 : 2
    });

    // 记录点击事件
    const isCoin = type === WALLET_TYPES[0];
    logFactClick({
      c: 'header',
      d: isCoin ? WALLET_TYPES[0] : WALLET_TYPES[1],
      count: isCoin ? coin : amount2Cash
    });

    openURL(`${window.location.href}${PAGE_ID.flow}`)
  }, [coin, amount2Cash, logFactClick]);


  return (
    <View className='inset-wallet-comp'>
      <Fact
        {...FACT_COMMON_PROPS}
        className="inset-wallet-money"
        onClick={() => handleWithdraw(WALLET_TYPES[0])}
        d={WALLET_TYPES[0]}
        expoExtra={{ count: coin }}
      >
        <Text className="inset-wallet-money-num">{coin}</Text>
        <Text className="inset-wallet-money-intro">元宝</Text>
      </Fact>
      <Fact
        {...FACT_COMMON_PROPS}
        className="inset-wallet-coin-num-wrap"
        onClick={() => handleWithdraw(WALLET_TYPES[1])}
        d={WALLET_TYPES[1]}
        expoExtra={{ count: amount2Cash }}
      >
        <Text className="inset-wallet-coin-num-unit">¥</Text>
        <Text className="inset-wallet-coin-num">{amount2Cash}</Text>
        <Text className="inset-wallet-coin-intro">现金</Text>
      </Fact>
      <View className="inset-wallet-coin-intro-btn" onClick={() => handleWithdraw(WALLET_TYPES[2])}>
        <Text className="inset-wallet-coin-intro-btn-text">提现/兑换</Text>
        <Image
          source={'https://gw.alicdn.com/imgextra/i3/O1CN01n4BE921ZtvQnr9OUy_!!6000000003253-55-tps-10-10.svg'}
          style={{ width: '20rpx', height: '20rpx' }}
        />
      </View>
    </View>
  );
});

// 添加组件显示名称，方便调试
InsetWallet.displayName = 'InsetWallet';

export default connector(InsetWallet);
