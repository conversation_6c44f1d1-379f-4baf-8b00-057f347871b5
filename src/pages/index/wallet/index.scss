.container-wallet {
  flex: 1;
  position: relative;
  z-index: 1;
  .wrap-coin {
    flex-direction: row;
    width: fit-content;
    padding-left: 10rpx;
    align-items: baseline;
    .coin-num-wrap {
      display: flex;
      flex-direction: row;
      align-items: center;
      .coin-num-wrap-money {
        font-family: D-DIN-Bold;
        font-size: 44rpx;
        color: #f02920;
        font-weight: 700;
      }
      .coin-num-wrap-intro {
        margin-left: 6rpx;
        font-size: 30rpx;
        color: #01255d;
        font-weight: 700;
      }
      .coin-num-wrap-tag {
        margin-left: 6rpx;
        padding: 4rpx 8rpx;
        font-family: PingFangSC-Semibold;
        font-size: 24rpx;
        color: #F02920;
        font-weight: 600;
        background: #FFFFFF;
        border-radius: 20rpx 20rpx 20rpx 0;
      }
    }

  }
  .icon-next {
    uc-perf-stat-ignore: image;
  }
  .num {
    font-family: D-DIN-Bold;
    font-size: 36rpx;
    color: #333;
    letter-spacing: 0;
  }
  .unit {
    font-family: PingFangSC-Medium;
    font-size: 32rpx;
    color: #333;
    letter-spacing: 0;
  }
  .intro {
    font-family: PingFangSC-Regular;
    font-weight: 700;
    font-size: 30rpx;
    color: #01255d;
    margin-left: 8rpx;
    margin-top: 8rpx;
  }
  .wallet-notice {
    width: 602rpx;
    height: 56rpx;
    border-radius: 8rpx;
    background: #fff9e1;
    line-height: 56rpx;
    color: #901906;
    font-size: 26rpx;
    letter-spacing: 0;
    text-align: center;
    margin-bottom: 12rpx;
  }
  .wrap-money {
    padding-left: 10rpx;
    flex-direction: row;
    // margin-bottom: 10rpx;
    width: fit-content;
    line-height: 36rpx;
    .money-num {
      font-size: 26rpx;
      font-weight: 700;
      color: #f02920;
      margin-left: 8rpx;
    }
    .money-intro {
      color: #01255d;
      font-size: 26rpx;
      font-weight: 700;
    }
    .money-intro-coin2cash {
      font-size: 24rpx;
      padding: 3rpx 10rpx;
      background: #FFFFFF;
      border-radius: 10px 10px 10px 0;
      font-weight: 700;
      color: #f02920;
      margin-left: 10rpx;
      display: flex;
      align-items: center;
      height: fit-content;
    }
  }
}

.inset-wallet-comp{
  display: flex;
  flex-direction: row;
  flex: 1;
  align-items: center;
  .inset-wallet-money,.inset-wallet-coin-num-wrap{
    span {
      display: inline-block;
    }
  }
  .inset-wallet-money{
    display: flex;
    flex-direction: row;
    align-items: baseline;
    .inset-wallet-money-num {
      font-size: 42rpx;
      font-weight: 700;
      line-height: 42rpx;
      font-family: D-DIN-Bold;
      color: #F02920;
    }
    .inset-wallet-money-intro{
      font-size: 24rpx;
      font-weight: 500;
      line-height: 34rpx;
      color: #01255D;
      margin-left: 6rpx;
      margin-bottom: -10rpx;
    }
  }
  .inset-wallet-coin-num-wrap{
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 28rpx;
    padding-left: 0;
    .inset-wallet-coin-num-unit{
      font-size: 28rpx;
      line-height: 34rpx;
      font-weight: 700;
      font-family: D-DIN-Bold;
      color: #F02920;
      margin-top: 2rpx;
      margin-right: 2rpx;
      
    }
    .inset-wallet-coin-num{
      font-size: 42rpx;
      font-family: D-DIN-Bold;
      color: #F02920;
      line-height: 42rpx;
      font-weight: 700;
    }
    .inset-wallet-coin-intro{
      font-size: 24rpx;
      font-weight: 500;
      line-height: 34rpx;
      color: #01255D;
      margin-left: 6rpx;
      margin-bottom: -10rpx;
    }
  }
  .inset-wallet-coin-intro-btn{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    padding: 8rpx 18rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    margin-right: 18rpx;
    .inset-wallet-coin-intro-btn-text{
      font-size: 22rpx;
      font-weight: 400;
      color: #12161A;
    }
  }
}

.wufu {
  .container-wallet {
    .wrap-money {
      padding-left: 0;
      .money-num {
        font-family: D-DIN-Bold;
        color: #ffffff;
        letter-spacing: 0;
      }
      .money-intro {
        font-family: PingFangSC-Semibold;
        color: #ffd7b7;
        letter-spacing: 0;
        font-weight: 600;
      }
    }
    .wrap-coin {
      padding-left: 0;
      .coin-num {
        font-family: D-DIN-Bold;
        color: #ffffff;
      }
      .coin-intro {
        font-family: PingFangSC-Semibold;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 600;
        margin-left: 2rpx;
      }
      .coin-desc {
        font-family: PingFangSC-Regular;
        font-size: 22rpx;
        color: #ffffff;
        letter-spacing: 0;
        font-weight: 400;
      }
    }
  }
}

.top-task-wrapper {
  position: relative;
  width: 152rpx;
  height: 104rpx;
  border-radius: 22rpx;
  overflow: hidden;
  right: 20rpx;
  top: 0;
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
  mask-image: radial-gradient(circle, white 100%, black 100%);
  .top-task-content {
    position: absolute;
    top: -40%;
    left: -16%;
    width: 200rpx;
    height: 200rpx;
    border-radius: 200rpx;
    animation: rotate 2s infinite linear;
    background-image: url("./images/<EMAIL>");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    animation: rotate 2s infinite linear;
    uc-perf-stat-ignore: image;
  }
  .top-task {
    width: 140rpx;
    height: 92rpx;
    background-image: linear-gradient(180deg, #EAF6FF 1%, #FFFFFF 98%);
    border-radius: 18rpx;
    position: absolute;
    top: 6rpx;
    left: 6rpx;
    justify-content: center;
    align-items: center;
    .reward-des {
      color: #01255D;
      text-align: center;
      font-weight: 600;
      font-family: PingFangSC-Regular;
      font-size: 20rpx;
      letter-spacing: 0;
      text-align: center;
      margin-bottom: 4rpx;
    }
    .reward-warp {
      flex-direction: row;
      justify-content: center;
      .reward-icon {
        uc-perf-stat-ignore: image;
      }
      .reward-money {
        font-family: D-DIN-Bold;
        font-size: 30rpx;
        color: #f02920;
        letter-spacing: 0;
        font-weight: 700;
      }
      .reward-text{
        font-size: 20rpx;
      }
    }
  }
  .top-task-farm {
    width: 152rpx;
    height: 104rpx;
    position: relative;
    #top-task-lottie {
      height: 100%;
      .top-task-img {
        width: 152rpx;
        height: 104rpx;
        background-size: cover;
        uc-perf-stat-ignore: image;
      }
    }
    .top-task-text {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 134rpx;
      height: 32rpx;
      overflow: hidden;
      background-image: linear-gradient(270deg, #f02920 0%, #ff8b80 100%);
      border: 1rpx solid rgba(255, 255, 255, 0.32);
      border-radius: 16rpx;
      line-height: 32rpx;
      padding: 0 8rpx;
      .text-container {
        .text-container-item {
          height: 32rpx;
          line-height: 32rpx;
          font-family: PingFangSC-Semibold;
          font-size: 22rpx;
          color: #ffffff;
          letter-spacing: 0;
          text-align: center;
          font-weight: 600;
        }
        .text-container-item-award {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: center;
          .item-award-icon {
            width: 30rpx;
            height: 26rpx;
            background-size: cover;
            uc-perf-stat-ignore: image;
          }
        }
      }
      .text-container-animation {
        animation: 4.5s moveup linear infinite normal
      }
    }
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.sign-entrance {
  background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.75) 100%);
  border-radius: 24rpx;
  padding: 20rpx;
  .reward-des {
    font-family: PingFangSC-Regular;
    font-size: 20rpx;
    color: #405a86;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
  .reward-warp {
    flex-direction: row;
    justify-content: center;
    .reward-icon {
      uc-perf-stat-ignore: image;
    }
    .reward-money {
      font-family: D-DIN-Bold;
      font-size: 30rpx;
      color: #f02920;
      letter-spacing: 0;
      font-weight: 700;
    }
  }
}
