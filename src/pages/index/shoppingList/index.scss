.shopping-list-wrap {
  width: 100%;
  padding: 0 24rpx;
  margin-top: 48rpx;
  img {
    uc-perf-stat-ignore: image;
  }
  .shopping-list-header {
    width: 702rpx;
    height: 72rpx;
    flex-direction: row;
    background-image: linear-gradient(270deg, #FF8B80 0%, #F02920 100%);
    border-radius: 36rpx;
    padding: 12rpx 4rpx 12rpx 12rpx;
    margin-bottom: 44rpx;
    .limit {
      width: 82rpx;
      line-height: 48rpx;
      padding: 0 10rpx;
      border-radius: 24rpx;
      background: #F45D56;
      color: #fff;
      font-size: 26rpx;
      font-weight: 700;
      text-align: center;
      margin-right: 12rpx;
    }
    .title {
      font-size: 28rpx;
      color: #FFFFFF;
      font-weight: 700;
      line-height: 48rpx;
    }
    .times {
      opacity: 0.5;
      font-size: 28rpx;
      color: #FFFFFF;
      letter-spacing: 0;
      line-height: 48rpx;
      margin-left: 8rpx;
    }
    .scroll-desc {
      font-size: 28rpx;
      color: rgba(255,255,255,0.50);
      flex-direction: row;
      align-items: center;
      justify-content: flex-end;
      flex: 1;
    }
  }
  .start-timer {
    background: #FFFFFF;
    border: 1rpx solid #DEE5EE;
    .limit {
      background: #FEEFE9;
      color: #FA6425;
      text-align: center;
      margin-right: 12rpx;
    }
    .title {
      color: #FA6425;
    }
    .times {
      opacity: 0.5;
      font-size: 28rpx;
      color: #B4C1D6;
      letter-spacing: 0;
      line-height: 48rpx;
      margin-left: 8rpx;
    }
  }
  .shopping-list {
    width: 702rpx;
    justify-content: space-between;
    flex-wrap: wrap;
    align-items: flex-start;
    .list-left {
      flex-basis: 50%;
      flex-direction: column;
    }
    .list-right {
      flex-basis: 50%;
      flex-direction: column;
    }
  }
  .list-loading {
    font-size: 24rpx;
    color: #B4C1D6;
    justify-content: center;
    margin: 20rpx;
  }
  .shopping-list-empty {
    margin-top: 60rpx;
    align-items: center;
    .title {
      font-size: 36rpx;
      color: #7E93B7;
      margin-bottom: 12rpx;

    }
    .desc {
      font-size: 28rpx;
      color: #B4C1D6;
      margin-bottom: 28rpx;
    }
    .retry-btn {
      width: 218rpx;
      height: 88rpx;
      justify-content: center;
      align-items: center;
      background: #EEF2F6;
      border-radius: 88rpx;
      font-size: 32rpx;
      color: #7E93B7;
      font-weight: 700;
      margin-bottom: 100rpx;
    }
  }
  .shopping-item {
    width: 342rpx;
    border-radius: 24rpx;
    overflow: hidden;
    border: 1rpx solid rgba(1,37,93,0.10);
    box-shadow: 0 0 30rpx 0 rgba(0,80,188,0.05);
    margin-bottom: 18rpx;
    .goods-detail {
      padding: 16rpx 20rpx;
      .title {
        font-size: 28rpx;
        color: #01255D;
        letter-spacing: 0;
      }
      .price-sell {
        align-items: flex-end;
        .price-icon {
          font-size: 24rpx;
          color: #FA6425;
          padding-bottom: 4rpx;
        }
        .price {
          font-size: 36rpx;
          color: #FA6425;
          font-weight: 700;
          margin-right: 12rpx;
        }
        .price-desc {
          font-size: 24rpx;
          color: #FA6425;
          margin-right: 10rpx;
          padding-bottom: 2rpx;
        }
        .sell-num {
          color: #B4C1D6;
          font-size: 24rpx;
          padding-bottom: 2rpx;
        }
      }
    }
  }
}
