import {Component, createElement} from 'rax';
import { connect } from 'rax-redux';
import View from 'rax-view';
import Image from '@/components/image';
import { StoreDispatch, StoreState} from '@/store';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category';
import './index.scss'
import ShoppingTaskProgress from "@/pages/index/shoppingTaskProgress";
import ScrollIcon from './assets/<EMAIL>';
import {TASK_EVENT_TYPE} from "@/store/models/task/types";
import { rewardDesc } from "@/pages/index/task/help";
import { IGoodsInfo } from "@/store/models/shop";
import Fact from "@/components/Fact";
import GoodsItem from './GoodsItem';
import { bindObserver, unbindObserver } from '@/lib/utils/help';

interface IState {
  leftGoodsList: IGoodsInfo[],
  rightGoodsList: IGoodsInfo[],
  // 是否展示组件
  renderComponent: boolean;
}

class shoppingList extends Component<IProps, IState> {
  state: IState = {
    leftGoodsList: [],
    rightGoodsList: [],
    renderComponent: false
  }

  async componentDidMount() {
    // 统一调整到距离底部900px 再展示
    const element = document.getElementById('shopping-list-render');
    element && bindObserver(
      element, 
      ()=> {
         this.setState({
            renderComponent: true
         });
        // 页面渲染后，不再观察
         element && unbindObserver(element);
      }, 
      ()=> {}, 
      {
      rootMargin: '900px'
    })
  }

  _renderGoodsList = (list) => {
    return list.map((item, index) => {
      // const hideSell = item.promotePrice && Number(item.promotePrice) > 99.99
      return <GoodsItem key={index} goodsInfo={item} curPageNo={this.props.curPageNo}/>
    })
  }

  retry = () => {
    tracker.log({
      category: WPK_CATEGORY_MAP.QUERY_ALIMAMA_GOODS_LIST,
      msg: '点击重试',
      w_succ: 0,
      c1: this.props.curPageNo + ''
    })
    return this.props.queryShoppingList({pageNo: 1})
  }

  render () {
    const { renderComponent } = this.state;
    const { shoppingList, showShoppingCountDown, shoppingTime, showShoppingRewardTip, alimamaShoppingTask, taskFinished, alimamaShoppingTargetTime } = this.props
    const hasGoods = shoppingList.length > 0;
    const leftList = shoppingList.filter((item:any, index) => {
      const isEven = index % 2 === 0;
      if (item.moveBy === 'toLeft') return true
      if (item.moveBy === 'toRight') return false
      return isEven
    })
    const rightList = shoppingList.filter((item:any, index) => {
      const isOdd = index % 2 === 1;
      if (item.moveBy === 'toLeft') return false;
      if (item.moveBy === 'toRight') return true;
      return isOdd
    })
    return (<View id="shopping-list-render">
      <View className="shopping-list-wrap" x-if={alimamaShoppingTask && renderComponent}>
        <View className={`shopping-list-header ${hasGoods && this.props.showShoppingCountDown ? 'start-timer' : ''}`} id="shoppingListHeader" >
          <View className="limit">限时</View>
          <View className="title">浏览商品{alimamaShoppingTargetTime}s领{rewardDesc(alimamaShoppingTask?.rewardItems[0])}</View>
          <View className="times">{ alimamaShoppingTask?.dayTimes?.progress}/{alimamaShoppingTask?.dayTimes?.target}次</View>
          <View x-if={!this.props.showShoppingCountDown} className="scroll-desc">
            <View>上滑</View>
            <Image source={ScrollIcon} style={{width: '64rpx', height: '64rpx'}}/>
          </View>
          <ShoppingTaskProgress showShoppingCountDown={showShoppingCountDown}
                                shoppingTime={shoppingTime}
                                showShoppingRewardTip={showShoppingRewardTip}
                                taskFinished={taskFinished} />
        </View>
        <View x-if={hasGoods} className="shopping-list row">
          <Fact className="shopping-list row" c="shoppingmall" d="feed" expoLogkey="shoppingfeed_expo" noUseClick>
            <View className="list-left">
              {this._renderGoodsList(leftList)}
            </View>
            <View className="list-right">
              {this._renderGoodsList(rightList)}
            </View>
          </Fact>
        </View>
        <View x-else className="shopping-list-empty">
          <View className="title">暂时没有商品</View>
          <View className="desc">先去做其他任务 稍后再来</View>
          <View className="retry-btn" onClick={this.retry}>重试</View>
        </View>
        <View className="list-loading row" x-if={this.props.listLoading}>
          更多好物加载中...
        </View>
      </View>
    </View>)
  }
}

const mapState = (state: StoreState) => {
  const alimamaShoppingTask = state.task?.resTaskList?.find(task => task.event === TASK_EVENT_TYPE.ALIMAMA_SHOP)
  return {
    shoppingList: state.shop?.shoppingList || [],
    curPageGoodsList: state.shop?.curPageGoodsList || [],
    taskList: state.task?.taskList,
    alimamaShoppingTask,
    curPageNo: state.shop?.curPageNo,
    alimamaShoppingTargetTime: state.app.alimamaShoppingTargetTime
  };
};

const mapDispatch = (dispatch: StoreDispatch) => ({
});


type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
  fixShoppingListHeader: boolean;
  showShoppingCountDown: boolean;
  shoppingTime: number;
  showShoppingRewardTip: boolean;
  taskFinished: boolean;
  queryShoppingList: ({pageNo: number}) => any;
  listLoading: boolean;
};
export default connect(mapState, mapDispatch)(shoppingList)
