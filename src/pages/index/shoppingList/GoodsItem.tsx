import { createElement, FC, useEffect } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import { IGoodsInfo } from "@/store/models/shop";
import tracker from "@ali/weex-toolkit/lib/tracker";
import { WPK_CATEGORY_MAP } from "@/constants/tracker_category";
import fact from "@/lib/fact/index";
import ucapi from "@/utils/ucapi";
import {dispatch} from "@/store";
import { addParams } from "@/utils/url";

interface IGoodsItem {
  goodsInfo: IGoodsInfo,
  curPageNo: number
}

const GoodsItem: FC<IGoodsItem> = ({goodsInfo, curPageNo}) => {
  useEffect(() => {
    dispatch.shop.goodsExpost(goodsInfo)
  }, [goodsInfo])

  const handleClick = (e, item) => {
    console.log('click goods:', item)
    const price = Math.ceil(Number(item.promotePrice || item.goodsPrice) / 100) * 100
    tracker.log({
      category: WPK_CATEGORY_MAP.CLICK_ALIMAMA_GOODS,
      w_succ: 1,
      sampleRate: 1,
      msg: `点击商品-第${curPageNo}页`,
      c1: String(curPageNo || 1),
      c2: String(price),
      c3: item.promotePrice || item.goodsPrice,
      bl1: item.eurl
    })
    fact.click('shoppingfeed_click', {
      c: 'shoppingmall',
      d: 'feed',
      goods_name: item.title,
      goods_price: '' + (item.promotePrice || item.goodsPrice)
    })
    ucapi.base.openURL({
      url: addParams(item.eurl, {
        uc_biz_str: 'S%3Acustom%7CC%3Atitlebar_hover_2%7CK%3Atrue%7CN%3Atrue'
      })
    })
  }

  return <View className="shopping-item" onClick={(e) => handleClick(e, goodsInfo)}>
    <Image className="goods-img" source={goodsInfo.tbGoodLink} style={{width: '342rpx'}} />
    <View className="goods-detail">
      <View className="title">{goodsInfo.title}</View>
      <View className="price-sell row">
        <View className="price-icon">¥</View>
        <View className="price din-num">{goodsInfo.promotePrice || goodsInfo.goodsPrice }</View>
        {/*<View x-if={item.promotePrice} className="price-desc">促销价</View>*/}
        <View className="sell-num">{goodsInfo.sell}人付款</View>
      </View>
    </View>
  </View>
}

export default GoodsItem
