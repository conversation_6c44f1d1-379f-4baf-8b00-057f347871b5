.shopping-progress-wrap {
  flex-direction: row;
  margin-left: 20px;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  .finish-desc {
    font-size: 28rpx;
    color: #7E93B7;
    width: 160rpx;
    padding-right: 30rpx;
    text-align: right;
  }
  .progress-circle {
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-out; /* Firefox 4 */
    -webkit-transition: all 0.3s ease-out; /* Safari 和 Chrome */
    -o-transition: all 0.3s ease-out; /* Opera */
    position: relative;
  }
  .ingot-icon {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
  }
  .award-tip {
    position: absolute;
    right: 0;
    top: -4rpx;
    z-index: 1;
    width: 152rpx;
    height: 56rpx;
    opacity: 0.8;
    transform: scale(0.8);
    border-radius: 56rpx;
    background: #FA6425;
    transition: all 0.5s ease-in-out;
    -webkit-transition: all 0.3s ease-out;
    -moz-transition: all 0.3s ease-out; /* Firefox 4 */
    -webkit-transition: all 0.3s ease-out; /* Safari 和 Chrome */
    -o-transition: all 0.3s ease-out; /* Opera */
    color: #fff;
    flex-direction: row;
    align-items: center;
    font-size: 28rpx;
    font-weight: 700;
    justify-content: center;
    overflow: hidden;
    .award-icon {
      margin-right: 2rpx;
    }
  }
  .number {
    color: #FA6425;
    font-size: 28rpx;
    font-weight: 700;
  }
  .unit {
    color: #FA6425;
    font-size: 28rpx;
    font-weight: 700;
  }
  svg {
    margin-left: 20rpx;
  }
  .circle-inner {
    fill: none;
    stroke: #EEF2F6;
    stroke-width: 3;
    stroke-linecap: round;
  }
  .circle-outer {
    fill: none;
    stroke: #FA6425;
    stroke-width: 3;
    stroke-linecap: round;
    transition: all 1s linear;
    transform:rotate(-90deg);
    transform-origin: center;
    transform-box:fill-box;
    z-index: 1;
  }
}
