import {Component, createElement} from 'rax';
import { StoreDispatch, StoreState} from '@/store';
import { connect } from 'rax-redux';
import View from "rax-view";
import Text from "rax-text";
import Image from "@/components/image";
import { INGOT_ICON } from '@/constants/static_img';
import './index.scss'
import {TASK_EVENT_TYPE} from "@/store/models/task/types";
interface IState {
  showRewardTip: boolean;
}

class ShoppingTaskProgress extends Component<IProps, IState> {
  getRewardVal = (rewardItems) => {
    if (!rewardItems || !rewardItems?.length) return '';
    if (rewardItems[0]?.mark?.indexOf('cash') > -1) {
      return rewardItems[0]?.amount / 100;
    }
    return rewardItems[0]?.amount;
  }

  render () {
    const { showShoppingCountDown, shoppingTime, showShoppingRewardTip, taskFinished, alimamaShoppingTask, alimamaShoppingTargetTime } = this.props
    let progress = Number((shoppingTime / alimamaShoppingTargetTime).toFixed(2));
    progress = progress > 1 ? 1 : progress
    const girth = 2 * Math.PI * 14
    const dasharray = `${progress * girth} ${girth}`
    return <View x-if={showShoppingCountDown} className="shopping-progress-wrap">
      <View x-if={taskFinished} className="finish-desc">已完成</View>
      <View x-else>
        <View className="progress-circle" style={{opacity: showShoppingRewardTip ? 0 : 1, transform: showShoppingRewardTip ? 'scale(0.8)' : 'scale(1)'}}>
          <Text className="number">{this.props.shoppingTime}</Text><Text className="unit">s</Text>
          <Image className="ingot-icon" source={INGOT_ICON} style={{width: '48rpx', height: '48rpx'}} />
          <svg width="32" height="32">
            <circle className="circle-inner" fill="none" cx="16" cy="16" r="14" stroke-dashoffset={314} />
            <circle x-if={progress} className="circle-outer" fill="none" cx="16" cy="16" r="14" stroke-dasharray={dasharray} />
          </svg>
        </View>
        <View className="award-tip" style={{opacity: showShoppingRewardTip ? 1 : 0, transform: showShoppingRewardTip ? 'scale(1)' : 'scale(0.8)'}}>
          <Image className="award-icon" source={INGOT_ICON} style={{width: '32rpx', height: '32rpx'}}></Image>
          <View className="coin-num">+{this.getRewardVal(alimamaShoppingTask?.rewardItems)}</View>
        </View>
      </View>
    </View>
  }
}

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
  showShoppingCountDown: boolean;
  shoppingTime: number;
  showShoppingRewardTip: boolean;
  taskFinished: boolean;
};

const mapState = (state: StoreState) => {
  const alimamaShoppingTask = state.task?.taskList?.find(task => task.event === TASK_EVENT_TYPE.ALIMAMA_SHOP)
  return {
    shoppingList: state.shop?.shoppingList,
    taskList: state.task?.taskList,
    alimamaShoppingTask,
    alimamaShoppingTargetTime: state.app.alimamaShoppingTargetTime
  };
};

const mapDispatch = (dispatch: StoreDispatch) => ({
});

export default connect(mapState, mapDispatch)(ShoppingTaskProgress)
