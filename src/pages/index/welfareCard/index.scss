.novel-welfare-comp{
  margin: 0 auto;
  margin-top: 24rpx;
  padding: 32rpx 40rpx 40rpx 40rpx;
  width: 690rpx;
  background-image: linear-gradient(0deg, #FFFFFF 0%, #F5F9FF 100%);
  border: 1rpx solid rgba(1,37,93,0.10);
  box-shadow: 0 0 15px 0 rgba(0,80,188,0.05), inset 0 0 30px 0 #FFFFFF;
  border-radius: 40rpx;
  box-sizing: border-box;

  .head-title{
    height: 40rpx;
    justify-content: space-between;
    align-items: center;
    .title-img{
      // width: 278rpx;
      height: 36rpx;
    }

    .right-btn-wrap{
      .btn-text{
        font-family: PingFangSC-Regular;
        font-size: 28rpx;
        color: #7E93B7;
        font-weight: 400;
      }
      .btn-icon{
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  .fact-task-item_last-child{
    border-bottom: none;
    .task-item{
      padding-bottom: 0;
    }
  }
  .novel-welfare-comp-task-item{
    border-bottom: none;
    .task-item{
      padding-top: 32rpx;
      padding-bottom: 10rpx;
    }
  }
}


