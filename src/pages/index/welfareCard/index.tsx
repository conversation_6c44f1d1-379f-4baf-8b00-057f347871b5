import { createElement, Fragment, useEffect, useMemo } from 'rax';
import './index.scss';

import { useSelector } from 'rax-redux';
import { dispatch, StoreState } from '@/store';
import CardItem from './cardItem';
import { TASK_EVENT_TYPE, TASK_STATUS } from '@/store/models/task/types';
import { ResourceDescriptions } from '@/store/models/resource/types';
import DoubleAwardBanner from '@/pages/index/DoubleAwardBanner';
import { getEvSub, isCloudDriveField, isMainField, isNovelField } from '@/lib/qs';
import { ifShowAdTask, hideUninstallTask } from '../task/help';
import config from '@/config';

export const getStorageKey = (resourceCode) => `${resourceCode}_${getEvSub()}`;

export const getBoolValue = (value: string): boolean => {
  if (value === '') return true;
  return value === 'true';
};

export const getInitFoldStatus = (resourceCode, isUnpack, isSubUnpack): boolean => {
  // note: storage为第一优先级，直接获取到storage的值为fold的状态，所以不用取反
  const cacheFoldStr = localStorage.getItem(getStorageKey(resourceCode)) || '';
  if (cacheFoldStr) {
    return getBoolValue(cacheFoldStr);
  }
  // 主场去各自的主场配置 isUnpack\isSubUnpack 配置含义都是默认展开配置，isFold为是否折叠，所以需要取反
  if (isMainField()) {
    // 对于三个资源都是主场
    return !getBoolValue(isUnpack);
  }

  if (isNovelField()) {
    // 小说分场只读小说资源在分场的配置
    if (resourceCode === 'uc_piggy_novel' || resourceCode === 'uc_piggy_tag_person') {
      // 对于小说资源和标签资源是分场
      return !getBoolValue(isSubUnpack);
    } else if (resourceCode === 'uc_piggy_clouddrive') {
      // 对于网盘资源是主场
      return !getBoolValue(isUnpack);
    }
  }

  if (isCloudDriveField()) {
    // 网盘分场只读网盘资源在分场的配置
    if (resourceCode === 'uc_piggy_clouddrive' || resourceCode === 'uc_piggy_tag_person') {
      // 对于网盘资源和标签资源是分场
      return !getBoolValue(isSubUnpack);
    } else if (resourceCode === 'uc_piggy_novel') {
      // 对于小说资源是主场
      return !getBoolValue(isUnpack);
    }
  }
  // note: 默认都是不折叠的，全部展开
  return false;
};

export default function Index() {
  let resourceCodeList = useSelector((state: StoreState) => state.cms.resourceCodeListSort || []);
  const resourceObj: ResourceDescriptions = useSelector((state: StoreState) => {
    return {
      uc_piggy_novel: state.resource.uc_piggy_novel || {},
      uc_piggy_clouddrive: state.resource.uc_piggy_clouddrive || {},
      uc_piggy_tag_person: state.resource.uc_piggy_tag_person || {},
      uc_piggy_limit_time: state.resource.uc_piggy_limit_time || {},
    };
  });

  useEffect(() => {
    setTimeout(() => {
      // 延时500ms再请求diamond配置，减少首屏请求
      dispatch.rightsGift.fetchDiamondData();
    }, 500);
  }, []);

  /**
   * 按照cms上resourceCodeList配置的code顺序给resourceObj的key进行排序
   */
  const newResourceObj = useMemo<ResourceDescriptions>(() => {
    let obj = {};
    resourceCodeList?.forEach((key) => {
      if (resourceObj.hasOwnProperty(key)) {
        obj[key] = resourceObj[key];
      }
    });
    return obj as ResourceDescriptions;
  }, [resourceCodeList, resourceObj]);

  return Object.entries(newResourceObj as ResourceDescriptions).map((resource) => {
    const resourceCode = resource?.[0];
    const taskList = resource?.[1]?.taskList || [];
    const {
      displayTaskIdList = [],
      isUnpack = '',
      isSubUnpack = '',
      title = '',
      titleText = '',
    } = resource?.[1]?.attributes || {};

    // 过滤掉阅读时长子任务
    let renderTaskList = taskList?.filter((task) => task?.event !== TASK_EVENT_TYPE.SUB_NOVEL_READ_MINS);
    // 过滤掉开启广告检测没有广告填充的、开启app安装检测未安装的
    renderTaskList = renderTaskList?.filter((task) => {
      if(task.state !== TASK_STATUS.TASK_DOING){
        return true;
      }
      return ifShowAdTask(task) && !hideUninstallTask(task, true, 'resource');
    });
    const foldStatus = getInitFoldStatus(resourceCode, isUnpack, isSubUnpack);

    if (!renderTaskList?.length) {
      return <Fragment></Fragment>;
    }

    if (resourceCode === config.limitTaskResourceCode && resourceObj[config.limitTaskResourceCode]?.taskList?.length) {
      return <DoubleAwardBanner />;
    }

    return (
      <CardItem
        key={resourceCode}
        titleText={titleText}
        taskList={renderTaskList}
        displayTaskIdList={displayTaskIdList}
        titleIcon={title}
        foldStatus={foldStatus}
        resourceCode={resourceCode}
        taskModule={resourceCode}
        moduleType={resourceCode}
      />
    );
  });
}
