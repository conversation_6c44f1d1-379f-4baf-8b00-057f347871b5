import { createElement, useMemo, useState } from 'rax';
import { useSelector } from 'rax-redux';
import View from 'rax-view';
import Image from '@/components/image';
import './index.scss';

import FoldIcon from './images/<EMAIL>';
import UnfoldIcon from './images/<EMAIL>';
import TaskItem from '../task/item';
import { TaskInfo, TASK_STATUS, TASK_EVENT_TYPE } from '@/store/models/task/types';
import DefaultTitleIcon from './images/default-title-icon.png';
import { checkTaskFinished } from '../task/help';
import fact from '@/lib/fact';
import Fact from '@/components/Fact';
import RightsGift from './components/rightsGift/index';
import { getStorageKey } from '.';
import { StoreState } from '@/store';
import { GiftListDrawCycle } from './components/rightsGift/const';

interface IProps {
  taskList: TaskInfo[];
  // 模块头图
  titleIcon: string;
  // title文字
  titleText: string;
  // 折叠状态
  foldStatus: boolean;
  // 折叠后要展示的任务ID数组
  displayTaskIdList: string[];
  // 资源code
  resourceCode: string;
  taskModule: string;
  moduleType: string;
}

function CardItem(props: IProps) {
  const { taskList, titleIcon, foldStatus = false, displayTaskIdList, taskModule, moduleType, resourceCode, titleText } = props;
  const [isFold, setIsFold] = useState(foldStatus);
  const { rightsGiftList } = useSelector((state: StoreState) => {
    return {
      rightsGiftList: state.rightsGift.rightsGiftList,
    };
  });

  const adTaskList = taskList.filter(task => task.event?.includes(TASK_EVENT_TYPE.VIDEO_AD));
  // 激励广告任务进度、副标题处理
  adTaskList?.forEach((adTask) => {
    const dayTimes = adTask.dayTimes || { target: 0, progress: 0 }
    // adTask.desc = `每天可完成${dayTimes.target}次, 已完成${dayTimes.progress}/${dayTimes.target}次`;
    if (dayTimes.progress < dayTimes.target && adTask.state === TASK_STATUS.TASK_CONFIRMED) {
      adTask.state = 0
    }
  })

  // 任务列表全部都包含折叠后需要展示的任务id时; 则隐藏掉折叠按钮
  const hideFold = taskList?.every((task) => displayTaskIdList?.includes(`${task?.id}`));
  const hasFinish = taskList?.some((item) => item?.state !== TASK_STATUS.TASK_DOING);
  // 当前resourceCode是否有权益，有权益就需要展示按钮
  const hasRightsGiftList = useMemo(() => {
    const currentCodeGiftList = rightsGiftList.filter((item) => item.classify === resourceCode && !(item.drawCycle === GiftListDrawCycle && !item.canDraw));
    const hasQuota = currentCodeGiftList.some((item) => item?.assetConsumeList?.[0]?.amount);
    return hasQuota.length > 0;
  }, [rightsGiftList, resourceCode]);

  const handleFold = () => {
    fact.click('task_button_click', {
      c: 'button',
      d: 'create',
      resource_location: resourceCode,
      resource_location_title: titleText,
      click_position: !isFold ? '展开' : '收起',
    });
    setIsFold(!isFold);
    const storageKey = getStorageKey(resourceCode);
    localStorage.setItem(storageKey, `${!isFold}`)
  };

  const handleClick = (task: TaskInfo) => {
    fact.click('resource_click', {
      c: 'list',
      d: 'withdraw',
      resource_location: resourceCode || '',
      resource_location_title: titleText || '',
      click_position: !isFold ? '展开' : '收起',
    });
  }

  return (
    <Fact
      key={resourceCode}
      c="button"
      d="create"
      expoLogkey="task_resource_exposure"
      noUseClick
      expoExtra={{
        resource_location: resourceCode || '',
        resource_location_title: titleText || '',
        resource_status: !isFold ? '展开' : '收起',
      }}
    >
      <View className="novel-welfare-comp">
        <View className="head-title row">
          <Image source={titleIcon || DefaultTitleIcon} className="title-img" />
          <View x-if={!hideFold || hasFinish || hasRightsGiftList} className="right-btn-wrap row" onClick={handleFold}>
            <View className="btn-text">{isFold ? '展开' : '收起'}</View>
            <Image source={isFold ? UnfoldIcon : FoldIcon} className="btn-icon" />
          </View>
        </View>
        {taskList?.map((renderItem, index) => {
          const isUnFold = displayTaskIdList?.includes(`${renderItem.id}`);
          const switchStatus = isUnFold && !checkTaskFinished(renderItem) ? (!isUnFold && isFold) : isFold;
          return (
            <Fact
              className={`fact-task-item novel-welfare-comp-task-item ${index === taskList.length - 1 ? 'fact-task-item_last-child' : ''}`}
              key={renderItem.id}
              style={{ display: switchStatus ? 'none' : 'block' }}
              c="list"
              d="withdraw"
              expoLogkey="resource_exposure"
              noUseClick
              expoExtra={{
                resource_location: resourceCode || '',
                resource_location_title: titleText || '',
                resource_click_position: !isFold ? '展开' : '收起',
              }}
              onClick={() => {
                handleClick(renderItem)
              }}
            >
              <TaskItem
                resourceCode={resourceCode}
                titleText={titleText}
                key={renderItem.id}
                index={index}
                taskInfo={renderItem}
                taskModule={taskModule}
                moduleType={moduleType}
              />
            </Fact>
          )
        })}
        <RightsGift switchStatus={isFold} resourceCode={resourceCode} />
      </View>
    </Fact>
  )
}

export default CardItem;
