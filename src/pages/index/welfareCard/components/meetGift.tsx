import { createElement } from 'rax';
import Text from 'rax-text';
import View from 'rax-view';
import Fact from '@/components/Fact';
import { TaskInfo } from '@/store/models/task/types';
import Image from '@/components/image';
import IngotIcon from '@/assets/<EMAIL>';
import { getTaskRewardItem, getExtraInfo } from '../../task/help';

interface IProps {
  handleClick: (e, taskInfo) => void | Promise<void>;
  renderBtn: () => JSX.Element;
  taskInfo: TaskInfo;
  index: number;
  taskModule: string;
  timemodule: string;
  resource_location: string;
  resource_location_title: string;
}

const MeetGift = (props: IProps) => {
  const { name, id, icon, rewardItems, desc } = props.taskInfo;
  const rewardIcon = rewardItems?.[0]?.icon || IngotIcon;
  const rewardName = getTaskRewardItem(props.taskInfo).name.replace('元宝', '');
  const showAddIcon = rewardItems?.[0]?.mark?.includes('coin') || rewardItems?.[0]?.mark?.includes('cash');
  const { showAwardText = true } = getExtraInfo(props.taskInfo);

  return (
    <Fact
      className="fact-task-item"
      c="task"
      d={`task${props.index + 1}`}
      expoLogkey="task_expo"
      noUseClick
      expoExtra={{
        task_id: id,
        module: props.taskModule,
        timemodule: props.timemodule || '',
        task_name: name,
        taskclassify: props.taskInfo?.taskClassify || '',
        groupcode: props.taskInfo?.groupCode || '',
        resource_location: props?.resource_location || '',
        resource_location_title: props.resource_location_title || '',
      }}
    >
      <View className="task-item common-task" onClick={props.handleClick}>
        {!!icon && <Image className="task-icon" source={icon} />}
        <View className="task-info">
          <Text className={`item-title`}>{name}</Text>
          <View className="gift-wrap row">
            <View className="gift-desc">{desc}</View>
            <Image x-if={showAwardText} className="gift-icon" source={rewardIcon} loading={"lazy"} />
            <View x-if={showAwardText} className="item-award">{showAddIcon ? '+' : ''}{rewardName}</View>
          </View>
        </View>
        {props.renderBtn()}
      </View>
    </Fact>
  )
}

export default MeetGift;
