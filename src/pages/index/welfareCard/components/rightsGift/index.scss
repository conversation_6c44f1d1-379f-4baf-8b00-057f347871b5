.rights-item-container {
  padding-bottom: 0 !important;
  &.task-item{
    padding-top: 32rpx;
  }
}
.rights-container {
  display: flex;
  flex-direction: column;
  .rights_title {
    font-family: PingFangSC-Semibold;
    font-size: 32rpx;
    color: #01255D;
    line-height: 40rpx;
    display: inline;
    font-weight: bold;
  }
  .rights_item_disable {
    border: 1px solid #DEE5EE !important;
    border-radius: 10rpx !important;
    background-image: linear-gradient(90deg, #F0F4FA 0%, #FFFFFF 100%) !important;
    .name {
      color: #7E93B7 !important;
    }
  }
  .rights_item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    border: 1rpx solid #FFE3BE;
    border-radius: 20rpx;
    width: 610rpx;
    height: 100rpx;
    background-image: linear-gradient(90deg, #FFF4E6 0%, #FFFFFF 100%);
    padding:0 30rpx;
    margin-top: 20rpx;
    .icon {
      width: 40rpx;
      height: 40rpx;
      uc-perf-stat-ignore: image;
    }
    .name {
      text-align: left;
      width: 300rpx;
      font-family: PingFangSC-Semibold;
      font-size: 28rpx;
      color: #901906;
      letter-spacing: 0;
      font-weight: bold;
      margin-left: 20rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .exchange-tips {
      position: absolute;
      top: 50%;
      margin-top: -22rpx;
      right: 30rpx;
      padding: 0 20rpx;
      background: #EEF2F6;
      border-radius: 27rpx;
      height: 44rpx;
      line-height: 44rpx;
      text-align: center;
      .text {
        font-family: PingFangSC-Semibold;
        font-size: 11px;
        color: #7E93B7;
        letter-spacing: 0;
        text-align: right;
        font-weight: bold;
      }
    }
    .exchange {
      position: absolute;
      top: 50%;
      margin-top: -22rpx;
      right: 30rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      border-radius: 20rpx;
      // width: 176rpx;
      height: 44rpx;
      justify-content: space-around;
      padding: 0 16rpx 0 8rpx;
      box-sizing: border-box;
      background: #FFFFFF;
      border: 1px solid rgba(240,41,32,0.25);
      border-radius: 14px;
      .ingot {
        width: 32rpx;
        height: 32rpx;
        background-image: url('https://gw.alicdn.com/imgextra/i2/O1CN01io6nXA29Ls9KXXEfo_!!6000000008052-2-tps-96-96.png');
        background-repeat: no-repeat;
        background-size: 32rpx;
        uc-perf-stat-ignore: image;
      }
      .amount {
        font-family: D-DIN-Bold;
        font-size: 11px;
        color: #F02920;
        letter-spacing: 0;
        text-align: right;
        font-weight: bold;
        margin: 0 8rpx;
      }
      .exchange-text {
        font-family: PingFangSC-Semibold;
        font-size: 11px;
        color: #F02920;
        letter-spacing: 0;
        text-align: right;
        font-weight: bold;
      }
    }
  }
}
