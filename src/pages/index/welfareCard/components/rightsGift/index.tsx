import { createElement, useCallback, useEffect, useMemo } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from '@/components/image';
import { useSelector } from 'rax-redux';
import { dispatch, StoreState } from '@/store';
import Fact from '@/components/Fact';
import toast from '@/lib/universal-toast';
import { IEquityGiftItem, exchangeFailFactText } from '@/store/models/rightsGift/typings';
import modal from '@ali/weex-rax-components/lib/base_modal';
import './index.scss';
import { MODAL_ID } from '@/components/modals';
import fact from '@/lib/fact';
import { getDateObj, inPeriod } from '@/utils/date';
import logoutCheck from '@/utils/logoutCheck';
import cz from 'classnames';
import { GiftListDrawCycle } from './const';

const categoryName = {
  uc_piggy_novel: '小说专属权益',
  uc_piggy_clouddrive: '网盘专属权益',
  uc_piggy_tag_person: '回归专属权益',
};
/**
 * 是否还有库存
 * 1、周期库存: 当前周期库存 大于 已兑换库存
 * 2、总库存: 总库存 大于 已兑换库存
 */
function isHasQuota(option: IEquityGiftItem) {
  // 是否为周期库存
  if (option.refreshQuota) {
    return option.availability > option.assigned;
  }
  // 总库存
  return option.quota > option.assigned;
}
function RightsGift(props: { resourceCode: string; switchStatus: boolean }) {
  const { resourceCode, switchStatus } = props;
  let { rightsGiftList, diamondData, coin, curTime } = useSelector((state: StoreState) => {
    return {
      rightsGiftList: state.rightsGift.rightsGiftList,
      coin: state.currency.coin,
      curTime: state.task.now,
      diamondData: state.rightsGift.diamondData,
    };
  });
  const hours = useMemo(() => {
    return getDateObj(curTime).hour;
  }, [curTime]);
  rightsGiftList = useMemo(() => {
    // 用户不能兑换: drawCycle=2147483647 && canDraw=false
    return rightsGiftList.filter((item) => item.classify === resourceCode && item?.assetConsumeList?.[0]?.amount && !(item.drawCycle === GiftListDrawCycle && !item.canDraw));
  }, [rightsGiftList, resourceCode]);

  if (rightsGiftList.length === 0) {
    return null;
  }

  const handleOptionItem = useCallback(
    (data: IEquityGiftItem, resourceCode) => {
      fact.click('exchange_click', {
        c: 'list',
        d: 'withdraw',
        exchange_name: data?.name || '',
        count: data?.assetConsumeList?.[0]?.amount || '',
        code: 'coin',
        resource_location: resourceCode,
        planid: data?.planId,
      });
      if (logoutCheck()) {
        return;
      }
      if (!diamondData) {
        return;
      }
      fact.click('exchange_resource_click', {
        c: 'list',
        d: 'withdraw',
        resource_location: resourceCode,
      });
      const { noticeStartTime, noticeEndTime } = diamondData?.noticeConfig || {};
      const showNotice = inPeriod(noticeStartTime, noticeEndTime, curTime);
      // 系统升级公告正在展示(系统在维护中)
      if (showNotice) {
        return;
      }
      // 是否达到兑换上限
      if (!data.canDraw && data.limitCode === 'AWARD:LIMIT_RECEIVE') {
        fact.event('exchange_click', {
          c: 'list',
          d: 'withdraw',
          exchange_name: data?.name || '',
          count: data?.assetConsumeList?.[0]?.amount || '',
          code: 'coin',
          resource_location: resourceCode,
          planid: data?.planId,
          if_success: '否',
          fail_reason: exchangeFailFactText[data.limitCode],
          planId: data.planId,
        });
        toast.show(`当前兑换次数已用完，每日中午12点更新 `);
        return;
      }

      if (coin < data.assetConsumeList?.[0]?.amount) {
        fact.event('exchange_click', {
          c: 'list',
          d: 'withdraw',
          exchange_name: data?.name || '',
          count: data?.assetConsumeList?.[0]?.amount || '',
          code: 'coin',
          resource_location: resourceCode,
          planid: data?.planId,
          if_success: '否',
          fail_reason: '余额不足',
          planId: data.planId,
        });
        toast.show('当前元宝不足');
        return;
      }

      // 库存不足
      if (!isHasQuota(data)) {
        fact.event('exchange_click', {
          c: 'list',
          d: 'withdraw',
          exchange_name: data?.name || '',
          count: data?.assetConsumeList?.[0]?.amount || '',
          code: 'coin',
          resource_location: resourceCode,
          planid: data?.planId,
          if_success: '否',
          fail_reason: '库存不足',
          planId: data.planId,
        });
        if (hours < 12 && data.refreshQuota) {
          toast.show(`12:00刷新份额，敬请期待`);
          return;
        }
        toast.show(`今日份额已兑完，请明天再来`);
        return;
      }
      fact.event('exchange_click', {
        c: 'list',
        d: 'withdraw',
        exchange_name: data?.name || '',
        count: data?.assetConsumeList?.[0]?.amount || '',
        code: 'coin',
        resource_location: resourceCode,
        planid: data?.planId,
        if_success: '是',
        fail_reason: '其他',
        planId: data.planId,
      });
      // 开始兑换
      modal.open(MODAL_ID.RIGHTS_EXCHANGE, { data, resourceCode });
    },
    [coin, hours, diamondData],
  );

  function renderExchangeBtn(rights) {
    return (
      <View className="exchange">
        <Text className="ingot"></Text>
        <Text className="amount">{rights.assetConsumeList?.[0]?.amount}</Text>
        <Text className="exchange-text">兑换</Text>
      </View>
    );
  }
  function renderTipsBtn(tipsText) {
    return (
      <View className="exchange-tips">
        <Text className="text">{tipsText || '今日兑完'}</Text>
      </View>
    );
  }
  function renderBtn(rights) {
    let showTips = false;
    let tipsText = '';
    const hasQuota = isHasQuota(rights);
    const limitReceive = !rights.canDraw && rights.limitCode === 'AWARD:LIMIT_RECEIVE';
    if (!hasQuota || limitReceive) {
      showTips = true;
      if (limitReceive) {
        tipsText = '已兑换';
      } else if (hours < 12 && rights.refreshQuota) {
        tipsText = '12:00刷新';
      } else {
        tipsText = '今日兑完';
      }
    }
    return (
      <Fact
        expoLogkey="exchange_exposure"
        noUseClick
        expoExtra={{
          exchange_name: rights.name,
          code: 'coin',
          resource_location: resourceCode,
          count: rights.assetConsumeList?.[0]?.amount,
          planid: rights.planId,
        }}
        c="list"
        d="withdraw"
        key={rights.id}
      >
        <View
          className={cz('rights_item', {'rights_item_disable': showTips })}
          onClick={() => {
            handleOptionItem(rights, resourceCode);
          }}
        >
          <Image className="icon" source={rights.icon}></Image>
          <Text className="name">{rights.name}</Text>
          {showTips ? renderTipsBtn(tipsText) : renderExchangeBtn(rights)}
        </View>
      </Fact>
    );
  }
  return (
    <Fact
      expoLogkey="exchange_resource_exposure"
      c="list"
      d="withdraw"
      noUseClick
      expoExtra={{
        resource_location: resourceCode,
      }}
    >
      <View className="task-item rights-item-container " style={{ display: switchStatus ? 'none' : 'block' }}>
        <View className="rights-container">
          <View className="rights_title">{categoryName[resourceCode]}</View>
          {rightsGiftList.map((rights) => {
            return renderBtn(rights);
          })}
        </View>
      </View>
    </Fact>
  );
}

export default RightsGift;
