.comp-time-task {
  position: relative;
  width: 690rpx;
  margin: 28rpx auto 0;
  border-radius: 40rpx;
  border: 1rpx solid rgba(1,37,93,0.10);
  background: #fff;
  overflow: hidden;
  img {
    uc-perf-stat-ignore: image;
  }
  .fact-task-item {
    border-bottom: none;
    .task-item {
      padding: 30rpx 20rpx 30rpx 30rpx;
      .task-info {
        width: 400rpx;
      }
      .btn-finish {
        background: #fff;
      }
      .btn {
        width: 124rpx;
        height: 56rpx;
        .btn-receive-text, .btn-amount-text, .btn-finish-text {
          font-size: 26rpx;
        }
      }
    }
  }
  .fact-task-item:nth-last-child(1) {
    .task-item {
      padding-top: 20rpx;
    }
  }
}
.feeds-task {
  position: relative;
  width: 100%;
}
#feedsTaskWrapper {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  pointer-events: none;
}
.feeds-task-item {
  position: relative;
  width: 610rpx;
  background-image: linear-gradient(180deg, #EEF2F6 0%, #F8FBFF 100%);
  border-radius: 14px;
  margin: 0 auto 40rpx;
  .item-title-desc {
    font-size: 26rpx;
    color: #F02920;
    margin: 24rpx 30rpx 0;
  }
  .bubble-arrow {
    position: absolute;
    top: -12rpx;
    left: 500rpx;
  }
}
.feeds-task-wrap {
  width: 100%;
  padding: 50rpx 0 30rpx;
  .task-icon {
    margin-right: 32rpx;
    border-radius: 88rpx;
  }
  .task-content {
    flex: 1;
    .content-main {
      padding: 0 40rpx;
      flex-direction: row;
      justify-content: space-between;
    }
    .task-desc {
      .task-title {
        font-size: 32rpx;
        color: #01255D;
        font-weight: 700;
      }
      .desc_text {
        font-size: 24rpx;
        font-weight: 700;
        line-height: 34rpx;
        color: #405A86;
        margin-top: 8rpx;
      }
    }
    .content-progress-wrapper {
      width: 100%;
      padding-left: 40rpx;
      justify-content: space-between;
      margin-top: 16rpx;
      flex-direction: row;
      .btn-close-count {
        width: 144rpx;
        height: 48rpx;
        border: 2rpx solid rgba(1, 37, 93, 0.1);
        border-radius: 48rpx;
        line-height: 48rpx;
        font-size: 22rpx;
        color: rgba(1, 37, 93, 0.5);
        text-align: center;
        font-weight: 700;
        margin-top: 22rpx;
      }
    }
    .content-progress {
      width: 588rpx;
      position: relative;
      margin-top: 25rpx;
      margin-right: 40rpx;
      .progress-content{
        position: relative;
        width: 100%;
        border-radius: 32rpx;
        overflow: hidden;
      }
      .progress-bar {
        width: 100%;
        height: 32rpx;
        background: #e2e6ee;
        border-radius: 32rpx;
      }
      .progress-bar-inner {
        position: absolute;
        left: 0;
        top: 0;
        height: 32rpx;
        border-radius: 32rpx 0 0 32rpx;
        background-image: linear-gradient(270deg, #F02920 0%, #FFA29D 98%);
        max-width: 100%;
      }
      .extra-task-award {
        position: absolute;
        right: -20rpx;
        top: -40rpx;
        z-index: 2;
        span {
          color: #FFF4C7;
          font-size: 24rpx;
          font-weight: 700;
          position: absolute;
          top: 56rpx;
          left: 32rpx;
        }
      }
      .get-extra-award-btn {
        position: absolute;
        right: -20rpx;
        top: -40rpx;
        z-index: 2;
        span {
          color: #FFF;
          font-size: 24rpx;
          font-weight: 700;
          position: absolute;
          top: 40rpx;
          left: 50rpx;
        }
      }
      .got-task-award {
        position: absolute;
        right: -20rpx;
        top: -40rpx;
        z-index: 2;
      }
      .progress-time {
        position: absolute;
        flex-direction: row;
        width: 76rpx;
        height: 76rpx;
        right: -32rpx;
        top: -32rpx;
        z-index: 1;
      }
      .time-num {
        // font-family: DINPro-Bold;
        font-size: 20rpx;
        color: #FFF;
        font-weight: 600;
        line-height: 32rpx;
        margin-left: -10rpx;
        text-align: center;
        width: 68rpx;
      }
      .progress-nodes {
        flex-direction: row;
        justify-content: space-between;
        margin-top: 24rpx;
        .nodes-num {
          position: relative;
          .gap-panel {
            position: absolute;
            width: 2rpx;
            height: 32rpx;
            background: #fff;
            top: -56rpx;
            left: 20rpx;
          }
        }
        .num {
          font-size: 24rpx;
          color: #7E93B7;
          font-weight: 700;
        }
      }
    }
  }
  .operate-area {
    flex-direction: row;
    justify-content: space-between;
    padding: 0 40rpx;
    margin-top: 48rpx;
    .btn-operate {
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 184rpx;
      height: 64rpx;
      line-height: 64rpx;
      border-radius: 64rpx;
      border: 2rpx solid rgba(240, 41, 32, 0.25);
      font-size: 28rpx;
      color: #F02920;
      font-weight: 700;
      img {
        margin-right: 6rpx;
      }
    }
  }
  .space-around {
    justify-content: space-around;
  }
  .button {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: row;
    width: 144rpx;
    height: 56rpx;
    background-size: cover;
    border-radius: 30rpx;
    justify-content: center;
    background-image: url("../../../assets/button_bg_red.png");
    margin-top: 4rpx;
    &.completed {
      // background-image: linear-gradient(to left, #FF9B34, #FFBF38);
      background-color: #FFA53D;
    }

    &.confirmed {
      background-image: url("../../../assets/button_bg_red.png");
    }
  }

  .button_text {
    white-space: nowrap;
    line-height: 40rpx;
    font-size: 26rpx;
    font-weight:700;
    color: #ffffff;
  }
  .btn-double {
    background-image: url("./assets/<EMAIL>");
    .button_text {
      color: #631313;
    }
  }
  .btn-view-news {
    width: 160rpx;
    height: 56rpx;
    border: 2rpx solid rgba(240, 41, 32, 0.25);
    line-height: 56rpx;
    border-radius: 56rpx;
    font-size: 26rpx;
    color: #FF1515;
    text-align: center;
  }
}

.in-double-bg {
  background-image: url("./assets/<EMAIL>");
  background-size: 100%;
  background-repeat: no-repeat;
}
