import {Component, createElement} from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import {connect} from 'rax-redux';
import {TASK_EVENT_TYPE, TASK_STATUS, TaskInfo} from '@/store/models/task/types';
// import { TASK_EVENT_TYPE } from '@/components/task/types/task';
import Image from '@/components/image';
import './index.scss';
import '../task/index.scss';
import {dispatch, store, StoreDispatch, StoreState} from '@/store';
// import uc from '@ali/weex-toolkit/lib/weex_config/uc';
import {execWithLock} from '@/utils/lock';
// import event from '@/utils/event';
import fact from '@/lib/fact';
import {LoginStatus} from '@/store/models/user';
import modal from '@/components/modals/modal'
import {openURL, taskActionHandler} from "@/pages/index/task/help";
import {appVersion, isLatestVersion, isIOS} from "@/lib/universal-ua";
import ucapi from '@/utils/ucapi';

// import VideoIcon from './assets/<EMAIL>'
// import TimeIcon from './assets/<EMAIL>';
import InfoIcon from './assets/<EMAIL>';
import NovelIcon from './assets/<EMAIL>';
import SearchIcon from './assets/<EMAIL>';
import { INGOT_ICON } from '@/constants/static_img';
import ExtraAward from './assets/<EMAIL>';
import GetExtraAward from './assets/<EMAIL>';
import GotAward from './assets/<EMAIL>';
import ArrowIcon from './assets/<EMAIL>';
import Fact from '@/components/Fact';
import Item from "../task/item";
import {getTaskDesc} from "@/store/models/task/helper";
import { bindObserver, unbindObserver } from '@/lib/utils/help';

interface IState {
  // 是否展示组件
  renderComponent: boolean;
}

enum TIME_TASK_TYPE {
  IFLOW = 'iflow',
  SEARCH = 'search',
  NOVEL = 'novel'
}

/** 信息流任务 */
class FeedsTask extends Component<IProps, IState> {
  state: IState = {
    renderComponent: false
  }
  handleToViewArticle = () => {
    const { feedsTask, readTaskConf} = this.props
    execWithLock('to_article_task', (unlockFn) => {
      fact.click('read_click', {
        c: 'read',
        d: 'click',
        double: this.props.doubleTask?.state === TASK_STATUS.TASK_DOING ? 0 : 1,
        task_id: feedsTask?.id || '',
        task_name: feedsTask?.name || '',
        taskclassify: feedsTask?.taskClassify || '',
        groupcode: feedsTask?.groupCode || '',
      });
      this.checkLogin() && openURL(feedsTask?.url || readTaskConf.newsLink)
      // taskActionHandler(this.props.feedsTask);
      setTimeout(() => unlockFn(), 500);
    });
  };

  handleToSearch = () => {
    const { feedsTask } = this.props
    execWithLock('to_search_task', (unlockFn) => {
      fact.click('read_click', {
        c: 'read',
        d: 'click2',
        double: this.props.doubleTask?.state === TASK_STATUS.TASK_DOING ? 0 : 1,
        task_id: feedsTask?.id || '',
        task_name: feedsTask?.name || '',
        taskclassify: feedsTask?.taskClassify || '',
        groupcode: feedsTask?.groupCode || '',
      });
      ucapi.mission.notifyClearWelfareBallCloseFlag()
      this.checkLogin() && openURL(this.props.readTaskConf.searchLink)
      setTimeout(() => unlockFn(), 500);
    });
  }

  checkLogin = () => {
    if (this.props.loginStatus !== LoginStatus.login) {
      dispatch.user.login();
      return false;
    }
    return true;
  }

  handleToNovel = () => {
    const { feedsTask } = this.props
    execWithLock('to_novel_task', (unlockFn) => {
      fact.click('read_click', {
        c: 'read',
        d: 'click3',
        double: this.props.doubleTask?.state === TASK_STATUS.TASK_DOING ? 0 : 1,
        task_id: feedsTask?.id || '',
        task_name: feedsTask?.name || '',
        taskclassify: feedsTask?.taskClassify || '',
        groupcode: feedsTask?.groupCode || '',
      });
      this.checkLogin() && openURL(this.props.readTaskConf.novelLink)
      setTimeout(() => unlockFn(), 500);
    });
  }

  isLatestVersion = () => {
    if (isIOS) {
      return false
    }
    if (appVersion) {
      return isLatestVersion(appVersion, '13.5.8.1120')
    }
    return !this.props.isOldVersion
  }

  showTouTiao = () => {
    // 极速版大包不展示看头条
    return !this.props.sv?.startsWith('ucliteplus')
  }


  showDoubleTask = () => {
    return this.isLatestVersion() && this.props.doubleTask
  }

  handleAwardDouble = () => {
    const taskInfo = this.props.doubleTask;
    if (!taskInfo) return
    fact.click('fuli_click', {
      c: 'read',
      d: 'double',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      taskclassify: taskInfo?.taskClassify,
      groupcode: taskInfo?.groupCode,
    });

    if (this.props.loginStatus !== LoginStatus.login) {
      dispatch.user.login();
      return
    }

    if (taskInfo.state === TASK_STATUS.TASK_DOING) {
      execWithLock(
        'finish_double_task',
        async () => {
          const res = await taskActionHandler(taskInfo);
          if (res !== undefined) {
            ucapi.mission.notifyReadMissionDouble();
            dispatch.task.queryTask(false);
          }
        },
        2000,
      );
    }
    modal.openDoubleAward()
  }

  getTaskTitle = () => {
    const descMap = {
      [TIME_TASK_TYPE.NOVEL]: '看小说',
      [TIME_TASK_TYPE.SEARCH]: '用搜索',
      [TIME_TASK_TYPE.IFLOW]: '看头条'
    }
    return descMap[this.getStoreTaskType()] || '做任务'
  }

  getStoreTaskType = () => {
    const storeTask = this.props.prtTaskList.find(task => task.event.includes('store'))
    if (!storeTask) return ''
    for (let key in TIME_TASK_TYPE) {
      if ((storeTask?.event || '').includes(TIME_TASK_TYPE[key])) {
        return TIME_TASK_TYPE[key]
      }
    }
    return ''
  }

  getArrowIconLeft = () => {
    const leftMap = {
      [TIME_TASK_TYPE.IFLOW]: '80rpx',
      [TIME_TASK_TYPE.SEARCH]: '290rpx',
      [TIME_TASK_TYPE.NOVEL]: '500rpx'
    }
    return leftMap[this.getStoreTaskType()] || '0'
  }

  handleGetExtraAward = () => {
    const feedsTask = this.props.feedsTask
    if (this.props.loginStatus !== LoginStatus.login) {
      dispatch.user.login();
      return
    }
    if (!feedsTask) return;
    execWithLock(
      'get-extra-award',
      async (unlockFn) => {
        await dispatch.task.complete({ id: feedsTask.id, type: 'award', params: {task: feedsTask} });
        unlockFn()
      },
    );
  }

  async componentDidMount() {
    // 统一调整到距离底部900px 再展示
    const element = document.getElementById('comp-time-task');
    element && bindObserver(
      element,
      ()=> {
         this.setState({
            renderComponent: true
         });
        //页面渲染后，不再观察
        element && unbindObserver(element);
      },
      ()=> {},
      {
      rootMargin: '900px'
    })
  }

  render() {
    const {renderComponent} = this.state;
    const { feedsTask, doubleTask, prtTaskList,  } = this.props
    const openDouble = doubleTask && (doubleTask.state === TASK_STATUS.TASK_COMPLETED || doubleTask.state === TASK_STATUS.TASK_CONFIRMED)
    const progressNodes = [0, 1/4, 2/4, 3/4, 1]
    let taskProgressPercent = 0
    if(feedsTask) {
      taskProgressPercent = (feedsTask.progress) * 100 / feedsTask.target
    }
    const storeTaskType = this.getStoreTaskType()
    const timeModule = storeTaskType === TIME_TASK_TYPE.IFLOW ? 'xxs' : storeTaskType
    const extraAwardAmount = feedsTask?.rewardItems?.[0]?.amount || 0
    const taskDesc = getTaskDesc(feedsTask?.event)

    return (
      <View id="comp-time-task" className={`${renderComponent && this.props.feedsTask ? 'comp-time-task' : ''}`}>
        <View className="feeds-task" x-if={renderComponent && this.props.feedsTask}>
          <Fact
            className={`feeds-task-wrap pure-bg row ${openDouble ? 'in-double-bg' : ''}`}
            c="read"
            d="display"
            expoLogkey="fuli_expo"
            noUseClick
            expoExtra={{
              double: openDouble ? 1 : 0,
              task_id: feedsTask?.id,
              task_name: feedsTask?.name,
              taskclassify: feedsTask?.taskClassify,
              groupcode: feedsTask?.groupCode,
            }}
          >
            {/*<Image className="task-icon" source={VideoIcon} style={{width: '88rpx', height: '88rpx'}} />*/}
            <View className="task-content">
              <View className="content-main">
                <View className="task-desc">
                  <View className="task-title">使用UC极速版领元宝</View>
                  <View x-if={taskDesc} className="desc_text din-num row">
                    <Image source={INGOT_ICON} style={{width: '34rpx', height: '34rpx'}}/>
                    { taskDesc }
                  </View>
                </View>
                <View x-if={this.showDoubleTask()} className={`button ${openDouble ? 'btn-double' : ''}`} onClick={this.handleAwardDouble}>
                  <View className="button_text">{openDouble ? '翻倍中' : '奖励翻倍'}</View>
                </View>
                <View x-if={!this.isLatestVersion()} className="btn-view-news" onClick={this.handleToViewArticle}>看头条</View>
              </View>
              <View className="content-progress-wrapper row">
                <View className="content-progress">
                  <View className="progress-content">
                    <View className="progress-bar" />
                    <View className="progress-bar-inner" style={{width: `${taskProgressPercent < 2.0 ? 2.0 : taskProgressPercent}%`}}>
                  </View>
                    {/*<View className="progress-time">*/}
                    {/*  <Image source={TimeIcon} style={{width: '76rpx', height: '76rpx'}}/>*/}
                    {/*</View>*/}
                  </View>
                  <View x-if={feedsTask?.state === TASK_STATUS.TASK_COMPLETED && extraAwardAmount} className="get-extra-award-btn" onClick={this.handleGetExtraAward}>
                    <Image source={GetExtraAward} style={{width: '118rpx', height: '84rpx'}} />
                    <Text>领取</Text>
                  </View>
                  <View x-elseif={feedsTask?.state === TASK_STATUS.TASK_DOING && extraAwardAmount} className="extra-task-award">
                    <Image source={ExtraAward} style={{width: '118rpx', height: '84rpx'}} />
                    <Text>{ extraAwardAmount }</Text>
                  </View>
                  <View x-elseif={feedsTask?.state === TASK_STATUS.TASK_CONFIRMED && extraAwardAmount} className="got-task-award">
                    <Image source={GotAward} style={{width: '118rpx', height: '84rpx'}} />
                  </View>
                  <View className="progress-nodes">
                    {
                      progressNodes.map((node, index) => {
                        return <View className="nodes-num num din-num" key={index}>
                          <View className="gap-panel" />
                          {/* @ts-ignore */}
                          {((node * feedsTask.target / 60) || 2).toFixed(0)}分钟{index === 4 ? `${extraAwardAmount ? '额外奖励' : ''}` : ''}
                        </View>
                      })
                    }
                  </View>
                </View>
              </View>
              <View x-if={this.isLatestVersion()} className={`operate-area ${this.showTouTiao() ? '' : 'space-around'}`}>
                <View x-if={this.showTouTiao()} className="btn-operate" onClick={this.handleToViewArticle}>
                  <Image source={InfoIcon} style={{width: '32rpx', height: '32rpx'}} />
                  看头条
                </View>
                <View className="btn-operate" onClick={this.handleToSearch}>
                  <Image source={SearchIcon} style={{width: '32rpx', height: '32rpx'}} />
                  去搜索
                </View>
                <View className="btn-operate" onClick={this.handleToNovel}>
                  <Image source={NovelIcon} style={{width: '32rpx', height: '32rpx'}} />
                  读小说
                </View>
              </View>
            </View>
          </Fact>
        </View>
        <View className="feeds-task-item" x-if={renderComponent && this.props.feedsTask && prtTaskList.length}>
          <Image className="bubble-arrow" source={ArrowIcon} style={{width: '30rpx', height: '12rpx', left: this.getArrowIconLeft()}} />
          <View className="item-title-desc">
            今天{this.getTaskTitle()} 额外有奖
          </View>
          {
            prtTaskList.map((taskInfo, index) => {
              return <Item className="time-task-item" key={taskInfo.id} taskInfo={taskInfo} index={index} taskModule={'时长任务'} moduleType='TIME' timemodule={timeModule} />
            })
          }
        </View>
      </View>
    );
  }
}

const mapState = (state: StoreState) => {
  const { taskList } = state.task
  const feedsTask = taskList.find(task => task.event === TASK_EVENT_TYPE.STORE_READ_TIME)

  // 时长加码任务展示顺序，信息流>搜索>小说
  let prtTaskList: TaskInfo[] = []
  prtTaskList = state.task.prtTaskList.filter(task => task.event.includes(TIME_TASK_TYPE.IFLOW))
  if (!prtTaskList.length) {
    prtTaskList = state.task.prtTaskList.filter(task => task.event.includes(TIME_TASK_TYPE.SEARCH))
  }
  if (!prtTaskList.length) {
    prtTaskList = state.task.prtTaskList.filter(task => task.event.includes(TIME_TASK_TYPE.NOVEL))
  }
  if (prtTaskList.length > 2) {
    prtTaskList.splice(2)
  }
  return {
    loginStatus: state.user.status,
    preTaskState: state.task.preTaskState,
    preTaskLeftTime: state.task.preTaskLeftTime,
    feedsTask,
    doubleTask: state.task.doubleTask,
    prtTaskList,
    welfareBallSettingEnable: state.task.welfareBallSettingEnable,
    readTaskConf: state.app.readTaskConf,
    sv: state.user?.sv,
    isOldVersion: state.task.isOldVersion
  }
};
const mapDispatch = (dispatch: StoreDispatch) => ({
});


type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
  taskInfo: TaskInfo
};
export default connect(mapState, mapDispatch)(FeedsTask)
