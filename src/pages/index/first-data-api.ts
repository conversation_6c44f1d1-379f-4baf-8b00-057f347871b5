// TIPS: 该模块会引入到mini-js中，应尽可能减少没必要的引用，以达到mini-js体积最小
import { getUserInfo, getUCParams } from '@/lib/ucapi';
import { queryTask, queryUserMultiBalance, getCmsData, queryResourceCodesInfo, queryEquityGiftList } from '@/http';
import ucapi from '@/utils/ucapi';
import { isNode } from 'universal-env';
import { isIOS } from '@/lib/universal-ua';

// GUIDANCE: 获取首屏数据方法 (需保证此方法在node和web都可调用)
// 接口出错时建议返回null，或直接抛出错误，否则会影响SSR失败时的前端hydrate。
// 首屏页面不依赖数据接口的情况，可直接返回空对象 {} (注意，非null)

/**
 * APP 检测
 */
async function appInstallCheck() {
  if (isNode || !ucapi?.biz?.queryApp) {
    return null;
  }
  const androidPkgList = [
    'com.greenpoint.android.mc10086.activity', // 中国移动
    'com.smile.gifmaker', // 快手
    'com.baidu.BaiduMap', // 百度地图
    'com.kuaishou.nebula', // 快手极速版
    'com.eg.android.AlipayGphone', // 支付宝
    'com.taobao.taobao', // 淘宝
    'me.ele', // 饿了么
    'com.dianping.v1', // 大众点评
    'com.dragon.read',  // 番茄小说
  ];
  const iosSchemaList = [
    'tbopen://', // 淘宝
    'taobao://', // 淘宝
  ];
  const appList = isIOS ? iosSchemaList : androidPkgList;
  const checkPromise = (appPkg: string[]) => {
    return new Promise(async (resolve) => {
      try {
        const res = await ucapi.biz.queryApp({
          cache_first: '0',
          pkgs: appPkg,
        });
        const appInstallInfo = (res || {});
        const result = Object.keys(appInstallInfo).map((appPkg) => {
          const installed = isIOS
          ? Boolean(appInstallInfo[appPkg].appName)
          : appInstallInfo[appPkg]?.appSize > 0 || appInstallInfo[appPkg]?.canOpen;
          return {
            appPkg,
            error: null,
            res: res[appPkg],
            installed
          }
        });
        resolve(result);
      } catch (error) {
        resolve([]);
      }
    });
  };
  const result = await checkPromise(appList);
  return result;
}
/**
 * 获取push 通知
 */
async function getPushState() {
  if (isNode || !ucapi?.biz?.getPushState) {
    return null;
  }
  let result;
  try {
    result = await ucapi.biz.getPushState();
  } catch (err) {
    console.error('[first-data-api] getPushState error', err);
  }
  return result;
}

/**
 * 设置默认浏览器
 */
async function getDefaultBrowser() {
  if (isNode || !ucapi?.biz?.getDefaultBrowser) {
    return null;
  }
  let result;
  try {
    result = await ucapi.biz.getDefaultBrowser();
  } catch (err) {
    console.error('[first-data-api] getDefaultBrowser error', err);
  }
  return result;
}

export function prefetchClientData() {
  try {
      // 先发起APP检测，不阻塞后续流程
    window.__APP_INSTALL_CHECK_PMS__ = appInstallCheck();
    // 获取push 通知
    window.__APP_PUSH_STATE__ = getPushState();
    // 获取设置浏览器
    window.__APP_DEFAULT_BROWSER__ = getDefaultBrowser();

  } catch(err) {
    console.error('prefetchClientData error', err);

  }
}


export async function getFirstData() {
  // TIPS:
  // Broccoli配置的运营数据 moduleData
  console.log('[first-data-api] getFirstData 获取首屏数据');
  // console.log(getModuleData());
  let data;
  try {
    let userInfo: any = null;
    try {
      userInfo = await getUserInfo();
    } catch (err) {
      console.error('[first-data-api] getUserInfo error', err);
    }
    
    const kps = userInfo?.kps_wg;

    // 福利分场兑换权益占1%，将接口移除首屏，缩减首屏响应体积
    const [taskRes, multiBalance, cmsResData, resourceCodesInfoRes, ucParams] = await Promise.all([
      queryTask(kps),
      queryUserMultiBalance(kps),
      getCmsData(true),
      queryResourceCodesInfo(kps),
      // queryEquityGiftList(kps),
      getUCParams(),
    ]);

    data = {
      ucParams,
      multiBalance,
      queryTaskRes: taskRes,
      cmsResData,
      resourceCodesInfoRes,
      userInfo,
      rightsGiftList: null,
      kps,
    };
  } catch (err) {
    console.error(`[first-data-api] getFirstData 获取首屏数据失败 | ${JSON.stringify(err)}`, err);
    data = null;
  }
  return data;
}
