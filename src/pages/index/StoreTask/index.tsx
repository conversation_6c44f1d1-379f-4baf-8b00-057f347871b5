import { createElement, Component } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { connect } from 'rax-redux';
import { TaskInfo, TASK_STATUS } from '@/store/models/task/types';
import Image from '@/components/image';

import fact from '@/lib/fact';
// import { getTaskResources } from '@/subpages/index/components/task/resources'

import './store_task.scss';
import { StoreState, StoreDispatch } from '@/store';
import { execWithLock } from '@/utils/lock';
import event from '@/utils/event';
import { isPureNumber } from "@/store/models/task/helper";

import TAG from './images/<EMAIL>';
import TAG_BTN from './images/<EMAIL>'
import TAG_GOT_IT from './images/<EMAIL>'

const statMap = [ 'low', 'middle', 'high' ];

/** 累计任务 */
class StoreTask extends Component<IProps, IState> {


  clickTaskItem = (task: TaskInfo, idx: number, e: Rax.SyntheticEvent<HTMLDivElement>) => {

    execWithLock('store_task_complete', async (unlock) => {
      if (task.state === TASK_STATUS.TASK_COMPLETED) {
        fact.click('totaltaskreceive_click', {
          c: 'totaltaskreceive',
          d: 'click',
          grade: statMap[idx] || 'unknown'
        });
        await this.props.getAward({
          id: task.id,
          type: 'award',
          params: {
            task
          },
        });
        event.emit('taskAward');
      } else {
        unlock();
      }
    })
  }

  render() {
    if (!this.props.list.length) return null;
    const { target, progress, list } = this.props;
    const ratio = progress / (target || 1) || 0;
    return (
      <View className="store-task-wrap pure-bg site-store-task">
        {/*<Image source={limitIcon} style={{ width: 68, height: 32 }} className="limit-icon"/>*/}
        <View className="store-hd j-center">
          <Text className="main-tit">累计完成任务越多，赚得越多</Text>
          <Text className="sub-tit din-num">已完成{progress}/{target}</Text>
        </View>
        <View className="store-bd j-center">
          <View className="progress-bar">
            <View className="bar-inner" style={{ width: ratio * 100 + '%', 'background-color': '#F02920' }}>
            </View>
          </View>
          <View className="progress-list row">
            {
              list.map((item, idx) => {
                // const currRatio = item.target / target;
                const isAwarded = item.state === TASK_STATUS.TASK_CONFIRMED;
                const isDone = item.state === TASK_STATUS.TASK_COMPLETED;
                const desc = isAwarded ? '已获得' : isDone ? `${item.btnName || item.desc}${isPureNumber(item.desc) ? '元宝' : ''}` : `完成${item.target}个`;
                return (
                  <View
                    className={`task-node state-${item.state}`}
                    key={item.id}
                    onTouchStart={e => this.clickTaskItem(item, idx, e)}
                    style={{ left: (idx + 1) * 33 + '%' }}
                  >
                    <View className="icon-wrap">
                      <Image source={isAwarded ? TAG_GOT_IT : (isDone ? TAG_BTN : TAG)}
                             style={{ width: 80, height: 36}}/>
                      {(!isDone && !isAwarded) &&
                      <Text className="item-award-text din-num">{item.btnName || item.desc}</Text>}
                    </View>
                    <Text className="desc-text din-num">{desc}</Text>
                  </View>
                )
              })
            }
          </View>
        </View>
      </View>
    );
  }

  componentDidMount() {
    if (this.props.list.length > 0) {
      this.doExposure();
    }
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any) {
    if (this.props.list.length > 0 && prevProps.list.length === 0) {
      this.doExposure();
    }
  }

  doExposure() {
    fact.exposureAsync('totaltask_display', {
      c: 'totaltask',
      d: 'display'
    }, 300);
    this.props.list.forEach((task, idx) => {
      if (task.state === TASK_STATUS.TASK_COMPLETED) {
        fact.exposureAsync('totaltaskreceive_display', {
          c: 'totaltaskreceive',
          d: 'display',
          grade: statMap[idx] || 'unknowm'
        }, 300);
      }
    });
  }
}

interface IState {

}

interface IStoreTask {
  target: number;
  progress: number;
  list: TaskInfo[];
}

const mapState = (state: StoreState) => {
  const { storeParentTask, storeTaskList } = state.task;
  const { progress: parentProgress = 0 } = storeParentTask || {}

  // XXX: ab 需要配置多组蓄水任务，最大值取最后一个蓄水任务，可能和父任务不一致，进度条也不能超 100%
  const target = Math.max(...storeTaskList.map(item => item.target));
  const progress = Math.min(parentProgress, target);

  const p: IStoreTask = {
    target,
    progress,
    list: storeTaskList.sort((a, b) => a.target - b.target),
  }
  return p;
};
const mapDispatch = (dispatch: StoreDispatch) => ({
  getAward: dispatch.task.complete,
});


type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {};
export default connect(mapState, mapDispatch)(StoreTask)
