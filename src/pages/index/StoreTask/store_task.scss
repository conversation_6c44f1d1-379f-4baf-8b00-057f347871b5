.store-task-wrap {
  // height: 144rpx;
  padding-top: 50rpx;
  border-bottom: 2rpx solid #eee;
  height: 240rpx;
  // border-radius: 18rpx;
  // padding-right: 24rpx;
  .limit-icon {
    position: absolute;
    left: 0;
    top: 0;
  }

  .store-hd {
    // margin-right: 24rpx;

    .main-tit {
      font-weight: 700;
      font-size: 32rpx;
      line-height: 40rpx;
      color: #01255D;
      // margin-bottom: 26rpx;
    }

    .sub-tit {
      font-size: 24rpx;
      color: #7E93B7;
      line-height: 28rpx;
      font-weight: 400;
      position: absolute;
      top: 4rpx;
      right: 0;
      font-family: PingFangSC-Regular;
    }
  }

  .store-bd,
  .store-hd {
    // height: 100%;
    position: relative;
  }

  .store-bd {
    flex-grow: 1;
    // background-color: #f2aa24;
  }

  .progress-bar {
    position: absolute;
    left:0;
    top: 52rpx;
    // width: 610rpx;
    width: 590rpx;
    height: 12rpx;
    // background-image: linear-gradient(0deg, #EEEEEE 0%, #D8D8D8 100%);
    // border-radius: 18rpx;
    background: #DEE5EE;
    border-radius: 6rpx;
    overflow: hidden;

    .bar-inner {
      // height: 100%;
      height: 16rpx;
      background-color:#F02920;
      // background-image: linear-gradient(179deg, #FE9191 0%, #D42525 100%);

      // border-radius: 18rpx;
    }
  }

  .progress-list {
    position: relative;
    height: 68rpx;
    margin-top: 16rpx;

    .task-node {
      position: absolute;
      transform: translate(-90%);
      top: -8rpx;
      .desc-text {
        font-weight: 700;
        font-size: 20rpx;
        line-height: 28rpx;
        margin-top: 4rpx;
        color: #01255D;
        text-align: center;
        white-space: nowrap;
      }

      &.state-2 {
        .desc-text {
          color:#C4B6AC;
        }
      }
    }
    .novel-task-item {
      .desc-text {
        color: #014D45;
      }
      &.state-2 {
        .desc-text {
          color:#C4B6AC;
        }
      }
    }

    .icon-wrap {
      position: relative;
      width: 80rpx;
      height: 36rpx;

      .item-award-text {
        position: absolute;
        width: 100%;
        height: 36rpx;
        top: 0;
        padding-left: 20rpx;
        // transform: translateX(-50%);
        white-space: nowrap;
        overflow: hidden;
        font-size: 20rpx;
        line-height: 36rpx;
        color: #01255D;
        text-align: center;
      }
    }
  }
}

.task-panel {
  .store-task-wrap {
    background-image: none;
    background: #fff;
    border-radius: 20rpx;
    position: relative;
  }
}
