import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import {Component, createElement} from 'rax';
import { connect } from 'rax-redux';
import Text from 'rax-text';
import View from 'rax-view';
import {dispatch, StoreDispatch, StoreState} from '@/store';
import Image from '@/components/image';
import { LoginStatus } from '@/store/models/user';

import './index.scss';
import { uc } from '@ali/weex-toolkit/lib/weex_config';
import fact from '@/lib/fact';
import Fact from '@/components/Fact';
import {convertCash2Display} from "@/utils/amount";


import { openURL, rewardDesc } from "@/pages/index/task/help";
import { debounce } from '@/lib/utils/index';
import { PAGE_ID } from '@/store/models/route';

const jump2setting = () => {
  fact.click('setting_click', { c: 'setting', d: 'click' });
  jsbridge.exec('biz.openPageUrl', { url: 'ext:help_opensetting' });
};


interface IState {
  isToolbar: boolean
}

class StickyHeader extends Component<IProps, IState> {

  state:IState = {
    isToolbar: true
  }
  componentDidMount() {
  }
  login = () => {
    const { userStatus } = this.props
    if (userStatus === LoginStatus.logout || userStatus === LoginStatus.visitor) {
      fact.click('login_click', { c: 'login', d: 'click' });
      dispatch.user.login();
    } else {
      fact.click('login_click', { c: 'userinfo', d: 'click' });
      jsbridge.exec('biz.openPageUrl', { url: 'ext:account_center:openEditInfo' });
      dispatch.user.setupUserInfo();
    }
  };
  checkIsToolbar = () => {
    const { entry } = uc.params
    const isToolbar = entry === 'toolbar'
    console.log('isToolbar:', isToolbar)
    this.setState({
      isToolbar
    })
  }
  jumpFarm = () => {
    const { farmUrl } =  this.props
    fact.click('entrance_click', { c: 'ding', d: 'entrance' } );
    jsbridge.exec('biz.openPageUrl', { url: farmUrl });
  };
  handleWithdraw = () => {
    openURL(`${window.location.href}${PAGE_ID.flow}`)
  }
  render () {
    const { avatar, coin, amount2Cash, showStickyHeader, indexResource,  isInsetPage } = this.props
    return (
      <View className={`comp-sticky-header ${showStickyHeader ? 'header-show' : ''} ${isInsetPage ? 'comp-sticky-inset-page' : ''}`} style={{'background': indexResource.stickyHeaderBg}}>
        <View className="sticky-header">
          <View className="wrap-avatar"  x-if={!isInsetPage}>
            <Image className="avatar" source={avatar} style={{ width: '48rpx', height: '48rpx' }} />
          </View>
          <View className="header-assets">
            <View x-if={isInsetPage} className="inset-header-assets">我的收益：</View>
            <View className="assets-cash">
              <View className="assets-amount din-num" style={{color: indexResource.fontColor.money}}>¥{amount2Cash}</View>
              <View className="assets-unit inset-header-assets-unit" style={{color: indexResource.fontColor.moneyUnit}}>现金</View>
            </View>
            <View className="assets-coin">
              <View className="assets-amount din-num" style={{color: indexResource.fontColor.money}}>{coin}</View>
              <View className="assets-unit inset-header-assets-unit" style={{color: indexResource.fontColor.moneyUnit}}>元宝</View>
            </View>
          </View>
          <Fact
            c="ding"
            d="entrance"
            expoLogkey="entrance_expo"
            noUseClick
            x-if={!isInsetPage}
          >
            <Image onClick={debounce(this.jumpFarm,500)} source="https://gw.alicdn.com/imgextra/i2/O1CN01R9zhOF25UL0CVDSyC_!!6000000007529-2-tps-300-78.png" style={{width: '200rpx', height: '52rpx', marginRight: '24rpx'}} />
          </Fact>
          <View x-if={isInsetPage} className="inset-header-withdraw-btn" onClick={() => this.handleWithdraw()}>
            <Text className="inset-header-withdraw-btn-text">提现/兑换</Text>
            <Image
              className="icon-next"
              source={'https://gw.alicdn.com/imgextra/i3/O1CN01n4BE921ZtvQnr9OUy_!!6000000003253-55-tps-10-10.svg'}
              style={{ width: '20rpx', height: '20rpx' }}
            />
          </View>
          <Image x-if={!isInsetPage} className="setting-btn" onClick={jump2setting} source={indexResource.setting} style={{width: '64rpx', height: '64rpx'}} />
        </View>
      </View>
    );
  }
};
const mapState = (state: StoreState) => {
  const { farmUrl } = state.app
  const { status, avator } = state.user;
  const avatar = avator || require('./images/avatar.png').default;
  const { coin, amount = 0 } = state.currency;
  const amount2Cash = convertCash2Display(amount);

  return {
    indexResource: state.app.indexResource,
    userStatus: status,
    coin,
    amount2Cash,
    avatar,
    farmUrl,
  };
};
const mapDispatch = (dispatch: StoreDispatch) => ({
});


type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
  showStickyHeader: boolean;
  isInsetPage?: boolean
};
export default connect(mapState, mapDispatch)(StickyHeader)

