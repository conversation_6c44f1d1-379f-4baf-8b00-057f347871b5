.comp-sticky-header {
  padding: 88rpx 24rpx 0 24rpx;
  width: 750rpx;
  background: #F8FBFF;
  position: fixed;
  top: 0;
  transition: all 0.3s;
  z-index: 100;
  opacity: 0;
  pointer-events: none;
  transform: translateZ(0);
  .sticky-header {
    flex-direction: row;
    align-items: center;
    height: 100rpx;
    width: 100%;
  }
  .shopping-countdown {
    width: 702rpx;
    height: 72rpx;
    flex-direction: row;
    background: #FFFFFF;
    border: 1rpx solid #DEE5EE;
    border-radius: 36rpx;
    padding: 12rpx 4rpx 12rpx 12rpx;
    margin-bottom: 16rpx;
    .limit {
      width: 82rpx;
      line-height: 48rpx;
      padding: 0 10rpx;
      border-radius: 24rpx;
      background: #FEEFE9;
      color: #FA6425;
      font-size: 26rpx;
      font-weight: 700;
      text-align: center;
      margin-right: 12rpx;
    }
    .title {
      font-size: 28rpx;
      color: #FA6425;
      font-weight: 700;
      line-height: 48rpx;
    }
    .times {
      opacity: 0.5;
      font-size: 28rpx;
      color: #B4C1D6;
      letter-spacing: 0;
      line-height: 48rpx;
      margin-left: 8rpx;
    }
  }
  .header-assets {
    flex-direction: row;
    flex: 1;
    .assets-cash {
      flex-direction: row;
      margin-right: 16rpx;
      align-items: baseline;
    }
    .assets-coin {
      flex-direction: row;
      margin-right: 16rpx;
      align-items: baseline;
    }
    .assets-amount {
      color: #F02920;
      font-weight: 700;
      font-size: 32rpx;
      margin-right: 4rpx;
      display: inline-block;
    }
    .assets-unit {
      color: #01255D;
      font-weight: 700;
      font-size: 32rpx;
      display: inline-block;
    }
  }
  .wrap-avatar {
    margin-right: 24rpx;
    width: 48rpx;
    height: 48rpx;
    border-radius: 100%;
    overflow: hidden;
    .avatar {
      uc-perf-stat-ignore: image;
    }
  }
  .setting-btn {
    width: 64rpx;
  }
  &.comp-sticky-inset-page{
    padding-top: 0;
    background: #F5F7F9;
    box-shadow: 0 2px 4px 0 rgba(0,0,0,0.06);
    .inset-header-assets{
      font-size: 24rpx;
      color: #01255D;
      font-weight: 600;
      margin-right: 8rpx;
      display: flex;
      justify-content: center;
    }
    .inset-header-assets-unit{
      font-size: 24rpx;
      font-weight: 600;
      color: #01255D;
    }
    .assets-amount{
      margin-right: 6rpx;
      margin-top: -4rpx;
    }
    .inset-header-assets-unit{
      font-size: 24rpx;
      font-weight: 600;
      color: #01255D;
    }
    .inset-header-withdraw-btn{
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin-left: auto;
      padding: 8rpx 18rpx;
      background: #FFFFFF;
      border-radius: 20rpx;
      .inset-header-withdraw-btn-text{
        font-size: 22rpx;
        font-weight: 400;
        color: #12161A;
      }
    }
  }
}
.header-show {
  opacity: 1;
  pointer-events: visible;
}
