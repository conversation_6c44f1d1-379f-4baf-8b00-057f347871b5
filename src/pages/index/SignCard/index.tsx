import {Component, createElement} from 'rax';
import { connect } from 'rax-redux';
// import View from 'rax-view';
import { StoreDispatch, StoreState } from '@/store';
import SignComp from '../SignComp';
import { getSignInfo, getDayDiff } from '@/store/models/task/helper';
import modal from '@/components/modals/modal';
import { setCalendarRemindersInWelfare } from '@/utils/calendar_helper';
import { TASK_EVENT_TYPE, TASK_STATUS } from '@/store/models/task/types';
import { getSignInTotalCash, getSignDateStr } from '@/utils/signin_helper';
import toast from '@/lib/universal-toast';
import Fact from '@/components/Fact';
import logoutCheck from '@/utils/logoutCheck';

import './index.scss';
import fact from '@/lib/fact';

interface IState {
}

class SignCard extends Component<IProps, IState> {

  componentDidMount() {

  }

  clickBody = () => {
    toast.show('点击其他区域');
  }

  getButtonFactVal = (signToday) => {
    if (signToday?.state === TASK_STATUS.TASK_CONFIRMED) {
      if (this.props.hadSetSignRemind) {
        return 0;
      } else {
        return 2;
      }
    } else {
      return 1;
    }
  }

  clickReceive = () => {
    this.submitClickFact();
    if (logoutCheck()){
      return;
    }
    modal.openSignIn();
  }

  remindSign = () => {
    if (logoutCheck()){
      return;
    }
    this.submitClickFact();
    setCalendarRemindersInWelfare();
  }

  submitClickFact = () => {
    const signToday = getSignInfo();
    fact.click('fuli_click', {
      c: 'signbar',
      d: 'button',
      button: this.getButtonFactVal(signToday),
      day: signToday?.title?.split('')[1] || '0',
    })
  }

  canShowSignCard = (signList) => {
    if (!signList || signList?.length === 0) {
      return false;
    }
    const find = signList.find((sign) => {
      if (sign.state !== TASK_STATUS.TASK_CONFIRMED) {
        return true;
      }
      return false;
    });
    // find 不为 undefined 表示有签到任务未完成
    // find 为 undefined 则需要判断最后一天的 completeTime 是否是当天
    if (find) {
      return true;
    } else {
      const len = signList.length;
      if (getDayDiff(signList[len - 1]?.completeTime, this.props.now) === 0) {
        // 签到最后一天（状态 state: 2）是今天
        return true;
      }
      return false;
    }
  }

  render () {
    const { signList, hadSetSignRemind, isSupportSignRemind } = this.props;
    const { firstDayStr, lastDayStr } = getSignDateStr(signList);
    const title = `新人7天签到必得${getSignInTotalCash(signList)}元`;
    const desc = `限${firstDayStr}～${lastDayStr}`;
    const signToday = getSignInfo();
    const day = signToday?.title?.split('')[1];
    return (
      <Fact
        className="sign-card"
        x-if={signList?.length && signList[0]?.event === TASK_EVENT_TYPE.UCLITE_SIGN && this.canShowSignCard(signList) && day}
        c="signbar"
        d="button"
        expoLogkey="fuli_expo"
        noUseClick
        expoExtra={{
          button: this.getButtonFactVal(signToday),
          day,
        }}
      >
        <SignComp
          signList={signList}
          currentDay={parseInt(day || '1')}
          hadSetSignRemind={hadSetSignRemind}
          isSupportSignRemind={isSupportSignRemind}
          title={title}
          desc={desc}
          showDesc={true}
          clickRecieve={this.clickReceive}
          remindSign={this.remindSign}
          // clickBody={() => this.clickBody()}
        />
      </Fact>
    );
  }
};
const mapState = (state: StoreState) => {
  return {
    signList: state.task.signin,
    hadSetSignRemind: state.app.hadSetSignRemind,
    isSupportSignRemind: state.app?.isSupportSignRemind,
    now: state.task.now,
  };
};
const mapDispatch = (dispatch: StoreDispatch) => ({
});


type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
};
export default connect(mapState, mapDispatch)(SignCard)

