{"$pegasus": {"title": "title mock"}, "shopping": {"items": []}, "$kangaroo": {"data": {"10000001": {"slider": []}, "10000101": null, "10000151": null, "10000201": null, "10000321": null, "10000331": null, "10000421": null, "10000431": null, "10000501": null}, "sysInfo": {"isAliIntranet": "true", "whEnv": "pre", "serverTime": "1579425103698"}, "pageInfo": {"activityId": "0", "path": "/zcache2/default/p-example-index", "schemaVersion": "71c17a9e-adba-4df5-87ae-97a605d46b20", "spma": "a3204", "seaConfig": "", "spmb": "13731006", "name": "源码测试 - index", "style": "<style></style>", "id": "51907", "title": "源码测试 - index", "utPageName": "ald_strategy_51907", "pageStatus": "online", "theme": {"itemBtnText": ""}, "campaignPageId": "NaN"}, "modules": [{"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--headerslider", "fullName": "@ali/pegasus-pages--pegasus-pages-example--headerslider", "uuid": "10000001", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276773", "dataConfig": {}}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--slogan", "fullName": "@ali/pegasus-pages--pegasus-pages-example--slogan", "uuid": "10000101", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276779"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--categorys", "fullName": "@ali/pegasus-pages--pegasus-pages-example--categorys", "uuid": "10000151", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276775"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--actbtn", "fullName": "@ali/pegasus-pages--pegasus-pages-example--actbtn", "uuid": "10000201", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276774"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--bktjtitle", "fullName": "@ali/pegasus-pages--pegasus-pages-example--bktjtitle", "uuid": "10000321", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276772"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--bktjitem", "fullName": "@ali/pegasus-pages--pegasus-pages-example--bktjitem", "uuid": "10000331", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276777"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--ttcctitle", "fullName": "@ali/pegasus-pages--pegasus-pages-example--ttcctitle", "uuid": "10000421", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276776"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--ttccitem", "fullName": "@ali/pegasus-pages--pegasus-pages-example--ttccitem", "uuid": "10000431", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276780"}, {"settings": {}, "hidden": "false", "name": "pegasus-pages--pegasus-pages-example--guess", "fullName": "@ali/pegasus-pages--pegasus-pages-example--guess", "uuid": "10000501", "limitConfig": {}, "version": "0.0.3", "componentVersionId": "276778"}], "seed": {"packages": {}, "modules": {}, "md5": "9224524FE92EEB36AEE7D468CF282421"}, "pvuuid": "v1-7dcac1e7-8035-4481-ad65-ff3f1ac9bed7-1579425103683", "fri": {"moduleIdList": ["10000001", "10000101", "10000151", "10000201", "10000321", "10000331", "10000421", "10000431", "10000501"], "processedTppId": []}, "isFirstScreen": "true", "skipPluginProcess": null, "removedModuleIdsMap": [], "allUI": [], "hasMoreModules": "false", "trackMap": {}, "serverBottomApp": [], "failApp": [], "success": "true", "defaultBottomUrl": "https://kangaroo.alicdn.com/bottom/aHR0cHM6Ly9wcmUtd29ybWhvbGUud2FwYS50bWFsbC5jb20vd293L3ovemNhY2hlMi9kZWZhdWx0L3AtZXhhbXBsZS1pbmRleA==/device=native/data.jsonp", "defaultBottomKey": "aHR0cHM6Ly9wcmUtd29ybWhvbGUud2FwYS50bWFsbC5jb20vd293L3ovemNhY2hlMi9kZWZhdWx0L3AtZXhhbXBsZS1pbmRleA==/device=native", "traceId": "0b16f5a215794251035073523eb2bd"}}