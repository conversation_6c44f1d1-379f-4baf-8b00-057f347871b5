import {createElement, Component} from 'rax';
import Image from "@/components/image";
import {connect} from 'rax-redux';
import './index.scss';
import { StoreDispatch, StoreState } from '@/store';
import { IBannerAd } from '@/store/models/app';
import Fact from '@/components/Fact';
import logoutCheck from '@/utils/logoutCheck';
import { openURL } from '@/pages/index/task/help';
import { appVersion, isLatestVersion, isIOS } from "@/lib/universal-ua";
import Toast from '@/lib/universal-toast';
import {IAd} from "@/utils/huichuan";
import openTaobao from "@/utils/ucapi/opentaobao";

interface IState {
  adImgLoaded: boolean;
}

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {};

class TopBanner extends Component<IProps, IState> {

  state = {
    adImgLoaded: false
  }

  hasValidAd(bannerInfo?) {
    const adInfo = bannerInfo === undefined ? this.props.bannerBrandAd : bannerInfo;
    return adInfo
      && adInfo.ad
      && adInfo.ad[0]
      && adInfo.ad[0].ad_content?.img_1;
  }

  onAdLoaded = () => {
    this.setState({
      adImgLoaded: true,
    });
  }

  componentDidMount() {
    if (this.hasValidAd()) {
      console.log('banner广告曝光');
      this.props.exposure('bannerBrandAd');
    }
  }

  componentDidUpdate(prevProps) {
    if (
      // 无到有
      !this.hasValidAd(prevProps.bannerBrandAd) && this.hasValidAd()
      ||
      // 更换广告
      // @ts-ignore
      (this.hasValidAd(prevProps.bannerBrandAd) && this.hasValidAd() && prevProps.bannerBrandAd.sid !== this.props.bannerBrandAd.sid)
    ) {
      console.log('banner广告曝光');
      this.props.exposure('bannerBrandAd');
    }
  }

  handleClick = () => {
    if (this.hasValidAd()) {
      this.props.click('bannerBrandAd');
    }
  }

  handleBackupAdClick = () => {
    console.log('cms banner点击');
    const banner: IBannerAd | undefined = this.props.backupAd;
    // 登录态判断
    if (banner?.needLogin) {
      if (logoutCheck()) {
        return;
      }
    }
    // 双端版本判断
    if (banner?.android || banner?.ios) {
      let isNewVersion
      if (appVersion) {
        if (isIOS) {
          isNewVersion = isLatestVersion(appVersion, banner.ios as string);
        } else {
          isNewVersion = isLatestVersion(appVersion, banner.android as string);
        }
      }
      if (!isNewVersion) {
        Toast.show('请升级到最新版本后参与活动');
        return;
      }
    }
    if (banner?.isTaobao) {
      openTaobao(banner?.link);
    } else {
      openURL(banner?.link);
    }
  }

  render() {
    if (
      this.hasValidAd()
    ) {
      const adInfo: Partial<IAd> = this.props.bannerBrandAd.ad[0];
      if (adInfo.ad_content?.img_1) {
        const extras = {
          ad_id: adInfo.ad_id,
          // @ts-ignore
          sid: this.props.bannerBrandAd.sid,
          row: 1,
          lnk_type: 'instal',
          account: adInfo.ad_content.account_id,
          banner_name: adInfo.ad_id,
        };
        return <Fact
          className="fuli-top-banner"
          key={adInfo.ad_id}
          onClick={this.handleClick}
          logkey="banner_click"
          c="banner"
          d="banner"
          expoLogkey="banner_display"
          expoExtra={extras}
          ckExtra={extras}
          style={{
            display: this.state.adImgLoaded ? 'flex' : 'none'
          }}
        >
          <Image onLoad={this.onAdLoaded} source={(adInfo.ad_content?.img_1 || '').replace('http://', 'https://')}
                 className="banner-image"/>
        </Fact>;
      }
    } else if ((this.props.bannerAdLoaded) && this.props.backupAd) {
      const backupAd = this.props.backupAd;
      const extras = {
        ad_id: backupAd.id,
        row: 1,
        lnk_type: 'base',
        account: '0',
        banner_name: backupAd?.id,
      };
      return <Fact
        className="fuli-top-banner"
        key={backupAd.id}
        onClick={this.handleBackupAdClick}
        logkey="banner_click"
        c="banner"
        d="banner"
        expoLogkey="banner_display"
        expoExtra={extras}
        ckExtra={extras}
        style={{
          display: this.state.adImgLoaded ? 'flex' : 'none'
        }}
      >
        <Image onLoad={this.onAdLoaded} source={backupAd.img} className="banner-image"/>
      </Fact>;
    }
    return null;
  }
}

const mapState = (state: StoreState) => {
  const backupAdList = state.app.bannerAdList;
  const now = state.task.now;
  const backupAd = backupAdList.find(ad => {
    return ad.startTime <= now && now <= ad.endTime;
  });
  return {
    bannerBrandAd: state.ad.bannerBrandAd,
    bannerAdLoaded: state.ad.bannerAdLoaded,
    backupAd,
    signList: state.task.signin,
  }
};

const mapDispatch = (dispatch: StoreDispatch) => {
  return {
    exposure: dispatch.ad.exposure,
    click: dispatch.ad.click,
  }
};

export default connect(mapState, mapDispatch)(TopBanner);

