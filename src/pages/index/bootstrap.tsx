import { render, createElement } from 'rax';
import DriverUniversal from 'driver-universal';
import Comp from './index';
import { setGlobalFirstData, getGlobalFirstData, isWithSkeleton } from '@/lib/render-utils/csr';
import { renderAction, perfMark } from '@/lib/render-utils/perf';
import { isNode } from 'universal-env';
import { getUserInfo } from '@/lib/ucapi';
import { IndexBg, NovelBg, TaskTitleIcon, TaskParvialTitle, SpringHeadBg } from '@/constants/static_img';
import { getFirstData } from './first-data-api';

export const preloadImg = (imgs: string | string[], onLoaded?: () => void) => {
  const imgList = typeof imgs === 'string' ? [imgs] : imgs;

  if (!imgs.length) return;

  for (let index = 0; index < imgList.length; index++) {
    const img = new Image();
    img.src = imgList[index];

    if (index === imgList.length - 1) {
      if (onLoaded && typeof onLoaded === 'function') {
        img.onload = onLoaded;
      }
    }
  }
};

const preloadImgList = [
  // 背景图片
  IndexBg,
  // 小说嵌入背景图片
  NovelBg,
  // 今日任务
  TaskTitleIcon,
  // 赚更多任务
  TaskParvialTitle,
  // SpringHeadBg,
]

// 预加载首屏主要图片
preloadImg(preloadImgList);


perfMark(renderAction.bootstrapExecute);

const handleCsrData = (data) => {
  console.log('CSR，更新FirstData', data);
  perfMark(renderAction.csrGotData);
  setGlobalFirstData(data);
}



(async function () {
  if (window.__CSRFirstDataPms__) {
    try {
      if (isWithSkeleton()) {
        const data = await window.__CSRFirstDataPms__;
        handleCsrData(data);
      } else {
        window.__CSRFirstDataPms__.then(handleCsrData);
      }
    } catch (err) {
      console.error('[bootstrap] handle window.__CSRFirstDataPms__ error ',err);
    }
  }

  let firstData = getGlobalFirstData();
  // 首屏数据存在且服务端没有获取到KPS
  if (!isNode && firstData && !firstData.kps) {
    const userInfo: any = await getUserInfo();
    // 如果客户端有KPS，重新获取一次数据
    if (userInfo?.kps_wg) {
      firstData = await getFirstData();
      if (firstData) {
        firstData.kps = userInfo?.kps_wg;
      }
      handleCsrData(firstData);
    }

  }


  console.log('[bootstrap] 准备hydrate | firstData', firstData)
  perfMark(renderAction.hydrate);
  render(
    <Comp firstData={firstData} />,
    document.getElementById('root'),
    { driver: DriverUniversal, hydrate: true },
  );
})();



