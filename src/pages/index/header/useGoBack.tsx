import { store } from '@/store';
import modal from '@/components/modals/modal';
import { checkTaskFinished } from '@/pages/index/task/help';
import { useBackIntercept } from '../../../hooks/useBackIntercept';
import { isIOS } from '@/lib/universal-ua';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import fact from '@/lib/fact';
import { updateWidget, widget1, widget2 } from '@/lib/utils/desktop';
import { TaskInfo } from '@/store/models/task/types';
import urlParams from '@/utils/urlParams';
import { getArraysIdIntersection } from '@/utils/array';
import taskClickHandler from './taskClickHandle';
import { isInsetPage } from '@/utils/url';

const defaultBackInterceptData = {
  attributes: {
    retentionInfo: [],
    interceptDayCount: 5,
    interceptPageCount: 1,
    title: '',
    leftIcon: '',
    mainName: '',
    subName: ''
  },
  taskList: [],
  hiddenTaskIdList: []
};

export function useGoBack() {
  const getBackInterceptTask = (): TaskInfo | null => {
    const state = store.getState();
    const interceptData = (state.resource.uc_piggy_back_intercept || defaultBackInterceptData)
    const attributes = interceptData.attributes
    const taskList = interceptData.taskList;
    const retentionInfo = attributes.retentionInfo
    const retentionConfig = (retentionInfo as any[]).map(item => ({...item, id: +item.taskId}));
    const allTaskList = (state.task.taskList || []).filter(item => !checkTaskFinished(item));
    const validTaskList = getArraysIdIntersection<TaskInfo>([taskList, allTaskList, retentionConfig]);
    const taskInfo = validTaskList[0]
    if (!taskInfo) {
      return null;
    }
    const retentionSelect = retentionConfig.find(item => item.id === taskInfo.id);

    Object.assign(taskInfo, {
      title: attributes?.title,
      leftIcon: attributes?.leftIcon,
      mainName: retentionSelect?.name,
      subName: retentionSelect?.desc,
      imgs: retentionSelect?.imgs
    });
    return taskInfo;
  };

  const getResourceCountInfo = () => {
    const state = store.getState();
    const interceptData = (state.resource.uc_piggy_back_intercept || defaultBackInterceptData)
    return {
      maxDailyInterceptCount: +interceptData.attributes.interceptDayCount,
      maxLifecycleInterceptCount: +interceptData.attributes.interceptPageCount
    };
  }

  const goBackHandler = () => {
    fact.click('head_back_click', {
      c: 'head',
      d: 'back'
    });
    if (isIOS) {
      jsbridge.exec('biz.openPageUrl', { url: 'ext:back' });
    } else {
      updateWidget(widget1);
      updateWidget(widget2);
      jsbridge.exec('biz.closeWebPage');
    }
  };

  const shouldAllowIntercept = () => {
    // note: 未登录不拦截
    const isLogin = store.getState().user.isLogin;
    if (!isLogin) {
      return false;
    }
    // 内嵌页面不拦截
    const inset = isInsetPage();
    if (inset) {
      return false;
    }
    return true;
  };

  const btnClickHandler = (task: TaskInfo) => {
    taskClickHandler(task, 'retain_popup');
  }
  const { useInterceptEffect, leftIconClickHandler } = useBackIntercept<TaskInfo>({
    getBackInterceptTask,
    goBackHandler,
    openBackInterceptPopup: modal.openBackIntercept,
    taskActionHandler: btnClickHandler,
    shouldAllowIntercept,
    getResourceCountInfo
  });
  useInterceptEffect()
  return {
    leftIconClickHandler
  }
} 
