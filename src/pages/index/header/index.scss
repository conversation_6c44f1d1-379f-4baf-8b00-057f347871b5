.comp-header {
  width: 750rpx;
  min-height: 760rpx;
  background-image: url("https://broccoli-static.uc.cn/file/others/2025/7/392737d2b6575370467861d06adceb1e.jpeg");
  uc-perf-stat-ignore: image;
  background-size: cover;

  &.spring-style {
    background-image: url("https://yes-file.uc.cn/file/1737525376124_2825770754_3747.png");
    uc-perf-stat-ignore: image;
  }
  
  .header-bar {
    height: 100rpx;
    padding-top: 18rpx;
    margin: 88rpx 24rpx 0;
    display: block;
    align-items: center;
    position: relative;
    .btn-back {
      uc-perf-stat-ignore: image;
    }
    .invite-notice {
      position: absolute;
      visibility: hidden;
      opacity: 0;
      top: -20rpx;
      left: 190rpx;
      transform: translateY(-40rpx);
      transition:all .3s ease-in-out;
    }
    .invite-notice-show {
      visibility: visible;
      opacity: 1;
      transform: translateY(0);
      z-index: 2;
      > img {
        uc-perf-stat-ignore: image;
      }
    }
    .spring-festival-logo {
      position: absolute;
      left: 84rpx;
      top: 2rpx;
    }
  }
  .header-logo {
    position: absolute;
    top: 88rpx;
    left: 108rpx;
  }

  .wrap-user {
    width: fit-content;
    width: calc(100% - 80rpx);
    // max-width: 600rpx;
    margin-left: 50rpx;
    margin-right: 30rpx;
    flex-direction: row;
    align-items: center;
    .wrap-avatar {
      margin-right: 20rpx;
      width: 88rpx;
      height: 88rpx;
      border-radius: 100%;
      overflow: hidden;
      .avatar {
        uc-perf-stat-ignore: image;
      }
    }
    .user-info {
      flex: 1;
      .nickname {
        font-family: PingFang SC,Hiragino Sans GB;
        font-size: 32rpx;
        color: #01255D;
        letter-spacing: 0;
        font-weight: 700;
      }
      .reg-days {
        opacity: 0.5;
        font-family: PingFangSC-Regular;
        font-size: 20rpx;
        color: #01255D;
        letter-spacing: 0;
      }
    }
  }

  &.comp-header-inset-page{
    // note: 图片源./images/<EMAIL>
    background-image: url("https://broccoli-static.uc.cn/file/others/2025/7/0b6be4bbbf1987f086a4779d515b2e40.jpeg");
    min-height: 572rpx; // (760 - 100 - 88)rpx;
    .wrap-user {
      margin-top: 15rpx;
    }
  }

  &.wufu {
    .wrap-user {
      width: 750rpx;
      flex-direction: row;
      height: 140rpx;
      margin: 0;
      margin-top: 20rpx;

      .wrap-avatar {
        margin-left: 40rpx;
        margin-right: 24rpx;
        width: 88rpx;
        height: 88rpx;
        border-radius: 100%;
        overflow: hidden;
        margin-top: 8rpx;
        .avatar {
          uc-perf-stat-ignore: image;
        }
      }
    }
  }
}

.wrap-control {
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  position: relative;
  top: -7rpx;
}

.setting-btn {
  display: block;
  float: right;
  uc-perf-stat-ignore: image;
}
.help-btn {
  display: block;
  float: right;
  margin-right: 32rpx;
  uc-perf-stat-ignore: image;
}
.btn-text {
  color: #666;
  font-weight: bold;
  font-size: 32rpx;
}

.btn-icon-share {
  margin-right: 5rpx;
  width: 30rpx;
  height: 32rpx;
}
