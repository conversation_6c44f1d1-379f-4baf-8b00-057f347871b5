import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import { createElement, useEffect, useState } from 'rax';
import { useSelector } from 'rax-redux';
import View from 'rax-view';
import cz from 'classnames';
import { dispatch, StoreState } from '@/store';
import Image from '@/components/image';
import { LoginStatus } from '@/store/models/user';
import { doOnAttach } from '@/lib/prerender';
import { addParams } from '@/utils/url';
import './index.scss';
import fact from '@/lib/fact';
import Fact from '@/components/Fact';
import Wallet from '@/pages/index/wallet';
import InsetWallet from '@/pages/index/wallet/inset';
import event from '@/utils/event';
import ucapi from '@/utils/ucapi';
import { execWithLock } from '@/utils/lock';
import { isSameDay } from '@/utils/date';
import helper from '@/utils/helper';
import { openURL } from '@/pages/index/task/help';
import { isIOS } from '@/lib/universal-ua';
import { inPeriod } from '@/utils/date';
import { isWeb } from 'universal-env';
import InviteNotice from './images/<EMAIL>';
import urlParams from '@/utils/urlParams';
import AsyncComponent from '@/components/AsyncComponent';
import { useGoBack } from './useGoBack';

const jump2setting = () => {
  fact.click('setting_click', { c: 'setting', d: 'click' });
  jsbridge.exec('biz.openPageUrl', { url: 'ext:help_opensetting' });
};

interface HeaderProps {
  isInsetPage?: boolean;
}

function Header({ isInsetPage }: HeaderProps) {
  const [isToolbar, setIsToolbar] = useState(true);
  const [disableBack, setDisableBack] = useState(false);
  const [inviteNoticeShow, setInviteNoticeShow] = useState(false);
  const {
    now,
    uid,
    inviteAddAmount,
    userStatus,
    avatar,
    invitePageLink,
    indexResource,
    showNotice,
    noticeList,
    isSpringPeriod,
    animationReady,
    helpCenterlink,
  } = useSelector((state: StoreState) => {
    const { nickname, status } = state.user;
    const avatar = state.user.avator || require('./images/avatar.png').default;
    const noName = status === LoginStatus.login ? '设置昵称' : '立即登录领福利';
    const { noticeStartTime, noticeEndTime, noticeList } = state.cms.indexNoticeConfig || {};
    const showNotice = inPeriod(noticeStartTime, noticeEndTime, state.task.now);
    
    return {
      now: state.task?.now,
      uid: state.user?.uid,
      inviteAddAmount: state.task?.inviteAddAmount,
      nickname: nickname || noName,
      userStatus: status,
      avatar,
      nuPacketLink: state.app.nuPacketLink,
      invitePageLink: state.app.invitePageLink,
      indexResource: state.app.indexResource,
      showNotice,
      noticeList,
      isSpringPeriod: state.app.isSpringPeriod,
      animationReady: state.app.animationReady,
      helpCenterlink: state.app.helpCenterlink,
    };
  });

  const { leftIconClickHandler } = useGoBack();

  useEffect(() => {
    doOnAttach(() => {
      checkIsToolbar();
    });

    event.on('disableBack', () => {
      setDisableBack(true);
    });

  }, []);

  useEffect(() => {
    checkInviteNoticeShow();
  }, [inviteAddAmount]);

  const checkNoticeAndHide = () => {
    setTimeout(() => {
      if (inviteNoticeShow) {
        setInviteNoticeShow(false);
      }
    }, 15000);
  };

  const checkInviteNoticeShow = () => {
    if (!inviteAddAmount) return;
    const lastShowTime = localStorage.getItem('inviteNoticeShowTime') || '';
    if (!lastShowTime || !isSameDay(lastShowTime, now)) {
      setTimeout(() => {
        localStorage.setItem('inviteNoticeShowTime', new Date().getTime() + '');
        setInviteNoticeShow(true);
      }, 1200);
      checkNoticeAndHide();
    }
  };

  const handleToHelp = () => {
    fact.click('head_kefu_click', { c: 'head', d: 'kefu' });
    const csLink = addParams(helpCenterlink, { uid });
    openURL(csLink);
  };

  const login = async () => {
    execWithLock('login', async (unlockFn) => {
      if (userStatus === LoginStatus.logout || userStatus === LoginStatus.visitor) {
        fact.click('fuli_click', { c: 'header', d: 'login' });
        // if (!isIOS) {
          // 流程不通，先暂时保留逻辑
          // try {
          //   const data = await dispatch.user.queryUserType();
          //   if (data?.userType === UserType.new || data?.userType === UserType.back) {
          //     console.log('uclink打开端新人首启红包页面');
          //     openURL(
          //       'uclink://www.uc.cn/19b64348381e629f44f43b8506f24e92?action=launch&module=welfare&sub_action=open_page&page_name=gain_nu_red_packet',
          //     );
          //     await helper.sleep(1000);
          //     unlockFn();
          //     return;
          //   }
          // } catch (err) {
          //   console.log('查询用户类型失败：', err);
          // }
        // }
        dispatch.user.login();
        await helper.sleep(1000);
        unlockFn();
      } else {
        fact.click('login_click', { c: 'userinfo', d: 'click' });
        if (!isIOS) {
          jsbridge.exec('biz.openPageUrl', {
            url: 'https://www.uc.cn/?uc_flutter_route=/usercenter/sub_business/welfare&enableTabStyle=0',
          });
        } else {
          jsbridge.exec('biz.openPageUrl', { url: 'ext:account_center:openEditInfo' });
        }
        dispatch.user.setupUserInfo();
        await helper.sleep(1000);
        unlockFn();
      }
    });
  };

  const checkIsToolbar = () => {
    const entry = urlParams.getParams('entry');
    setIsToolbar(entry === 'toolbar');
  };

  const showBack = () => {
    const disableBackFromUrl = location.href.includes('disable_back');
    return !isToolbar && !disableBack && !disableBackFromUrl;
  };

  const handleToInvite = () => {
    setInviteNoticeShow(false);
    return ucapi.base.openURL({ url: invitePageLink });
  };

  const renderHeaderBar = () => {
    return (
      <View className="header-bar" style={{ marginBottom: isSpringPeriod ? '20rpx' : '0' }}>
        <Image
          x-if={showBack()}
          onClick={leftIconClickHandler}
          className="btn-back"
          style={{ width: '64rpx', height: '64rpx' }}
          source={indexResource.back}
        />
        <View className="setting-btn" onClick={jump2setting}>
          <Image source={indexResource.setting} style={{ width: '64rpx', height: '64rpx' }} />
        </View>
        <View className="help-btn" onClick={handleToHelp}>
          <Image source={indexResource.help} style={{ width: '64rpx', height: '64rpx' }} />
        </View>
        {/* <View x-if={isSpringPeriod} className="spring-festival-logo">
          <Image source={SpringFestivalLogo} style={{ width: '400rpx', height: '100rpx' }} />
        </View> */}
        <View className={`invite-notice ${!isSpringPeriod && inviteNoticeShow ? 'invite-notice-show' : ''}`}>
          {!isSpringPeriod && inviteNoticeShow && <Image onClick={handleToInvite} source={InviteNotice} style={{ width: '340rpx', height: '132rpx' }} />}
        </View>
      </View>
    );
  };
  
  return (
    <Fact
      className={`${cz('comp-header')} ${isSpringPeriod ? 'spring-style' : ''} ${isInsetPage ? cz('comp-header-inset-page') : ''}`}
      expoLogkey="fuli_head_expo"
      c="head"
      d="head"
    >
      {isInsetPage ? null : renderHeaderBar()}
      <View className="wrap-user" id="wrap-user">
        <View x-if={!isInsetPage} className="wrap-avatar" onClick={login}>
          <Image className="avatar" source={avatar} style={{ width: '88rpx', height: '88rpx' }} />
        </View>
        {isInsetPage ? <InsetWallet /> : <Wallet />}
      </View>
      <Fact
        x-if={showNotice}
        expoLogkey="notice_board_expo"
        c="welfare"
        d="notice"
        expoExtra={{
          content: JSON.stringify(noticeList),
        }}
      >
        {isWeb ? (
          <AsyncComponent
            noticeList={noticeList}
            animationReady={animationReady}
            resolve={() => import('../Notice')}
          />
        ) : null}
      </Fact>
    </Fact>
  );
}

export default Header;
