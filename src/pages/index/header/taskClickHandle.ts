import modal from '@/components/modals/modal';
import { dispatch, store } from '@/store';
import { isCallAppTask, MINIGAME_TASK_TAG } from '@/store/models/task/helper';
import {
  DOWNLOAD_APP_EVENT,
  FINISH_CAN_JUMP_LINK_EVENT,
  RTA_TASK_TYPE,
  TASK_EVENT_TYPE,
  TASK_STATUS,
  TaskInfo,
} from '@/store/models/task/types';
import logoutCheck from '@/utils/logoutCheck';
import ucapi from '@/utils/ucapi';
import toast from '@/lib/universal-toast';
import {
  checkTaskCountDown,
  checkTaskFinished,
  dongfengTaskReport,
  isHuiChuangAdEffectTask,
  isPrtStoreTask,
  openURL,
  taskActionHandler,
} from '../task/help';
import { execWithLock } from '@/utils/lock';
import fact from '@/lib/fact';
import { logToFinishTask } from '@/store/models/task/taskMonitor';

const { TASK_COMPLETED, TASK_NOT_COMPLETED } = TASK_STATUS;

export default async function taskClickHandler(taskInfo: TaskInfo, resource_location: string = 'retain_popup') {
  const { state, event } = taskInfo;
  const isMiniGame = event.includes(MINIGAME_TASK_TAG);

  dongfengTaskReport(taskInfo, 'click');
  if (isHuiChuangAdEffectTask(taskInfo)) {
    fact.event('task_hcad_click', {
      event_id: '19999',
      c: 'task',
      d: 'click',
      task_id: taskInfo.id,
      task_name: taskInfo.name,
      resource_location,
    });
  }
  // 素人任务单独处理，仅跳转
  if (event === TASK_EVENT_TYPE.USER_GROUP_LINK) {
    fact.click('amateur_click', {
      c: 'task',
      d: 'link',
      task_id: taskInfo?.id || '',
      task_name: taskInfo?.name || '',
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      resource_location,
    });
    return ucapi.base.openURL({ url: taskInfo.url });
  }
  handleClickFact(taskInfo, resource_location);
  if (event === TASK_EVENT_TYPE.UCLITE_INVITE_TASK && !logoutCheck()) {
    fact.click('invite_click', {
      c: 'task',
      d: 'share_go',
      task_id: taskInfo?.id || '',
      task_name: taskInfo?.name || '',
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      resource_location,
    });
    logToFinishTask(taskInfo, 'share_go');
    const invitePageLink = store.getState().app.invitePageLink;
    if (invitePageLink) {
      return ucapi.base.openURL({ url: invitePageLink });
    }
    return ucapi.base.openURL({ url: taskInfo.url || invitePageLink });
  }
  if (event === TASK_EVENT_TYPE.UCLITE_INVITE_ENTRY) {
    fact.click('code_click', {
      c: 'task',
      d: 'code',
      task_id: taskInfo?.id || '',
      task_name: taskInfo?.name || '',
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      resource_location,
    });
    if (logoutCheck()) {
      return;
    }
    const { shieldInviteModule } = store.getState().app;

    if (shieldInviteModule) {
      return toast.show('活动已下线');
    } else {
      return modal.openInviteCode();
    }
  }
  if (state === TASK_NOT_COMPLETED && DOWNLOAD_APP_EVENT.includes(event)) {
    // 触发下载类任务完成
    const result = await dispatch.task.checkAppDownloadFinish({
      taskInfo: taskInfo,
      showToast: false,
    });
    if (result) {
      return;
    }
  }
  if (state === TASK_COMPLETED) {
    return award(taskInfo, resource_location);
  }
  if (state !== TASK_STATUS.TASK_DOING && isPrtStoreTask(event)) {
    return;
  }

  if (!isMiniGame && checkTaskFinished(taskInfo)) {
    fact.click('task_click', {
      c: 'task',
      d: `task1`,
      clicl_action: 'gotask',
      task_id: taskInfo?.id,
      module: isCallAppTask(taskInfo) ? '逛APP赚钱' : '任务中心',
      timemodule: '',
      task_name: taskInfo?.name,
      task_progress: taskInfo?.dayTimes?.progress || taskInfo?.task_progress || '',
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      task_click_position: '去完成',
      resource_location,
    });
    if (FINISH_CAN_JUMP_LINK_EVENT.includes(event)) {
      openURL(taskInfo?.url || '');
    }
    return;
  }

  if (checkTaskCountDown(taskInfo, Date.now())) {
    return toast.show('倒计时结束后再来做任务');
  }

  return finishTask(taskInfo, resource_location);
}

const award = async (task: TaskInfo, resource_location: string = 'retain_popup') => {
  console.log('award task', task);
  const taskInfo = task;
  console.log('award taskInfo', taskInfo);
  const { id, event, name, taskClassify, groupCode, task_progress } = taskInfo;
  fact.click('task_click', {
    c: 'task',
    d: `task1`,
    clicl_action: 'getprize',
    task_id: id,
    module: isCallAppTask(taskInfo) ? '逛APP赚钱' : '任务中心',
    timemodule: '',
    task_name: name,
    task_progress: task_progress || '',
    taskclassify: taskClassify || '',
    groupcode: groupCode || '',
    task_click_position: '待领取',
    resource_location,
  });
  if (
    event === TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH ||
    event === TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER ||
    event === TASK_EVENT_TYPE.UCLITE_SIGN_MINDER
  ) {
    dispatch.task.complete({
      id,
      type: 'complete',
      publishId: taskInfo?.publishId,
      useUtCompleteTask: taskInfo?.useUtCompleteTask,
      params: { task: taskInfo },
    });
  } else {
    if (isHuiChuangAdEffectTask(taskInfo)) {
      const prizes = await dispatch.task.completeCorpTask({
        id,
        publishId: taskInfo?.publishId,
        type: 'award',
        params: { task: taskInfo },
      });
      if (prizes && prizes.length > 0) {
        const validPrizes = prizes.filter((prize) => prize.win);
        if (validPrizes.length) {
          modal.openTreasure(validPrizes, (validPrizes[0]?.rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin');
        }
      }
    } else if (event === TASK_EVENT_TYPE.BRAND_TASK) {
      const prizes = await dispatch.task.completeBrandTask({
        id,
        type: 'award',
        publishId: taskInfo?.publishId,
        params: { task: taskInfo },
      });
      if (prizes && prizes.length > 0) {
        const validPrizes = prizes.filter((prize) => prize.win);
        if (validPrizes.length) {
          modal.openTreasure(validPrizes, (validPrizes[0]?.rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin');
        }
      }
    } else {
      dispatch.task.complete({
        id,
        type: 'award',
        publishId: taskInfo?.publishId,
        params: { task: taskInfo, toast: true },
      });
    }
  }
};

const finishTask = async (task: TaskInfo, resource_location: string = 'retain_popup') => {
  const state = store.getState();
  const taobaoRtaInfo = state.rta.taobaoRtaInfo;
  const rtaScene = state.app?.taobaoRtaConfig?.sceneId;
  const isTaobaoRta = RTA_TASK_TYPE.includes(task?.event) && taobaoRtaInfo;
  fact.click('task_click', {
    c: 'task',
    d: `task1`,
    clicl_action: 'gotask',
    task_id: task.id,
    module: isCallAppTask(task) ? '逛APP赚钱' : '任务中心',
    timemodule: '',
    task_name: task.name,
    taskclassify: task?.taskClassify || '',
    groupcode: task?.groupCode || '',
    award_amount: task?.rewardItems[0]?.amount || '',
    task_progress: task?.dayTimes?.progress || task?.task_progress || '',
    resource_location,
    ...(isTaobaoRta
      ? {
          scene: rtaScene,
          taobao_rta_type: taobaoRtaInfo?.category,
          sid: taobaoRtaInfo?.adInfo?.sid,
          rta_price: taobaoRtaInfo.adInfo?.price,
        }
      : {}),
    task_click_position: '去完成',
  });
  await execWithLock(
    'finish_task_lock',
    async () => {
      await taskActionHandler(task, {location: resource_location});
    },
    3000,
  );
};

const handleClickFact = (taskInfo: TaskInfo, resource_location: string = 'retain_popup') => {
  // 逛app模块打点
  if (isCallAppTask(taskInfo)) {
    fact.click('guangapptask_click', {
      c: 'mokuai',
      d: 'guangapp',
      task_id: taskInfo?.id || '',
      task_name: taskInfo?.name || '',
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
      task_progress: taskInfo?.dayTimes?.progress || '',
      resource_location
    });
    return;
  }
};
