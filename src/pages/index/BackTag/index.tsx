import { createElement, useEffect, useState } from 'rax';
import Image from '@/components/image';
import View from 'rax-view';
import './index.scss';
import { getParam } from '@/lib/qs';
import { dispatch, store } from '@/store';
import config from '@/config';
import { TASK_EVENT_TYPE, TASK_STATUS, TaskInfo } from '@/store/models/task/types';
import {checkTaskFinished, handleCallAppTask} from '../task/help';
import stat from '@/lib/stat';
import Fact from '@/components/Fact';

interface taskContactEntryFace {
  tagName: string;
  showTime: string;
  taskId: string;
  entry: string;
}

const getTaskAward = (taskInfo) => taskInfo?.rewardItems[0]?.amount || '';

const arrowIcon = 'https://img.alicdn.com/imgextra/i1/O1CN01hXdMsb1Q69qp5L1Br_!!6000000001926-55-tps-30-30.svg';

function BackTag() {
  const [flagTag, setFlagTag] = useState<boolean>(false);
  const [task, setTask] = useState<TaskInfo>({} as TaskInfo);
  const [taskContactEntry, setTaskContactEntry] = useState<taskContactEntryFace>({
    tagName: '',
    showTime: '',
    taskId: '',
    entry: '',
  });
  const entry = getParam('entry') || 'unknown';
  const resource = store?.getState()?.resource;
  useEffect(() => {
    let taskList = resource?.[config.backTagResourceCode]?.taskList || [];
    let attributes = resource?.[config.backTagResourceCode]?.attributes || {};
    const resultEntryObj = attributes?.taskContactEntry?.find((item) => item?.entry === entry);
    if (resultEntryObj) {
      const resultTask = taskList?.find((item) => {
        return item?.event === TASK_EVENT_TYPE?.CALL_APP_NO_AWARD && String(item?.id) === resultEntryObj?.taskId;
      });
      if (resultTask && resultTask?.state === TASK_STATUS?.TASK_DOING) {
        setTask(resultTask);
        setTaskContactEntry(resultEntryObj);
        setFlagTag(true);
        dispatch.highValueTask.resourceExposure({
          taskInfo: resultTask,
          actionType: 'EXPOSURE',
          code: config?.backTagResourceCode
        })
        if (resultEntryObj?.showTime) {
          const timer = setTimeout(() => {
            setFlagTag(false);
            clearTimeout(timer);
          }, resultEntryObj?.showTime);
        }
        return;
      }
    }
    setFlagTag(false);
  }, [resource?.uc_pigg_back_tag]);
  const toBack = async () => {
    stat.click('resource_click', {
      c: 'label',
      d: 'label',
      resource_location: 'retain_label',
    });
    stat.click('task_click', {
      c: 'label',
      d: 'label',
      task_id: task?.id,
      task_name: task?.name,
      taskclassify: task?.taskClassify,
      groupcode: task?.groupCode,
      award_amount: getTaskAward(task),
      task_count: task?.dayTimes?.progress,
      isfinish: checkTaskFinished(task) ? 1 : 0,
      resource_location: 'retain_label',
    });
    handleCallAppTask(task)
    setFlagTag(false);
  };
  return flagTag ? (
    <Fact
      c="label"
      d="label"
      expoLogkey="resource_exposure"
      expoExtra={{
        resource_location: 'retain_label',
      }}
      noUseClick
    >
      <Fact
        c="label"
        d="label"
        expoLogkey="task_exposure"
        expoExtra={{
          task_id: task?.id,
          task_name: task?.name,
          taskclassify: task?.taskClassify,
          groupcode: task?.groupCode,
          award_amount: getTaskAward(task),
          task_count: task?.dayTimes?.progress,
          isfinish: checkTaskFinished(task) ? 1 : 0,
          resource_location: 'retain_label',
        }}
        noUseClick
      >
        <View onClick={toBack} className="back-tag">
          <Image className="back-tag-icon" source={arrowIcon} alt="" />
          {taskContactEntry?.tagName}
        </View>
      </Fact>
    </Fact>
  ) : null;
}

export default BackTag;
