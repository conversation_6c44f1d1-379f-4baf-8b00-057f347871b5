import {createElement, Component} from 'rax';
import {connect} from 'rax-redux';
import { StoreDispatch, StoreState } from '@/store';
import Banner from '@/pages/index/Banner';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';
import { EBannerNuProtectDay, IBannerAd } from '@/store/models/app';

enum EBannerType {
  FIRST = '1',
  SECOND = '2',
}

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  isTop: boolean;
};

class BannerWrap extends Component<IProps> {

  showBannerByNuProtectDay = (type: EBannerType) => {
    const { signList, cdFuliAdProtect } = this.props;
    const bannerNuProtectDay = type === EBannerType.FIRST ? this.props.bannerNuProtectDay : this.props.banner2NuProtectDay;
    if (bannerNuProtectDay === EBannerNuProtectDay.SEVEN) {
      return signList?.[0]?.event !== TASK_EVENT_TYPE.UCLITE_SIGN;
    } else if (bannerNuProtectDay === EBannerNuProtectDay.ONE) {
      return !cdFuliAdProtect;
    } else {
      return true;
    }
  }

  /**
   * 裂变 banner
   * 1、7日nu 不展示
   * 2、非7日nu 则可展示 7 日，重新投放则重新计算展示天数
   * 3、非7日nu ，若最近3天内有新邀请成功过，展示
   */
  hiddenBackupAd = (backupAd: IBannerAd | undefined) => {
    if (backupAd?.id !== '裂变') return false;
    const { signList, now, cdInviteRecordIn3days } = this.props;

    if (signList?.length && signList[0]?.event === TASK_EVENT_TYPE.UCLITE_SIGN) return true;

    const setStorage = () => {
      const lastShowTime = now + 1000 * 60 * 60 * 24 * 7;
      const updateFlag = backupAd?.updateFlag;
      localStorage.setItem('uc-lite-welfare-new-invite-banner-last-ts', JSON.stringify({ updateFlag, lastShowTime }));
    }

    // 获取本地数据记录
    const storageInviteBanner = JSON.parse(localStorage.getItem('uc-lite-welfare-new-invite-banner-last-ts') as string || '{}');
    // 判断可展示最后一天日期
    if (!storageInviteBanner?.lastShowTime) {
      setStorage();
      return false;
    } else {
      // 存在记录，判断活动是否有更新，有则重新计算展示周期
      if (backupAd?.updateFlag !== storageInviteBanner?.updateFlag) {
        setStorage();
        return false;
      }
      // 判断是否符合3天内有邀请记录
      if (cdInviteRecordIn3days) return false;
      // 判断展示期限
      if (storageInviteBanner?.lastShowTime < now) {
        return true;
      } else {
        return false;
      }
    }
  }

  render() {
    const {
      bannerBrandAd,
      bannerAdLoaded,
      backupAd,
      bannerBrandAd2,
      bannerAd2Loaded,
      backupAd2,
      exposure,
      click,
    } = this.props;

    if (this.props.isTop) {
      return <Banner
        x-if={this.showBannerByNuProtectDay(EBannerType.FIRST)}
        bannerBrandAd={bannerBrandAd}
        bannerAdLoaded={bannerAdLoaded}
        backupAd={backupAd}
        exposure={exposure}
        click={click}
        bannerBrandAdName={'bannerBrandAd'}
        hiddenBackupAd={this.hiddenBackupAd(backupAd)}
      />
    } else {
      return <Banner
        x-if={this.showBannerByNuProtectDay(EBannerType.SECOND)}
        bannerBrandAd={bannerBrandAd2}
        bannerAdLoaded={bannerAd2Loaded}
        backupAd={backupAd2}
        exposure={exposure}
        click={click}
        bannerBrandAdName={'bannerBrandAd2'}
        hiddenBackupAd={this.hiddenBackupAd(backupAd2)}
      />
    }
  }
}

const mapState = (state: StoreState) => {
  const backupAdList = state.app.bannerAdList;
  const backupAd2List = state.cms.bannerAdList;  
  const now = state.task.now;
  const backupAd = backupAdList.find(ad => {
    return ad.startTime <= now && now <= ad.endTime;
  });
  const backupAd2 = backupAd2List.find(ad => {
    return ad.startTime <= now && now <= ad.endTime;
  });
  
  return {
    bannerBrandAd: state.ad.bannerBrandAd,
    bannerAdLoaded: state.ad.bannerAdLoaded,
    backupAd,
    bannerBrandAd2: state.ad.bannerBrandAd2,
    bannerAd2Loaded: state.ad.bannerAd2Loaded,
    backupAd2,
    signList: state.task.signin,
    bannerNuProtectDay: state.app.bannerNuProtectDay,
    banner2NuProtectDay: state.app.banner2NuProtectDay,
    now: state.task.now,
    cdFuliAdProtect: state.app.cdFuliAdProtect,
    cdInviteRecordIn3days: state.app.cdInviteRecordIn3days,
  }
};

const mapDispatch = (dispatch: StoreDispatch) => {
  return {
    exposure: dispatch.ad.exposure,
    click: dispatch.ad.click,
  }
};

export default connect(mapState, mapDispatch)(BannerWrap);

