import { TaskInfo } from "@/store/models/task/types";

export interface IMultiBalance {
  [key: string]: {
    amount: number;
    balance: number;
    flowId: number;
  }
}

export interface IQueryTaskRes {
  inviteAddAmount: number;
  rate: number;
  timestamp: number;
  values: TaskInfo[];
}

export interface IDiamondConf {
  [key: string]: any
}

export interface ClientInfo {
  kps_wg: string;
  sign_wg: string;
  uId: string;
  nickname: string;
  avatar_url: string;
  vCode: number;
  utdId: string;
  ut: string;
  ds: string;
  loginStatus: boolean;
}

export const enum WORKER_EVENT_TYPE {
  UC_TASK_WORKER_QUERY = 'UC_TASK_WORKER_QUERY', // 查询已经注册的任务
  UC_TASK_REGISTER = 'UC_TASK_REGISTER', // worker把该业务注册过的任务
  UC_TASK_COMPLETE = 'UC_TASK_COMPLETE', // worker监听到任务完成
}
