import { createElement, Component, createRef, CSSProperties } from 'rax';
import ScrollView from 'rax-scrollview';
import { connect } from 'rax-redux';
import View from 'rax-view';
import Header from './header';
import BackTag from './BackTag';
import './index.scss';
import { StoreDispatch, StoreState, store, dispatch } from '@/store';
import ucapi from '@/utils/ucapi';
import config from '@/config';
import global, { EGolbalInstance } from '@/utils/global';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { doOnAttach } from '@/lib/prerender';
import SignCard from '@/pages/index/SignCard';
import { openURL } from '@/pages/index/task/help';
import { inPeriod } from '@/utils/date';
import event from '@/utils/event';
import { handleDeleteSearchWordsCacheOne, openSearchWordTaskCompletedDialog } from '@/store/models/task/helper';
import { preThirdTokenTask } from '@/utils/token_task';
import { visitRecorderAfterToFinishTask } from '@/store/models/task/taskMonitor';
import { isWeb } from 'universal-env';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';
import { initPageLogParam } from '@/utils/log';
import ModalWrap from '@/components/modals/modal_wrap';
import { WORKER_EVENT_TYPE } from '@/pages/index/typings';
import { addParams, isInsetPage } from '@/utils/url';
import { getParam } from '@/lib/qs';
import { updateWidget, widget1, widget2 } from '@/lib/utils/desktop';
import { isHuiChuangAdEffectTask } from '../index/task/help';
import AsyncComponent from '@/lib/async_component';

// import Image from '@/components/image';
import Task from './task';
import CorpTask from './CorpTask';
import BannerWrap from './BannerWrap';
// import DoubleAwardBanner from '@/pages/index/DoubleAwardBanner';
// import FarmTaskBanner from '@/pages/index/FarmTaskBanner';
import WelfareCard from '@/pages/index/welfareCard';
// import TopBanner from './TopBanner';

import fact from '@/lib/fact/index';
import factStat from '@ali/fact-stat';

import { RTA_TASK_TYPE, TASK_STATUS } from '@/store/models/task/types';
import eventer, { EventMap } from "@/lib/utils/event";

type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {};
interface IState {
  showStickyHeader: boolean;
  listLoading: boolean;
  updateLayout: boolean;
  treasureShow: boolean;
}

class PageIndex extends Component<IProps, IState> {
  private personContainerObserver?: IntersectionObserver;
  state: IState = {
    showStickyHeader: false,
    listLoading: false,
    updateLayout: false,
    treasureShow: false,
  };
  scrollView = createRef();
  refreshFlag = 0;
  cacheAdExposure = {};
  constructor(props) {
    super(props);
    factStat.on('auto_exposure', (data) => {
      const {
        ad_id: adId,
        arg1,
        task_state: taskState,
        task_event: taskEvent,
        account_id: accountId,
        slot_id: slotId,
        task_id: taskId,
      } = data || {};
      // 汇川广告任务处理
      if (this.isHCadTask(adId, accountId, slotId, taskState, taskEvent)) {
        this.handleHCadTask(data, arg1, accountId, slotId, taskId);
        return;
      }

      // 品牌广告处理
      if (this.isBrandTask(adId, taskState, taskEvent)) {
        console.log('品牌广告真曝光');
        dispatch.ad.exposure('taskBrandAd');
        return;
      }
      // RTA任务处理
      if (this.isRtaTask(taskEvent)) {
        console.log('[AD] rta广告曝光', taskId);
        dispatch.rta.ratTaskExposure({});
        return;
      }

      // 高价值任务处理
      if (this.isHightValueTask(taskEvent)) {
        dispatch.highValueTask.rtaTaskExposure();
      }
    });
  }

  isTaskDoingOrNotCompleted(taskState) {
    return [TASK_STATUS.TASK_DOING, TASK_STATUS.TASK_NOT_COMPLETED].includes(taskState);
  }

  isBrandTask(adId, taskState, taskEvent) {
    return adId && taskEvent === TASK_EVENT_TYPE.BRAND_TASK && this.isTaskDoingOrNotCompleted(taskState);
  }

  isRtaTask(taskEvent) {
    return RTA_TASK_TYPE.includes(taskEvent);
  }
  isHightValueTask(taskEvent) {
    return taskEvent === TASK_EVENT_TYPE.HIGH_VALUE_TASK;
  }

  isHCadTask(adId, accountId, slotId, taskState, taskEvent) {
    return (
      adId &&
      accountId &&
      slotId &&
      this.isTaskDoingOrNotCompleted(taskState) &&
      isHuiChuangAdEffectTask({ event: taskEvent } as any)
    );
  }

  handleHCadTask(data, arg1, accountId, slotId, taskId) {
    console.log('二方广告真曝光', data);
    dispatch.ad.corpTaskExposure({ accountId, slotId });

    fact.event('task_hcad_display', {
      event_id: '19999',
      task_id: data.task_id,
      task_name: data.task_name,
      c: data.stat_c,
      d: data.stat_d,
    });
    // 禁用了自动曝光埋点，命令式方式上报曝光统计，只统计一次
    if (!this.cacheAdExposure[taskId]) {
      fact.exposure(arg1, {
        ...data,
        c: data.stat_c,
        d: data.stat_d,
      });
      this.cacheAdExposure[taskId] = true;
    }
  }

  setPersonContainerObserver() {
    // note: 项目中没有caniuse，本着不添加第三方包劣化包体积和启动性能的原则，加上是业务代码简单粗暴判断一下运行时是否可用此Api
    if (!window.IntersectionObserver || typeof window.IntersectionObserver !== 'function') {
      this.personContainerObserver = undefined;
      return;
    }
    const target = document.querySelector('#wrap-user');
    if (!target) {
      this.personContainerObserver = undefined;
      return;
    }
    const callBack = (entries) => {
      entries.forEach((entry) => {
        if (entry?.isIntersecting === this.state.showStickyHeader) {
          this.setState({
            showStickyHeader: !entry.isIntersecting,
          });
        }
      });
    };
    this.personContainerObserver = new IntersectionObserver(callBack, { threshold: 1.0 });
    this.personContainerObserver.observe(target);
  }

  async componentDidMount() {
    doOnAttach(() => {
      this.initPageview();
      document.addEventListener('UCEVT_OnReceiveMessage', this.onReceiveMessage);
    });
    this.setPersonContainerObserver();
    global.set(EGolbalInstance.SCROLL_VIEW, this.scrollView.current);
    // 先渲染首屏元素，开宝箱入口延迟加载
    setTimeout(() => {
      this.setState({
        treasureShow: true,
      });
    }, 1000);
  }

  componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<IState>, snapshot?: any) {
    if (!prevProps.firstInitd && this.props.firstInitd) {
      const renderTaskExist = this.props.taskList && this.props.taskList.length > 0;
      const serverTaskExist = this.props.resTaskList && this.props.resTaskList.length > 0;
      const appInitCostTime = Date.now() - window.__app_init_time;
      if (renderTaskExist) {
        tracker.log({
          category: WPK_CATEGORY_MAP.APP_TASK_INIT,
          msg: '有任务',
          wl_avgv1: appInitCostTime,
          c1: serverTaskExist ? '0' : '1',
        });
      } else {
        tracker.log({
          category: WPK_CATEGORY_MAP.APP_TASK_INIT,
          msg: '无任务',
          wl_avgv1: appInitCostTime,
          c1: serverTaskExist ? '0' : '1',
        });
      }
    }
    if (!prevProps.animationReady && this.props.animationReady) {

      this.listen();
      preThirdTokenTask();
    }
  }

  componentWillUnmount() {
    this.unlisten();
    this.personContainerObserver?.disconnect();
  }

  listen() {
    eventer.on(EventMap.PageVisible,  this.handlePageVisible);
    eventer.on(EventMap.PageHidden, () => {
      updateWidget(widget1);
      updateWidget(widget2);
    });
    // 针对 ios 监听 push 开启事件（ios 不用打开设置界面）
    document.addEventListener('UCEVT_OnPushNotiOpened', this.props.init);
    // 搜索关键词浏览15s端完成任务通知
    document.addEventListener('UCEVT_Welfare_onReadOnceMissionFinished', handleDeleteSearchWordsCacheOne);
  }

  onReceiveMessage = (event) => {
    const data = event?.detail?.data || {};
    const receiveTypes = [WORKER_EVENT_TYPE.UC_TASK_REGISTER, WORKER_EVENT_TYPE.UC_TASK_COMPLETE];
    if (data.businessKey !== config.businessKey || !receiveTypes.includes(data.type)) {
      return;
    }
    switch (data.type) {
      case WORKER_EVENT_TYPE.UC_TASK_REGISTER:
        // 已经注册过的任务
        const taskIds = data.taskIds;
        console.log('appWorkerRegisterTaskIds:', taskIds);
        if (taskIds) {
          tracker.log({
            category: WPK_CATEGORY_MAP.TASK_WORKER,
            msg: `查询到注册的任务`,
            sampleRate: 1,
            w_succ: 1,
            c1: JSON.stringify(taskIds),
            c2: getParam('entry'),
          });
          dispatch.task.updateState({
            workerRegisterTaskIds: taskIds,
          });
        } else {
          tracker.log({
            category: WPK_CATEGORY_MAP.TASK_WORKER,
            msg: `未查询到注册任务`,
            sampleRate: 1,
            w_succ: 1,
            c2: getParam('entry'),
          });
        }
        break;
      case WORKER_EVENT_TYPE.UC_TASK_COMPLETE:
        console.log('worker触发任务完成');
        fact.event('worker_complete_task', {
          action: 'completetask',
          task_id: data.taskId || '',
        });
        if (data.taskId) {
          tracker.log({
            category: WPK_CATEGORY_MAP.TASK_WORKER,
            msg: `worker触发任务完成`,
            sampleRate: 1,
            w_succ: 1,
            c1: `${data.taskId}`,
            c2: getParam('entry'),
          });
          dispatch.task.complete({ id: data.taskId, type: 'complete' });
        }
    }
  };

  unlisten() {
    document.removeEventListener('UCEVT_OnPushNotiOpened', this.props.init);
    document.removeEventListener('UCEVT_Welfare_onReadOnceMissionFinished', handleDeleteSearchWordsCacheOne);
  }

  handlePageVisible = async () => {
    // 跳转出去回来需要刷新的标志, 看视频广告为false
    const needPageVisibleUpdate = store.getState().app.needPageVisibleUpdate;
    if (!needPageVisibleUpdate) {
      setTimeout(() => {
        // 视频广告任务异步查奖
        dispatch.task.dealwithQueryAndGetAward(false);
      }, 2000)
      dispatch.app.updateState({
        needPageVisibleUpdate: true,
      });
      return
    }
    // 更新接口数据
    await this.getInitData();
  }

  getInitData = async () => {
    if (this.refreshFlag === 0) {
      await visitRecorderAfterToFinishTask(this.props.resTaskList);
      this.refreshFlag = 1;
      event.emit('appearInitData');
      openSearchWordTaskCompletedDialog();
      this.props.init();
      setTimeout(() => {
        this.refreshFlag = 0;
      }, 1000);
    }
  };

  // note: 先简单在class中实现需要隐藏nav的标识，之后根据业务决定是否抽离扩展出一个manager来处理nav的状态
  get isInsetPage(): boolean {
    return isInsetPage();
  }

  handleScroll = (e) => {
    if (this.personContainerObserver) {
      // note: 使用监听逻辑实现，而不是使用魔法数字判定，理论上scroll的判断的代码可以清除掉，为了代码的稳定性，先对IntersectionObserver Api做一下降级处理
      return;
    }
    const scrollTop = e.target.scrollTop;
    const { showStickyHeader } = this.state;
    const showDistanceTop = this.isInsetPage ? 52 : 130; // note: 理论上当个人信息刚好完全消失的时候需要出现粘性布局信息，同理收起逻辑一样
    if (scrollTop > showDistanceTop && !showStickyHeader) {
      this.setState({
        showStickyHeader: true,
      });
      return;
    }
    const hideDistanceTop = this.isInsetPage ? 52 : 110;
    if (scrollTop < hideDistanceTop && showStickyHeader) {
      this.setState({
        showStickyHeader: false,
      });
    }
  };


  initPageview = () => {
    initPageLogParam('index', true);
    dispatch.app.checkInstalledTaobao();
  };

  handleToHelp = () => {
    const { helpCenterlink, uid } = this.props;
    const csLink = addParams(helpCenterlink, {
      uid,
    });
    openURL(csLink);
  };

  handleToRuleAll = () => {
    const { clientType, liteActivityRuleLink, activityRuleLink } = this.props;
    const link = clientType === 'UCLite' ? liteActivityRuleLink : activityRuleLink;
    ucapi.base.openURL({ url: link });
  };

  renderRule = () => {
    return (
      <View
        x-if={this.props.animationReady}
        className={`wrap-rule wrap-rule-margin`}
      >
        <View className="rule-all" onClick={this.handleToRuleAll}>
          活动通用总则 |
        </View>
        <View
          className="rule-this"
          onClick={() => {
            ucapi.base.openURL({ url: this.props?.curRulelink });
          }}
        >
          本活动规则 |
        </View>
        <View className="rule-customer" onClick={this.handleToHelp}>
          咨询客服
        </View>
      </View>
    );
  };

  getTaskListStyle = () => {
    const styles: CSSProperties = {};
    const { showNotice, isSpringPeriod } = this.props;
    let noticeHeight = 70;
    let marginTopNum = -464;
    if (isSpringPeriod) {
      marginTopNum = -444;
    }
    if (isInsetPage()) {
      marginTopNum = -500;
    }
    styles.marginTop = `${showNotice ? marginTopNum + noticeHeight : marginTopNum}rpx`;
    return styles;
  };

  render() {
    const { indexResource } = this.props;
    const { treasureShow } = this.state;

    return (
      <ScrollView
        id="pageScrollView"
        className="page-root"
        ref={this.scrollView}
        onScroll={this.handleScroll}
        style={{ background: indexResource.bgColor }}
      >
        {isWeb ? (
          <AsyncComponent
            showStickyHeader={this.state.showStickyHeader}
            isInsetPage={this.isInsetPage}
            resolve={() => import('@/pages/index/stickyHeader')}
          />
        ) : null}
        <Header isInsetPage={this.isInsetPage} />
        {isWeb ? (
          <BackTag />
        ) : null}
        
        <View className="wrap-task-list" style={this.getTaskListStyle()}>
          {/* <TopBanner /> */}
          {/*<BannerWrap isTop={true} />*/}
          <SignCard />
          {/* 已经切换成资源位，下线 */}
          {/* <FarmTaskBanner /> */}
          <WelfareCard />
          <CorpTask />
          <BannerWrap />
          <Task />
          {isWeb && this.props.showFeedsTask ? <AsyncComponent resolve={() => import('./FeedsTask')} /> : null}
          {this.renderRule()}
        </View>
        {isWeb ? (
          <>
            {treasureShow ? (
              <AsyncComponent hiddenTreasure={false} resolve={() => import('./treasure')} />
            ) : null}
            <AsyncComponent
              hiddenTreasure={false}
              resolve={() => import('@/components/InviteQrcodePanel')}
            />
            <ModalWrap />
            <AsyncComponent resolve={() => import('@/components/font-compress/index')} />
          </>
        ) : null}
      </ScrollView>
    );
  }
}

const mapState = (store: StoreState) => {
  const { noticeStartTime, noticeEndTime } = store.cms.indexNoticeConfig || {};
  const showNotice = inPeriod(noticeStartTime, noticeEndTime, store.task.now);

  return {
    uid: store.user?.uid,
    showNotice,
    curTime: store.task?.now,
    resTaskList: store.task?.resTaskList,
    taskList: store.task?.taskList,
    firstInitd: store.app.firstInitd,
    indexResource: store.app.indexResource,
    animationReady: store.app.animationReady,
    showFeedsTask: store.app.welfareBallState,
    helpCenterlink: store.app.helpCenterlink,
    curRulelink: store.app.curRulelink,
    clientType: store.app.clientType,
    liteActivityRuleLink: store.app.liteActivityRuleLink,
    activityRuleLink: store.app.activityRuleLink,
    isSpringPeriod: store.app.isSpringPeriod,
  };
};

const mapDispatch = (dispatch: StoreDispatch) => ({
  init: dispatch.app.init,
  updateAll: dispatch.app.updateAll,
});

export default connect(mapState, mapDispatch)(PageIndex);
