import network from '@/lib/network';
import config from '@/config';
import { getQueryUCParamsObj, getParam } from '@/lib/qs';
import { isNode } from 'universal-env';
import { ITaskSwitchStatus } from './typings';
import cmsRes from '@ali/cms-res';
import { ICMSRes, CMS_RESOURCE_CODES } from '@/store/models/cms/typings';
import { IQueryResourceInfo } from '@/store/models/resource/types';

export const getMainVersion = () => {
  // 测试环境MAIN_VERSION 格式为1.20.0-beta.1244142421, 去掉多余的字符
  // @ts-ignore
  const version = (MAIN_VERSION || '').replace(/-.+/, '');
  if (+version.replace(/\./g, '') === 0) {
    return config.fve;
  }
  return version;
};
export function geneRequestId() {
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
}

// node环境调用接口补充 URL上解析的公参，for 串行SSR or 并行SSR但数据预取失败场景
const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {};

/**
 * 资源位列表 接口请求
 */
export const queryResourceCodesInfo = (kps: string): Promise<IQueryResourceInfo > | null => {
  const { coralHost, appId, taskResourceCode, novelResourceCode, clouddriveResourceCode, tagPersonResourceCode, backInterceptResourceCode, backTagResourceCode, taskNestResourceCode, limitTaskResourceCode } = config;
  const codes = [taskResourceCode, novelResourceCode, clouddriveResourceCode, tagPersonResourceCode, backInterceptResourceCode, backTagResourceCode, taskNestResourceCode, limitTaskResourceCode];
  const requestId = geneRequestId();
  const fve = getMainVersion();
  const entry = getParam('entry') || 'unknown';
  const query = {
    codes: codes.join(','),
    kps,
    requestId,
    entry,
    appId,
    fve,
    activeUser: 1,
    prioritySize: 1,
    ...ucParamsQueryObj,
  };
  return network.get(`${coralHost}/uclite/queryByMultiResource`, query, {
    originalResponse: true,
  }).catch(err => {
    console.error(`[http] GET queryByMultiResource  | params ${JSON.stringify(query)} | error ${JSON.stringify(err)}`, err);
    return null;
  });
}

export async function getCmsData(firstInit = false) {
  try {
  
    const cmsResData: ICMSRes = await cmsRes.init({
      // 请求域名
      host: config.cmsHost,
      // 合作方ID（必填)
      partner_id: 'uc-interactive-service',
      // 分组编码(必填)
      group: 'uc-interactive-service',
      // 自定义公参串 (如果留空，sdk请求时 会默认加上 uc_param_str=cgligimiosntwilasspijbnwdnnifrpfbivecpchbtbmprpvstsvgddsudkt )
      uc_param_str: getParam('uc_param_str') || 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf',
      // 数据缓存类型(默认: LS). 可选值 WX | LS | SS | UNI | CUSTOM | NONE. NONE 表示数据不缓存
      cacheType: 'NONE',
      // 自定义额外参数
      params: {
        ver: '200000',
        apply_res: Object.values(CMS_RESOURCE_CODES).join(','),
        entry: getParam('entry') || 'unknown',
        ...ucParamsQueryObj,
      },
      timeout: firstInit ? 12e3 : 60e3
    });
    return cmsResData;
  } catch (err) {
    return null;
  }
}

export function queryDiamondConf(kps = '') {
  const api = `${config.coralHost}/aggregation/home?kps=${kps}`;
  const req_services = `[{"module":"diamond","key":"ucliteWelfare"}]`;
  return network
    .get(api, {
      req_services,
      ...ucParamsQueryObj,
    })
    .then((res) => {
      return res?.[0]?.data;
    })
    .catch((err) => {
      console.error(
        `[http] GET ${api} | params ${JSON.stringify(ucParamsQueryObj)} | error ${JSON.stringify(err)}`,
        err,
      );
      return null;
    });
}

// export function queryMultiBalance(kps) {
//   const { moduleCodeCoin, moduleCodeAmount } = config;
//   const moduleCodeList = `${moduleCodeCoin},${moduleCodeAmount}`;
//   const { appId, coralHost } = config;
//   const params = { appId, moduleCodeList, kps };
//   const api = `${coralHost}/currency/v1/queryMultiBalance`;
//   return network.get(api, params).catch((err) => {
//     console.error(`[http] GET ${api} | params ${JSON.stringify(params)} | error ${JSON.stringify(err)}`, err);
//     return null;
//   });
// }

export function queryEquityGiftList(kps) {
  const { coralHost, walletAppId, rightsExchangeCode, rightsExchangeSceneCode } = config;
  const requestId = geneRequestId();
  const api = `${coralHost}/uclite/exchange/queryGiftBagList`;
  const params = {
    appId: walletAppId,
    kps: kps || '',
    requestId,
    ruleCode: rightsExchangeCode,
    sceneCode: rightsExchangeSceneCode,
    ...ucParamsQueryObj,
  };
  return network.get(api, params).catch(err => {
    console.error(`[http] GET ${api} | params ${JSON.stringify(params)} | error ${JSON.stringify(err)}`, err);
    return null;
  });
}

export function queryTask(kps) {
  const { coralHost, appId, moduleCodeTask } = config;
  const extraParams = kps ? { kps, ...ucParamsQueryObj } : { ...ucParamsQueryObj };
  const params = { appId, moduleCode: moduleCodeTask, fve: getMainVersion(), activeUser: 1, apiVersion: "2",  ...extraParams };
  const targetUrl = `${coralHost}/uclite/query`;
  console.log(`[http] before GET ${targetUrl} | params ${JSON.stringify(params)}`);
  return network.get(targetUrl, params).catch((err) => {
    console.error(`[http] GET ${targetUrl} | params ${JSON.stringify(params)} | error ${JSON.stringify(err)}`, err);
    return null;
  });
}

// 素人任务，已下线
// export function queryAmateurTask(kps) {
//   const { taskHost, amateurAppId, amateurModuleCode } = config;
//   const ucParamsQueryObj = isNode ? getQueryUCParamsObj() : {}
//   const extraParams = kps ? { kps, ...ucParamsQueryObj } : {...ucParamsQueryObj};
//   const params = { appId: amateurAppId, moduleCode: amateurModuleCode,fve: getMainVersion(), ...extraParams};
//   const targetUrl = `${taskHost}/task/query`;
//   return network.get(targetUrl, params)
//     .catch((err) => {
//       console.error(`[http] GET ${targetUrl} | params ${JSON.stringify(params)} | error ${JSON.stringify(err)}`, err);
//       return null;
//     });
// }

export function queryShopList() {
  return network
    .get(`${config.shopHost}/uclite/shop/queryFeatureShopList`, {
      appId: config.appId,
      pageSize: 100,
      pageNo: 1,
      shopType: 'low_price',
    })
    .then((res) => {
      res.items = res.items?.map((item) => {
        const url = item.pictUrl.startsWith('http')
          ? item.pictUrl.replace('http://', 'https://')
          : 'https:' + item.pictUrl;
        return {
          ...item,
          pictUrl: url + '_460x460q90.jpg',
        };
      });
      return res;
    })
    .catch(() => null);
}

// 查询换端任务的开关状态
export function queryTaskSwitchStatus(kps, rtaType, idfa): Promise<ITaskSwitchStatus> {
  const { taskHost, appId, moduleCodeTask } = config;
  const params = { appId, moduleCode: moduleCodeTask, rtaType, idfa };
  return network.get(`${taskHost}/task/rta/checkTaskDisplay`, params).catch(() => null);
}

// 查询个人资产接口
export function queryUserMultiBalance(kps: string){
  const { moduleCodeCoin, moduleCodeAmount, moduleCodeOldVersionCash, highVlaueModuleCode, appId, coralHost } = config;
  const moduleCodeList = `${moduleCodeCoin},${moduleCodeAmount},${moduleCodeOldVersionCash},${highVlaueModuleCode}`;
  const params = { appId, moduleCodeList, kps,  toModuleCode: moduleCodeAmount, ...ucParamsQueryObj };
  const api = `${coralHost}/currency/v1/ucliteHomepage`;
  return network.get(api, params).catch((err) => {
    console.error(`[http] GET ${api} | params ${JSON.stringify(params)} | error ${JSON.stringify(err)}`, err);
    return null;
  });
}
