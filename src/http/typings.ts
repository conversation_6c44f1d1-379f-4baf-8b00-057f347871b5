/** 任务事件编码 */
export const enum TASK_EVENT_TYPE {
  COUNT = 'count',
  /** 跳转链接任务 */
  JUNP_URL = 'junp_url',
  /** 搜索 */
  SEARCH = 'search',
  /** 广告 */
  VIDEO_AD = 'video_ad',
  /** 看视频广告，不合并(客户端任务类型) */
  WATCH_VIDEO = 'watch_video',
  /** 分享 */
  SHARE = 'share',
  /** 阅读 */
  READ = 'read',
  /** 幽默社区 */
  HUMOR_READ = 'humor_read',
  /** 玩x局斗地主 */
  GAME_DDZ = 'minigame_num_ddz',
  /** 玩x局成语接龙 */
  GAME_CCJL = 'minigame_num_ccjl',
  /** 去C游戏任意充值 */
  RECHARGE_GAME = 'minigame_recharge_paidgame',
  GAME_POINT_TIME = 'minigame_time_pointgame',
  GAME_PLAY_TIME = 'minigame_time_all',
  /** 实名任务 */
  CERTIFICATION = 'certification',
  /** 额外奖励任务 */
  EXTRA_AWARD = 'uc-piggy-extra',
  /** 淘宝调端 */
  CALL_TB = 'call_tb',
  /** link任务 */
  LINK = 'link',
  /** 信息流跳链 */
  IFLOW_LINK = 'iflow_link',
  /** 调端手淘充值中心 */
  THIRD_ENTRY_TAO_RECHARGE = 'third_entry_tao_recharge',
  /** 单日蓄水父任务 */
  STORE_PARENT = 'store_piggy',
  /** 单日蓄水子任务 */
  STORE_CHILD = 'sub_piggy',
  /** 玩转手猫任务 */
  THIRD_ENTRY_TMALL = 'third_entry_tmall',
  /** 绑定支付宝（客户端任务） */
  BIND_ALIPAY_ACCOUNT = 'bind_alipay_account',
  /** 二方合作任务(token) */
  LINK_TOKEN = 'link_token',
  /** 签到任务（点击就算完成) */
  SIGNIN = 'signin',
  /** 云备份 */
  IS_SET_BACKUP = 'is_set_backup',
  // 看信息流文章或视频任务
  STORE_READ_TIME = 'store_read_time',
  // 品牌广告任务
  BRAND_TASK = 'ad_brand',
  // 二方广告任务
  CORP_APP_TASK = 'ad_inside_group',
  // 签到领福卡任务
  WUFU_CARD = 'wufu_card',
  TREASURE = 'uclite_treasure',
  // 小说阅读任务
  STORE_NOVEL_TIME = 'store_novel_time',
}


export enum TASK_STATUS {
  TASK_DOING = 0,
  TASK_COMPLETED = 1,
  TASK_CONFIRMED = 2,
  TASK_REPEAT = 3,
  TASK_NOT_READY = 4,
  TASK_PRE_TASK_NOT_FINISH = 5,
  TASK_TIMES_LIMIT = 6,
  TASK_ERROR = 7,
  TASK_NOT_COMPLETED = 8,
  TASK_INVALID_TIME = 9
}

export interface RewardItem {
  name: string;
  mark: string;
  amount: number;
  icon: string;
}

export enum TASK_FLAG {
  EXTRA_TASK = 'EXTRA_TASK'
}

export interface TaskInfo {
  /** 任务id */
  id: number;
  preId?: number;
  /** 任务名称 */
  name: string;
  /** 任务描述 */
  desc: string;
  /** 任务事件 */
  event: TASK_EVENT_TYPE;
  /** 任务完成目标 */
  target: number;
  /** 任务进度 */
  progress: number;
  /** 排序 */
  sort: number;
  /** 图标 */
  icon: string;
  /** 任务跳转链接 */
  url: string;
  /** 任务按钮名称 */
  btnName: string;
  /** 0 任务进行中 1 任务完成 2 任务奖励完成 3 任务重复完成 4 任务未完成 5 前置任务未完成 6 任务受限,次数限制 7 任务不存在 8 领奖失败 */
  state: TASK_STATUS;
  /** 预告文案 */
  preMessage?: string;
  rewardItems: RewardItem[]
  /** 画像 */
  profile?: string;
  /** 任务完成时间 */
  completeTime: string;
  /* 任务开始时间 */
  beginTime: number;
  /* 每日每次完成间隔 */
  dayTimesInterval?: number;

  taskFlag?: TASK_FLAG;

  adId?: string; // 广告任务附加的属性，在前端将广告id填入
  sid?: string; // 广告任务附加的属性，广告请求id
  accountId?:string;// 广告账户id
  slotId?:string; // 广告slotId

  token?: string; // 广告用的加密串，服务端返回的。
  extra?: string; // 额外字段

  toDelete?: boolean; // 数据处理过程中，用于筛选准备删除的任务
  errMsg?: string
}

export interface ITaskSwitchStatus{
  display: boolean;
}
