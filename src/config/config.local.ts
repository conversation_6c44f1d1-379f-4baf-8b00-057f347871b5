interface IConfig {
  actId: string;
  businessKey: string;
  currencyToken: string;
  currencySalt: string;

  appCode: 'uclite' |'uc'
  /** 微服务 */
  coralHost: string;
  /** 素人任务appId */
  amateurAppId: string;
  /** 素人任务moduleCode */
  amateurModuleCode: string;
  /** appId */
  appId: string;
  /** 任务服务host */
  taskHost: string;
  navHost: string;
  shopHost: string;
  cmsHost: string;
  shopUrl: string;
  /** 任务Code */
  moduleCodeTask: string;
  // 福卡签到任务code
  moduleCodeFukaTask: string;
  /** bindHost */
  bindHost: string;
  /** 设备绑定code */
  moduleCodeBind: string;
  /** 元宝code */
  moduleCodeCoin: string;
  /** 钱包code */
  moduleCodeAmount: string;
  moduleCodeInvite: string;
  /** 主端招财猪老版本钱包查询现金 */
  moduleCodeOldVersionCash: string;
  /** 猪微服务 */
  piggyHost: string;
  fve: string;
  /** 猪微服务 code */
  moduleCodePiggybank: string;
  /** 跳转链接  */
  link: {
    ruleAll: string;
    ruleThis: string;
    customerService: string;
    wallet: string;
    // 小说书架主页-ios
    novelIosHomepage: string;
    // 小说书架主页-安卓
    novelAndroidHomepage: string;
  };

  HC_AD_API_URL: string;
  HC_AD_API_PATH_NAME: string;
  HC_TASK_API: string;
  LOGIN_TASK_APP_ID: string;
  HC_AD_CACHE_HR: number;

  HC_BRAND_BANNER: string;
  HC_BRAND_BANNER2: string;
  HC_BRAND_TASK: string;
  HC_BRAND_POP: string;
  ignoreBroPackIdCheck: boolean,
  WORKER_SECRET: string,
  /** 主端appId */
  newAppId: string;
  /** 主端元宝code */
  newModuleCodeCoin: string;
  /** 主端钱包code */
  newModuleCodeAmount: string;
  /** 高价值弹窗资源位code */
  taskResourceCode: string;
  backTagResourceCode: string;
  /** 高价值钱包appid */
  highVlaueAppid: string;
  /** 高价值钱包moduleCode */
  highVlaueModuleCode: string;
  /** 小说分场投放code */
  novelResourceCode: string;
  /** 网盘分场投放code */
  clouddriveResourceCode: string;
  /** 标签人群（端NU/福利NU/福利回流） */
  tagPersonResourceCode: string;
  /** 限时任务资源位 */
  limitTaskResourceCode: string;
  /** 合一页子任务资源位 */
  videoTimeResourceCode: string;
  /** 拦截挽留code */
  backInterceptResourceCode: string;
  // 任务套娃资源投放code
  taskNestResourceCode: string;
  walletAppId: string;
  rightsExchangeCode: string;
  rightsExchangeSceneCode: string;
  /** 权益兑换流水appid */
  rightsExchangeFollowCode: string;
  wallet_diamond_key: string;
  equity_act_id: string;
  equity_tag: string;
  WPK_BID: string;
}
const config: IConfig = {
  actId: 'aj1ba4u9vv3u3jhr',
  businessKey: 'uclitewelfareh5',
  currencyToken: 'rro702lyfhnvqimrislu620f',
  currencySalt: 'sy5th908xb9bmgiz2ssy0cykzezkq1jf',
  appCode: 'uclite',
  amateurAppId: 'uclite_piggy_user_grow',
  amateurModuleCode: '7f14537ea4ee4dab91a088154b686a04',
  appId: '_dft_uclite_piggy',
  coralHost: 'https://coralnew.uc.alibaba-inc.com',
  taskHost: 'https://task12.uc.alibaba-inc.com',
  bindHost: 'https://coral-daily.uc.alibaba-inc.com',
  navHost: 'httpss://navi-user.uc.alibaba-inc.com',
  shopHost: 'https://coralnew.uc.alibaba-inc.com',
  shopUrl: 'https://broccoli.uc.cn/apps/fuli_test/routes/qddVv7VXF?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401',
  moduleCodeTask: 'c88961c22ae34386b40c528cafd7c7b2',
  moduleCodeFukaTask: 'd5f185300c254ba48971840c55d8e68f',
  moduleCodeBind: 'awardtouristtoken2',
  moduleCodeCoin: '7104d1fdb22c44959b521d23d2d10fcd',
  // moduleCodeAmount: '031af81899b34354964d661fc850ed56',
  moduleCodeAmount: 'd7c55dc3c3a74957a7371f8920b7f319',
  moduleCodeInvite: '2aac43524ea04b81bf0eeed06b2ddc6d',
  moduleCodeOldVersionCash: '5ba632144ae94c86bdf2363c441b606b',
  piggyHost: 'http://uc-piggy-bank.uc.alibaba-inc.com',
  moduleCodePiggybank: 'uclitepiggy',
  /** cms服务 */
  cmsHost: 'https://cms-server.uc.alibaba-inc.com',
  walletAppId: 'uclite_piggy_task',
  /** 权益兑换资源位编码 */
  rightsExchangeCode: 'uclite_novel_award_exchange',
  rightsExchangeSceneCode: 'uclite_piggy',
  /** 权益兑换流水appid */
  rightsExchangeFollowCode: 'uclite_piggy_task',

  link: {
    ruleAll: 'https://terms.alicdn.com/legal-agreement/terms/suit_bu1_uc/suit_bu1_uc202109171745_48846.html',
    ruleThis: 'https://broccoli.uc.cn/apps/_xqovigxn/routes/TmDqCghrh?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Atitlebar_hover_2',
    customerService: 'https://cs-center.uc.cn/index?self_service=true&pf=145&instance=UC_lite&uc_param_str=einibicppfmivefrlantcunwsssvjbktchnnsnddds',
    wallet:
      // eslint-disable-next-line max-len
      'https://broccoli.uc.cn/apps/rJjck5l2G/routes/FwJr9A3er?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwkt&uc_biz_str=S%3Acustom%7CC%3Afull_screen&debug=*&logonline=-1004755097',
    novelIosHomepage: 'ext:open_novelbox_web::extparam:sub_index=书架::type:tab::from:fuli',
    novelAndroidHomepage: 'ext:open_novelbox:index=1&sub_index=1&from=fuli&type=tab'
  },
  fve: '3.8.12',
  HC_AD_API_URL: 'https://test.huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativeadnb',
  HC_TASK_API: 'https://ad-qatest1.sm.cn/reward',
  LOGIN_TASK_APP_ID: 'uclite_login_task',
  HC_AD_CACHE_HR: 6,

  HC_BRAND_BANNER: '100004312',
  HC_BRAND_BANNER2: '100004557',
  HC_BRAND_TASK: '100004311',
  HC_BRAND_POP: '100004310',
  ignoreBroPackIdCheck: false,
  WORKER_SECRET: 'dWNhY3R0YXNrd29ya2Vy',

  // 以下是主端模块
  newAppId: 'daily_piggy_task_new',
  newModuleCodeCoin: '5df786d8f6d64265a8f5d51395d3562b',
  newModuleCodeAmount: '********************************',

  taskResourceCode: 'uc_piggy_high_value',
  backTagResourceCode: 'uc_pigg_back_tag',
  highVlaueAppid: 'uclite_piggy_task',
  highVlaueModuleCode: '71487ef18444494aa8c40fd5e9fc48ef',
  novelResourceCode: 'uc_piggy_novel',
  clouddriveResourceCode: 'uc_piggy_clouddrive',
  tagPersonResourceCode: 'uc_piggy_tag_person',
  backInterceptResourceCode: 'uc_piggy_back_intercept',
  taskNestResourceCode: 'uc_piggy_task_nest',
  wallet_diamond_key: 'uc-lite-wallet',
  limitTaskResourceCode: 'uc_piggy_limit_time',
  videoTimeResourceCode: 'uc_piggy_xssvideo_fuli',

  // 权益模块
  equity_act_id: '4piiu0jq5xlmfix6',
  equity_tag: 'uc_cash_exchange_award',
  WPK_BID: '45w4uv8x-nrs4k62g',
};
export default config;
