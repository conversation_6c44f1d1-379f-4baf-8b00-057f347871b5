export default {
  actId: 'aj1ba4u9vv3u3jhr',
  currencyToken: 'rro702lyfhnvqimrislu620f',
  currencySalt: 'sy5th908xb9bmgiz2ssy0cykzezkq1jf',
  appCode:'uclite',
  amateurAppId: 'uclite_piggy_user_grow',
  amateurModuleCode: '7f14537ea4ee4dab91a088154b686a04',
  appId: '_dft_uclite_piggy',
  coralHost: 'https://coralnew.uc.alibaba-inc.com',
  taskHost: 'https://task12.uc.alibaba-inc.com',
  bindHost: 'https://coral-daily.uc.alibaba-inc.com',
  navHost: 'https://navi-user.uc.alibaba-inc.com',
  shopHost: 'https://coralnew.uc.alibaba-inc.com',
  shopUrl: 'https://broccoli.uc.cn/apps/fuli_test/routes/qddVv7VXF?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401',
  moduleCodeTask: 'c88961c22ae34386b40c528cafd7c7b2',
  moduleCodeFukaTask: 'd5f185300c254ba48971840c55d8e68f',
  moduleCodeBind: 'awardtouristtoken2',
  moduleCodeCoin: '7104d1fdb22c44959b521d23d2d10fcd',
  // moduleCodeAmount: '031af81899b34354964d661fc850ed56',
  moduleCodeAmount: 'd7c55dc3c3a74957a7371f8920b7f319',
  moduleCodeInvite: '2aac43524ea04b81bf0eeed06b2ddc6d',
  moduleCodeOldVersionCash: '5ba632144ae94c86bdf2363c441b606b',
  piggyHost: 'https://uc-piggy-bank.uc.alibaba-inc.com',
  moduleCodePiggybank: 'uclitepiggy',
  /** cms服务 */
  cmsHost: 'https://cms-server.uc.alibaba-inc.com',
  walletAppId: 'uclite_piggy_task',
  /** 权益兑换资源位编码 */
  rightsExchangeCode: 'uclite_novel_award_exchange',
  rightsExchangeSceneCode: 'uclite_piggy',
  /** 权益兑换流水appid */
  rightsExchangeFollowCode: 'uclite_piggy_task',
  link: {
    ruleAll: 'https://terms.alicdn.com/legal-agreement/terms/suit_bu1_uc/suit_bu1_uc202109171745_48846.html',
    ruleThis: 'https://broccoli.uc.cn/apps/_xqovigxn/routes/TmDqCghrh?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Atitlebar_hover_2',
    customerService: 'https://cs-center.uc.cn/index?self_service=true&pf=145&instance=UC_lite&uc_param_str=einibicppfmivefrlantcunwsssvjbktchnnsnddds',
    wallet:
      // eslint-disable-next-line max-len
      'https://broccoli.uc.cn/apps/rJjck5l2G/routes/FwJr9A3er?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwkt&uc_biz_str=S%3Acustom%7CC%3Afull_screen&debug=*&logonline=-1004755097',
    novelIosHomepage: 'ext:open_novelbox_web::extparam:sub_index=书架::type:tab::from:fuli',
    novelAndroidHomepage: 'ext:open_novelbox:index=1&sub_index=1&from=fuli&type=tab'
  },

  HC_AD_API_URL: 'https://test.huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativeadnb',
  HC_TASK_API: 'https://ad-qatest1.sm.cn/reward',
  LOGIN_TASK_APP_ID: 'uclite_login_task',
  HC_AD_CACHE_HR: 6,

  HC_BRAND_BANNER: '100004312',
  HC_BRAND_BANNER2: '100004557',
  HC_BRAND_TASK: '100004311',
  HC_BRAND_POP: '100004310',
  fve: '3.8.12',
  ignoreBroPackIdCheck: false,
  taskResourceCode: 'uc_piggy_high_value',
  highVlaueAppid: 'uclite_piggy_task',
  highVlaueModuleCode: '71487ef18444494aa8c40fd5e9fc48ef',
  novelResourceCode: 'uc_piggy_novel',
  clouddriveResourceCode: 'uc_piggy_clouddrive',
  tagPersonResourceCode: 'uc_piggy_tag_person',
  backInterceptResourceCode: 'uc_piggy_back_intercept',
  backTagResourceCode: 'uc_pigg_back_tag',
  taskNestResourceCode: 'uc_piggy_task_nest',
  wallet_diamond_key: 'uc-lite-wallet',
  limitTaskResourceCode: 'uc_piggy_limit_time',
  videoTimeResourceCode: 'uc_piggy_xssvideo_fuli',
  secondPopResourceCode: 'task_push_second',

  // 权益模块
  equity_act_id: '4piiu0jq5xlmfix6',
  equity_tag: 'uc_cash_exchange_award',
  WPK_BID: '45w4uv8x-nrs4k62g',
};
