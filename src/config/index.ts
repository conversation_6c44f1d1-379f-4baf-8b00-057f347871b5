import config from './config.local';
import dailyConfig from './config.daily';
import preConfig from './config.pre';
import prodConfig from './config.prod';
import { getParam } from '@/lib/qs';

if (PUBLISH_ENV) {
  // 云构建环境
  if (PUBLISH_ENV === 'daily') {
    switch (getParam('__env')) {
      case 'pre':
        Object.assign(config, preConfig);
        break;
      case 'prod':
        Object.assign(config, prodConfig);
        break;
      default:
        Object.assign(config, dailyConfig);
    }
  } else {
    Object.assign(config, prodConfig);
  }
} else {
  switch (getParam('__env')) {
    case 'daily':
      Object.assign(config, dailyConfig);
      break;
    case 'pre':
      Object.assign(config, preConfig);
      break;
    case 'prod':
      Object.assign(config, prodConfig);
      break;
  }
}

export default config;
