export default {
  actId: 'yir2ziawe88okn9f',
  currencyToken: 'wdyt7xvivk1xs3d7bmg1nj3c',
  currencySalt: 'sy5th908xb9bmgiz2ssy0cykzezkq1jf',
  appCode:'uclite',
  amateurAppId: 'uclite_piggy_user_grow',
  amateurModuleCode: '5894b7ab4c8248ab827c61a79725450c',
  /** 预发 */
  /** 任务服务 */
  taskHost: 'https://pre-coral-task.uc.cn',
  coralHost: 'https://coral2-pre.uc.cn',
  /** 福利猪 */
  piggyHost: 'https://coral2-pre.uc.cn',
  /** 用户绑定 */
  bindHost: 'https://coral2-pre.uc.cn',
  navHost: 'https://navi-user.uc.cn',
  shopHost: 'https://coral2.uc.cn',
  /** cms服务 */
  cmsHost: 'https://cms-sdk-pre.alibaba-inc.com',
  walletAppId: 'uclite_piggy_task',
  /** 权益兑换资源位编码 */
  rightsExchangeCode: 'uclite_novel_award_exchange',
  rightsExchangeSceneCode: 'uclite_piggy',
  /** 权益兑换流水appid */
  rightsExchangeFollowCode: 'uclite_piggy_task',
  shopUrl: 'https://broccoli.uc.cn/apps/fuli_test/routes/qddVv7VXF?__env=pre&uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401',
  /** 生产 */
  appId: '_dft_uclite_piggy',
  moduleCodePiggybank: 'uclitepiggy',
  moduleCodeCoin: '6a7acf9bf37c4c49b515c369fa4a46b4',
  // moduleCodeAmount: 'b795ea6e75a544118d10b08663644a1d',
  moduleCodeAmount: '9c524f8bb1524b14840d15c5cec38133',
  moduleCodeTask: '8ee46ec7f90543a290e8667c02c0ecb2',
  moduleCodeBind: 'XmojPXb7cIcFLga5VWvqGM5td2Rif9',
  moduleCodeFukaTask: '0b1ce4b8c0d24a1197007d2f800eb000',
  moduleCodeInvite: '30e56bad6ab44c66a49072347eea15ed',
  moduleCodeOldVersionCash: '5ecbf1ebc8554065a824064fd71c3dfb',
  link: {
    ruleAll: 'https://terms.alicdn.com/legal-agreement/terms/suit_bu1_uc/suit_bu1_uc202109171745_48846.html',
    ruleThis: 'https://broccoli.uc.cn/apps/_xqovigxn/routes/TmDqCghrh?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Atitlebar_hover_2',
    customerService: 'https://cs-center.uc.cn/index?self_service=true&pf=145&instance=UC_lite&uc_param_str=einibicppfmivefrlantcunwsssvjbktchnnsnddds',
    wallet:
        // eslint-disable-next-line max-len
        'https://broccoli.uc.cn/apps/BkWcpU7hz/routes/DxIn0dUce?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwkt&uc_biz_str=S%3Acustom%7CC%3Afull_screen',
    novelIosHomepage: 'ext:open_novelbox_web::extparam:sub_index=书架::type:tab::from:fuli',
    novelAndroidHomepage: 'ext:open_novelbox:index=1&sub_index=1&from=fuli&type=tab'
  },
  HC_AD_API_URL: 'https://huichuan.sm.cn',
  HC_AD_API_PATH_NAME: 'nativead',
  HC_TASK_API: 'https://e.uc.cn/reward',
  LOGIN_TASK_APP_ID: 'uclite_login_task',
  HC_AD_CACHE_HR: 6,

  HC_BRAND_BANNER: '100004312',
  HC_BRAND_BANNER2: '100004557',
  HC_BRAND_TASK: '100004311',
  HC_BRAND_POP: '100004310',
  fve: '3.8.12',
  ignoreBroPackIdCheck: true,

  // 以下是主端模块
  newAppId: 'uc_piggy_task',
  newModuleCodeCoin: '87f0aee279ca4cb7b8b592316a05b829',
  newModuleCodeAmount: '3d8966840f2d41a692606d7e3ddea14c',
  taskResourceCode: 'uc_piggy_high_value',
  backTagResourceCode: 'uc_pigg_back_tag',
  highVlaueAppid: 'uclite_piggy_task',
  highVlaueModuleCode: '61fc17c6e1884a90b57738c9973439a3',
  novelResourceCode: 'uc_piggy_novel',
  clouddriveResourceCode: 'uc_piggy_clouddrive',
  taskNestResourceCode: 'uc_piggy_task_nest',
  tagPersonResourceCode: 'uc_piggy_tag_person',
  backInterceptResourceCode: 'uc_piggy_back_intercept',
  limitTaskResourceCode: 'uc_piggy_limit_time',
  videoTimeResourceCode: 'uc_piggy_xssvideo_fuli',
  secondPopResourceCode: 'task_push_second',

  // 权益模块
  equity_act_id: '1e7px902j5uq2atd',
  equity_tag: 'uc_cash_exchange_award',
  WPK_BID: '45w4uv8x-nrs4k62g',
};

