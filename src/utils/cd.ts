import ucapi from '@/utils/ucapi';

/**
 * 获取是否新手保护
 * @return {Boolean} true: 新手保护
 * @return {Boolean} false: 非新手保护
 */
export async function getCdAdProtect(): Promise<boolean> {
  try {
    const result = await ucapi.biz.getCDParams('fuli_ad_protect');
    // 处理 val
    console.log('ad数据fuli_ad_protect', result);
    if (result.value) {
      if (result.value === '1') return true;  // 有新手保护
    }
    return false;
  } catch (err) {
    console.log(err);
    return false;
  }
}

/**
 * 获取3天内是否有邀请记录
 * @return {Boolean} true: 有
 * @return {Boolean} false: 没有
 */
export async function getCdInviteRecordIn3days(): Promise<boolean> {
  try {
    const result = await ucapi.biz.getCDParams('invite_record_in_3days');
    // 处理 val
    console.log('ad数据invite_record_in_3days', result);
    if (result.value) {
      if (result.value === '1') return true;
    }
    return false;
  } catch (err) {
    console.log(err);
    return false;
  }
}
