import uc from '@ali/weex-toolkit/lib/weex_config/uc';
import { statusBarHeight, isIOS } from '../lib/universal-ua';

export const titleBarHeight = 96 + statusBarHeight;

export const screenWidth = 750;

export const screenHeight = Math.ceil(uc.env.windowHeight / uc.env.windowWidth * screenWidth);

export const safeAreaInsets = {
  top: statusBarHeight,
  bottom: isIOS && screenHeight / screenWidth > 2 ? Math.ceil(44 / uc.env.windowWidth * 375) : 0,
};

// 取自双11的iphone全面屏判断，解决底部黑边覆盖影响点击问题
// 底部需要预留 22 的安全距离
export function isFullScreen() {
  const { windowHeight, windowWidth }: any = uc.env;
  // return windowHeight / windowWidth > 2;
  return isIOS && windowHeight / windowWidth > 2;
}
export function getButtomSafetyHeight() {
  return isFullScreen() ? 22 : 0;
}

/** 计算成750宽度下的尺寸 */
export function convert2StandardLen(n: number) {
  return n * 750 / window.innerWidth;
}
