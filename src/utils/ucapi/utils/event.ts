import ucapi from '@ali/weex-toolkit/lib/ucapi';
import { isIOS } from '../../../lib/universal-ua';
import { parseJSON } from './middlewares';

export type CLIENT_EVENT_NAME = 'appStateChange'
| 'sceneAction'
| 'hardwareBackPress'
| 'keyboardDidShow'
| 'keyboardDidHide'
| 'UCEVT_Global_AccountStateChange'
| 'UCEVT_Global_AccountSendSmsCode'
| 'UCEVT_Global_AccountThirdPartyToken'
| 'weex.initParamsEvent'
| 'UCEVT_IAP_OrderStateChange'
| 'UCEVT_IAP_PaymentStateChange'
| 'UCEVT_PAY_ResultNotify'
| 'UCEVT_Biz_AdActionNotify';

interface Config {
  name: CLIENT_EVENT_NAME;
  callback: (data: any) => any;
  once?: boolean;
  key?: string;
}

class ClientEvent {
  task: Config[] = [];

  eventList: string[] = [];

  middlewares: { [eventName: string]: (data: any) => any } = {};

  on(eventName: CLIENT_EVENT_NAME, callback: Config['callback'], key?: string) {
    this.initEvent(eventName);
    this.addTask({ name: eventName, once: false, callback, key });
    return () => this.off(eventName, callback);
  }

  once(eventName: CLIENT_EVENT_NAME, callback: Config['callback'], key?: string) {
    this.initEvent(eventName);
    this.addTask({ name: eventName, once: true, callback, key });
    return () => this.off(eventName, callback);
  }

  off(eventName: CLIENT_EVENT_NAME, callback: Config['callback']) {
    const index = this.task.findIndex((item) => item.name === eventName && item.callback === callback);
    if (index !== -1) this.task.splice(index, 1);
  }

  userMiddleware(eventName: CLIENT_EVENT_NAME, transform: (data: any) => any) {
    this.middlewares[eventName] = transform;
  }

  private addTask(task: Config) {
    const sameTaskIndex = this.task.findIndex((item) => task.key && item.key === task.key);
    if (sameTaskIndex !== -1) this.task.splice(sameTaskIndex, 1);

    this.task.push(task);
  }

  private;

  initEvent(eventName: CLIENT_EVENT_NAME) {
    if (this.eventList.indexOf(eventName) > -1) return;

    this.eventList.push(eventName);
    // todo uc-event?
    // const ucEvent = weex.requireModule('uc-event') as any;
    // ucEvent.addEventListener(eventName, data => this.triggerEvent(eventName, data));
  }

  private triggerEvent(eventName: string, data) {
    this.task.forEach((config) => {
      if (config.name !== eventName) return;
      try {
        const result = this.middlewares[eventName] ? this.middlewares[eventName](data) : data;
        config.callback(result);
      } catch (e) {
      }
    });

    this.task = this.task.filter((config) => config.name !== eventName || !config.once);
  }
}

const clientEvent = new ClientEvent();
clientEvent.userMiddleware('appStateChange', parseJSON);
clientEvent.userMiddleware('keyboardDidShow', parseJSON);
clientEvent.userMiddleware('UCEVT_PAY_ResultNotify', parseJSON);

/**
 * 封装 ucapi 与客户端 globalEvent 回调，适用于异步 API，如登陆
 */
function wrapClientEvent(method: string, params: any, eventName: CLIENT_EVENT_NAME): Promise<any> {
  let eventHandler;

  return Promise.all([
    ucapi.exec(method, params),
    // Android 授权接口目前没有事件回调，由前端兼容
    isIOS ? new Promise((resolve) => {
      eventHandler = clientEvent.once(eventName, resolve, method);
    }) : null,
  ])
    .then((ret) => {
      return isIOS ? ret[1] : ret[0];
    })
    .catch((ret) => {
      if (eventHandler) eventHandler();
      return Promise.reject(ret);
    });
}

export default clientEvent;
export { wrapClientEvent };
