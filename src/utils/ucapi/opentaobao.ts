import ucapi from '@ali/weex-toolkit/lib/ucapi';

const primaryOpenTaobao = ucapi.biz.openTaobao;

function openTaobao(url, params = {}) {
  let kv = '';
  let link_url = url;
  if (!url) return;
  const keyArr = Object.keys(params);
  if (keyArr.length) {
    keyArr.forEach((item, idx) => {
      kv += item + '=' + encodeURIComponent(params[item]) + (idx === keyArr.length - 1 ? '' : '&');
    });
    link_url = url + (url.indexOf('?') > 0 ? '&' : '?') + kv;
  }
  return primaryOpenTaobao({
    link_url,
  });
}
export default openTaobao;
