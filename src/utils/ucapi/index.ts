import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import jssdk from '@ali/uc-toolkit/lib/jssdk';
import { isIOS, appName, APP_NAME, isAndroid} from '../../lib/universal-ua';
import clientEvent, { wrapClientEvent } from './utils/event';
import { IBindThirdInfo } from "@/store/models/user/types";

const ucapi = {
  spam: {
    encrypt: async (params: { text: string }): Promise<string> => {
      if (isIOS) {
        if (appName === APP_NAME.DDLEARN) {
          return (await jsbridge.exec('security.encrypt', params)).outputText;
        } else {
          return (await jsbridge.exec('biz.encryptOrDecrypt', { enc: 1, type: 1, data: { key: params.text } })).key;
        }
      } else if (appName === APP_NAME.DDLEARN) {
        return (await jsbridge.exec('security.encrypt', params)).output_text;
      } else if (appName === APP_NAME.QUARK) {
        return (await jsbridge.exec('biz.encryptOrDecrypt', { enc: 1, type: 1, data: { key: params.text } })).key;
      } else {
        return (await jsbridge.exec('biz.encryptOrDecrypt', { enc: 1, type: 1, data: { key: params.text } })).key;
      }
    },
    async decrypt(params: { text: string }): Promise<string> {
      if (appName === APP_NAME.DDLEARN) {
        return Promise.reject();
      } else {
        const result = await jsbridge.exec('biz.encryptOrDecrypt', { enc: 1, type: 0, data: { key: params.text } });
        return result?.key || '';
      }
    },
    async sign(params: { text: string; salt: string }): Promise<string> {
      if (appName === APP_NAME.DDLEARN) {
        const ret = await jsbridge.exec('security.sign', params);
        return ret?.output_text || '';
      } else {
        const ret = await jsbridge.exec('spam.sign', params);
        return ret?.output_text || '';
      }
    },
  },
  base: {
    openURL(params: { url: string }) {
      const {url} = params;
      jsbridge.exec('biz.openPageUrl', {url});
    },
    copyToClipboard(params: { text: string, toast: '1' | '0' }) {
      return jsbridge.exec('base.copyToClipboard', { ...params });
    },
    getVersion(): Promise<string> {
      return jsbridge.exec('base.getVersion', {});
    },
    hasStoragePermission(): Promise<boolean> {
      return jsbridge.exec('base.hasStoragePermission', {});
    },
    requestStoragePermission(): Promise<boolean> {
      return jsbridge.exec('base.requestStoragePermission', {});
    },
    getABTestInfo(): Promise<boolean> {
      return jsbridge.exec('base.getABTestInfo', {});
    },
    checkAPI(params: { apiList: string }): Promise<boolean> {
      return jsbridge.exec('base.checkAPI', params);
    },
    gotoSettings() {
      return jsbridge.exec('base.gotoSettings', {});
    },
    async getPushToken() {
      if (appName === APP_NAME.DDLEARN) {
        return jsbridge.exec('base.getPushToken', {});
      }
      try {
        const data = await jsbridge.exec('biz.ucparams', { params: 'td', isHttps: false });
        let appkey;
        if (appName === APP_NAME.QUARK) {
          appkey = isIOS ? 24515349 : 24493918;
        } else if (appName === APP_NAME.UC) {
          appkey = isIOS ? 21803344 : 21711551;
        }
        return { device_id: data.td, appkey };
      } catch {
        return {};
      }
    },
    addBookmark(params = {}) {
      return jsbridge.exec('base.addBookmark', params);
    },
    checkBookmarkIsExist(params = {}) {
      return jsbridge.exec('base.checkBookmarkIsExist', params);
    },
    canOpenApp(params: { scheme: string }) {
      if (isAndroid) return Promise.resolve({ result: 1 });
      return jsbridge.exec('base.canOpenApp', params);
    },
    async openThirdBrowser(params: { url: string }) {
      return jsbridge.exec('base.openThirdBrowser', params);
    },
    async getEventInfo(event_name = 'weex.initParamsEvent') {
      return jsbridge.exec('base.getEventInfo', {
        event_name,
      });
    },
    postmessage(params = {}) {
      return jsbridge.exec('base.postmessage', params);
    }
  },
  biz: {
    async getIDFA() {
      const result = await jsbridge.exec('biz.getIDFA', {});
      return result?.idfa ? result.idfa : '';
    },
    getCDParams(key: string) {
      return jsbridge.exec('biz.getCDParams', { key });
    },
    lockScreen() {
      return jsbridge.exec('biz.lockScreen', {});
    },
    ucparams(params: { params: string }, isHttps = false) {
      return jsbridge.exec('biz.ucparams', { ...params, isHttps });
    },
    queryApp(params) {
      return jsbridge.exec('biz.queryApp', { ...params });
    },
    getCMSResource(params) {
      return jsbridge.exec('biz.getCMSResource', { ...params });
    },
    getPushState() {
      return jsbridge.exec('biz.getPushState', {});
    },
    gotoPushSetting(params = {}) {
      return jsbridge.exec('biz.gotoPushSetting', { ...params });		      return jsbridge.exec('biz.gotoPushSetting', { ... params });
    },
    getDefaultBrowser() {
      return jsbridge.exec('biz.getDefaultBrowser', {});
    },
    gotoDefaultBrowserSetting() {
      return jsbridge.exec('biz.gotoDefaultBrowserSetting', {});
    },
    gotoAuthSetting(params) {
      return jsbridge.exec('biz.gotoAuthSetting', { ...params });
    },
    openPicViewer(sourcePicUrl: string | string[], index = 0) {
      const data = ([] as string[]).concat(sourcePicUrl).map(picUrl => ({ title: '查看图片', picUrl, itemType: 2 }));
      const params = { data, index };
      jsbridge.exec('biz.openPicViewer', params);
    },
    share(params) {
      return jsbridge.exec('biz.share', params);
    },
    startApp(scheme) {
      return jsbridge.exec('biz.startApp', {
        pkg: scheme,
        data: '',
        appstoreURL: '',
      })
    },
    gestureDisabled() {
      return jsbridge.exec('biz.gestureDisabled', {
        disabled: true
      });
    },
    getStoreData(params) {
      return jsbridge.exec('biz.getStoreData', {
        page: params.page, 		// 存储所属的页面
        id: params.id, 	// 数据存储的key标识位
      });
    },
    deleteStoreData(params) {
      return jsbridge.exec('biz.deleteStoreData', {
        page: params.page, 		// 存储所属的页面
        id: params.id, 	// 数据存储的key标识位
      });
    },
    setStoreData(params) {
      return jsbridge.exec('biz.setStoreData', {
        page: params.page, 	// 存储所属的页面
        id: params.id,  // 数据存储的 key 标识位
        storeType: params.storeType, // cache和memory是内存级别，disk会持久化写盘。cache或者为空时，调用getStoreData自动清除内存数据，memory调用getStoreData不清除内存数据
        data: params.data, 	// 所存储的数据对象
      })
    },
    getPersonalizedRecommendSwitch() {
      return jsbridge.exec('biz.getPersonalizedRecommendSwitch', {});
    },
    loadEmbedNativeAd () {
      return jsbridge.exec('biz.loadEmbedNativeAd', {
        scene: 'welfare_task_dialog',
        requestCount: 1
      }).then(res => res?.ads?.[0])
    },
    queryRewards (options: {slotKey: string, requestId: string, appId: string}) {
      const {slotKey, requestId, appId } = options;
      return jsbridge.exec('biz.queryRewards', {
        requestId,
        appId,
        rewarded_video_slot_key: slotKey,
      })
    },
    consumeSuccessRewards(options: {slotKey: string; rewardId: string; appId: string; requestId: string; businessCode?: string }) {
      const { slotKey, rewardId, businessCode = 'uclite_activity_ad', requestId, appId } = options;
      return jsbridge.exec('biz.consumeSuccessRewards', {
        businessCode,
        reward_success_id: rewardId,
        requestId,
        rewarded_video_slot_key: slotKey,
        appId,
      })
    }
  },
  device: {
    getNetworkStatus() {
      return jsbridge.exec('device.getNetworkStatus');
    },
  },
  account: {
    logout() {
      return wrapClientEvent('account.logout', {}, 'UCEVT_Global_AccountStateChange');
    },
    login() {
      return jsbridge.exec('account.openLoginWindow', { uiType: 'window' }).then(res => {
        return Promise.resolve(res);
      });
    },
    getUserInfo() {
      return jsbridge.exec('account.getUserInfo', { vCode: Date.now() });
    },
    bindTaobao(webBusiness = 'activity') {
      return jsbridge.exec('account.bindThirdPartyAccount', {
        type: 'taobao',
        webBusiness
      });
    },
    bindAlipay(webBusiness = 'activity') {
      return jsbridge.exec('account.bindThirdPartyAccount', {
        type: 'alipay',
        webBusiness,
      })
    },
    async getBindInfo(bindThirdType: 'taobao' | 'alipay', force = false): Promise<IBindThirdInfo> {
      return new Promise((resolve, reject) => {
        if (window?.ucapi) {
          window?.ucapi?.invoke('account.getBindUserInfo', {
            vCode: Date.now(),
            bindThirdType,
            forceUpdate: force,
            doubleRelation: false,
            success : function(data) {
              resolve(data)
            },
            fail: function(data) {
              reject(data)
            }
          })
        }
      })
      // return jsbridge.exec('account.getBindUserInfo', {
      //   vCode: Date.now(),
      //   /** 同时获取绑定的第三方账号信息 */
      //   bindThirdType,
      //   /** 是否强制请求开放平台第三方账号绑定信息 */
      //   forceUpdate: force,
      // });
    },
    openLoginWindow(loginType: string) {
      return jsbridge.exec('account.openLoginWindow', {
        loginType,
        uiType: "window",
        loginTip: '登录',
        webBusiness: "fuli",
        fail: function (err) {
          console.log('授权登录失败', err)
        },
        loginCallback: "alipayLoginCB()" // 登录成功的回调
      });
    }
  },
  cms: {
    executeAction(params) {
      return jsbridge.exec('cms.executeAction', params);
    },
  },
  push: {
    openReciveNotification() {
      return jsbridge.exec('push.openReciveNotification', {});
    },
    checkAuth() {
      return jsbridge.exec('push.checkAuth', {});
    },
    registerPush() {
      return jsbridge.exec('push.registerPush', {});
    },
  },
  ut: {
    toUT2(params) {
      return jsbridge.exec('ut.toUT2', params);
    },
  },
  event: {
    dismissKeyboard() {
      try {
        const ucEvent = weex.requireModule('uc-event') as any;
        ucEvent.dismissKeyboard();
      } catch (e) {
        console.log(e);
      }
    },
  },
  navigation: {
    async addNavigation(params) {
      return jsbridge.exec('navigation.addNavigation', { ...params });
    },
    async removeNavigation(params) {
      return jsbridge.exec('navigation.removeNavigation', { ...params });
    },
    async isExists(params) {
      return jsbridge.exec('navigation.isExists', { ...params });
    },
  },
  mission: {
    async setCalendarReminders(params) {
      return jsbridge.exec('mission.setCalendarReminders', { ...params });
    },
    async queryCalendarReminders(params) {
      return jsbridge.exec('mission.queryCalendarReminders', { ...params });
    },
    async queryCalendarPermission(params) {
      return jsbridge.exec('mission.queryCalendarPermission', { ...params });
    },
    async deleteCalendarReminders(params) {
      return jsbridge.exec('mission.deleteCalendarReminders', { ...params });
    },
    async closeWelfareBall() {
      return jsbridge.exec('mission.closeWelfareBall');
    },
    async getWelfareBallSettingEnable() {
      return jsbridge.exec('mission.getWelfareBallSettingEnable')
    },
    // 信息流获取前置任务
    async getPreTaskState() {
      return jsbridge.exec('mission.getPreTaskState')
    },
    // 宝箱任务领奖后通知客户端
    async updateBoxState () {
      return jsbridge.exec('mission.updateBoxState')
    },
    // 通知客户端用户金币变更, 极速版小包的功能，现在的客户端已经没有这个功能了
    async notifyCoinsChange (params = {}) {
      return jsbridge.exec('mission.notifyCoinsChange', { ...params });
    },
    // 通知客户端时长任务翻倍生效
    async notifyReadMissionDouble () {
      return jsbridge.exec('welfare.setupReadMissionX2')
    },
    // 通知客户端, 清除不显示悬浮球的标记
    async notifyClearWelfareBallCloseFlag () {
      return jsbridge.exec('welfare.clearCommonWebWelfareBallCloseFlag')
    },
    // 注册摇一摇
    async registerShakeListener (params = {}) {
      return jsbridge.exec('mission.registerShakeListener', { ...params })
    },
    // 销毁监听摇一摇
    async unregisterShakeListener () {
      return jsbridge.exec('mission.unregisterShakeListener')
    },
    // 获取阅读时长计时器展示状态
    async getSettingSwitch (token = 'welfare_ball') {
      return jsbridge.exec('welfare.getSettingSwitch', {token})
    },
  },
  security: {
    async miniwua(params) {
      return jsbridge.exec('security.miniwua', { ...params });
    }
  },
  novel: {
    // jsapi文档: https://jas.alibaba-inc.com/jsapi/5e4f7810ecb2070105814144?type=jump
    sendNativeEvent(eventName: 'refreshUserGiftState' | 'refreshSignState' | 'exchangeBeanTicketSuccess' | 'updateIgnoreHideFullScreen' = 'refreshUserGiftState', status: 0 | 1 = 1): Promise<{result: string, eventName: string, status: 0 | 1}> {
      return new Promise((resolve, reject) => {
        if (window?.ucapi) {
          window.ucapi.invoke('novel.sendNativeEvent', {
            action: eventName,
            status,
            success: function(data) {
              if (data.result === 'success') {
                resolve(data)
              } else {
                reject(data)
              }
            },
            fail: function(data) {
              reject(data)
            }
          })
        }
      })
    }
  }
};

export default ucapi;
export { clientEvent };
