import ucapi from '@ali/weex-toolkit/lib/ucapi';
import { isWeex } from '@ali/weex-toolkit/lib/ua';
import { IShareObj, IShareChannelTarget as Target } from '../types/types';

export const getShareUrl = (shareConfig: IShareObj, target?: Target) => {
  const { link: sourceUrl } = shareConfig || {};
  let entry = 'others';
  switch (target) {
    case Target.WechatFriends:
      entry = 'weixin';
      break;
    case Target.WechatTimeline:
      entry = 'weixin';
      break;
    case Target.Qzone:
      entry = 'qzone';
      break;
    case Target.QQ:
      entry = 'qq';
      break;
    case Target.DingDing:
      entry = 'dingding';
      break;
    case Target.SinaWeibo:
      entry = 'weibo';
      break;
    default:
      entry = 'others';
  }
  const ch = sourceUrl.indexOf('?') >= 0 ? '&' : '?';
  const weexFlag = isWeex() ? 1 : 0;
  let targetUrl = `${sourceUrl}${ch}weex=${weexFlag}`;
  if (targetUrl.indexOf('entry=') === -1) {
    targetUrl += `&entry=${entry}`;
  }
  return targetUrl;
};

export function share(shareConfig: IShareObj, target?: Target) {
  const { title, content, iconUrl: imageUrl } = shareConfig;
  const sourceUrl = getShareUrl(shareConfig, target);
  const options: Record<string, string> = { imageUrl, content, title, sourceUrl };
  if (target || shareConfig.target) {
    options.target = target || shareConfig.target;
  }
  console.log('share option ', options);
  return ucapi.biz.shareEx(options);
}
