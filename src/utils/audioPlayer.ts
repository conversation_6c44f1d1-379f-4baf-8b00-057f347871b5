// import fact from '../lib/fact';
// import { localDebug } from './localDebug';
//
// // const weexAudioPlayer = weex.requireModule('audio') as any;
// const BGM_SOURCE = 'https://image.uc.cn/s/uae/g/3o/broccoli/resource/202008/f5b262ef95c80823.mp3';
//
// const AUDIO_PLAYER_MAPS = {};
//
// let INCREMENT_ID = 0;
//
// enum AUDIO_STATUS {
//   INIT = 1,
//   READY = 2,
//   PLAYING = 3,
//   PAUSE = 4,
//   ENDED = 5,
//   ERROR = 6,
// }

export async function loadLottieBgm(needPlay = false) {
  console.log(needPlay);
  // const st = Date.now();
  // const audioOption = {
  //   url: BGM_SOURCE,
  //   id: AUDIO_PLAYER_MAPS[BGM_SOURCE] || ++INCREMENT_ID,
  //   loop: false,
  //   autoplay: false,
  //   volume: 1,
  // };
  // weexAudioPlayer.load(audioOption, param => {
  //   AUDIO_PLAYER_MAPS[BGM_SOURCE] = param.id;
  //
  //   localDebug({ type: 'loaded-bgm', used: Date.now() - st, status: param.status, map: AUDIO_PLAYER_MAPS });
  //   switch (param.status) {
  //     case AUDIO_STATUS.READY:
  //       if (needPlay) {
  //         weexAudioPlayer.play(param.id);
  //       }
  //       break;
  //     case AUDIO_STATUS.PLAYING:
  //       break;
  //     // case AUDIO_STATUS.INIT:
  //     // case AUDIO_STATUS.PAUSE:
  //     case AUDIO_STATUS.ENDED:
  //       fact.event('lottie_audio_play', {
  //         success: 'true',
  //       });
  //       break;
  //     case AUDIO_STATUS.ERROR:
  //       fact.event('lottie_audio_play', {
  //         success: 'false',
  //       });
  //       break;
  //     default:
  //   }
  // });
}
