import uc from '@ali/weex-toolkit/lib/weex_config/uc';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import tracker from '@/lib/tracker';
import fact from '../lib/fact';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { ILogParams } from '@ali/weex-toolkit/lib/tracker/typings';
import { store, dispatch } from '@/store';
import UrlParams from '@/utils/urlParams/index';
import { getParam } from "@/lib/qs";
import stat from "@/lib/stat";

export const enum THIRD_TASK_FROM {
  TMAL_WITH_PLAY = 'tmallwithplay',
  TAOBAO_RECHARGE = 'taobaorecharge'
}

const mtopSuccessCode = ['0000', 'successful'];

const getDelaySeconds = (from: string) => {
  const delayCallbackConfig = store.getState().cms.delayCallbackConfig;
  return delayCallbackConfig.entryDelayList?.find((item) => item?.entry === from)?.delaySeconds || delayCallbackConfig.defaultDelaySeconds;
}

export const preThirdTokenTask = async () => {
  const { taskToken = '', taskFrom = '', openId = '', deliveryId = '', implId = '', sceneId = '', entry = '', sceneCode = ''} = UrlParams.getParams()
  const from = taskFrom || entry;
  const paramsToken = store.getState().app.paramsToken
  const token = getParamsToken(paramsToken)
  if (!token || !from) return
  const delaySeconds = getDelaySeconds(from);
  setTimeout(() => {
    fact.event('call_third_task', {
      from: taskFrom,
    });
    if (taskFrom === THIRD_TASK_FROM.TMAL_WITH_PLAY) {
      thiredMotop({
        api: 'mtop.tmall.jiuxi.activity.task.token.trigger',
        instanceName: 'TAOBAO',
        dataType: 'json',
        data: {
          token: taskToken,
        },
      });
    } else {
      dispatch.task.noticeByToken({
        token,
        from,
        openId,
        deliveryId,
        implId,
        sceneId,
        convertTag: sceneCode
      })
    }
    stat.pv('page_fuli_index', {b: 'index'});
  }, delaySeconds * 1000)
};

interface BaseParams {
  [key: string]: any;
}

interface MtopParams {
  api: string;
  instanceName: string;
  dataType: string;
  type?: string;
  data?: BaseParams;
  v?: string;
}
async function thiredMotop(params: MtopParams) {
  const { api, instanceName, dataType, type = 'GET', v = '1.0', data = {} } = params;
  const { taskFrom = '' } = uc.params;
  if (!api) return;
  const mtopMonitor = tracker.Monitor(WPK_CATEGORY_MAP.MTOP_THIRD, { sampleRate: 1 });
  const response = await jsbridge.exec('mtop.request', {
    api,
    v,
    data,
    instanceName,
    type,
    dataType,
    ecode: 0,
    timeout: 3000,
  });
  const code = response?.data?.code;
  const baseLogParams: Omit<ILogParams, 'category'> = {
    c1: api + '',
  };
  console.log('接口数据？？', response);
  if (mtopSuccessCode.indexOf(code) > -1) {
    console.log('调用成功', code);
    mtopMonitor.success({
      msg: 'success',
      ...baseLogParams,
    });
    fact.event('call_third_task_success', {
      from: taskFrom,
    });
  } else {
    console.log('调用失败', response.data);
    mtopMonitor.fail({
      msg: 'fail',
      ...baseLogParams,
      bl1: JSON.stringify(
        {
          ret: response.ret,
          data: response.data,
        }
      ),
    });
    fact.event('call_third_task_fail', {
      from: taskFrom,
    });
  }
}

function getParamsToken(tokenList: string[]) {
  const urlParams = UrlParams.getParams();
  let paramsToken = '';
  if (!tokenList?.length) {
    tokenList = ["taskToken", "fromToken"];
  }
  for (const token of tokenList) {
    if(urlParams.hasOwnProperty(token)) {
      paramsToken = getParam(token);
      break;
    }
  }

  return paramsToken
}
