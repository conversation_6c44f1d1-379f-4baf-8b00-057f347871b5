import config from '@/config';

import {
  STORAGE_HC_AD_CACHE_KEY,
} from '@/constants/storage';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import ucapi from "@/utils/ucapi";
import {isIOS} from "@/lib/universal-ua";
import fact from '@/lib/fact';
import {store, dispatch} from '@/store';
import storage from '@ali/weex-toolkit/lib/storage';

function byteToString(arr) {
  if (typeof arr === 'string') {
    return arr;
  }
  let str = '';
  for (let i = 0; i < arr.length; i++) {
    const one = arr[i].toString(2);
    const v = one.match(/^1+?(?=0)/);
    if (v && one.length === 8) {
      const bytesLength = v[0].length;
      let store = arr[i].toString(2).slice(7 - bytesLength);
      for (let st = 1; st < bytesLength; st++) {
        store += arr[st + i].toString(2).slice(2);
      }
      str += String.fromCharCode(parseInt(store, 2));
      i += bytesLength - 1;
    } else {
      str += String.fromCharCode(arr[i]);
    }
  }
  return str;
}

export interface IHcAdRes {
  code: string;
  reason: string;
  serverTime: string;
  sid: string;
  slot_ad?: ISlotAd[];
}

export interface ISlotAd {
  from_cache?: boolean;
  slot_id: string;
  ad?: IAd[];
  sid: string;
}

export interface IAd {
  ad_action?: any;
  ad_content: {
    scheme?: string;
    package_name?: string;
    adm_fixed_ulk?: string;
    title: string;
    img_1: string;
    account_id?: string; // 账户id todo 真正字段叫啥？
    logo_url?: string;
  };
  ad_id: string;
  ad_is_effect?: string;
  ad_source_type?: number;
  curl: Array<string>;
  eurl: string;
  furl: string;
  style: string;
  turl: Array<string>;
  vurl: Array<string>;
}

interface ITaskRecordRes {
  status: number;
  message: string;
  extend?: any;
}

export const generateCacheKey = (slotId) => {
  return `${STORAGE_HC_AD_CACHE_KEY}-${slotId}`;
};

let promiseCache;

export const hcAdHelper = {
  generateBaseParams: () => {
    if (promiseCache) {
      return promiseCache;
    }
    promiseCache = new Promise(async (resolve) => {
      const ucParam = await ucapi.biz.ucparams({
        params: `dnfrvelautogpimiodaacabd${isIOS ? '' : 'me'}`
      });
      let idfa = '';
      if (isIOS) {
        idfa = await ucapi.biz.getIDFA();
      }
      await Promise.all(['ut', 'od', 'aa', 'ca', 'me'].filter(paramName => {
        return !!ucParam[paramName];
      }).map(async paramName => {
        const result = await ucapi.spam.decrypt({text: decodeURIComponent(ucParam[paramName])});
        ucParam[paramName] = result;
        return result;
      }));

      // try {
      //   if (ucParam.ca) {
      //     const caObj = JSON.parse(ucParam.ca);
      //     if (Array.isArray(caObj) && caObj[0] && caObj[0].caid) {
      //       ucParam.ca = caObj[0].caid;
      //     }
      //   }
      // } catch (e) {
      //   ucParam.ca = '';
      //   console.error(e);
      // }

      console.log('huichuan personalizedAdSwitch', store.getState().ad.personalizedAdSwitch)

      const data = {
        ad_app_info: {
          app_country: '',
          app_name: 'UC',
          dn: ucParam.dn || '', // dn
          fr: ucParam.fr || '', // fr
          lang: ucParam.la || '', // la
          // pkg_name: ucParam.pr || '', // pr
          pkg_name: 'com.UCMobile', // pr
          pkg_ver: ucParam.ve || '', // ve
          sn: '',
          timezone: '',
          ua: '',
          utdid: encodeURIComponent(decodeURIComponent(ucParam.ut || '')), // 默认期望是没encode，但部分可能会被encode了，为了避免encode两次
        },
        ad_device_info: {
          access: ucParam.nw || '', // nw
          aid: '',
          android_id: ucParam.di || '', // di
          carrier: '',
          client_ip: '',
          cp: '',
          cpu: '',
          device: ucParam.mi || '', // mi
          devid: ucParam.di, // di
          idfa: idfa || '',
          imei: ucParam.me || '',
          oaid: ucParam.od || '', // od
          aaid: ucParam.aa || '', // aa
          caid: ucParam.ca || '', // ca
          is_jb: '0',
          mac: '',
          open_udid: '',
          os: isIOS ? 'ios' : 'android', // fr
          osv: '',
          sh: (ucParam.pi && ucParam.pi.split('x')[1]) || '', // +pi.split('x)[1]
          sw: (ucParam.pi && ucParam.pi.split('x')[0]) || '', // +pi.split('x)[0]
          udid: '',
          brand: ucParam.bd ?? ''
        },
        ad_gps_info: {
          amap_code: '',
          gps_time: '',
          lat: '',
          lng: '',
        },
        ext_info: [
          {
            key: 'personalized_ad',
            value: store.getState().ad.personalizedAdSwitch ? '1' : '0',
          }
        ],
        page_info: {},
        res_info: {},
      };
      fact.event('adid_stat', {
        im: ucParam.me ? 1 : 0,
        id: idfa ? 1 : 0,
        oa: ucParam.od ? 1 : 0,
        ca: ucParam.ca ? 1 : 0,
        aa: ucParam.aa ? 1 : 0,
        ut: ucParam.ut ? 1 : 0,
      });
      resolve(data);
    });
    return promiseCache;
  },
  getAdCache(slotId: string) {
    // 判断本地广告缓存标识，缓存时间内直接返回缓存数据
    const currentTs = new Date().getTime();
    const hcAdCache = localStorage.getItem(generateCacheKey(slotId));
    if (hcAdCache) {
      const cacheInfo = JSON.parse(hcAdCache);
      if (cacheInfo.expireTime > currentTs) {
        return cacheInfo.ad;
      }
    }
    return null;
  },
  async requestHuichuanAd(slotId, forceUpdate) {
    const CACHE_KEY = generateCacheKey(slotId);

    // 判断本地广告缓存标识，缓存时间内直接返回缓存数据
    const currentTs = new Date().getTime();
    const hcAdCache = localStorage.getItem(CACHE_KEY);

    if (hcAdCache) {
      const cacheInfo = JSON.parse(hcAdCache);
      if (cacheInfo.expireTime > currentTs && !forceUpdate) {
        cacheInfo.ad.from_cache = true;
        return cacheInfo.ad;
      } else {
        localStorage.removeItem(hcAdCache);
      }
    }
    const baseData = await hcAdHelper.generateBaseParams();
    const data = {
      ...baseData,
      ad_pos_info:
        [{
          // 61 大图打开 62 小图打开 63  三图打开 64  大图下载 65  小图下载 66  三图下载。不传则使用服务端配置
          ad_style: [],
          req_cnt: '1',
          slot_id: slotId,
          slot_type: '0',
        }]
      ,
    }
    const dataStr = JSON.stringify(data);
    // console.log('dataStr', dataStr);
    const encriptedDataStr = await ucapi.spam.encrypt({text: dataStr});
    const buffer = new Uint8Array(16);
    buffer[0] = 5; // ENV: 0 - 不加密 1 - UC-m9加密 2 - RSA加密 4 -无线保镖 5 - 无线保镖Base64
    buffer[1] = 2; // DATATYPE: 2-json
    buffer[2] = 1; // VER。协议版本号。1表示1.0
    buffer[3] = 1; //  SDK_VER(1 byte) sdk版本号，1表示1.0
    const bodyStr = byteToString(buffer) + encriptedDataStr;
    // console.log(bodyStr);
    // console.log('准备 fetch post 请求 汇川');
    const fetchMonitor = tracker.Monitor(WPK_CATEGORY_MAP.HC_AD_FETCH, {sampleRate: 1});
    try {
      const res = await fetch(`${config.HC_AD_API_URL}/${config.HC_AD_API_PATH_NAME}?app=UC`, {
        method: 'POST',
        body: bodyStr,
      }).then(res => {
        return res.text();
      });
      // console.log('fetch post 请求 汇川 返回', res);
      const resStr = res.substr(16);
      const resObj: IHcAdRes = JSON.parse(resStr);
      if (resObj.code
        && resObj.code === '0'
      ) {
        if (resObj.slot_ad
          && resObj.slot_ad.length > 0
          && resObj.slot_ad[0].slot_id === slotId
          && resObj.slot_ad[0].ad
          && resObj.slot_ad[0].ad.length > 0) {
          resObj.slot_ad[0].ad.forEach(async (adInfo, idx) => {
            const account_id = adInfo.ad_content.account_id || '';
            let monitor = idx === 0 ? fetchMonitor : tracker.Monitor(WPK_CATEGORY_MAP.HC_AD_FETCH, {sampleRate: 1});
            // 判断当天是否上报过此msg
            const msg = `${slotId}-${account_id}-请求成功`;
            const val = await storage.get(msg);
            if (!val) {
              monitor.success({
                msg,
                c1: resObj.code,
                c2: adInfo.ad_id,
                c3: resObj.sid,
                c4: account_id,
                bl2: data.ad_device_info,
              });
              storage.setDaily(msg, true);
              fact.event('huichuan_ad_fetch', {
                slot_id: slotId,
                account_id: account_id,
                sid: resObj.sid,
                ad_id: adInfo.ad_id,
                is_fill: 1,
                is_error: 0,
              });
            }
            /**
             * 12.28 增加二方 App 本地安装情况打点
             * queryApp
             *
             * 入参：
             * 安卓 -> 包名
             * ios -> schema
             *
             * 出参:
             *
             * 若已安装
             * 安卓 -> {"com.UCMobile": {versionCode: 18, versionName: '9.9.2', appType: "user", appName: "UC浏览器", size: 11324}}
             * ios -> {"com.UCMobile": {versionCode = -1, versionName = "", appType = "user", appName = "传进来的值", size = -1}
             *
             * 若未安装
             * 安卓 -> {"com.UCMobile": {}}
             * ios -> {"com.UCMobile": ""}
             **/
            // todo: 这里需要注意 forEach 与普通 for 循环或 for of/in 中 await 执行的差异
            // 这里最新需要带上 task_id、task_name 等参数，打点时机换到 CorpTask 组件
            // if (!isIOS) { // ios需要用 schema 查询，暂无
            //   if (adInfo.ad_content?.package_name) {
            //     const queryRes = await ucapi.biz.queryApp({
            //       cache_first: '1',
            //       pkgs: [adInfo.ad_content?.package_name]
            //     })
            //     // 展示阶段
            //     fact.event('huichuan_ad_before_click_install_log', {
            //       slot_id: slotId,
            //       account_id: account_id,
            //       sid: resObj.sid,
            //       ad_id: adInfo.ad_id,
            //       is_ios: isIOS ? 1 : 0,
            //       installed: isIOS ? (queryRes[adInfo.ad_content?.package_name] ? 1 : 0) : (queryRes[adInfo.ad_content?.package_name]?.versionName ? 1 : 0),
            //     });
            //   }
            // }

          });
          // 获取当前时间戳
          const ts = new Date().getTime();
          // 计算过期时间戳，如果有配置时间，则使用配置的过期时间，没有则默认当天 24 点过期
          let hcAdCacheEndTs = 0;
          // 不同广告类型不同缓存时间
          const { huichuanBanner2SlotId, huichuanPopSlotId, huichuanBrandTaskSlotId, huichuanBidAdCacheHr, huichuanBrandAdCacheHr } = store.getState().cms;
          const brandAdArr = [huichuanBanner2SlotId, huichuanPopSlotId, huichuanBrandTaskSlotId];
          const hcAdCacheHr = brandAdArr.includes(slotId) ? huichuanBrandAdCacheHr : huichuanBidAdCacheHr;
          if (hcAdCacheHr) {
            hcAdCacheEndTs = ts + (hcAdCacheHr || 0) * 60 * 60 * 1000;
          } else {
            hcAdCacheEndTs = new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000).getTime();
          }
          resObj.slot_ad[0].sid = resObj.sid;
          const resStr = {
            ad: resObj.slot_ad[0], // todo 补充点击过的广告，排重填进去
            expireTime: hcAdCacheEndTs
          };
          // 缓存广告&过期时间到本地
          localStorage.setItem(CACHE_KEY, JSON.stringify(resStr));
          return resObj.slot_ad[0];
        } else {
          localStorage.removeItem(CACHE_KEY);
          const msg = `${slotId}-无广告填充`;
          const val = await storage.get(msg);
          if (!val) {
            fetchMonitor.fail({
              msg,
              c1: resObj.code,
              bl1: resStr,
              bl2: data.ad_device_info,
            });
            storage.setDaily(msg, true);
            fact.event('huichuan_ad_fetch', {
              slot_id: slotId,
              account_id: '',
              sid: resObj.sid,
              ad_id: '',
              is_fill: 0,
              is_error: 0,
            });
          }
          return null;
        }
      } else {
        const msg = `${slotId}-请求失败-格式`;
        const val = await storage.get(msg);
        if (!val) {
          fetchMonitor.fail({
            msg,
            c1: resObj.code,
            bl1: resStr,
            bl2: data.ad_device_info,
          });
          storage.setDaily(msg, true);
          fact.event('huichuan_ad_fetch', {
            slot_id: slotId,
            account_id: '',
            sid: resObj.sid,
            ad_id: '',
            is_fill: 0,
            is_error: 1,
          });
        }
        return null;
      }
    } catch (e) {
      console.log(e);
      const msg = `${slotId}-请求失败-其他异常`;
      const val = await storage.get(msg);
      if (!val) {
        fetchMonitor.fail({
          msg,
          bl1: JSON.stringify(e),
          bl2: data.ad_device_info,
        });
        storage.setDaily(msg, true);
        fact.event('huichuan_ad_fetch', {
          slot_id: slotId,
          account_id: '',
          sid: '',
          ad_id: '',
          is_fill: 0,
          is_error: 1,
        });
      }
      return null;
    }
  },
  getTimestampInSecond() {
    return Math.floor(Date.now() / 1000);
  },
  // 获取曝光打点 url
  getExposeStatUrl(ad: IAd) {
    if (!ad?.vurl || !ad?.vurl.length) {
      return '';
    }
    const timestamp = hcAdHelper.getTimestampInSecond();
    return ad?.vurl[0].replace('{TS}', `${timestamp}`);
  },
  getOtherExposeStatUrl(ad: IAd): string[] {
    if (!ad?.vurl || !ad?.vurl?.length) {
      return [];
    }
    return ad?.vurl?.splice(1, ad.vurl.length);
  },
  // 获取点击打点 url，点击打点的timestamp需要和跳转的timestamp一致
  getClickStatUrl(ad: IAd, timestamp: number) {
    if (!ad.curl || !ad.curl.length) {
      return '';
    }
    let isDownload = false;
    if (ad.turl.length > 1) {
      isDownload = true;
    }
    return ad.curl[0].replace('{TS}', `${timestamp}`) + (isDownload ? '&hc_subid=0' : '');
  },
  getOtherClickStatUrl(ad: IAd): string[] {
    if (!ad.curl || !ad.curl.length) {
      return [];
    }
    return ad.curl.splice(1, ad.curl.length);
  },
  // 获取点击后需要跳转的 url
  getRedirectUrl(ad: IAd, timestamp: number) {
    if (!ad.turl.length) {
      return '';
    }
    let redirectUrl = ad.turl[0];
    // 如果turl[0]里有callback参数，需要urldecode callback参数值后添加stm，再urlencode回去，否则保持下发的turl不变。
    if (redirectUrl.indexOf('callback=') > -1) {
      const exp = /callback=(\S+?)(?=&|$)/;
      redirectUrl = redirectUrl.replace(exp, `callback=$1${timestamp}`);
    }
    return redirectUrl;
  },
  // 曝光上报汇川
  hcAdExpose(ad: IAd) {
    const urls = [hcAdHelper.getExposeStatUrl(ad), ...hcAdHelper.getOtherExposeStatUrl(ad)];
    urls.forEach(url => {
      if (url) {
        const rpt = new Image();
        rpt.src = url.replace('http://', 'https://');
      }
    });
  },
  // 点击上报汇川，点击打点的timestamp需要和跳转的timestamp一致
  async hcAdClick(ad: IAd) {
    const timestamp = hcAdHelper.getTimestampInSecond()
    const redirectUrl = hcAdHelper.getRedirectUrl(ad, timestamp);
    console.log('adClick', redirectUrl)
    if (!redirectUrl) {
      return
    }
    const statUrls = [hcAdHelper.getClickStatUrl(ad, timestamp), ...hcAdHelper.getOtherClickStatUrl(ad)];
    statUrls.forEach(clickStatUrl => {
      if (clickStatUrl) {
        const rpt = new Image();
        rpt.src = clickStatUrl.replace('http://', 'https://');
      }
    });
    const targetLinks: { type: string; link: string; scheme?: string; }[] = [];
    if (isIOS && ad.ad_content.adm_fixed_ulk && ad.ad_content.scheme) {
      targetLinks.push({
        type: 'ulk',
        link: ad.ad_content.adm_fixed_ulk,
        scheme: ad.ad_content.scheme
      });
    } else if (ad.ad_content.scheme) {
      targetLinks.push({
        type: 'scheme',
        link: ad.ad_content.scheme
      });
    }
    targetLinks.push({
      type: 'url',
      link: redirectUrl
    });
    let curr;
    console.log('处理跳转的顺序列表：' + JSON.stringify(targetLinks));
    while (curr = targetLinks.shift()) {
      let needBreak = false;
      console.log('处理：' + curr.type + ' ' + curr.link);
      switch (curr.type) {
        case 'ulk':
          console.log('处理ulk');
          const schemeName = curr.scheme;
          const queryResult = await ucapi.biz.queryApp({
            cache_first: '0',
            pkgs: [schemeName],
          });
          console.log('app安装查询结果：', schemeName, '\n', JSON.stringify(queryResult));
          if (queryResult[schemeName] === '') {
            console.log('查询ulk对应应用不存在');
            break;
          } else {
            console.log('ulk对应应用存在，准备通过startApp打开');
          }
        case 'scheme':
          console.log('处理scheme:' + curr.type);
          const startResult = await ucapi.biz.startApp(curr.link);
          console.log('startApp:', JSON.stringify(startResult));
          if (!startResult.result || startResult.result === 'false') { // 打开失败
            console.log('处理scheme:' + curr.type + ',打开失败，准备通过url跳转');
            break;
          } else {
            console.log('处理scheme:' + curr.type + ',打开成功，结束');
            needBreak = true; // 打开成功，需要结束
            break;
          }
        case 'url':
          console.log('处理url:' + curr.type);
          ucapi.base.openURL({url: redirectUrl});
          needBreak = true;
          break;
      }
      if (needBreak) {
        break;
      }
    }
    console.log('处理结束');
  },
  // 提交记录到汇川进行记录 sid、ucutid
  async submitTaskRecord(sid: string, thirdid: string, adInfos: { account_id: string; slot_id: string }): Promise<boolean> {
    const submitMonitor = tracker.Monitor(WPK_CATEGORY_MAP.UT_SID_SUBMIT_HC, {sampleRate: 1});
    try {
      const res: ITaskRecordRes = await fetch(`${config.HC_TASK_API}/web/main/reward/taskrecord/add4third`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sid,
          thirdid,
          channel: 2
        })
      }).then(data => data.json());
      if (res.status === 0) {
        submitMonitor.success({
          msg: `${adInfos.slot_id}-${adInfos.account_id}-提交成功`,
          c1: JSON.stringify(res.status),
          c2: sid,
          c3: thirdid
        });
        return true;
      }
      submitMonitor.fail({
        msg: `${adInfos.slot_id}-${adInfos.account_id}-提交失败`,
        c1: JSON.stringify(res.status),
        bl1: JSON.stringify(res),
      });
      return false;
    } catch (e) {
      console.log(e);
      submitMonitor.fail({
        msg: `${adInfos.slot_id}-${adInfos.account_id}-提交失败`,
        bl1: JSON.stringify(e),
      });
      return false;
    }
  }
}

/** 获取个性化广告推荐开关  **/
const getPersonalizedRecommendSwitch = async () => {
  try {
    const data = await ucapi.biz.getPersonalizedRecommendSwitch();
    const closePersonalizedAd = data?.personalized_ad === 0
    dispatch.ad.updateState({
      personalizedAdSwitch: !closePersonalizedAd
    });
    localStorage.setItem('closePersonalizedAd','' + closePersonalizedAd)
  } catch (e) { console.log('getPersonalizedRecommendSwitch err', e) }
}

export async function getAndUpdatePersonalizedAdSwitch() {
  const closePersonalizedAd = localStorage.getItem('closePersonalizedAd')
  const checkStart = Date.now();
  if (!closePersonalizedAd) {
    await getPersonalizedRecommendSwitch()
    tracker?.log({
      category: 177,
      msg: '获取个性化广告推荐开关',
      wl_avgv1: Date.now() - checkStart
     });
    return
  } else {
    dispatch.ad.updateState({
      personalizedAdSwitch: closePersonalizedAd === 'true'
    });
    getPersonalizedRecommendSwitch()
  }
}
