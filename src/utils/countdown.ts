export default class Countdown {
  duration: number; // 毫秒
  interval: number; // 毫秒
  startTime: number; // 毫秒
  timer;

  constructor(params) {
    this.interval = params.interval;
    this.duration = params.duration;
    this.callback = params.callback || this.callback;
  }

  callback: (diffTime: number) => void = () => {};

  start() {
    this.startTime = Date.now();
    this.callback(0);
    this._run();
  }

  _run() {
    this.timer = setInterval(() => {
      const diffTime = Date.now() - this.startTime;
      if (diffTime > this.duration) {
        clearInterval(this.timer);
      }
      this.callback(diffTime);
    }, this.interval);
  }

  stop() {
    clearInterval(this.timer);
  }
}
