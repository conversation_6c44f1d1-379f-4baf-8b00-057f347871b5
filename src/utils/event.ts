import EventEmitter3 from 'eventemitter3';

export interface IEventValType {
  /** 领取膨胀收益事件 */
  TakeVirtual: null;
  /** 页面点击 */
  PageClkAction: null;
  /** 分享任务 */
  taskShare: number | string;
  /** 任务奖励领取 */
  taskAward: null;
  /** 兑换成功  */
  exchangeSuccess?: number;
  maskShow: null;
  maskHide: null;
  maskClick: null;
  swipeTop: null;
  /** 元宝动画 */
  showIngotAni: {
    height?: number;
    align?: 'center' | 'right';
  };
  /** 元宝动画 */
  showModalIngotAni: {
    height?: number;
    align?: 'center' | 'right';
  };
  /** 单个图标动画结束 */
  ingotAniDone: null;
  /** 一组图标动画结束 */
  ingotGroupDone: null;
  /** 首页动画replay通知 */
  indexAnimationReplay: null;
  /** 登录状态变化 */
  AccountStateChange: null;
  disableBack: null;
  /** 裂变邀请扫码半屏 */
  showInviteQrcodePanel: null;
  /** appear */
  appearInitData: null;
  /** Modal 弹窗显示 */
  modalOpen: null;
  /** Modal 弹窗关闭 */
  modalClose: null;
  /** 首页初始化 */
  pageIndexDataInit: {
    isRetrySuccess: boolean
  };
}

const eventer = new EventEmitter3();

/** 触发事件 */
export function emit<K extends keyof IEventValType>(key: K, data?: IEventValType[K]) {
  return eventer.emit(key, data);
}

/** 监听事件 */
export function on<K extends keyof IEventValType>(
  key: K,
  cb: (data?: IEventValType[K] extends null ? any : IEventValType[K]) => void,
  isOnce = false,
) {
  if (isOnce) {
    return eventer.once(key, cb);
  }
  return eventer.on(key, cb);
}

export function off<K extends keyof IEventValType>(
  key: K,
  cb: (data?: IEventValType[K] extends null ? any : IEventValType[K]) => void,
) {
  return eventer.off(key, cb);
}

export default {
  emit,
  on,
  off,
};
