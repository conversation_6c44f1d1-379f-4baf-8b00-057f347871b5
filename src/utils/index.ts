import * as ua from '@ali/uc-toolkit/lib/ua';
import { isIOS } from "@/lib/universal-ua";
import {appVersion} from "@/lib/universal-ua";

export const isLatestVersion = (latestVersion: string): boolean => {
  const currentVersion = appVersion || '99.99.9999'
  return ua.isLatestVersion(currentVersion, latestVersion)
};
/** 兼容服务端引号&quot转义错误 */
export const strToEscape = (str) => {
  const arrEntities = {'quot': '"'};
  return str.replace(/&(quot);/ig,function(all,t){return arrEntities[t];});
}
/** 是否为json格式数据 */
export const isJson = (str: string) => {
  try {
    JSON.parse(str);
  } catch (e) {
      return false;
  }
  return true;
};

/** 获取礼品使用地址 */
export const getGiftUseUrl = (url: string) => {
  const newUrl: string = strToEscape(url);
  if (isJson(newUrl)) {
    const obj = JSON.parse(newUrl);
    return (isIOS ? obj?.ios : obj?.android) as string
  }
  return newUrl;
};

