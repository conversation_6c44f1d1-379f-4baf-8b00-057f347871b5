import { getParam } from "@/lib/qs";

interface IParseUrlRes {
  scheme: string | undefined,
  host: string | undefined,
  port: string | undefined,
  path: string | undefined,
  query: string | undefined,
  hash: string | undefined
}

export function parseUrl (url: string): IParseUrlRes {
  const regExp = /^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/;
  const result = regExp.exec(url) || []
  return {
    scheme: result[0],
    host: result[3],
    port: result[4],
    path: result[5],
    query: result[6],
    hash: result[7]
  }
}

interface IParams {
  [key: string]: any
}
export function addParams (url: string, params: IParams) {
  const paramsKeys = Object.keys(params)
  if (!url) {
    return ''
  }
  if (!paramsKeys.length) {
    return url
  }
  url += !url ? '' : url.includes('?') ? '&' : '?'
  return url + paramsKeys.map((key) => `${key}=${params[key]}`).join('&')
}

// 解析url参数，返回query参数对象
export function parseQueryStr (url = window.location.href) {
  let obj = {};
  if (url.includes('#')) {
    url.replace(/#\/.+/, '')
  }
  let index = url.indexOf('?');
  let params = url.substr(index + 1);

  if(index != -1) {
    let parr = params.split('&');
    for(let i of parr) {
      let arr = i.split('=');
      obj[arr[0]] = arr[1];
    }
  }
  return obj;
}

export const isInsetPage = () => {
  return +getParam('inset') === 1
}
