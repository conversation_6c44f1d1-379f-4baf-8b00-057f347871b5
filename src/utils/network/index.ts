import network from '@ali/weex-toolkit/lib/network';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category';
import { isUC } from '@ali/weex-toolkit/lib/ua';
import { getParam } from '@ali/uc-toolkit/lib/network/plugin/qs';
import { store } from '@/store';
import ucapi from "@/utils/ucapi";
import { parseUrl } from "@/utils/url";
import qs from '../../lib/qs';

interface IbaxiaHeader {
  'x-sign'?: string;
  'x-sgext'?: string;
  'x-mini-wua'?: string;
  'x-umt'?: string;
  'x-appkey'?: string;
  'x-pv'?: string;
  'x-t'?: string;
  asac?: string;
}

network.init({
  // @ts-ignore
  sampleRate: 1,
  // @ts-ignore
  timeout: 12e3,
  needExpandUCParams: isUC(),
});


// 在这里增加需要接入霸下的接口路径
const needMiniwuaApiPath = [
  '/invite/v2/bindInviteCode',
  '/are/v1/collect'
]

const miniwuaMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_MINI_WUA, { sampleRate: 1 });

async function optionsHandler (url, data, option) {
  const path = url.includes('://') ? '/' + parseUrl(url).path : url
  if (needMiniwuaApiPath.includes(path)) {
    const text = getParam('miniwuaText') || `${store.getState().user.utdId}`
    const miniwuaRes = await ucapi.security.miniwua({
      text,
      api: path,
    })
    if (miniwuaRes?.output) {
      miniwuaMonitor.success({
        msg: '获取成功',
        c1: encodeURIComponent(miniwuaRes?.output?.['x-sign'] || ''),
        c2: encodeURIComponent(miniwuaRes?.output?.['x-mini-wua'] || ''),
        c3: path,
        bl1: JSON.stringify(miniwuaRes)
      })
    } else {
      miniwuaMonitor.success({
        msg: '获取失败',
        c3: path,
        bl1: JSON.stringify(miniwuaRes)
      })
    }
    let baxiaHeader:IbaxiaHeader = {};
    const xMiniWua = encodeURIComponent(miniwuaRes?.output?.['x-mini-wua'] || '');
    const xUmt = encodeURIComponent(miniwuaRes?.output?.['x-umt'] || '');
    if (xMiniWua && xUmt) {
      baxiaHeader = {
        'x-sign': encodeURIComponent(miniwuaRes?.output?.['x-sign'] || ''),
        'x-sgext': encodeURIComponent(miniwuaRes?.output?.['x-sgext'] || ''),
        'x-mini-wua': xMiniWua,
        'x-umt': xUmt,
        'x-appkey': miniwuaRes?.appkey || '',
        'x-pv': miniwuaRes?.['x-pv'] || '',
        'x-t': miniwuaRes?.['x-t'] || ''
      }
      if (path === '/are/v1/collect') {
        baxiaHeader = {
          ...baxiaHeader,
          asac: '2A21C09M2Q24BHN3RLAUOI',
        }
      }
    }
    return option ? {
      ...option,
      headers: option?.headers ? {
        ...option.headers,
        ...baxiaHeader
      } : {
        ...baxiaHeader
      }
    } : {
      headers: {
        ...baxiaHeader,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
    }
  }
  return option
}


let networkAddHeader = {
  ...network
}
networkAddHeader.post = async function (url, data, option) {
  const reqOptions = await optionsHandler(url, data, option);
  if (typeof data === 'object') {
    data = {
      ...(data || {}),
      entry: qs.getParam('entry')
      || '',
      evSub: qs.getEvSub(),
      uc_param_str: 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf'
    }
  }
  return network.post(url, data, reqOptions)
}

networkAddHeader.get = async function (url, data, option) {
  const reqOptions = await optionsHandler(url, data, option)
  data = {
    ...(data || {}),
    entry: qs.getParam('entry')
    || '',
    evSub: qs.getEvSub(),
    uc_param_str: 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf'
  };
  return network.get(url, data, reqOptions)
}

export default networkAddHeader;
