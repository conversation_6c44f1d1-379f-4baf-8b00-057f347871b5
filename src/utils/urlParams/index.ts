import { uc } from '@ali/weex-toolkit/lib/weex_config';
import { parseQueryStr } from "@/utils/url";
import prerenderEvent from '../../lib/prerender/event';
import { isPrerender } from '../../lib/prerender';

type Params = {
  [s: string]: string
}

class UrlParams {
  params: Params
  constructor() {
    this.params = {}
    this.init()
  }
  init() {
    if(isPrerender()) {
      prerenderEvent.on('prerendercommit', e => {
        let realUrl = e && e.detail && e.detail.url;
        if (realUrl) {
          this.params = parseQueryStr(realUrl) || {}
        }
      });
    }
  }
  getParams(key?: string): any {
    if (!key) {
      return  Object.keys(this.params || {}).length ? this.params : uc.params
    }
    if (this.params[key]) return this.params[key]
    return uc.params[key]
  }
}

const urlParams = new UrlParams()

export default urlParams
