export const inittialUserId = (function () {
  const KEY = '__wpkuid__';
  const n = localStorage.getItem(KEY);
  if (n) {
    return n;
  }
  function s4() {
    // tslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  const mockUid = `${s4() + s4() }-${ s4() }-${ s4() }-${ s4() }-${ s4() }${s4() }${s4()}`;
  localStorage.setItem(KEY, mockUid);
  return mockUid;
})();
