import { store } from '@/store';

const Units = [
  {
    unit: '百万',
    zero: 6,
  },
];
/**
 * 展示元宝
 * @param amount 元宝值
 */
export function convertCoin2Display(amount: number) {
  if (amount === 0) return '0';
  let result = `${amount}`;
  Units.every(item => {
    if (amount >= Math.pow(10, item.zero)) {
      result = (Math.floor(amount / Math.pow(10, item.zero - 2)) / Math.pow(10, 2)).toFixed(2) + item.unit;
      // result = (amount / Math.pow(10, item.zero)).toFixed(2) + item.unit;
      return false;
    }
    return true;
  });
  return result;
}

/**
 * 元宝转现金
 */
export function convertCoin2Cash(amount: number): number {
  const { rate } = store.getState().task;
  return getMost2PointNum(amount / (rate * 100));
}

/**
 * 现金单位转换（分 => 元）
 */
export function convertCashUnit(amount: number): number {
  return amount / 100;
}

/**
 * 元宝转为现金展示，默认向下舍去
 * @param amount 元宝值
 * @param decimal 小数点
 */
export function convertCoin2CashDisplay(amount: number, decimal = 2, ratio10 = 4) {
  if (amount === 0) return '0';
  // const numStr = (Math.floor(amount / Math.pow(10, ratio10 - decimal)) / Math.pow(10, decimal)).toFixed(decimal);
  const numStr = getMost2PointNum(amount).toString();
  let isStartZero = false;
  let dotIndex = -1;
  const result = [];
  const tmp = numStr.split('').reverse();
  tmp.forEach((value, index) => {
    if (value !== '0') isStartZero = true;
    if (!isStartZero && value === '0') {
      return;
    }
    if (value === '.' && result.length === 0) return;
    // @ts-ignore
    result.push(value);
    if (value === '.') {
      dotIndex = 0;
    } else if (dotIndex !== -1) {
      // eslint-disable-next-line no-plusplus
      dotIndex++;
      if (dotIndex % 4 === 0 && index !== tmp.length - 1) {
        // @ts-ignore
        result.push(',');
      }
    }
  });
  return result.reverse().join('');
}

/**
 * 现金展示
 */
export function convertCash2Display(amount: number) {
  return `${parseFloat((amount / 100).toFixed(2))}`;
}

export const convertCoin2chineseUnit = (amount: number) => {
  if (amount < 1e3) return amount;
  if (amount < 1e4) return `${amount % 1e3 ? '约' : ''}${Math.floor(amount / 1e3)}千`;
  if (amount < 1e6) return `${amount % 1e3 ? '约' : ''}${Math.floor(amount / 1e4)}万`;
  return amount;
};

/**
 * 最多保留两位小数
 */
export const getMost2PointNum = (amount: number): number => {
  return Math.floor(amount * 100) / 100;
}
