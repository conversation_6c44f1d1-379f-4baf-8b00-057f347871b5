import ucapi from '@/utils/ucapi';
import storage from '@ali/weex-toolkit/lib/storage';
import { STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY } from '@/constants/storage';
import toast from '@/lib/universal-toast';
import { dispatch } from '@/store';
import {isIOS} from "@/lib/universal-ua";

const CALENDAR_REMINDER_TITLE = '【UC极速版】快去签到领取元宝啦';

// 设置日历
export async function setCalendarReminders() {
  const startTime = new Date(new Date().toLocaleDateString()).getTime() + 34 * 60 * 60 * 1000; // 第二天10点
  const endTime = new Date(new Date().toLocaleDateString()).getTime() + 34.5 * 60 * 60 * 1000; // 第二天10点半
  return await ucapi.mission.setCalendarReminders({
    title: CALENDAR_REMINDER_TITLE,
    description: '',
    startTime, 	// long 开始时间，必填（毫秒）
    endTime, 					//long 结束时间，必填（毫秒）
    duration: 30, // 分钟
    count: isIOS ? 30 : 365, //循环365次一年，默认1
    reminderMin: 10,//提前5分钟提醒，默认0
    calendarType: 'welfare',//设置日历的类型（保持每种日历唯一）
  });
}

export async function setCalendarRemindersInWelfare() {
  try {
    await deleteAllCalendarReminders();
    const data = await setCalendarReminders();
    console.log('设置日历', data);
    if (!data.errCode) {
      toast.show('已打开提醒');
      dispatch.app.updateState({
        hadSetSignRemind: true,
      });
      dispatch.app.updateAll();
      // 通过签到任务 switch 入口写入日历时，签到提醒任务不自动领取，需要手动领取
      storage.set(STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY, true);
      setCalendarReminderStoreData();
    } else {
      if (isIOS) {
        toast.show('开启失败，请前往设置-UC极速版-日历开启读写权限');
      } else {
        toast.show('开启失败，请前往系统设置-权限管理-UC极速版开启日历读写权限');
      }
    }
  } catch (err) {
    // 不支持api
    console.log(err);
  }
}

// 判断是否日历内容是否是提醒签到的
export function hadSignCalendarReminder(reminders) {
  // 用标题匹配
  const find = reminders.find(reminder => reminder.title === CALENDAR_REMINDER_TITLE);
  if (find) return true;
  return false;
}

// 查询日历权限
export async function queryCalendarPermission() {
  return await ucapi.mission.queryCalendarPermission({
    calendarType: 'welfare'
  });
}

// 查询日历
export async function queryCalendarReminders() {
  return await ucapi.mission.queryCalendarReminders({
    calendarType: 'welfare'
  });
}

// 删除日历
export async function deleteCalendarReminders() {
  return await ucapi.mission.deleteCalendarReminders({
    calendarType: 'welfare'
  });
}

// 删除重复日历
export async function deleteAllCalendarReminders() {
  // 先查询，后遍历删除
  console.log('删除重复日历');
  const reminders = await queryCalendarReminders();
  console.log('reminders', reminders);
  if (!reminders.errCode) {
    const targetReminders = reminders.events.filter(reminder => reminder.title === CALENDAR_REMINDER_TITLE);
    console.log('targetReminders', targetReminders);
    for (let i = 0; i <targetReminders.length; i++) {
      await ucapi.mission.deleteCalendarReminders({
        calendarType: 'welfare'
      });
    }
  }
  return;
}

// 写入日历设置成功数据到disk
export async function setCalendarReminderStoreData() {
  return await ucapi.biz.setStoreData({
    page: 'uclite-welfare-new',
    id: 'set-calendar-reminder',
    storeType: 'disk',
    data: {
      setCalendarReminder: true,
    },
  })
}

// 读取日历设置成功数据从disk
export async function getCalendarReminderStoreData() {
  return await ucapi.biz.getStoreData({
    page: 'uclite-welfare-new',
    id: 'set-calendar-reminder',
  })
}

// 删除日历设置成功数据从disk
export async function deleteCalendarReminderStoreData() {
  return await ucapi.biz.deleteStoreData({
    page: 'uclite-welfare-new',
    id: 'set-calendar-reminder',
  })
}
