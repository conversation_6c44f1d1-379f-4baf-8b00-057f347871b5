// 绑定用户邀请码
export const BIND_INVITE_CODE_CODE_MSG = {
  'CORAL:SYSTEM_ERROR': '系统错误',
  'DB:ACCESS_ERROR': '系统错误(DB)',
  'CORAL:INPUT_PARAM_INVALID': '参数无效',
  'INVITE:INVALID_USER': '无效用户',
  'INVITE:INVALID_CODE': '无效邀请码',
  'INVITE:OWN_CODE': '自己的邀请码(账号或设备)',
  'INVITE:BIND_CODE_LIMIT': '已经绑过邀请码(达到上限)',
  'INVITE:UNUSUAL_USER': '风险用户类错误',
  'INVITE:SAME_DEVICE_BIND_NUM': '同设备绑码数量达到上限',
  'CORAL:INVALID_REQUEST': '无效请求(签名错误)',
  'INVITE:NULL_DEVICE': '空设备标识',
  'CORAL:CONCURRENT_OPERATION': '并发请求',
  'INVITE:RELATION_ACCOUNT_ERROR': '关联游客服务时异常',
  'NOT_LOGIN': '未登录',
  'INVITEE_NUM_DAILY_LIMIT': '每天邀请用户数量达到上限',
  'INVITEE_NUM_LIMIT': '邀请用户数量达到上限',
}
export const BIND_INVITE_CODE_BIZ_MSG = {
  // 'INVITE:INVALID_CODE': '无效邀请码',
  // 'INVITE:BIND_CODE_LIMIT': '无效邀请码',
  // 'INVITE:OWN_CODE': '无效邀请码',
  'INVITE:INVALID_CODE': '你填写的邀请码不正确',
  'INVITE:BIND_CODE_LIMIT': '您已经填写过好友的邀请码了，不可重复填写哦',
  'INVITE:OWN_CODE': '无法接受自己的邀请哦',
  'INVITE:UNUSUAL_USER': '您或好友的账号异常，无法参与活动',
  'INVITE:SAME_DEVICE_BIND_NUM': '您当前设备已经填写过好友的邀请码了，不可重复填写哦',
  // 'INVITE:INVITEE_NUM_DAILY_LIMIT': '当日邀请用户数量已达上限',
  // 'INVITE:INVITEE_NUM_LIMIT': '邀请用户数量已达上限',
  'INVITE:INVITEE_NUM_DAILY_LIMIT': '这个邀请码每天的填写次数已被用光​',
  'INVITE:INVITEE_NUM_LIMIT': '这个邀请码的填写次数已被用光​',
}

// 错误的码
export const WRONG_CODE_ARR = [
  'INVITE:INVALID_CODE',
];

// 提示上限的码
export const LIMIT_CODE_ARR = [
  'INVITE:INVITEE_NUM_DAILY_LIMIT',
  'INVITE:INVITEE_NUM_LIMIT',
  'INVITE:BIND_CODE_LIMIT',
  'INVITE:SAME_DEVICE_BIND_NUM',
];

// 自己的码
export const OWN_CODE_ARR = [
  'INVITE:OWN_CODE',
];

export const COMMON_ERROR_MSG = '服务器开小差了';
