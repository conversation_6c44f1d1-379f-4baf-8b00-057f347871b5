import Toast from '@/lib/universal-toast';
import {
  BIND_INVITE_CODE_CODE_MSG,
  BIND_INVITE_CODE_BIZ_MSG,
  COMMON_ERROR_MSG,
  WRONG_CODE_ARR,
  OWN_CODE_ARR,
  LIMIT_CODE_ARR
} from './code';

interface IError {
  code: string;
  msg: string;
  success: boolean;
  timestamp: number;
  traceId?: string;
  ucCode?: string;
  ucErrorCode?: string;
}

// 绑定邀请码
export function dealWithBindInviteCodeCodeMsg(err: IError) {
  if (BIND_INVITE_CODE_CODE_MSG.hasOwnProperty(err.code)) {
    console.log(BIND_INVITE_CODE_CODE_MSG[err.code]);
  }
}
export function dealWithBindInviteCodeBizCodeMsg(err: IError) {
  if (BIND_INVITE_CODE_BIZ_MSG.hasOwnProperty(err.code)) {
    Toast.show(BIND_INVITE_CODE_BIZ_MSG[err.code]);
  } else {
    Toast.show(`${COMMON_ERROR_MSG}`);
  }
}

export function getBindInviteCodeFalseCode(err: IError) {
  if (WRONG_CODE_ARR.includes(err.code)) {
    return 'wrongcode';
  } else if (OWN_CODE_ARR.includes(err.code)) {
    return 'owncode';
  } else if (LIMIT_CODE_ARR.includes(err.code)) {
    return 'limit';
  } else {
    return err?.code || 'unknown';
  }
}
