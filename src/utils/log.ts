import fact from '../lib/fact';
import UrlParams from '@/utils/urlParams/index';
import qs from '@/lib/qs';

export const SPM_A = 'uclite_fuli';
export const EV_CT = 'uclite_fuli';
export const EV_SUB = 'uclite_fuli_index';

export type TPageName = 'index' | 'cash' | 'coin' | 'profit' | 'exchange';

interface IPageParam {
  page: string;
  a: string;
  b: string;
}

export const PAGE_LOG_MAP: {[key in TPageName]: IPageParam} = {
  index: {
    page: 'page_fuli_index',
    a: SPM_A,
    b: 'index',
  },
  cash: {
    page: 'page_uclite_fuli_cash',
    a: SPM_A,
    b: 'cash'
  },
  coin: {
    page: 'page_uclite_fuli_coin',
    a: SPM_A,
    b: 'coin'
  },
  // 我的收益
  profit: {
    page: 'page_uclite_fuli_profit',
    a: SPM_A,
    b: 'profit',
  },
  // 元宝兑换
  exchange: {
    page: 'page_fulijs_exchange',
    a: SPM_A,
    b: 'exchange'
  }
};

/** 初始页面参数，可选是否打 2001 事件 */
export function initPageLogParam(page: TPageName, needLogView = false) {
  const pageLogObj = PAGE_LOG_MAP[page];
  fact.setup({
    b: pageLogObj.b,
    a: pageLogObj.a,
    ev_ct: EV_CT,
    ev_sub: qs.getEvSub(),
  });
  fact.baseParam({ page: pageLogObj.page, entry: UrlParams.getParams('entry'), from: UrlParams.getParams('from') });
  if (needLogView) {
    fact.pageview(pageLogObj.page, {
      b: pageLogObj.b,
    });
  }
}
