import { appVersion, isIOS } from '../lib/universal-ua';
import storage from '@ali/weex-toolkit/lib/storage';
import config from './../config';

const TARGET_COUNT = 20;
let SURPRISE_COUNT = 0;
let SURPRISE_TIMER;

interface SurpriseShowArgs {
  params?: any;
  clearStorage?: boolean;
}

const defaultArgs = { params: {}, clearStorage: true };

export async function surpriseShow({ params = {}, clearStorage = true }: SurpriseShowArgs = defaultArgs) {
  if (SURPRISE_TIMER) {
    clearTimeout(SURPRISE_TIMER);
  }
  SURPRISE_TIMER = setTimeout(() => {
    SURPRISE_COUNT = 0;
  }, 500);
  SURPRISE_COUNT++;
  if (SURPRISE_COUNT >= TARGET_COUNT) {
    SURPRISE_COUNT = 0;
    if (clearStorage) {
      const keys = await storage.getAllKeys() || [];
      keys.forEach((key) => {
        storage.remove(key);
      });
    }
    alert(JSON.stringify({
      // env: config.env,
      appVersion,
      feVer: MAIN_VERSION,
      platform: isIOS ? 'IOS' : 'Android',
      ...params,
      clearStorage,
    }));
  }
}
