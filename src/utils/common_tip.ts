const Message = {
  takeVirtualAward: { // TODO 如果具体行为的异常码有要求，则配置该值，否则只需要配置common即可
    NOT_ENOUGH: '别着急，请等待元宝生产哦',
    OVER_DAILY_LIMIT: '超过今日元宝上限啦，明天再来',
    REPEAT_SUBMIT: '操作太快啦，请稍后再试',
    OVER_LIMIT: '亲，请先登录再收元宝吧',
  },
  common: {
    'BIZ:ERROR': '业务异常',
    ACT_END: '亲，活动已经结束啦',
    APP_INIT: '亲，初始化异常',
  },
  exchange: {
    UNUSUAL_USER: '账号异常',
    NOT_ENOUGH: '哎呀~元宝不够兑换，请先攒元宝吧',
    REPEAT_SUBMIT: '操作太快啦，请稍后再试',
  },
  other: '网络异常，请重新访问试试~',
};

export default function getToastText(params: { code: string; type?: keyof typeof Message }) {
  const { code, type } = params;
  const result = (type && Message[type][code]) || Message.common[code] || Message.other;
  // if (code) {
  //   return `${result}(${code})`;
  // }
  return result;
}
