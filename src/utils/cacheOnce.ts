const caches = {};
const used = {};

export default function cacheOnce(key, fn, times = 1) {
  const _this = this;
  let currTimes = 0;
  return function() {
    if (!caches[key]) {
      const rtn = fn.apply(_this, arguments);
      if (!used[key]) {
        caches[key] = rtn;
      }
      return rtn;
    }
    currTimes++;
    const prevPromise = caches[key];
    if (currTimes >= times) {
      used[key] = true;
      caches[key] = null;
    }
    return prevPromise;
  };
}
