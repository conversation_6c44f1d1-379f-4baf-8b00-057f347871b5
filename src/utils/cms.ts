import ucapi from '@/utils/ucapi';
import { isIOS } from "@/lib/universal-ua";
import tracker from "@/lib/tracker";
import { WPK_CATEGORY_MAP } from "@/constants/tracker_category";

/** 获取 cms 运营 banner 数据 */
export async function getCmsBannerAdList(key: string): Promise<Array<any>> {
  const cmsMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_CMS_DATA)
  try {
    const result = await ucapi.biz.getCMSResource({
      key,
      array: true,
    });
    // 处理 val
    console.log(`cms数据${key}`, result);
    if (result.state === 1 || result.state === 2) {
      let data;
      if (Object.prototype.toString.call(result?.data) === '[object Array]') {
        data = result?.data?.[0];
      } else {
        data = result?.data;
      }
      cmsMonitor.success({
        msg: `获取成功-${key}`,
        c1: data.test_id || '',
        c2: data.test_data_id || '',
        c3: key,
        bl1: JSON.stringify(result)
      })
      if (!data) {
        cmsMonitor.fail({
          msg: '获取失败-无数据',
          c3: key,
          bl1: JSON.stringify(result)
        })
        return []
      }
      const now = new Date().getTime();
      if (data.start_time && data.end_time) {
        const dataStartTime = JSON.parse(data.start_time) * 1000;
        const dataEndTime = JSON.parse(data.end_time) * 1000;
        // cms 数据时间段判断
        if (dataStartTime > now || dataEndTime < now) {
          console.log('cms 数据时间段过期')
          return [];
        }
      }
      const list = data.items;
      const bannerList = list.map((item) => {
        item.startTime = item.startTime ? new Date(isIOS ? item.startTime.replace(/-/g, '/') : item.startTime).getTime() : 0;
        item.endTime = item.endTime ? new Date(isIOS ? item.endTime.replace(/-/g, '/') : item.endTime).getTime() : 0;
        item.needLogin = item.needLogin ? JSON.parse(item.needLogin) : false;
        item.isTaobao = item.isTaobao ? JSON.parse(item.isTaobao) : false;
        return item;
      })
      console.log('cms bannerList', bannerList);
      return bannerList;
    } else {
      return [];
    }
  } catch (err) {
    cmsMonitor.fail({
      msg: '获取失败',
      c3: key,
      bl1: JSON.stringify(err)
    })
    console.log(err);
    return [];
  }
}
