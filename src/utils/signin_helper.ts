import { convertCoin2Cash } from '@/utils/amount';
import { store } from '@/store';

// 获取第一天与第七天的日期
export const getSignDateStr = (signList) => {
  const sixDayMsDiff = 3600 * 24 * 6 * 1000;
  const firstDayTimeStr = signList[0]?.beginTime ? new Date(signList[0]?.beginTime) : new Date();
  const lastDayTimeStr = signList[0]?.beginTime ? new Date(signList[0]?.beginTime + sixDayMsDiff) : new Date(new Date().getTime() + sixDayMsDiff);
  const firstDayStr = getDoubbleNumberStr(firstDayTimeStr.getMonth() + 1) + '.' + getDoubbleNumberStr(firstDayTimeStr.getDate());
  const lastDayStr = getDoubbleNumberStr(lastDayTimeStr.getMonth() + 1) + '.' + getDoubbleNumberStr(lastDayTimeStr.getDate());
  return {
    firstDayStr,
    lastDayStr,
  };
};

// 日期两位表示
export const getDoubbleNumberStr = (num) => {
  if (num < 10) {
    return `0${num}`;
  }
  return `${num}`;
};

// 计算7天签到任务奖励金额（元）
export const getSignInTotalCash = (signList) => {
  const { rate } = store.getState().task
  // 有些是元宝、有些是现金
  let totalCoinAmount = 0;
  signList.forEach(sign => {
    const amount = sign?.rewardItems[0]?.amount || 0;
    if (sign?.rewardItems[0]?.mark?.indexOf('coin') > -1) {
      totalCoinAmount += amount;
    } else if (sign?.rewardItems[0]?.mark?.indexOf('cash') > -1) {
      totalCoinAmount += amount * rate;  // 1分=300元宝、1元=100分=30000元宝
    } else { // 配置错误当元宝，与下面默认的icon一致
      totalCoinAmount += amount;
    }
  });
  return convertCoin2Cash(totalCoinAmount);
};

// 获取真实显示数量（真实数字）
export const getRealAmount = (reward) => {
  if (reward?.mark?.indexOf('cash') > -1) {
    return reward.amount / 100;
  }
  return reward?.amount || 0;
};
