function todayLeft() {
  const date = new Date();
  const now = date.getTime();
  date.setHours(23);
  date.setMinutes(59);
  date.setSeconds(59);
  date.setMilliseconds(0);
  return date.getTime() - now;
}

export function isSameDay(st1, st2) {
  const d1 = new Date();
  const d2 = new Date();
  d1.setTime(st1);
  d2.setTime(st2);

  function getFullDate(d: Date) {
    return [d.getFullYear(), d.getMonth(), d.getDate()].join('');
  }
  return getFullDate(d1) === getFullDate(d2);
}

export function getDateObj(t: number) {
  const d = new Date();
  d.setTime(t);
  return {
    year: d.getFullYear(),
    month: d.getMonth() + 1,
    date: d.getDate(),
    hour: d.getHours(),
    min: d.getMinutes(),
    second: d.getSeconds()
  }
}

export function secondFormat (seconds: number) {
  const mins = Math.floor(seconds / 60)
  const leftSecond = seconds % 60
  const minStr = mins < 10 ? '0' + mins : '' + mins
  const secondsStr = leftSecond < 10 ? '0' + leftSecond : '' + leftSecond
  return  `${minStr}:${secondsStr}`
}

export function inPeriod(startTime: number, endTime: number, currTime: number) {
  return currTime >= startTime && currTime <= endTime;
}

/**
 * 获取时间戳相差的天数:
 * t1 > t2 返回正数;
 * t1 < t2 返回负数;
 * @param t1 
 * @param t2 
 * @returns 
 */
export const getDistanceTimeToDay = (t1: number, t2: number) => {
  if (!t1 || !t2) {
    return 0;
  }
  const diffSeconds = t1 - t2;
  const diffDay = diffSeconds / (24 * 60 * 60 * 1000);
  return Math.floor(diffDay);
}

/**
 * 格式化毫秒时间戳
 *
 * @param {String, Number} timestamp (单位为豪秒)
 * @param {String} format (格式)
 *    format='YYYY-MM-DD'
 *    format='MM/DD hh:mm'
 * @param {boolean} buling 日期不足两位，是否需要前置补零
 *    默认 true
 * @returns {String} default return YYYY/MM/DD hh:mm:ss
 */
export function formatTimestamp(timestamp, format = 'YYYY/MM/DD hh:mm:ss', buling = true): string {
  const time = Number.parseInt(timestamp, 10);
  const date = new Date(time);

  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let hour = date.getHours();
  let minute = date.getMinutes();
  let second = date.getSeconds();

  // 辅助函数，用于处理小于10的数字
  const padZero = (value: number) => (value > 9 ? `${value}` : (buling ? `0${value}` : `${value}`));

  return format
    .replace('YYYY', year.toString())
    .replace('MM', padZero(month))
    .replace('DD', padZero(day))
    .replace('hh', padZero(hour))
    .replace('mm', padZero(minute))
    .replace('ss', padZero(second));
}

export function calculateDaysBetweenDates(beginTime: number, endTime: number) {
  // 确保beginTime和endTime都是Date对象
  let oneDay = 24 * 60 * 60 * 1000; // 一天的时间毫秒数
  let start = new Date(beginTime).getTime(); // 将开始时间转换为毫秒
  let end = new Date(endTime).getTime(); // 将结束时间转换为毫秒

  // 计算时间差并转换为天数
  let days = Math.ceil(Math.abs((end - start) / oneDay));
  return days;
}

export function formatLeftTime(leftTime: number) {
  const leftHour = Math.floor(leftTime / (3600 * 1000))
  const leftMin = Math.floor((leftTime - (leftHour * 3600 * 1000)) / (60 * 1000))
  const leftSecond = Math.floor(leftTime % (60 * 1000) / 1000)
  return {
    hour: leftHour > 9 ? leftHour : `0${leftHour}`,
    min: leftMin > 9 ? leftMin : `0${leftMin}`,
    second: leftSecond > 9 ? leftSecond : `0${leftSecond}`
  }
}

export default {
  todayLeft,
  isSameDay,
};
