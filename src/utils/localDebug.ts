import network from './network';
import qs from 'qs';

// FIXME: 这里拼出来的参数不对
export const localDebug = (params: Record<string, any>) => {
  let query = '';
  for (const key in params) {
    if (Object.prototype.hasOwnProperty.call(params, 'key')) {
      const val = params[key];
      if (typeof val === 'object') {
        query += `&${key}=${JSON.stringify(val)}`;
      } else {
        query += `&${key}=${JSON.stringify(val)}`;
      }
    }
  }

  if (qs.parse(location.search.substr(1)).debug) {
    // 链接 + #uc_wx_init_params={"debug":true}
    return network.get(`http://myvv.cn?${ query.slice(1)}`);
  }
};
