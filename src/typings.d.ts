declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}

declare module '*.json' {
  const value: any;
  export default value;
}

declare interface Window {
  ucweb: any;
  __INITIAL_DATA__: any;
  ucapi?: any;
  __WaitPms__: Promise[];
  __CSRFirstDataPms__?: Promise;
  __documentStartTime?: number;
  __CHUNK2_LOAD_TIME__?: number;
  __DISPATCH_CHUNK2_EVENT_TIME__?: number;
  __APP_INSTALL_CHECK_PMS__: Promise;
  __APP_PUSH_STATE__: Promise;
  __APP_DEFAULT_BROWSER__: Promise;
  __app_init_time: number;
  __first_app_init_time: number;
  compass?: {
    lifecycle?: {
      visibilityState?: 'visible' | 'hidden';
    };
  };
  _his1?: any;
  _his?: any;
}

declare let MAIN_VERSION;
declare let DEV;
declare let PUBLISH_ENV;
declare let __ctx__: {
  logger: any;
} | undefined

declare module '*.png';
declare module '*.svg';
declare module '*.jpeg';
declare module '*.jpg';
