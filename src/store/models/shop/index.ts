import { createModel } from '@rematch/core';
import { RootModel, store } from "@/store";
import network from '@/utils/network';
import config from "@/config";
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { getErrorInfo } from "@/store/models/task/task_util";
import PreLoadingImage from './preLoadingImage.class';
// import whData from '@/lib/wormhole-data'

export interface IGoodsInfo {
  title: string;
  sell: number | string,
  goodsPrice: string;
  promotePrice: string;
  tbGoodLink: string;
  eurl: string;
  ifs: string;
}

export interface ItaobaoGood {
  title: string;
  shortTitle: string;
  zkFinalPrice: number;
  finalPromotionPrice: number;
  clickUrl: string;
  pictUrl: string;
  volume: number;
}

type IpriceGoodsList = {
  goodsList: ItaobaoGood[];
  pageNo: number,
  isDone: boolean;
  isEmpty: boolean;
}

export interface ShopState {
  curPageNo: number
  shoppingList: IGoodsInfo[];
  curPageGoodsList: IGoodsInfo[];
  low_priceGoodsList: IpriceGoodsList
  you_likeGoodsList: IpriceGoodsList
  hot_sellGoodsList: IpriceGoodsList
  isUpdateFirstData: boolean;
}

export interface shopListResponse {
  goodsList: ItaobaoGood[];
  pageNo: number,
  goodsType: shopType,
}

export type shopType = 'low_price' | 'you_like' | 'hot_sell'

const shop = createModel<RootModel>()({
  state: {
    curPageNo: 1,
    shoppingList: [],
    curPageGoodsList: [],
    preGoodsList: [],
    low_priceGoodsList: {
      goodsList: [],
      pageNo: 0,
      isDone: false,
      isEmpty: false,
    },
    you_likeGoodsList: {
      goodsList: [],
      pageNo: 0,
      isDone: false,
      isEmpty: false,
    },
    hot_sellGoodsList: {
      goodsList: [],
      pageNo: 0,
      isDone: false,
      isEmpty: false,
    },
    isUpdateFirstData: false,
  },
  reducers: {
    updateState(state: ShopState, payload: Partial<ShopState>): ShopState {
      return {
        ...state,
        ...payload
      }
    },
    updateShoppingList(state: ShopState, payload: { shoppingList: IGoodsInfo[], pageNo: number }): ShopState {
      const newList = payload.pageNo > state.curPageNo ? state.shoppingList.concat(payload.shoppingList) : payload.shoppingList
      console.log('shoppingList length:', newList.length)
      return {
        ...state,
        curPageNo: payload.pageNo,
        shoppingList: newList,
        curPageGoodsList: payload.shoppingList
      }
    },
    updateTaobaoGoodsList(state: ShopState, payload: { goodsList: any[], goodsType: shopType, pageNo: number }) {
      const { goodsList, goodsType, pageNo } = payload;
      const goodsListType = goodsType === 'low_price' ? 'low_priceGoodsList' : (goodsType === 'hot_sell' ? 'hot_sellGoodsList' : 'you_likeGoodsList')
      const newList = state[goodsListType].goodsList.concat(goodsList)
      return {
        ...state,
        [goodsListType]: {
          goodsList: newList,
          pageNo,
          isDone: goodsList.length === 0,
          isEmpty: newList.length === 0,
        },
      }
    },
  },
  effects: dispatch => ({
    async queryShoppingList(params: { pageNo: number }) {
      const pageNo = params?.pageNo || 1;
      const { kps } = store.getState().user
      const queryMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_ALIMAMA_GOODS_LIST);
      try {
        // test host: 'https://yyact.uc.cn'
        const queryRes = await network.get(`${config.shopHost}/uclite/shop/queryShoppingList`, {
          kps,
          appId: config.appId,
          pageSize: 30,
          pageNo
        });
        const shoppingList: IGoodsInfo[] = queryRes.items;
        if (shoppingList?.length) {
          queryMonitor.success({
            msg: '查询成功',
            c1: '' + pageNo,
            c2: '' + shoppingList.length,
          })
          if (pageNo < 3) {
            dispatch.shop.updateShoppingList({
              shoppingList: shoppingList,
              pageNo
            });
          } else {
            let imagesLoadPromise = [];
            shoppingList.forEach((item, index) => {
              imagesLoadPromise[index] = new Promise((resolve, reject) => {
                let img = new Image();
                img.onload = function () {
                  resolve(index)
                }
                img.src = item.tbGoodLink
              })
            })
            return Promise.all(imagesLoadPromise).then((result) => {
              console.log(`imagesLoadPromise 第${pageNo}页图片加载完成`, new Date().getTime())
              dispatch.shop.updateShoppingList({
                shoppingList: shoppingList,
                pageNo
              });
            })
          }
        } else {
          queryMonitor.fail({
            msg: '查询失败-无数据',
            c1: '' + pageNo,
          })
        }
        return shoppingList
      } catch (e) {
        queryMonitor.fail({
          msg: '查询失败-接口异常',
          c1: '' + pageNo,
          bl1: JSON.stringify(getErrorInfo(e))
        })
        return []
      }
    },
    async goodsExpost(goodsInfo) {
      try {
        const img = new Image()
        img.src = goodsInfo.ifs
      } catch (e) {
      }
    },
    async queryFeatureShopList(params: { pageNo: number, shopType: shopType }) {
      const pageNo = params?.pageNo || 1;
      const shopType = params?.shopType || 'low_price';
      const queryMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_TAOBAO_GOODS_LIST);
      const requestUrl = `${config.shopHost}/uclite/shop/queryFeatureShopList`;
      try {
        const queryRes = await network.get(requestUrl, {
          appId: config.appId,
          pageSize: 100,
          pageNo,
          shopType
        }).then(res => {
          res.items = res.items?.map((item) => {
            const url = item.pictUrl.startsWith('http') ? item.pictUrl.replace('http://', 'https://') : 'https:' + item.pictUrl;
            return {
              ...item,
              pictUrl: url + '_460x460q90.jpg'
            }
          })
          return res
        })
        if (queryRes?.items.length) {
          queryMonitor.success({
            msg: '查询成功',
            c1: '' + pageNo,
            c2: '' + queryRes?.items.length,
            c3: shopType,
          })
          // const imagesLoadPromise = queryRes.items.map((item, index) => {
          //   return new Promise((resolve, reject) => {
          //     let img = new Image();
          //     img.onload = function () {
          //       resolve(index)
          //     }
          //     img.src = item.pictUrl
          //   })
          // })
          // const p = Promise.allSettled(imagesLoadPromise).then(() => {
          //   dispatch.shop.updateTaobaoGoodsList({ goodsList: queryRes.items, goodsType: shopType, pageNo: pageNo })
          //   return {length: queryRes.items.length}
          // })
          // return p
        } else {
          queryMonitor.fail({
            msg: '查询失败-无数据',
            c1: '' + pageNo,
            c2: '0',
            c3: shopType,
          })
          // dispatch.shop.updateTaobaoGoodsList({ goodsList: [], goodsType: shopType, pageNo: pageNo })
        }
        return { goodsList: queryRes?.items, goodsType: shopType, pageNo }
      } catch (error) {
        queryMonitor.fail({
          msg: '查询失败-接口异常',
          c1: '' + pageNo,
          c3: shopType,
          bl1: JSON.stringify(getErrorInfo(error))
        })
        return { goodsList: [], goodsType: shopType, pageNo }
      }
    },
    async addShopList(shopType: shopType) {
      const goodsListType = shopType === 'low_price' ? 'low_priceGoodsList' : (shopType === 'hot_sell' ? 'hot_sellGoodsList' : 'you_likeGoodsList')
      const pageNo = store.getState().shop[goodsListType].pageNo;
      const response: shopListResponse = await dispatch.shop.queryFeatureShopList({ pageNo: pageNo + 1, shopType: shopType })
      return response
    },
    async preLoadingImage({ goodsList, goodsType, pageNo }: shopListResponse) {
      const proloadIamgeHandler = new PreLoadingImage((listChunk) => {
        dispatch.shop.updateTaobaoGoodsList({ goodsList: listChunk, goodsType, pageNo })
      })
      return proloadIamgeHandler.runPreload(goodsList)
    }
  })
})

export default shop;
