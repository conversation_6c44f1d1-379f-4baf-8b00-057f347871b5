import { ItaobaoGood } from ".";

class PreloadingImage {
  public preloadImages: ItaobaoGood[];
  public preloadTick: Function;
  constructor(callBack: Function) {
    this.preloadTick = callBack;
  }
  runPreload(images: ItaobaoGood[]) {
    this.preloadImages = images;
    const length = this.preloadImages.length;
    let current = 0;
    if (!this.preloadTick) {
      new Error('setup preloadTick')
    }
    return new Promise((resolve, reject) => {
      this.loadImages(length, current)
        .then((res) => {
          resolve(res)
        })
    })
  }
  loadImages(length: number, current: number) {
    const loadImages = this.preloadImages.slice(current, current + 20)
    const imagesPromise = loadImages.map(image => this.loadImage(image.pictUrl));
    return Promise.allSettled(imagesPromise)
      .then((loadingImages) => {
        if (current < length) {
          current += loadingImages.length
          this.preloadTick(loadImages)
          return this.loadImages(length, current)
        } else {
          return this.preloadImages
        }
      })
  }
  loadImage(url: string) {
    return new Promise((resolve, reject) => {
      let img = new Image();
      img.onload = function () {
        resolve(img)
      }
      img.src = url
    })
  }
}

export default PreloadingImage