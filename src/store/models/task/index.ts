import Toast from '@/lib/universal-toast/component/toast';
import {store} from '@/store';
import logoutCheck from '@/utils/logoutCheck';
import tracker from '@/lib/tracker';
import {createModel} from '@rematch/core';
import {
  checkAppDownload,
  checkNeedRecommendTaskEvent,
  completeTask,
  dealWithAppDownLoadTask,
  defaultCallAppEventList,
  excludeFirstScreenEventTypes,
  geneSecondTokenParams,
  geneTaskRequestId,
  getHighValueTaskList,
  getInviteCode,
  getTaskInfo,
  handleBatchAward,
  handleInsertSignToTaskList,
  handleSigninTask,
  hiddenTimePeriodTask,
  queryTask,
  receiveTask,
  searchWordTaskProgressAddOne
} from './helper';
import {getMainVersion, queryTaskSwitchStatus} from '@/http';
import {
  DEFAULT_MODAL_EVENT,
  DESKTOP_TASK_ENTRY_LIST,
  DESKTOP_TASK_EVENT,
  HAS_RECEVICE_TASK_STATUS,
  INoticeByTokenParam,
  ITaskDesc,
  IWord,
  QueryADRewardsRes,
  QueryTaskRespData,
  ReqBatchRewardTask,
  ReqCompleteTask,
  ResCompleteTask,
  RewardItem,
  SignInfo,
  TASK_EVENT_TYPE,
  TASK_STATUS,
  TaskInfo
} from './types';
import {taskFilter} from './taskCheck';
import ucapi from "@/utils/ucapi";
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category'
import {ISlotAd} from "@/utils/huichuan";
import {TaoBaoRtaInfo} from '@/utils/rta';
import {generateLists, getErrorInfo, getFinishTaskInfo, getTotalRewardAmount, handleTaskCompleteNewMonitor} from './task_util';
import network from "@ali/weex-toolkit/lib/network";
import config from "@/config";
import storage from '@ali/weex-toolkit/lib/storage';
import baseModal from '@/components/modals/modal';
import modal from '@/components/modals/modal';
import event from '@/utils/event';
import {
  STORAGE_DEFAULT_BROWSER_CLICKED_KEY,
  STORAGE_PUSH_SWITCH_CLICKED_KEY,
  STORAGE_SIGN_MINDER_TASK_SET_CALENDAR_KEY,
  STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY,
} from '@/constants/storage';
import {hadSignCalendarReminder, queryCalendarPermission, queryCalendarReminders} from '@/utils/calendar_helper';
import {
  checkInstallApp,
  checkMultipleInstallApp,
  checkTaskFinished,
  findDongfengTask,
  getExtraInfo,
  getTaskAppInstallMap,
  isClickTypeSearchWordsTask,
  isIncentiveAdTask,
  isSearchWordsTask
} from "@/pages/index/task/help";
import {appVersion, bizVersion, isIOS} from "@/lib/universal-ua";
import {getGlobalFirstData, isStreamMode} from '@/lib/render-utils/csr';
import {AppState, TaobaoRtaConfig} from '../app';
import stat from '@/lib/stat';
import qs, { EvSubType, getEvSub } from '@/lib/qs';
import {MODAL_ID} from '@/components/modals';
import {getIncentiveAdSlotData, isOpenQueryAward, notifyAdAwardSuccess} from '@/lib/utils/incentive_ad_help';
import {IRewardInfoItem} from '@/pages/index/task/ad_video_browse';
import fact from '@/lib/fact';
import {setHighDialogCookie} from '../highValueTask/utils';
import {factToClickAndExposure} from './taskMonitor';
import {refreshUserGiftState} from '../rightsGift';
import adPlayerCache from '@/pages/index/task/adPlayerCache';
import {TanxRewardType} from "@/pages/index/task/typings";
import { skipCompleteOrderTask } from "@/pages/index/task/util";
import { getAppInstallNotInstallMapByLocalStorage, getAppInstallWithExpiryMinutes } from '@/lib/utils/app_install';

// const trackerAjax = tracker.Monitor(100);

export interface TaskState {
  /** 签到列表 */
  signin: SignInfo[];
  newNewSignList: TaskInfo[]; // 签到任务
  resTaskList: TaskInfo[];
  /** 任务列表 */
  taskList: TaskInfo[];
  // 二方任务
  callAppTaskList: TaskInfo[];
  miniGameTaskList: TaskInfo[];
  inviteAddAmount: number; // 邀请奖励
  prtTaskList: TaskInfo[]; // 时长任务区域的任务
  taskListFromServer: TaskInfo[],
  storeParentTask: TaskInfo | null;
  storeTaskList: TaskInfo[];
  doubleTask: TaskInfo | null;
  fukaSignTask?: TaskInfo | null;
  /** 服务器时间 */
  now: number;
  /** 汇率比例 300 表示 30000元宝 = 1元 */
  rate: number;
  preTaskState: number; // 前置任务状态 -1无前置任务，0 进行中  1 已完成
  preTaskLeftTime: number; // 前置任务剩余时间

  welfareBallSettingEnable: boolean; // 信息流任务球是否可见
  isOldVersion: boolean; // 小于 13.5.8.1120 版本为老版本

  searchWordsCache: IWord[];  // 搜索词缓存
  searchWordsCacheFlag: boolean; // true：重新请求列表时不更新搜索词

  clickTypeSearchWordsCache: IWord[]; // 搜索词缓存
  clickTypeSearchWordsCacheFlag: boolean;

  // 百度搜索词缓存
  baiduSearchWordsCache: IWord[];
  baiduSearchWordFlag: boolean;


  searchWordCompleted: {  // 记录完成的搜索词名称与奖励
    taskId: number,
    event: TASK_EVENT_TYPE
    name: string,
    prizes: [
      {
        rewardItem: RewardItem,
      }
    ],
  },
  isShowTaskUncompletedToast: {
    flag: boolean,
    event: string,
  }, // 搜索任务未完成时，返回页面出toast提示 >=13.7.7
  taskDescList: ITaskDesc[];
  // 快手换端任务开关
  displayCallAppTask: boolean;
  // 显示在顶部签到区的任务
  taskDisplayOnTop: TaskInfo | null,
  workerRegisterTaskIds: string[];
  // 显示在任务列表最上面的任务
  toppingTask: TaskInfo | null;
  dealWithUninstallTaskList: {
    id: number,
    name: string,
    /** 是否展示在任务列表 */
    display: boolean,
    /** 是否展示在套娃任务 */
    recommendDisplay: boolean,
  }[];
  /** 东风监测首次曝光任务 */
  dongfengExposeTaskMap: Map<string, number>;
  /** 过滤了未安装换量任务 */
  recommendTaskList: TaskInfo[];
  /** 预加载成功的激励广告任务列表 */
  adTaskPreloadSuccessList: TaskInfo[];
  /** 预加载的广告任务map */
  preloadAdTaskMap: Map<string, number>;
  /** 端缓存的汇川广告数据 */
  adStoreDataList: {
    accountId: string;
    taskId: number;
  }[]
}
// 上次调用异步查奖的时间
let lastCallQueryAndGetAwardTime = 0;

const State: TaskState = {
  signin: [],
  newNewSignList: [],
  resTaskList: [],
  taskList: [],
  taskListFromServer: [],
  storeTaskList: [],
  miniGameTaskList: [],
  prtTaskList: [],
  callAppTaskList: [],
  inviteAddAmount: 0,
  doubleTask: null,
  fukaSignTask: null,
  storeParentTask: null,
  now: Date.now(),
  rate: 300,
  preTaskState: -1,
  preTaskLeftTime: 0,
  welfareBallSettingEnable: true, // 信息流任务球是否可见
  isOldVersion: false,
  // 搜索词任务相关
  searchWordsCache: [],
  searchWordsCacheFlag: false,
  clickTypeSearchWordsCache: [],
  clickTypeSearchWordsCacheFlag: false,
  // 百度搜索词任务相关
  baiduSearchWordsCache: [],
  baiduSearchWordFlag: false,
  searchWordCompleted: {
    taskId: 0,
    event: TASK_EVENT_TYPE.UCLITE_READ_ONCE,
    name: '',
    prizes: [
      {
        rewardItem: {
          randomAmount: false,
          amount: 0,
          icon: "",
          mark: "coin",
          name: "元宝",
        }
      }
    ],
  },
  isShowTaskUncompletedToast: {
    flag: false,
    event: '',
  },
  taskDescList: [],
  displayCallAppTask: true,
  taskDisplayOnTop: null,
  toppingTask: null,
  workerRegisterTaskIds: [],
  dealWithUninstallTaskList: [],
  dongfengExposeTaskMap: new Map(),
  recommendTaskList: [],
  adTaskPreloadSuccessList: [],
  preloadAdTaskMap: new Map(),
  adStoreDataList: []
};

const Task = createModel()({
  state: State,
  reducers: {
    setTaskList: (state: TaskState, payload: {
      resList: TaskInfo[],
      extra: {
        taskBrandAd: ISlotAd,
        taskCorpAd: { [k: string]: ISlotAd },
        taobaoRtaInfo?: TaoBaoRtaInfo | null,
      },
      taobaoRtaConfig: TaobaoRtaConfig | null,
      clientType: AppState['clientType'],
      needHideHighValueTask: string[],
      callAppModuleEvents: TASK_EVENT_TYPE[],
      adStoreDataList: TaskState['adStoreDataList']
    }): TaskState => {
      const dealTaskList = handleInsertSignToTaskList(payload.resList, state);

      const callAppEventList = payload.callAppModuleEvents?.length ? payload.callAppModuleEvents : defaultCallAppEventList;

      const result = generateLists(dealTaskList, payload.extra, payload.taobaoRtaConfig, payload.clientType, payload.needHideHighValueTask, callAppEventList, payload.adStoreDataList);
      return {
        ...state,
        ...result,
        // ...payload
      };
    },
    updateState(state: TaskState, payload: Partial<TaskState>): TaskState {
      return {
        ...state,
        ...payload
      }
    },
    updateNow(state: TaskState, timestamp: number) {
      if (!timestamp) return state;
      return { ...state, now: timestamp };
    },
    updateSignList(state: TaskState, taskList: TaskInfo[]) {
      const { now } = state;
      const signList = handleSigninTask(taskList, now);
      return { ...state, signin: signList };
    },
    updateNewSignList(state: TaskState, taskList: TaskInfo[]) {
      return { ...state, newNewSignList: taskList };
    },
    setShowTaskUncompletedToast(state: TaskState, isShowTaskUncompletedToast: {flag: boolean, event: string}) {
      return {
        ...state,
        isShowTaskUncompletedToast,
      }
    },
    updateTaskList(state: TaskState, taskList) {
      return {
        ...state,
        taskList
      }
    }
  },
  effects: dispatch => ({
    async generateTaskList(_, rootState) {
      const { taskBrandAd, taskCorpAd } = rootState.ad;
      const { taobaoRtaInfo} = rootState.rta;
      const { taskListFromServer, adStoreDataList } = rootState.task;
      const { taobaoRtaConfig } = rootState.app;
      const { clientType, callAppModuleEvents } = rootState.app;
      const callAppEventList = callAppModuleEvents?.length ? callAppModuleEvents : defaultCallAppEventList;
      const needHideHighValueTask = getHighValueTaskList({
        resourceTaskList: rootState.highValueTask.resourceTaskList || [],
        hiddenTaskIdList: rootState.highValueTask.hiddenTaskIdList || [],
        currentTaskInfo: rootState.highValueTask.currentTaskInfo
      });
      const result = generateLists(taskListFromServer, { taskBrandAd, taskCorpAd, taobaoRtaInfo }, taobaoRtaConfig, clientType, needHideHighValueTask, callAppEventList, adStoreDataList);
      dispatch.task.updateState({
        ...result
      });
    },
    async updateHuichuanTaskList(payload: {
      taskBrandAd: ISlotAd,
      taskCorpAd: { [k: string]: ISlotAd }
    }, rootState) {
      const { taskBrandAd, taskCorpAd } = payload;
      const { taobaoRtaInfo} = rootState.rta;
      const { taskListFromServer, adStoreDataList } = rootState.task;
      const { clientType, callAppModuleEvents } =rootState.app;
      const callAppEventList = callAppModuleEvents?.length ? callAppModuleEvents : defaultCallAppEventList;
      const needHideHighValueTask = getHighValueTaskList({
        resourceTaskList: rootState.highValueTask.resourceTaskList || [],
        hiddenTaskIdList: rootState.highValueTask.hiddenTaskIdList || [],
        currentTaskInfo: rootState.highValueTask.currentTaskInfo
      });
      const result = generateLists(taskListFromServer, { taskBrandAd, taskCorpAd, taobaoRtaInfo }, rootState.app.taobaoRtaConfig, clientType, needHideHighValueTask, callAppEventList, adStoreDataList);
      dispatch.task.updateState({
        ...result
      });
    },
    async updateRTATaskList(payload: {
      taobaoRtaInfo: TaoBaoRtaInfo | null
    }, rootState) {
      const { taobaoRtaInfo } = payload;
      const { taskBrandAd, taskCorpAd} =rootState.ad;
      const { taskListFromServer, adStoreDataList } = rootState.task;
      const { clientType, callAppModuleEvents } =rootState.app;
      const callAppEventList = callAppModuleEvents?.length ? callAppModuleEvents : defaultCallAppEventList;
      const needHideHighValueTask = getHighValueTaskList({
        resourceTaskList: rootState.highValueTask.resourceTaskList || [],
        hiddenTaskIdList: rootState.highValueTask.hiddenTaskIdList || [],
        currentTaskInfo: rootState.highValueTask.currentTaskInfo
      });
      const result = generateLists(taskListFromServer, { taskBrandAd, taskCorpAd, taobaoRtaInfo }, rootState.app.taobaoRtaConfig, clientType, needHideHighValueTask, callAppEventList, adStoreDataList);
      dispatch.task.updateState({
        ...result
      });
    },
    async queryTask(firstInit, rootState) {
      const kps = store.getState().user.kps;
      const { taobaoRtaConfig, clientType } = store.getState().app;
      const taskQueryMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_QUERY, {sampleRate: 1});
      const doQueryTask = async (kps) => {
        return await queryTask({ kps });
      }
      try {
        let queryTaskRes: QueryTaskRespData;
        if (firstInit &&
          (
            (getGlobalFirstData()?.queryTaskRes && (getGlobalFirstData()?.userInfo?.kps_wg || window.__CSRFirstDataPms__ || !kps || isStreamMode()))
            ||  (!getGlobalFirstData()?.queryTaskRes && window.__CSRFirstDataPms__)
          )
        ) {
          console.log('[store/models/task] queryTask - 初始化, 首屏数据请求已发出，不重复请求');
          let firstData = getGlobalFirstData();
          queryTaskRes = firstData?.queryTaskRes;
          if (!queryTaskRes) {
            console.log('[store/models/task] queryTask - getFirstData 可能还没回来，等一下');
            firstData = await window.__CSRFirstDataPms__;
            if (!firstData) {
              console.log('[store/models/task] queryTask 首屏数据请求出错，重试');
              queryTaskRes = await doQueryTask(kps);
            } else {
              queryTaskRes = firstData?.queryTaskRes;
            }
          }
        } else {
          // console.log('[store/models/task] 发起 queryTask ', firstInit, getGlobalFirstData()?.queryTaskRes, window.__CSRFirstDataPms__);
          queryTaskRes = await queryTask({ kps });
        }
        let taskList: TaskInfo[] = [];
        let signList: TaskInfo[] = [];

        let { values = [], timestamp, rate = 300, inviteAddAmount, frontData, areaInfo } = queryTaskRes || {};

        // 第一次进入且重试失败后，任务列表数组为空，默认进入刷新
        if(firstInit && !values.length){
          event.emit('pageIndexDataInit', {
            isRetrySuccess: false
          });
          tracker.log({
            category: WPK_CATEGORY_MAP.GET_FIRST_DATA,
            msg: '进入重试页面',
            sampleRate: 1,
          });
        }

        if (firstInit) {
          if (frontData) {
            if (clientType === 'UCMobile') {
              frontData.taobaoRtaConfig = frontData.ucTaobaoRtaConfig;
            }
            // 更新diamond配置数据
           dispatch.app.updateState({...frontData});
          } else {
            const fromFirstScreen = '' + !getGlobalFirstData()?.queryTaskRes;
            taskQueryMonitor.fail({
              msg: '接口成功-数据异常-diamond',
              c2: '' + (inviteAddAmount || 0),
              c3: bizVersion,
              c6: fromFirstScreen,
              bl1: JSON.stringify(queryTaskRes)
            });
          }

          // 换量重定向处理(主端才需要, 极速版不需要重定向)
          if (areaInfo && frontData?.exchangeRedirectConfig?.filterEntry?.length && clientType=== 'UCMobile') {
            dispatch.redirect.handleTaskRedirectEvent({
              areaInfo: areaInfo,
              exchangeRedirectConfig: frontData?.exchangeRedirectConfig,
            })
          }
        }

        if (values?.length) {
          taskQueryMonitor.success({
            msg: '接口成功',
            c1: '' + values.length,
            c2: '' + (inviteAddAmount || 0),
            c3: bizVersion,
            bl1: JSON.stringify(values),
            bl2: JSON.stringify(frontData),
          });
          values = taskFilter(values)
        } else {
          taskQueryMonitor.fail({
            msg: '接口成功-数据异常',
            c2: '' + (inviteAddAmount || 0),
            c3: bizVersion,
            bl1: JSON.stringify(queryTaskRes)
          });
        }
        let isOldSign = false;
        const oldSignList: TaskInfo[] = [];
        const newSignList: TaskInfo[] = [];
        let searchWords: IWord[] = [];
        let clickTypeSearchWords: IWord[] = [];
        let baiduSearchWords: IWord[] = [];
        values.forEach(task => {
          // 接口是否能确保返回签到数据是正确的？
          if (task.event === TASK_EVENT_TYPE.UCLITE_SIGN) {
            newSignList.push(task);
          } else if (task.event === TASK_EVENT_TYPE.UCLITE_SIGN_NM) {
            isOldSign = true;
            oldSignList.push(task);
          } else {
            taskList.push(task);
            if (isSearchWordsTask(task.event)) {
              if (isClickTypeSearchWordsTask(task.event)) {
                clickTypeSearchWords = task?.ext?.words || [];
              } else if (task.event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
                baiduSearchWords = task?.ext?.words || []
              } else {
                searchWords = task?.ext?.words || [];
              }
            }
          }
        });
        signList = isOldSign ? oldSignList : newSignList;
        // console.log('query task signList:', values)

        dispatch.task.updateNow(timestamp);

        dispatch.task.updateState({ rate, inviteAddAmount, resTaskList: values });
        const { searchWordsCacheFlag, clickTypeSearchWordsCacheFlag, baiduSearchWordFlag } = store.getState().task
        if (!searchWordsCacheFlag) {
          dispatch.task.updateState({
            searchWordsCache: searchWords,
            searchWordsCacheFlag: true,
          });
        }
        if (!clickTypeSearchWordsCacheFlag) {
          dispatch.task.updateState({
            clickTypeSearchWordsCache: clickTypeSearchWords,
            clickTypeSearchWordsCacheFlag: true,
          });
        }
        if (!baiduSearchWordFlag) {
          dispatch.task.updateState({
            baiduSearchWordsCache: baiduSearchWords,
            baiduSearchWordFlag: true,
          });
        }

        dispatch.task.updateSignList(signList);
        dispatch.task.updateNewSignList(signList);
        // 涉及到同步问题，只能放到这里先处理
        for (let i = 0; i < taskList.length; i++) {
          if (taskList[i].event === TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH && taskList[i].state === TASK_STATUS.TASK_DOING) {
            // 有任务返回，且对应任务进行中
            try {
              let data;
              if(rootState?.app?.pushStateResponse){
                data = rootState?.app?.pushStateResponse;
              } else  {
                data = await ucapi.biz.getPushState();
              }
              const clicked = await storage.get(STORAGE_PUSH_SWITCH_CLICKED_KEY);
              if (data?.state == '1') { // 为了兼容双端差异（ios为number），改为 ==
                if (clicked) {
                  taskList[i].state = 1;
                } else {
                  taskList[i].toDelete = true;
                }
              }
              if (data.errCode) {
                taskList[i].toDelete = true;
              }
            } catch (e) {
              console.log(e);
              taskList[i].toDelete = true;
            }
          }
          if (taskList[i].event === TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER && taskList[i].state === TASK_STATUS.TASK_DOING) {
            // 有任务返回，且对应任务进行中
            try {
              let data;
              if(rootState?.app?.defaultBrowserResponse){
                data = rootState.app.defaultBrowserResponse;
              }else {
                data =  await ucapi.biz.getDefaultBrowser();
              }
              console.log('读取是否设置默认浏览器', data);
              const clicked = await storage.get(STORAGE_DEFAULT_BROWSER_CLICKED_KEY);
              if (data?.state == '1') {
                if (clicked) {
                  taskList[i].state = 1;
                } else {
                  taskList[i].toDelete = true;
                }
              }
              if (data?.enable_start_default_setting === '0') {
                taskList[i].toDelete = true;
              }
              if (data.errCode) {
                taskList[i].toDelete = true;
              }
            } catch (e) {
              console.log(e);
              console.log('不支持设置默认浏览器');
              dispatch.task.updateState({
                isOldVersion: true,
              });
              taskList[i].toDelete = true;
            }
          }
          if (taskList[i].event === TASK_EVENT_TYPE.UCLITE_SIGN_MINDER && taskList[i].state === TASK_STATUS.TASK_DOING) {
            // 有任务返回，且对应任务进行中
            // const switchSetStatus = await storage.get(STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY);
            // const taskSetStatus = await storage.get(STORAGE_SIGN_MINDER_TASK_SET_CALENDAR_KEY);
            // const dataFromDisk = await getCalendarReminderStoreData();
            // if (switchSetStatus || taskSetStatus || dataFromDisk?.data?.setCalendarReminder) {
            try {
              const checkCalendarPermission = Date.now();
              const permission = await queryCalendarPermission();
              console.log('permission', permission);
              if (permission?.hasReadPermission === '1') {
                try {
                  const data = await queryCalendarReminders();
                  console.log('查询日历', data)
                  const clicked = await storage.get(STORAGE_SIGN_MINDER_TASK_SET_CALENDAR_KEY);
                  const clicked2 = await storage.get(STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY);
                  if (!data.errCode && data?.events?.length > 0 && hadSignCalendarReminder(data.events)) {
                    if (clicked || clicked2) {
                      taskList[i].state = 1;
                    } else {
                      taskList[i].toDelete = true;
                    }
                  }
                  tracker.log({
                    category: 177,
                    msg: '读取日历相关耗时',
                    wl_avgv1: Date.now() - checkCalendarPermission
                   });
                } catch (e) {
                  console.log(e);
                  console.log('不支持查询日历');
                  taskList[i].toDelete = true;
                }
              }
              // {errCode: 2, ext: 'INVALID_METHOD'}
              if (permission.errCode === 2) {
                console.log('不支持查询日历权限api');
                taskList[i].toDelete = true;
              }
            } catch (e) {
              console.log(e);
              console.log('不支持查询日历权限api');
              taskList[i].toDelete = true;
            }
          }
        }

        getInviteCode(taskList);
        const needHideHighValueTask = getHighValueTaskList({
          resourceTaskList: rootState.highValueTask.resourceTaskList || [],
          hiddenTaskIdList: rootState.highValueTask.hiddenTaskIdList || [],
          currentTaskInfo: rootState.highValueTask.currentTaskInfo,
          needCurrentTask: true
        });

        // 批量领取奖励
        await handleBatchAward(taskList, needHideHighValueTask);

        // 处理下载任务,并隐藏
        taskList = await dealWithAppDownLoadTask(taskList, firstInit);

        const callAppEventList = rootState.app.callAppModuleEvents?.length ? rootState.app.callAppModuleEvents : defaultCallAppEventList;
        // 任务定时上下线隐藏
        taskList = hiddenTimePeriodTask(taskList, timestamp)

        dispatch.task.setTaskList({
          resList: taskList || [],
          extra: {
            taskBrandAd: rootState.ad.taskBrandAd as ISlotAd,
            taskCorpAd: rootState.ad.taskCorpAd,
            taobaoRtaInfo: rootState.rta.taobaoRtaInfo,
          },
          taobaoRtaConfig,
          clientType: rootState.app.clientType,
          callAppModuleEvents: callAppEventList,
          needHideHighValueTask,
          adStoreDataList: store.getState().task.adStoreDataList
        });

        await dispatch.task.dealwidthUnistallAppTask({taskList, type: 'list', firstInit})
        dispatch.task.dealwithQueryAndGetAward(firstInit)
        return queryTaskRes;
      } catch (error) {
        // console.error('[store/models/task] 查询任务失败',error);
        const msg = (error && error.message) || JSON.stringify(error);
        taskQueryMonitor.fail({
          msg: '接口失败',
          c3: bizVersion,
          c4: getErrorInfo(error)?.errCode,
          c5: getErrorInfo(error)?.msg,
          bl1: msg
        });
        return null;
      }
    },
    async queryDesc(_, rootState) {
      const { kps } = rootState.user;
      const queryDescMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_TASK_DESC);
      try {
        const queryDescRes = await network.get(`${config.coralHost}/uclite/queryDesc`, {
          appId: config.appId,
          kps,
          event: `${TASK_EVENT_TYPE.UCLITE_INVITE_TASK},${TASK_EVENT_TYPE.STORE_READ_TIME}`,
          entry: qs.getParam('entry') || ''
        })
        if (queryDescRes?.length) {
          dispatch.task.updateState({
            taskDescList: queryDescRes
          })
          queryDescMonitor.success({
            msg: '请求成功',
            bl1: JSON.stringify(queryDescRes)
          })
        } else {
          queryDescMonitor.fail({
            msg: '请求失败-无数据',
            bl1: JSON.stringify(queryDescRes)
          })
        }
      } catch (err) {
        queryDescMonitor.fail({
          msg: '请求失败',
          c1: getErrorInfo(err).errCode,
          bl1: JSON.stringify(getErrorInfo(err))
        })
      }
    },
    async signIn(taskInfo: TaskInfo, roootState) {
      const { id, state } = taskInfo;
      const { kps } = roootState.user;
      if (logoutCheck()){
        return;
      }
      const signMonitor = tracker.Monitor(WPK_CATEGORY_MAP.SIGN_IN);
      const finishType = state === TASK_STATUS.TASK_COMPLETED ? 'award' : 'complete';
      try {
        const res = await completeTask({ type: finishType, id, kps });
        // trackerAjax.success({ msg: '成功', c1: '完成签到' });
        signMonitor.success({ msg: '签到成功', c1: `${id}`, c2: finishType, bl1: JSON.stringify(res.prizes || {}) });
        dispatch.app.updateTaskAndCurrency();
        const { prizes } = res;
        return prizes;
      } catch (error) {
        Toast.show('网络异常，请重新试一试~');
        signMonitor.fail({ msg: '签到失败', c1: `${id}`, c2: finishType,  bl1: JSON.stringify(error) });
        return false;
      }
    },
    async complete({ id, type, useUtCompleteTask, publishId,  params }: Omit<ReqCompleteTask, 'kps'>, roootState) {
      const highValueTask = store.getState().highValueTask;
      const { needFinishShowRewardTaskList = [] } = highValueTask;
      let toast = params?.toast === undefined || params?.toast === true;
      // 高价值任务不需要展示toast
      if(this.currentTaskIsHighTask(id)){
        toast = false;
      }
      const { kps } = roootState.user;

      // 高价值任务完成不需要限制登录，领奖需要限制登录
      if (type === 'award' && logoutCheck()) {
        return;
      }
      const taskCompleteMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE);
      // 调整完成任务监控，版本覆盖前同时存在
      const taskCompleteNewMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE_NEW);
      try {
        const res = await completeTask({ type, id, kps, useUtCompleteTask, publishId, params });
        // 跳转任务分两步发奖: 点击时触发任务完成state=1, 返回uc时走批量领奖
        // 点击跳转任务不更新列表，避免提前触发自动领奖
        const delayUpdateListEvent = [TASK_EVENT_TYPE.APP_LINK, TASK_EVENT_TYPE.LINK, TASK_EVENT_TYPE.UCLITE_SEARCH_WORD, TASK_EVENT_TYPE.BAIDU_READ_ONCE, TASK_EVENT_TYPE.WUFU_BENEFITS_LINK, TASK_EVENT_TYPE.BRAND_TASK, TASK_EVENT_TYPE.CALL_APP_LINK, TASK_EVENT_TYPE.CALL_APP_TOKEN]
        const event = params?.task?.event;
        // 接口成功日志上报
        handleTaskCompleteNewMonitor({
          resp: res,
          task: params?.task,
          taskId: id
        });
        /**
         * 公告任务不配置跳转url的情况下, 需要点击直接触发领奖
         * 配置了跳转url的情况下, 按照跳转任务逻辑处理, 不直接触发领奖
         */
        if (event === TASK_EVENT_TYPE.FULI_ANNOUNCE && params?.task?.url) {
          delayUpdateListEvent.push(TASK_EVENT_TYPE.FULI_ANNOUNCE)
        }

        if (event?.includes(TASK_EVENT_TYPE.VIDEO_AD)) {
          await dispatch.app.updateTaskAndCurrency()
        } else if (event && !delayUpdateListEvent.includes(event) || type === 'award') {
          // 不是跳转任务类型 或者是手动领奖励的要 更新数所有数据
          dispatch.app.updateAll();
        }

        // 高价值奖励弹窗
        if (needFinishShowRewardTaskList.includes(String(id)) && res?.prizes?.length && res.prizes[0].win) {
          // 高价值弹窗
          baseModal.close(MODAL_ID.HIGH_VALUE_TASK)
          baseModal.openHighValueAward({
            // 奖励信息
            ...res.prizes[0].rewardItem,
            // 任务名称
            taskName: res?.curTask?.name || '',
            taskId: id,
            isLogin: true
          });
          setHighDialogCookie((params?.task || {}) as TaskInfo, true);
          // 任务会投放在列表一份，此刻任务列表也需要更新
          dispatch.task.queryTask(false);
        }

        const { prizes } = res;
        const validPrizes = prizes?.filter(prize => prize.win);

        if (!prizes?.length || !validPrizes?.length) {
          taskCompleteMonitor.fail({
            msg: '领奖失败-无奖品',
            c1: id + '',
            c2: params?.task?.name || '',
            c5: res.state,
            bl1: JSON.stringify(res || {})
          });
          return null
        }

        // 判断任务类型，蓄水任务需要弹窗
        let { rewardItem } = validPrizes[0];
        let coinNum = 0
        const coinRewardItem = validPrizes.filter(prize => prize.rewardItem?.icon?.includes('coin')) || []
        // 有两份元宝奖励，需要合并
        if (coinRewardItem.length > 1) {
          coinRewardItem.forEach(prizes => {
            if (prizes.rewardItem?.amount) {
              coinNum += prizes.rewardItem?.amount
            }
          })
          rewardItem = Object.assign({}, rewardItem, {
            amount: coinNum
          })
        }
        taskCompleteMonitor.success({
          msg: '领奖成功',
          c1: id + '',
          c2: params?.task?.name || '',
          c6: rewardItem?.amount || 0,
          bl1: JSON.stringify(res || {})
        });

        const rewardMark = (rewardItem?.mark || '').includes('cash') ? 'cash' : (rewardItem?.mark || '').includes('equity') ? 'equity' : 'coin';

        if (event === TASK_EVENT_TYPE.UCLITE_SEARCH_WORD) {
          searchWordTaskProgressAddOne(event);
        }
        // 小说福利场景，刷新用户权益
        if (rewardMark === 'equity') {
          await refreshUserGiftState('taskComplete')
        }
        if (!toast) {
          return prizes;
        }
        const { needRecommendTaskEvent } = store.getState().app;
        if (needRecommendTaskEvent?.includes(event)) {
          modal.openTreasure(validPrizes, rewardMark, params?.task);
        } else if (DEFAULT_MODAL_EVENT.includes(event)) {
          modal.openTreasure(validPrizes, rewardMark, params?.task, false, false);
        } else {
          Toast.show('任务已完成', {
            award: rewardItem,
          });
        }
        return prizes;
      } catch (error) {
        Toast.show('网络异常，请重新试一试~');
        taskCompleteMonitor.fail({
          msg: '领奖失败',
          c1: id + '',
          c2: params?.task?.name || '',
          c3: getErrorInfo(error).errCode,
          bl1: JSON.stringify(getErrorInfo(error))
        });
        taskCompleteNewMonitor.fail({
          msg: id,
          c1: '领奖失败-无奖品',
          c2: params?.task?.name || '',
          c3: getErrorInfo(error).errCode,
          c7: params?.task?.event || '',
          bl1: JSON.stringify(getErrorInfo(error)),
          bl2: JSON.stringify(params?.task || {})
        });
        return null;
      }
    },
    async receive({ id, publishId, useUtCompleteTask, params }: Omit<ReqCompleteTask, 'kps'| 'type'>, roootState) {
      const { kps } = roootState.user;
      // 高价值任务不需要登录,仅设备完成的任务
      // if (logoutCheck()) {
      //   return;
      // }
      const receiveMonitor = tracker.Monitor(165);
      const highValueTask = store.getState().highValueTask;
      let toastShow = true;
      if(this.currentTaskIsHighTask(id)){
        toastShow = false;
      }

      try {
        const res = await receiveTask({
          id,
          kps,
          publishId: highValueTask.currentTaskInfo?.id === id ? highValueTask.currentTaskInfo?.publishId || publishId : publishId,
          useUtCompleteTask,
          params
        });
        toastShow && Toast.show('任务已领取');
        receiveMonitor.success({
          msg: `领取任务接口成功`,
          c1: `${id}`,
          bl1: JSON.stringify(params),
          bl2: JSON.stringify(res || {})
        });

        dispatch.app.updateAll();
      } catch (error) {
        console.log(error);
        receiveMonitor.fail({
          msg: `领取任务接口异常`,
          c1: `${id}`,
          bl1: JSON.stringify(params),
          bl2: JSON.stringify(error)
        })
      }

    },
    // 完成汇川二方效果广告
    async completeCorpTask({ id, type, publishId, params }: Omit<ReqCompleteTask, 'kps'>, roootState) {
      const { kps } = roootState.user;
      if (logoutCheck()){
        return;
      }
      const taskCompleteMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE);
      // 调整完成任务监控，版本覆盖前同时存在
      const taskCompleteNewMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE_NEW);
      try {
        const res = await completeTask({ type, id, kps, publishId });
        console.log('corp task 领取奖励结果：', res);
        const { prizes = [] } = res;
        const validPrizes = prizes.filter(prize => prize.win);
        taskCompleteMonitor.success({
          msg: '成功',
          c1: id + '',
          c2: params?.task?.name || '',
          c6: validPrizes?.[0]?.rewardItem?.amount || 0,
        });        // 接口成功日志上报
        handleTaskCompleteNewMonitor({
          resp: res,
          task: params?.task,
          taskId: id
        });
        await dispatch.app.updateTaskAndCurrency();
        dispatch.ad.fetchTaskCorpAd({
          slot_id: params?.task?.slotId,
          force: true,
        });
        return prizes;
      } catch (error) {
        Toast.show('网络异常，请重新试一试~');
        taskCompleteMonitor.fail({
          msg: '领奖失败',
          c1: id + '',
          c2: params?.task?.name || '',
          c3: getErrorInfo(error).errCode,
          bl1: JSON.stringify(getErrorInfo(error))
        });
        taskCompleteNewMonitor.fail({
          msg: id,
          c1: '领奖失败',
          c2: params?.task?.name || '',
          c3: getErrorInfo(error).errCode,
          c7: params?.task?.event || '',
          bl1: JSON.stringify(getErrorInfo(error)),
          bl2: JSON.stringify(params?.task || {})
        });
        return false;
      }
    },
    // 完成汇川品牌任务
    async completeBrandTask({ id, type, publishId, params }: Omit<ReqCompleteTask, 'kps'>, roootState) {
      const { kps } = roootState.user;
      if (logoutCheck()){
        return;
      }
      const taskCompleteMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE);
      // 调整完成任务监控，版本覆盖前同时存在
      const taskCompleteNewMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE_NEW);
      try {
        const res = await completeTask({ type, id, kps, publishId });
        console.log('brand task 领取奖励结果：', res);
        const { prizes = [] } = res;
        const validPrizes = prizes.filter(prize => prize.win);
        taskCompleteMonitor.success({
          msg: '成功',
          c1: id + '',
          c2: params?.task?.name || '',
          c6: validPrizes?.[0]?.rewardItem?.amount || 0,
        });
        // 接口成功日志上报
        handleTaskCompleteNewMonitor({
          resp: res,
          task: params?.task,
          taskId: id
        });
        await dispatch.app.updateTaskAndCurrency();
        // 记录品牌广告完成记录，并重新获取新的品牌广告
        const key = `ad_brand_${params?.task?.adId}_completed`;
        console.log(`品牌广告ad_id: ${params?.task?.adId}今天已完成，setDaily`);
        storage.setDaily(key, true);
        dispatch.ad.fetchTaskBrandAd(true);
        return prizes;
      } catch (error) {
        Toast.show('网络异常，请重新试一试~');
        taskCompleteMonitor.fail({
          msg: '领奖失败',
          c1: id + '',
          c2: params?.task?.name || '',
          c3: getErrorInfo(error).errCode,
          bl1: JSON.stringify(getErrorInfo(error))
        });
        taskCompleteNewMonitor.fail({
          msg: id,
          c1: '领奖失败',
          c2: params?.task?.name || '',
          c3: getErrorInfo(error).errCode,
          c7: params?.task?.event || '',
          bl1: JSON.stringify(getErrorInfo(error)),
          bl2: JSON.stringify(params?.task || {})
        });
        return false;
      }
    },
    async batchAward({tids, publishList}: ReqBatchRewardTask, rootState) {
      const batchAwardMonitor = tracker.Monitor(WPK_CATEGORY_MAP.BATCH_AWARD);
      if (!tids || !tids.length) return
      const kps = store.getState().user.kps;
      if (logoutCheck()){
        batchAwardMonitor.fail({
          msg: '未登录-用户领奖失败',
          c1: tids,
        });
        return;
      }
      const { taskHost, appId, moduleCodeTask } = config;
      const requestId = geneTaskRequestId();
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      let ut = store.getState().user.utdId
      if (!ut) {
        const utRes = await ucapi.biz.ucparams({ params: 'ut' });
        ut = utRes.ut || '';
      }
      const signOriText = `${decodeURIComponent(ut)}${tids}${requestId}`;
      const sign = await ucapi.spam.sign({ text: signOriText, salt });
      try {
        const batchAwardRes = await network.get(`${taskHost}/task/batch/reward`, {
          appId,
          moduleCode: moduleCodeTask,
          kps,
          tids,
          requestId,
          salt,
          sign,
          fve: getMainVersion(),
          publishList: publishList ?? []
        })

        batchAwardMonitor.success({
          msg: '领取成功',
          c1: tids,
          c2: JSON.stringify(batchAwardRes.succTids)
        });
        const { prizes } = batchAwardRes;
        const batchAwardTipsMonitor = tracker.Monitor(WPK_CATEGORY_MAP.BATCH_AWARD_TIPS);
        const taskInfo = getTaskInfo(tids);
        const needRecommendTaskEvent = store.getState().app.needRecommendTaskEvent;
        const needRecommend = checkNeedRecommendTaskEvent(String(taskInfo?.id));

        const monitorInfo = {
          c1: String(needRecommend),
          c2: tids,
          bl2: JSON.stringify(prizes),
          bl1: JSON.stringify(taskInfo),
          bl3: JSON.stringify(needRecommendTaskEvent),
        }

        if (prizes?.length) {
          const validPrizes = prizes.filter(prize => prize.win);
          if (validPrizes.length) {
            if (needRecommend && taskInfo) {
              batchAwardTipsMonitor.success({
                msg: '福利弹窗展示',
                ...monitorInfo,
              })
              modal.openTreasure(validPrizes, 'treasure', taskInfo)
            } else {
              // const { rewardItem } = validPrizes[0];
              const tidsArr = tids.split(',');
              if (tidsArr.length === 1) {
                const taskList = store.getState().task.taskList;
                // uclite_search_keyword 搜索任务
                let searchTask = taskList.find(task => {
                  return `${task.id}` === tidsArr[0] && task.event === TASK_EVENT_TYPE.UCLITE_SEARCH_WORD;
                })
                if (searchTask) {
                  batchAwardTipsMonitor.success({
                    msg: '福利弹窗展示-搜索任务',
                    ...monitorInfo,
                  })
                  modal.openTreasure(validPrizes, (validPrizes[0]?.rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin', searchTask);
                } else {
                  batchAwardTipsMonitor.success({
                    msg: 'Toast展示1',
                    ...monitorInfo,
                  })
                  Toast.show('任务已完成', {
                    award: validPrizes[0]?.rewardItem,
                    more: validPrizes[1]?.rewardItem
                  })
                }
              } else {
                batchAwardTipsMonitor.success({
                  msg: 'Toast展示2',
                  ...monitorInfo,
                })
                Toast.show('任务已完成', {
                  award: validPrizes[0]?.rewardItem,
                  more: validPrizes[1]?.rewardItem
                })
              }
            }
          }
        } else {
          batchAwardTipsMonitor.fail({
            msg: '不展示弹窗和toast',
            ...monitorInfo,
          })
        }
        await dispatch.app.updateAll();
      } catch (e) {
        batchAwardMonitor.fail({
          msg: '领取失败',
          c1: tids,
          c2: '',
          c3: getErrorInfo(e).errCode,
          bl1: JSON.stringify(getErrorInfo(e))
        });
      }
    },
    // 更新搜索关键词缓存标识 (主要是SSR使用)
    async updateSearchWordsCache(values: TaskInfo[]) {
      let searchWords: IWord[] = [];
      let clickTypeSearchWords: IWord[] = [];
      let baiduSearchWords: IWord[] = [];
      let signList: TaskInfo[] = [];
      let isOldSign = false;
      const oldSignList: TaskInfo[] = [];
      const newSignList: TaskInfo[] = [];
      values.forEach(task => {
        if (task.event === TASK_EVENT_TYPE.UCLITE_SIGN) {
          newSignList.push(task);
        } else if (task.event === TASK_EVENT_TYPE.UCLITE_SIGN_NM) {
          isOldSign = true;
          oldSignList.push(task);
        } else {
          if (isSearchWordsTask(task.event)) {
            if (isClickTypeSearchWordsTask(task.event)) {
              clickTypeSearchWords = task?.ext?.words || [];
            } else if (task.event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
              baiduSearchWords = task?.ext?.words || []
            } else {
              searchWords = task?.ext?.words || [];
            }
          }
        }
      });
      signList = isOldSign ? oldSignList : newSignList;
      dispatch.task.updateSignList(signList);
      dispatch.task.updateNewSignList(signList);
      const { searchWordsCacheFlag, clickTypeSearchWordsCacheFlag, baiduSearchWordFlag } = store.getState().task
      if (!searchWordsCacheFlag) {
        dispatch.task.updateState({
          searchWordsCache: searchWords,
          searchWordsCacheFlag: true,
        });
      }
      if (!clickTypeSearchWordsCacheFlag) {
        dispatch.task.updateState({
          clickTypeSearchWordsCache: clickTypeSearchWords,
          clickTypeSearchWordsCacheFlag: true,
        });
      }
      if (!baiduSearchWordFlag) {
        dispatch.task.updateState({
          baiduSearchWordsCache: baiduSearchWords,
          baiduSearchWordFlag: true,
        });
      }
    },
    // 重置搜索关键词缓存标识
    async resetSearchWordsCache(event: TASK_EVENT_TYPE) {
      if (isClickTypeSearchWordsTask(event)) {
        dispatch.task.updateState({
          clickTypeSearchWordsCacheFlag: false,
        });
        return
      }
      if (event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
        dispatch.task.updateState({
          baiduSearchWordFlag: false
        });
        return
      }
      dispatch.task.updateState({
        // searchWordsCache: [],
        searchWordsCacheFlag: false,
      });
      return
    },
    // 剔除某个搜索关键词缓存
    async deleteSearchWordsCacheOne(searchWordsTaskInfo: { event: TASK_EVENT_TYPE, name: string }) {
      const { searchWordsCache, clickTypeSearchWordsCache, baiduSearchWordsCache } = store.getState().task;
      let targetCache = isClickTypeSearchWordsTask(searchWordsTaskInfo.event) ? clickTypeSearchWordsCache : searchWordsCache
      if (searchWordsTaskInfo.event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
        targetCache = baiduSearchWordsCache
      }
      const searchWords = targetCache.filter(word => word?.name !== searchWordsTaskInfo.name);
      if (isClickTypeSearchWordsTask(searchWordsTaskInfo.event)) {
        dispatch.task.updateState({
          clickTypeSearchWordsCache: searchWords,
          clickTypeSearchWordsCacheFlag: searchWords.length !== 0,
        });
      }else if (searchWordsTaskInfo.event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
        dispatch.task.updateState({
          baiduSearchWordsCache: searchWords,
          baiduSearchWordFlag: searchWords.length !== 0
        })
      } else {
        dispatch.task.updateState({
          searchWordsCache: searchWords,
          searchWordsCacheFlag: searchWords.length !== 0,
        });
      }
    },
    async noticeByToken(p: INoticeByTokenParam, rootState) {
      const { token, from } = p;
      if (!token || !from) return;
      const { kps } = rootState?.user || {};
      const tokenTaskMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TOKEN_TASK_COMPLETE);
      const params = await geneSecondTokenParams(p);
      try {
        const res = await network.get(
          `${config.taskHost}/task/noticeByToken`,
          {
            kps,
            appId: config.appId,
            ...params,
          },
        );
        if (res) {
          tokenTaskMonitor.success({
            msg: '任务完成',
            c1: from,
            c2: token
          })
        } else {
          tokenTaskMonitor.fail({
            msg: '任务失败',
            c1: from,
            c2: token
          })
        }
      } catch (err) {
        const msg = (err && err.message) || JSON.stringify(err);
        const errCode = err.code || (err.data && err.data.code) || '';
        tokenTaskMonitor.fail({
          msg: '任务失败',
          c1: from,
          c2: token,
          c3: errCode,
          bl1: msg
        })
      }
    },
    // 唤端任务开关状态
    async queryCallAppTaskSwitchStatus(rtaType = 'kuai_shou') {
      const kps = store.getState().user.kps;
      const callAppTaskMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_CALL_APP_TASK_AWITCH);
      let idfa = '';
      try {
        if (isIOS) {
          idfa = await ucapi.biz.getIDFA();
        }
        const resTaskSwitch = await queryTaskSwitchStatus(kps, rtaType, idfa);
        console.log('resTaskSwitch====', resTaskSwitch);
        if (rtaType === 'kuai_shou') {
          dispatch.task.updateState({
            displayCallAppTask: resTaskSwitch?.display
          })
        }
        if (resTaskSwitch) {
          callAppTaskMonitor.success({
            msg: `唤端任务开关状态-查询成功-${rtaType}`,
            c1: rtaType,
            c2: idfa,
            c3: JSON.stringify(resTaskSwitch?.display),
            bl1: JSON.stringify(resTaskSwitch)
          })
        } else {
          callAppTaskMonitor.fail({
            msg: `唤端任务开关状态-查询失败-${rtaType}`,
            c1: rtaType,
            c2: idfa,
            bl1: JSON.stringify(resTaskSwitch)
          })
        }
      } catch (error) {
        const msg = (error && error.message) || JSON.stringify(error);
        callAppTaskMonitor.fail({
          msg: `唤端任务开关状态-查询失败-${rtaType}`,
          c1: rtaType,
          c2: idfa,
          bl1: msg
        })
      }
    },
    // 查询tanx视频任务是否需要发奖，需要时触发领取视频奖励
    async queryAndGetAdAward (params: {task: TaskInfo, slotKey: string, appId: string, isInit: boolean}) {
      const {task, slotKey, appId, isInit} = params
      const queryMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_JS_API);
      const queryParams = {
        slotKey,
        appId,
        requestId: `${Date.now()}`,
      }
      let hasAdAward = false;
      try {
        const res:QueryADRewardsRes = await ucapi.biz.queryRewards(queryParams)
        // console.log('[ucapi]queryRewards', res)
        const errMsg = res && (res.error || (res.ext || {}).error || res.errMsg || !!res.errCode);
        if (res && !errMsg || (res?.completeTime || res?.code?.toString() === '0')) {
          hasAdAward = true;
          const rewardInfoList: IRewardInfoItem[] = res?.reward_data?.reward_info_list || [];
          queryMonitor.success({
            msg: `${slotKey}-查询到奖励`,
            c1: `${task?.id}`,
            c2: slotKey,
            c4: queryParams?.appId,
            c5: `${task?.name}`,
            c6: rewardInfoList?.[0]?.adn_id || '',
            c7: res?.reward_data?.reward_success_id ? '1' : '0',
            c8: `${isInit}`,
            c9: res?.reward_data?.reward_type,
            bl1: JSON.stringify(res),
            bl2: JSON.stringify(queryParams),
            bl3: JSON.stringify(task),
          })
          fact.event('incentive_ad_query_award', {
            task_id: task?.id,
            task_name: task?.name,
            taskclassify: task?.taskClassify || '',
            groupcode: task?.groupCode || '',
            slot_id: rewardInfoList?.[0]?.slot_id || queryParams?.slotKey,
            adapp_id: rewardInfoList?.[0]?.app_id || queryParams?.appId,
            adn_id: rewardInfoList?.[0]?.adn_id || '',
            sid: rewardInfoList?.[0]?.sid || '',
            price: rewardInfoList?.[0]?.price || '',
            pid: rewardInfoList?.[0]?.pid || '',
            task_progress: task?.dayTimes?.progress || '',
            award_amount: task?.rewardItems?.[0]?.amount || ''
          });
          const tanxRewardTypes = [TanxRewardType.BROWSE, TanxRewardType.ORDER, TanxRewardType.BROWSE_ORDER];
          const resRewardType = res?.reward_data.reward_type;
          if (!skipCompleteOrderTask(task) && tanxRewardTypes.includes(resRewardType)) {
            // 异步查到奖励移除调起记录
            dispatch.task.completeTanxProgressiveTask({
              task,
              rewardType: resRewardType
            })
          } else {
            dispatch.task.complete({
              id: task.id,
              useUtCompleteTask: task?.useUtCompleteTask,
              publishId: task?.publishId,
              type: 'complete',
              params: { task }
            });
          }
          if (res.reward_data?.reward_success_id) {
            const reward_success_id = res.reward_data.reward_success_id;
            notifyAdAwardSuccess(task, {slotKey, rewardId: reward_success_id, requestId: `${Date.now()}`, appId }, 'queryRewards')
          }
        } else {
          queryMonitor.fail({
            msg: `${slotKey}-无奖励`,
            c1: `${task?.id}`,
            c2: slotKey,
            c3: errMsg,
            c4: queryParams?.appId,
            c5: `${task?.name}`,
            c8: `${isInit}`,
            bl1: JSON.stringify(res),
            bl2: JSON.stringify(queryParams),
            bl3: JSON.stringify(task),
          })
        }
      } catch (e) {
        const errorMsg = e?.ext?.error_msg || e?.errMsg || '';
        queryMonitor.fail({
          msg: `${slotKey}-无奖励-catch`,
          c1: `${task?.id}`,
          c2: slotKey,
          c3: errorMsg,
          c4: queryParams?.appId,
          c5: `${task?.name}`,
          c8: `${isInit}`,
          bl1: JSON.stringify(e),
          bl2: JSON.stringify(queryParams),
          bl3: JSON.stringify(task),
        })
      }
      if (!isIOS && !isInit && !hasAdAward) {
        // 未回调奖励 + 异步查询无奖励， toast提示
        Toast.show('任务未完成');
      }
      // 异步查询后移除调起记录
      adPlayerCache.removeInvokeTaskIds(task.id);
    },

    async dealwidthUnistallAppTask (params: {taskList: TaskInfo[], type: 'list' | 'resource', firstInit?: boolean}){
      const { taskList = [], type, firstInit = false } = params;
      let list: any[] = [];
      const checkStart = Date.now();

      const needCheckAppInstall = new Map();
      taskList.forEach(item => {
        const result = getTaskAppInstallMap(item);
        result && (needCheckAppInstall[result] = 1);
      });
      
      const checkAPPInstallResult = await checkMultipleInstallApp(Object.keys(needCheckAppInstall), firstInit);
      for (const item of taskList) {
        const pkg = getTaskAppInstallMap(item);
        const display = pkg ? !!checkAPPInstallResult[pkg]?.install : true;
        const { onlyCheckInstallOnPop, showUninstallApp = false } = getExtraInfo(item);
        // 当前任务在列表的显隐
        let curTaskDisplayOnList = display;
        /**
         * 列表不展示任务:
         * 1、开启了未安装检测 并且 有安装
         *
         * 列表需要展示任务:
         * 1、开启了未安装检测 并且 没有安装
         * 1、只开启了弹框类安装检测 - onlyCheckInstallOnPop
         * 2、任务有领取过 - 完成等状态
         * 3、其他情况: 根据display来决定显示隐藏
         */

        if ((showUninstallApp && !display) || onlyCheckInstallOnPop || HAS_RECEVICE_TASK_STATUS?.includes(item?.state)) {
          curTaskDisplayOnList = true
        } else if (showUninstallApp && display) {
          curTaskDisplayOnList = false
        } else {
          curTaskDisplayOnList = display
        }
        list.push({
          id: item?.id,
          name: item?.name,
          display: curTaskDisplayOnList,
          recommendDisplay: showUninstallApp ? !display : display
        });

       }
       tracker.log({
        category: 177,
        msg: 'App是否安装检测耗时',
        wl_avgv1: Date.now() - checkStart
       });

      const recommendTaskList = taskList?.filter((itemTask)=> {
        const callAppTask = list?.find((item)=> item?.id === itemTask?.id);
        const ifShow = callAppTask ? callAppTask?.recommendDisplay : true;
        return ifShow
      });

      if (type === 'list') {
        dispatch.task.updateState({
          recommendTaskList,
          dealWithUninstallTaskList: list,
        })
      } else {
        dispatch.resource.updateState({
          dealWithUninstallTaskList: list,
        })
      }

      setTimeout(()=> {
        findDongfengTask(taskList);
      }, 500)
    },
    // 判断下载类任务是否完成
    async checkAppDownloadFinish(params: {taskInfo: TaskInfo, showToast: boolean}){
      const {taskInfo, showToast = false} = params;
      const {event, state} = taskInfo;
      if (state !== TASK_STATUS.TASK_NOT_COMPLETED) {
        return false;
      }
      // 不走页面http通知，按照第三方回传通知为准
      const extraObj = getExtraInfo(taskInfo);
      if (extraObj?.isHttpFinish !== true) {
        return false;
      }
      let appIsInstalled = false;
      switch (event) {
        case TASK_EVENT_TYPE.CALL_APP_DOWNLOAD:
        case TASK_EVENT_TYPE.CORP_APP_TASK:
        case TASK_EVENT_TYPE.CORP_APP_TASK_NEW:
        case TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND:
          appIsInstalled = await checkAppDownload(taskInfo);
          break;
        case TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU:
        case TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD:
          [appIsInstalled] = await checkInstallApp('tbopen://', 'com.taobao.taobao');
          // 未安装不展示补充打点
          if (!appIsInstalled) {
            stat.custom('task_hide_installed', {
              task_id: taskInfo?.id,
              task_name: taskInfo?.name,
              taskclassify: taskInfo?.taskClassify,
              groupcode: taskInfo?.groupCode,
            })
          }
          break;
        default:
          break;
      }
        if (appIsInstalled) {
          try {
            await dispatch.task.complete({
              id: taskInfo?.id,
              type: 'complete',
              useUtCompleteTask: !!taskInfo?.useUtCompleteTask,
              publishId: taskInfo?.publishId,
              params: { task: taskInfo }
            });
            return true;
          } catch (error) {
            return false;
          }
        }
        return false;
    },
    /*
    * 异步查询视频广告奖励
    * @param isInit 是否首次打开页面，首次打开查询所有的slot
    * */
    async dealwithQueryAndGetAward(isInit: boolean){
      const currentTime = Date.now();
      const timeDiff = currentTime - lastCallQueryAndGetAwardTime;
      if (timeDiff < 2000) {
        // 间隔小于2s不执行。
        console.log('[dealwithQueryAndGetAward]执行间隔')
        return;
      }
      lastCallQueryAndGetAwardTime = currentTime;
      const list = store.getState().task?.taskList;
      const resourceList = store.getState().resource?.resourceAdTaskList;
      const invokeVideoTaskIds = adPlayerCache.getInvokeTaskIds();
      let adTaskList: TaskInfo[] = [];
      const uniqueTaskList = Array.from(new Map([...list, ...resourceList].map(task => [task.id, task])).values());
      if (isInit) {
        adTaskList = uniqueTaskList?.filter((task) => isIncentiveAdTask(task) && !checkTaskFinished(task));
      } else if (invokeVideoTaskIds?.length){
        // 非首次打开页面，只查上次调起且未收到领奖回调的任务
        adTaskList = uniqueTaskList?.filter((item) => invokeVideoTaskIds.includes(item.id) && item.state === TASK_STATUS.TASK_DOING);
      }
      const isLite = store.getState().app.clientType === 'UCLite';
      if (adTaskList?.length && isOpenQueryAward(appVersion, isLite)) {
        adTaskList.sort((a, b) => {
          // 多个slot同时查时，先查询激励浏览类的任务，避免浏览任务奖励查询时间过长
          const aPriority = a.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE ? -1 : 1;
          const bPriority = b.event === TASK_EVENT_TYPE.VIDEO_AD_BROWSE ? -1 : 1;
          return aPriority - bPriority;
        })
        for (const itemAd of adTaskList) {
          const adData = getIncentiveAdSlotData(itemAd);
          dispatch.task.queryAndGetAdAward({
              task: itemAd,
              slotKey: adData.slotKey,
              appId: adData.appId,
              isInit,
          })
          // tanx限制，每个slot查询必须间隔2s，否则会查不到
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    },

    // 当前的任务ID是否是高价值任务
    currentTaskIsHighTask(id: number){
      const highValueTask = store.getState().highValueTask;
      const highValueTaskList = getHighValueTaskList({
        resourceTaskList: highValueTask.resourceTaskList,
        hiddenTaskIdList: highValueTask.hiddenTaskIdList,
        currentTaskInfo: highValueTask.currentTaskInfo,
        needCurrentTask: true
      });
      return highValueTaskList.includes(String(id));
    },
    /*
    * 完成tanx进阶浏览下单任务, 分别有只浏览、只下单、浏览+下单
    * @params task: TaskInfo
    * @params rewardType: TanxRewardType
    * */
    async completeTanxProgressiveTask(params: {
      task: TaskInfo;
      rewardType: TanxRewardType;
    }){
      const task = params.task;
      const taskList = store.getState().task.taskList;
      // Tanx下单任务，用户在淘宝下单了才会完成
      const orderTask = taskList?.find((task)=> task?.event === TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER);
      if (params.rewardType === TanxRewardType.BROWSE) {
        return dispatch.task.complete({
          id: task.id,
          useUtCompleteTask: task.useUtCompleteTask,
          publishId: task.publishId,
          type: 'complete',
          params: { task }
        });
      }
      if (params.rewardType === TanxRewardType.ORDER && orderTask) {
        return dispatch.task.complete({
          id: orderTask.id,
          useUtCompleteTask: orderTask.useUtCompleteTask,
          publishId: orderTask.publishId,
          type: 'complete',
          params: { task: orderTask }
        });
      }
      if (params.rewardType === TanxRewardType.BROWSE_ORDER) {
        const completeTasks = orderTask ? [task, orderTask] : [task];
        const resDataList: ResCompleteTask[] = await dispatch.resource.serialTaskTrigger(completeTasks);
        const rewardAmount = getTotalRewardAmount(resDataList);
        const { needRecommendTaskEvent } = store.getState().app;
        if (needRecommendTaskEvent.includes(task.event)) {
          const winPrizeDate = [{rewardItem: {amount: rewardAmount}}] as ResCompleteTask['prizes'];
          modal.openTreasure(winPrizeDate,'coin', task);
        } else {
          Toast.show('任务已完成', {
            award: {
              amount: rewardAmount,
              mark: 'coin',
            }
          })
        }
        await dispatch.app.updateTaskAndCurrency()
      }
    },

    /** 批量完成桌面组件任务 */
    async batchFinishDesktopTask(){
      const entry: string = qs.getParam('entry') || '';
      const { isLogin } = store.getState().user;
      const taskList = store.getState().task.taskList;
      const doTaskList = taskList?.filter((task)=> DESKTOP_TASK_EVENT?.includes(task?.event) && task?.state === TASK_STATUS.TASK_DOING);
      // 桌面组件进入页面的entry列表
      // hash路由模式下 链接有可能会自动拼上 # 号
      const newEntry = entry?.split('#')?.[0];

      if (!doTaskList?.length || !isLogin || !DESKTOP_TASK_ENTRY_LIST?.includes(newEntry)) {
        return
      }

      const resDataList: ResCompleteTask[] = await dispatch.resource.serialTaskTrigger(doTaskList);
      const dataInfo = getFinishTaskInfo(resDataList, doTaskList);
      // 发奖成功
      if (dataInfo?.isWin) {
        const winPrizeDate = [{rewardItem: {amount: dataInfo?.amountTotal}}] as ResCompleteTask['prizes'];
        modal.openTreasure( winPrizeDate , 'treasure', dataInfo?.taskInfo, false, true,
          {
            dialogSubTitle: dataInfo?.dialogSubTitle,
            pop_source: 'widget',
          }
        );

        // 复访组件埋点
        const visitTask = resDataList?.find((item)=> item?.curTask?.event === TASK_EVENT_TYPE.FULI_DESKTOP_VISIT);
        if (visitTask) {
          factToClickAndExposure(visitTask);
        }
      } else {
        // 发奖失败
        Toast.show('网络异常，稍后再试~');
      }

      // 更新所有数据
      dispatch.app.updateAll();
    },

    /**
     * 添加实时标签
     */
    async addRealTimeTag(task: TaskInfo){
      const kps = store.getState().user.kps;
      const { taskHost } = config;
      const tagMonitor = tracker.Monitor(WPK_CATEGORY_MAP.ADD_REAL_TIME_TAG, { sampleRate: 1})
      try {
        const tagRes = await network.get(`${taskHost}/task/deeplinkFailTag`, {
          kps,
          taskId: task?.id
        })
        tagMonitor.success({
          msg: '添加实时标签-成功',
          c1: `${tagRes?.__meta?.code}`,
          c2: `${task?.id}`,
          c3: `${task?.event}`,
          bl1: JSON.stringify(tagRes),
          bl2: JSON.stringify(task),
        })
      } catch (e) {
        const err = getErrorInfo(e);
        tagMonitor.fail({
          msg: '添加实时标签-失败',
          c1: `${err?.errCode}`,
          c2: `${task?.id}`,
          c3: `${task?.event}`,
          bl1: JSON.stringify(e),
          bl2: JSON.stringify(task),
        })
      }
    },
    scrollToTask(taskId: string) {
      const element = document.getElementById(`tid-${taskId}`);
      if (!element) return;
      element?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      })
      setTimeout(() => {
        // 延迟等待滚动完成再动画
        element?.classList.add('hight-lighted-task')
        setTimeout(() => {
          element?.classList.remove('high-lighted-task')
        }, 3000);
      }, 1500);
    },
    /**
     * 桌面组件承接
     */
    async handleDesktopTask(){
      const entry: string = qs.getParam('entry') || '';
      const tid =  qs.getParam('tid') || '';
      const resTaskList = store.getState().task.resTaskList;
      const taskList = store.getState().task.taskList;
      const callAppTaskList = store.getState().task.callAppTaskList;
      // 桌面组件进入页面的entry列表
      const newEntry = entry?.split('#')?.[0];
      const curTask = taskList?.find((task) => Number(task.id) === Number(tid)) || callAppTaskList?.find((task) => Number(task.id) === Number(tid)) || resTaskList?.find((task) => Number(task.id) === Number(tid));
      const isSignTask = curTask?.event === TASK_EVENT_TYPE.UCLITE_SIGN || curTask?.event === TASK_EVENT_TYPE.UCLITE_SIGN_NM;
      /**
       * 1. tid不存在
       * 2. 桌面组件任务: 在 batchFinishDesktopTask 中处理
       * 3. 当前任务是签到任务: 在 finishSignTaskAndOpenModal 中处理
       * 4. 当前任务不存在
       */
      if (!tid || DESKTOP_TASK_EVENT.includes(curTask?.event as TASK_EVENT_TYPE) || isSignTask || !curTask) {
        return
      }
      // 桌面组件进来的才进行滚动
      if(DESKTOP_TASK_ENTRY_LIST.includes(newEntry)) {
        // 列表锚点模式，并当前任务ID 处于高亮模式
        setTimeout(() => {
          this.scrollToTask(tid)
        }, 2000); // 延时2秒，等待页面任务列表先渲染完成
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('tid');
        window.history.replaceState(null, '', newUrl.toString());
      }
    },
    /**
     * 合一页承接
     */
    async handleVideoTask() {
      const tid = qs.getParam('tid') || '';
      const evSub = getEvSub();
      /**
       * 1. tid不存在
       * 2. 不是合一页过来的
       */
      if (!tid || evSub !== EvSubType.VIDEO_FULI) {
        return;
      }
      // 列表锚点模式，并当前任务ID 处于高亮模式
      setTimeout(() => {
        this.scrollToTask(tid);
      }, 1500); // 延时1.5秒，等待页面任务列表先渲染完成
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('tid');
      window.history.replaceState(null, '', newUrl.toString());
    },
    // 任务承接
    async scrollContainerByTaskId() {
      const tid = qs.getParam('tid') || '';
      const evSub = getEvSub();
      const entry: string = qs.getParam('entry') || '';
      if (tid && evSub === EvSubType.VIDEO_FULI) {
        await this.handleVideoTask();
        return;
      }
      if (tid && DESKTOP_TASK_ENTRY_LIST.includes(entry)) {
        await this.handleDesktopTask();
      }
    },
    /*
     * 首屏渲染初始化数据处理,初步处理
     */
    firstScreenTaskListHandler(resTaskList: TaskInfo[]){
      if(!resTaskList.length){
        return [];
      }

      const appInstallMap = store.getState().app.appInstallMap;
      const appNotInstallMap = getAppInstallNotInstallMapByLocalStorage();
      const pushStateResponse = store.getState().app.pushStateResponse;
      const defaultBrowserResponse = store.getState().app.pushStateResponse;

      const taskList: TaskInfo[] = [];
      for (const item of resTaskList) {
        // 签到任务，不展示在列表中
        if([TASK_EVENT_TYPE.UCLITE_SIGN, TASK_EVENT_TYPE.UCLITE_SIGN_NM].includes(item?.event)){
          continue;
        }
        // 不是在做的任务，直接展示
        if (item.state !== TASK_STATUS.TASK_DOING) {
          taskList.push(item);
          continue;
        }

        // 汇川效果&品牌 & RTA L1-L3任务，暂时不展示在列表中，处理相关商业信息后展示
        if (excludeFirstScreenEventTypes.includes(item.event)){
          continue;
        }

        // 开启push权限, 已经开启过了，就不展示了
        if (item.event === TASK_EVENT_TYPE.UCLITE_PUSH_SWITCH && pushStateResponse && pushStateResponse.state === '1') {
          continue;
        }
        // 设置浏览器, 已经开启过了，就不展示了
        if (item.event === TASK_EVENT_TYPE.UCLITE_DEFAULT_BROWSER && defaultBrowserResponse && defaultBrowserResponse.state === '1') {
          continue;
        }

        // 没有APP安装缓存，就不处理了
        if(appNotInstallMap.size === 0 && appInstallMap.size === 0) {
          taskList.push(item);
          continue;
        }

        // 需要对在做的任务进行安装检测
        const pkg = getTaskAppInstallMap(item);
        // 不需要检测的
        if(!pkg){
          taskList.push(item);
          continue;
        }
        const { showUninstallApp = false, iosCheckInstall, andCheckInstall } = getExtraInfo(item);
        let install: Boolean | null = null;
        // 有缓存数据，则取缓存的数据
        if(appInstallMap.get(pkg)){
          install = !!appInstallMap.get(pkg)?.installed;
        }
        // 缓存的数据，如果不为null,则取缓存的数据 
        if(getAppInstallWithExpiryMinutes(pkg) !== null){
          install = !!appNotInstallMap.get(pkg)?.installed;
        }
        // 没有拿到缓存，默认展示
        if(install === null){
          taskList.push(item);
          continue;
        }
        const isCheckInstall = isIOS ? iosCheckInstall : andCheckInstall;
        // 未安装不展示,就不丢进数组
        if (showUninstallApp && install) {
          continue;
        }
        // 安装后展示, 不安装不展示，就不丢进数组
        if (isCheckInstall && !install) {
          continue;
        }
        taskList.push(item);
      }
      console.log('firstScreenTaskListHandler', resTaskList.length, taskList.length);
      return taskList;
    }
  })
});

export default Task;
