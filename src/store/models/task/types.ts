import { EBannerNuProtectDay } from "../app";
import {TanxRewardType} from "@/pages/index/task/typings";
import { IRewardInfoItem } from "@/pages/index/task/ad_video_browse";
/** 任务事件编码 */
export const enum TASK_EVENT_TYPE {
  // RTA淘宝首唤今日任务
  RTA_CALL_TAOBAO_DAILY = 'rta_call_taobao_today',
  // RTA淘宝首唤逛APP
  RTA_CALL_TAOBAO_APP_LINK = 'rta_call_taobao_app_link',
  // RTA淘宝新登
  RTA_CALL_TAOBAO_NU = 'rta_call_taobao_nu',
  // RTA淘宝NU 下载任务 对应 rta_call_taobao_nu
  RTA_CALL_TAOBAO_DOWNLOAD = 'rta_call_taobao_download',
  /** 素人AIGC任务 */
  USER_GROUP_LINK = 'user_grow_link',
  /** 签到任务 */
  UCLITE_SIGN = 'uclite_signin',
  /** 老用户签到任务 */
  UCLITE_SIGN_NM = 'uclite_signin_nm',
    /** 宝箱任务 */
  TREASURE = 'uclite_treasure',
  COUNT = 'count',
  /** 跳转链接任务 */
  JUNP_URL = 'junp_url',
  /** 搜索 */
  SEARCH = 'search',
  /** 广告 */
  VIDEO_AD = 'video_ad',
  VIDEO_AD_NEW = 'video_ad_new',
  UCLITE_VIDEO_AD = 'uclite_video_ad',
  PROGRESSIVE_INCENTIVE_ORDER = 'progressive_incentive__order',
  /** 看视频广告，不合并(客户端任务类型) */
  WATCH_VIDEO = 'watch_video',
  /** 分享 */
  SHARE = 'share',
  /** 阅读 */
  READ = 'read',
  /** 实名任务 */
  CERTIFICATION = 'certification',
  /** 淘宝调端 */
  CALL_TB = 'call_tb',
  /** link任务 */
  LINK = 'link',
  /** 二方合作任务(token) */
  LINK_TOKEN = 'link_token',
  // 点淘二方任务
  UCLITE_DT_LINK_TOKEN = 'uclite_dt_link_token',

  UCLITE_TMALL_LINK_TOKEN = 'uclite_tmall_link_token',

  // 逛app任务，服务端生成token并自动拼接，由二方触发完成
  APP_TOKEN = 'app_token',
  // 逛app任务，跳转后即触发完成
  APP_LINK = 'app_link',

  // 直接调端的二方任务
  CALL_APP_TOKEN = 'call_app_token',
  CALL_APP_LINK = 'call_app_link',

  /** 信息流跳链 */
  IFLOW_LINK = 'iflow_link',
  /** 调端手淘充值中心 */
  THIRD_ENTRY_TAO_RECHARGE = 'third_entry_tao_recharge',
  /** 单日蓄水父任务 */
  STORE_PARENT = 'store_piggy',
  /** 单日蓄水子任务 */
  STORE_CHILD = 'sub_piggy',
  /** 玩转手猫任务 */
  THIRD_ENTRY_TMALL = 'third_entry_tmall',
  /** 绑定支付宝（客户端任务） */
  BIND_ALIPAY_ACCOUNT = 'bind_alipay_account',
  /** 云备份 */
  IS_SET_BACKUP = 'is_set_backup',
  // 看信息流文章或视频任务
  STORE_READ_TIME = 'store_read_time',
  // 品牌广告任务
  BRAND_TASK = 'ad_brand',
  // 二方广告任务
  CORP_APP_TASK = 'ad_inside_group',
  // 组团蓄水任务
  TEAM_SUB = 'sub_team',
  TEAM_PARENT = 'store_team',
  /** 看小说时长任务 */
  ELD_NOVEL_TIME = 'eld_novel_time',
  UCLITE_REAL_NAME = 'uclite_real_name',
  /** 新增任务 */
  /** 首次提现任务 */
  UCLITE_WITHDRAWAL = 'uclite_withdrawal',
  /** 设置默认浏览器任务 */
  UCLITE_DEFAULT_BROWSER = 'uclite_default_browser',
  /** 开启push权限 */
  UCLITE_PUSH_SWITCH = 'uclite_push_switch',

  // 激励视频奖励翻倍任务
  UCLITE_DOUBLE_AWARD = 'ad_double_award',

  // 阅读奖励翻倍任务
  UCLITE_READ_DOUBLE = 'uclite_double',
  // 签到提醒任务
  UCLITE_SIGN_MINDER = 'uclite_signin_minder',
  // 搜索蓄水任务
  STORE_BIZ_PRT = 'store_biz_prt',
  // 时长加码跳转任务
  LINK_PRT = 'link_prt',
  // 签到领福卡任务
  WUFU_CARD = 'wufu_card',
  WUFU_BENEFITS_LINK = 'wufu_benefits_link',
  /** 关键词搜索任务 */
  UCLITE_SEARCH_WORD = 'uclite_search_keyword',
  /** 关键词搜索任务2，15秒计时 */
  UCLITE_READ_ONCE = 'uclite_read_once',
  // 搜索关键词，点击完成
  UCLITE_READ_CLICK = 'uclite_read_click',
  // 百度搜索词任务
  BAIDU_READ_ONCE = 'baidu_read_once',
  BAIDU_READ_CLICK = 'baidu_read_click',
  // 搜索任务新样式
  SEARCH_READ_ONCE = 'search_read_once',
  SEARCH_READ_CLICK = 'search_read_click',
  // 邀请任务
  UCLITE_INVITE_TASK = 'uclite_invite_task',
  // 填写邀请码任务
  UCLITE_INVITE_ENTRY = 'uclite_invitee',
  // 头条任务
  TOUTIAO_TASK = 'toutiao_',

  ALIMAMA_SHOP = 'uclite_alimama_shop',
  UCLITE_TAOBAO_LIANMENG = 'uclite_taobao_lianmeng',
  UCLITE_TAOBAO_SHOPPING = 'uclite_taobao_shopping',
  UCLITE_TAOBAO_BROWSE = 'uclite_taobao_browse_goods',

  /** 激励视频浏览任务 */
  VIDEO_AD_BROWSE = 'video_ad_browse',
  /** 桌面小组件1*1 */
  UC_FULI_DESKTOP = 'uc_fuli_desktop',
  /** 桌面小组件2*2 */
  UC_FULI_DESKTOP2 = 'uc_fuli_desktop2',

  /** 高价值多步骤任务 */
  HIGH_VALUE_TASK = 'high_value_task',
  /** 通用下载安装判断 */
  CALL_APP_DOWNLOAD = 'call_app_download',
  /* 登录绑定 */
  UC_LOGIN = 'uc_login',
  /** 主端搜索浏览15 */
  UC_READ_ONCE = 'uc_read_once',
  /** 主端搜索点击 */
  UC_READ_CLICK = 'uc_read_click',

  /**
   * 激励广告类任务事件标签
   */
  INCENTIVE_AD_TAG = 'video_ad',

  /**
   * 新汇川二方效果广告任务
   */
  CORP_APP_TASK_NEW = 'ad_inside_group_new',
  /**
   * 扩展汇川二方效果广告任务事件
   */
  CORP_APP_TASK_EXTEND = 'ad_inside_group_extend',

  /** 公告任务 */
  FULI_ANNOUNCE = 'uc_fuli_announce',

  /** 见面礼 */
  FULI_MEET_GIFT = 'uclite_biz_first_visit',
  /** 多天到访任务 */
  FULI_LIMIT_SIGNIN = 'uclite_biz_multi_visit',
  /** 小说专属福利 - 阅读时长累计 */
  NOVEL_READ_MINS = 'store_uc_novel_read_time',

  /** 小说专属福利 - 阅读时长累计子任务 */
  SUB_NOVEL_READ_MINS = 'sub_store_uc_novel_read_time',

  /** 桌面小组件复访任务 */
  FULI_DESKTOP_VISIT = 'uc_desktop_visit',
  /** 新添加桌面小组件任务 */
  FULI_ADD_DESKTOP = 'uc_desktop_add',
  /** 福利资源父任务 */
  FULI_STORE_WELFARE = 'store_resource_welfare',
  /** 福利资源子任务 */
  FULI_SUB_STORE_WELFARE = 'sub_store_resource_welfare',
  // 标签返回任务
  CALL_APP_NO_AWARD = 'call_app_no_award',

  // 视频浏览子任务
  SUB_STORE_UC_VIDEO_READ_TIME = 'sub_store_uc_video_read_time',
  // 视频浏览总任务
  STORE_UC_VIDEO_READ_TIME = 'store_uc_video_read_time'
}

/** 新样式搜索任务 */
export const NEW_SEARCH_TASK_EVENT = [
  TASK_EVENT_TYPE.SEARCH_READ_ONCE,
  TASK_EVENT_TYPE.SEARCH_READ_CLICK,
]

/** 需要在任务列表过滤的任务 */
export const FILTER_TASK_TYPE = [
  TASK_EVENT_TYPE.TREASURE, // 宝箱
  TASK_EVENT_TYPE.STORE_READ_TIME,
  TASK_EVENT_TYPE.UCLITE_TAOBAO_BROWSE,
  TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER, // tanx激励下单
  TASK_EVENT_TYPE.UCLITE_DOUBLE_AWARD,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU
];

export const RTA_TASK_TYPE = [
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DAILY,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD
];

// 下载APP任务类型
export const DOWNLOAD_APP_EVENT = [
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DAILY,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD,
  TASK_EVENT_TYPE.CALL_APP_DOWNLOAD,
  TASK_EVENT_TYPE.CORP_APP_TASK,
  TASK_EVENT_TYPE.CORP_APP_TASK_NEW,
  TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND,
];

// 展示待领取状态的任务事件
export const SHOW_WAIT_RECEIVE_BTN_EVENT = [
  TASK_EVENT_TYPE.FULI_MEET_GIFT,
  TASK_EVENT_TYPE.FULI_LIMIT_SIGNIN,
  TASK_EVENT_TYPE.NOVEL_READ_MINS,
];

/** 展示任务奖励弹框、但是不展示套娃任务的event */
export const DEFAULT_MODAL_EVENT = [
  TASK_EVENT_TYPE.FULI_MEET_GIFT,
  TASK_EVENT_TYPE.FULI_LIMIT_SIGNIN,
  TASK_EVENT_TYPE.NOVEL_READ_MINS,
]

/**
 * 任务完成可以继续点击跳转的event
 */
export const FINISH_CAN_JUMP_LINK_EVENT = [
  TASK_EVENT_TYPE.FULI_MEET_GIFT
]

/** 桌面组件任务事件 */
export const DESKTOP_TASK_EVENT = [
  TASK_EVENT_TYPE.UC_FULI_DESKTOP,
  TASK_EVENT_TYPE.UC_FULI_DESKTOP2,
  TASK_EVENT_TYPE.FULI_DESKTOP_VISIT,
  TASK_EVENT_TYPE.FULI_ADD_DESKTOP,
]
/** 桌面组件任务entry */
export const DESKTOP_TASK_ENTRY_LIST = ['zjbx1', 'zjbx2', 'zjqd', 'zjq','desktopwidget'];

/** 奖品枚举 */
export const enum PRIZE_CODE {
  /** 元宝 */
  COIN = 'coin',
  /** 现金 */
  CASH = 'cash',
}
/** 任务请求参数 */
export interface ReqCompleteTask {
  kps: string;
  id: TaskInfo['id'];
  type: ITaskOpType;
   /** 投放ID */
  publishId?: number;
  /** 是否用设备登录 */
  useUtCompleteTask?: boolean;
  params?: {
    /** 提示toast奖励 */
    toast?: boolean;
    /** 当前任务 */
    task?: TaskInfo;
    /** 搜索关键词任务的关键词 */
    keyword?: string;
    /** 关键词扩展参数 */
    ext?: string;
  },
}

/** 批量领奖任务请求参数 */
export interface ReqBatchRewardTask {
  tids: string;
  publishList?: Array<{
    tid: number;
    publishId: number;
  }> | string;
}

export enum WELFARE_CARD_CODE {
  SURPRISE_CODE = 'SURPRISE_CODE',
  COMMON_CODE = 'COMMON_CODE',
  RANDOM_CODE = 'RANDOM_CODE',
}
export interface IWelfareCard {
  totalCardNum: number;
  /**
   * 福卡类型
   */
  intro: WELFARE_CARD_CODE | '';
  newAddCardNum: number;
  number?: number;
}

/** 任务请求返回值 */
export interface ResCompleteTask {
  state: TASK_STATUS;
  prizes: IPrize[];
  nextTask: TaskInfo;
  curTask: TaskInfo;
}
export enum TASK_STATUS {
  TASK_DOING = 0,
  TASK_COMPLETED = 1,
  TASK_CONFIRMED = 2,
  TASK_REPEAT = 3,
  TASK_NOT_READY = 4,
  TASK_PRE_TASK_NOT_FINISH = 5,
  TASK_TIMES_LIMIT = 6,
  TASK_FINISH = 7,
  TASK_NOT_COMPLETED = 8,
  TASK_INVALID_TIME = 9
}

/** 表示用户已经领取过任务的状态 */
export const HAS_RECEVICE_TASK_STATUS = [
  TASK_STATUS.TASK_COMPLETED,
  TASK_STATUS.TASK_CONFIRMED,
  TASK_STATUS.TASK_FINISH,
  TASK_STATUS.TASK_NOT_COMPLETED
]

export interface RewardItem {
  name: string;
  mark: string;
  amount: number;
  icon: string;
  /** 是否随机奖励 */
  randomAmount: boolean;
  multiple: number;
}
export interface TaskInfo {
  /** 任务id */
  id: number;
  preId?: number;
  /** 任务名称 */
  name: string;
  /** 任务描述 */
  desc: string;
  /** 任务事件 */
  event: TASK_EVENT_TYPE;
  /** 任务完成目标 */
  target: number;
  /** 任务进度 */
  progress: number;
  /** 排序 */
  sort: number;
  /** 图标 */
  icon: string;
  /** 任务跳转链接 */
  url: string;
  /** 任务按钮名称 */
  btnName: string;
  /** 0 任务进行中 1 任务完成 2 任务奖励完成 3 任务重复完成 4 任务未完成 5 前置任务未完成 6 任务受限,次数限制 7 任务不存在 8 领奖失败 */
  state: TASK_STATUS;
  /** 预告文案 */
  preMessage?: string;
  rewardItems: RewardItem[]
  /** 画像 */
  profile?: string;
  /** 任务完成时间 */
  completeTime: number;
  /* 任务开始时间 */
  beginTime: number;
  /** 任务结束时间 */
  endTime: number;
  // 双倍奖励结束时间
  doubleAwardEndTime: number;
  /* 每日每次完成间隔 */
  dayTimesInterval?: number;

  taskFlag?: TASK_FLAG;

  adId?: string; // 广告任务附加的属性，在前端将广告id填入
  sid?: string; // 广告任务附加的属性，广告请求id
  accountId?:string;// 广告账户id
  slotId?:string; // 广告slotId

  token?: string; // 广告用的加密串，服务端返回的。
  extra?: string; // 额外字段

  toDelete?: boolean; // 数据处理过程中，用于筛选准备删除的任务

  nextSign?: TaskInfo;  // 明天签到任务
  isLastDay?: boolean;  // 是否是最后一天签到

  dayTimes?: {
    target: number;
    progress: number;
  }, // 视频任务总目标数和进度

  ext?: {
    words?: IWord[];
  },  // 搜索关键词

  errMsg?: string

  prizes?: IPrize[],

  allTimesRewardMap: {
    [key: string]: IRewardPerTime[]
  },

  /** 分类 */
  taskClassify: string;
  /** 分组 */
  groupCode: string;
  /** 任务类型 */
  taskType: string;
  /** 资源位ID */
  publishId: number;
  /** 限时任务list */
  preTaskList?: TaskInfo[];

  /**  */
  useUtCompleteTask: boolean;

  /** 周期内总天数--循环周期会重置  --********新增 */
  cycleTotalDay: number;

  /** 不分周期连续天数，断了从1开始 --********新增 */
  continuousDay: number;

  timeLimit?: Array<{
    left: string;
    right: string;
  }>;

  /** 任务进度, 打点使用 */
  task_progress?: string;
  // 自定义任务次数间隔控制时间
  dayTimeIntervalMap: {
    [key: number]: number
  }

  // 蓄水子任务list
  storeChildTaskList?: TaskInfo[];
  /** 任务时间限制类型 */
  timeLimitType: 'DAY' | 'HOUR' | 'MINUTE';
}

interface IRewardPerTime {
  name: string;
  mark: string;
  amount: number;
  icon: string;
  times: number;
}

export interface IPrize {
  win: boolean;
  rewardType: string;
  rewardItem: RewardItem;
  [key: string]: any;
}

// 搜索关键词
export interface IWord {
  name: string;
  type: IWordType;
  from?: string;
}
export enum IWordType {
  /** 高商词 */
  highbiz = 0,
  /** 运营词 */
  operation = 1,
}

export enum TASK_FLAG {
  EXTRA_TASK = 'EXTRA_TASK'
}

export enum SIGNIN_STATE {
  /** 过期 */
  EXPIRE = 'expire',
  /** 已签到 */
  SIGNED = 'signed',
  /** 未签到 */
  NO_SIGN_IN = 'noSignIn',
}
/** 签到任务数据 */
export interface SignInfo extends TaskInfo {
  /** 红包类型 */
  size?: 'small' | 'big';
  /** 红包名称(天) */
  title?: string;
  /** 是否是允许签到的当天 */
  isSignDay?: boolean;
  /** 签到状态 */
  signinState?: SIGNIN_STATE;
}
export type ITaskOpType = 'count' | 'complete' | 'award' | 'store';

export interface ITaskV2ReqParam {
  /** 业务方id */
  appId: string;
  /** 模块编码 */
  moduleCode: string;
  /** kps(优于serviceTicket) */
  kps?: string;
  /** serviceTicket */
  serviceTicket?: string;
  /** 公参 */
  uc_param_str?: string;
  /** 任务id【加签参数】 */
  tid: number;
  /** 接口请求类型（count=计数/complete=完成/award=领奖/store=蓄水）【加签参数】 */
  type: ITaskOpType;
  /** 请求类型为count、store时必填，其他类型填默认值1 【加签参数】 */
  value?: number;
  /** 请求幂等 id  业务内用户保证唯一 【加签参数】 */
  requestId: string;
  /** 加签盐 */
  salt: string;
  /** 加签顺序 tid,type,value,requestId 如上述:WsgiUtil.sign("10complete100000","123456) */
  sign: string;
}

export interface INoticeByTokenParam {
  appId?: string;
  token: string;
  from: string;
  kps?: string;
  sceneId?: string;
  convertTag?: string;
  deliveryId?: string;
  openId?: string;
  implId?: string;
  sign?: string;
  salt?: string;
  requestId?: string;
}

export interface ITaskDesc {
  event: string;
  desc: string;
}


export interface WelfareAward {
  code: string;
  name: string;
  icon: string;
  intro?: string;
  url?: string;
}

export enum WITHDRAW_STATUS {
  /**
   * 已提现
   */
  DONE = 2,
  /**
   * 未提现
   */
  DOING = 1,
}

export interface WelfareCashAward extends WelfareAward {
  state: WITHDRAW_STATUS;
  amount: number;
}

export interface QueryTaskRespData {
  values?: TaskInfo[];
  timestamp: number;
  rate: number;
  inviteAddAmount: number;
  exchange: any;
  frontData: IDiamondData;
  areaInfo: {
    city: string;
    province: string;
  }
}

export interface IDiamondData {
  /** 完成签到后第二次进入弹窗顶部图片 */
	modalFarmIamge: string;
  /** 完成签到后第二次进入弹窗按钮文案 */
	modalFarmText: string;
  /** 芭芭农场链接 */
	farmUrl: string;
  /** 客服h5链接 */
	helpCenterlink: string;
  /** 本活动规则 */
	curRulelink: string;
  /** 极速版活动通用总则 */
	liteActivityRuleLink: string;
  /** 主端活动通用总则 */
	activityRuleLink: string;
  /** banner广告新手保护天数 */
	bannerNuProtectDay: EBannerNuProtectDay;
  /** banner2广告新手保护天数 */
	banner2NuProtectDay: EBannerNuProtectDay;
  /** 提现页面链接 */
	withdrawLink: string;
  /** 无门槛红包页面链接 */
	oldWithdrawlink: string;
  /** 新人首启红包页面链接 */
	nuPacketLink: string;
  /** 流水页汇率文案 */
	flowRateDesc: string;
  /** 阅读任务配置 */
	readTaskConf: {
    /** 【看头条】链接 */
		newsLink: string;
    /** 【去搜索】链接 */
		searchLink: string;
    /** 【读小说】链接 */
		novelLink: string;
    /** 翻倍前奖励元宝数量 */
		awardAmount: number;
    /** 翻倍后奖励元宝数量 */
		doubleAwardAmount: number;
	},
  /** 微信分享口令 */
	inviteDesc: string;
  /** 邀请提示（邀请码区域显示） */
	inviteTips: string;
  /** 邀请好友规则 */
	inviteRule: string;
  /** 春节活动开始时间 */
	newSpringStartTime: number;
  /** 春节活动结束时间 */
	newSpringEndTime: number;
  /** 裂变邀请独立页链接 */
	invitePageLink: string;
  /** 是否屏蔽裂变邀请相关模块（邀请码、面对面、微信粘 */
	shieldInviteModule: boolean;
  /** tanx SDK slot Key */
	tanxSlotKey: string;
  /** ios tanx SDK slot Key */
	iOSTanxSlotKey: string;
  /** 完成后需要套娃弹窗的任务event */
	needRecommendTaskEvent: string[];
  /** 任务n次完成成功率监控配置 */
	taskWPKCategoryConf:
		{
      /** 监控代码 */
			category: number;
      /** 任务事件 */
			event: string[];
      /** n次成功率 */
			limitTimes: number;
		}[];
  /** 逛APP任务默认显示数量（超出折叠） */
  showAppTaskNum: number;
  /** 搜索关键词显示数量 */
	searchWordSize: number;
  /** 【点击类型】搜索关键词显示数量 */
	clickTypeSearchWordSize: number;
  /** 二方换量任务token参数配置 */
	paramsToken: string[];
  /** 单次逛阿里妈妈商品任务时间（秒） */
	alimamaShoppingTargetTime: number;
  /** 过滤未安装手淘用户任务event  */
	eventFilterUninstalledTaobao: string[];
  /** 淘宝联盟倒计时时长 */
	countDownTime: number;
  /** 视频任务奖励节点 */
	newAdTaskNodes: string[];
  /** 跳过激励视频可领奖(仅测试环境使用) */
	skipVideoGetAward: boolean;
  /** 素人任务 */
	amateur: {
    /** 素人任务ID */
		taskId: number;
    /** 素人任务在今日任务的位置 */
		order: number;
	},
  /** 看广告双倍奖励最高可得元宝数 */
	doubleADTaskMaxAward: number;
  /** 极速版手淘RAT配置 */
	taobaoRtaConfig: RTAConfig | null,
  /** UC主端手淘RAT配置 */
  ucTaobaoRtaConfig: RTAConfig | null,
  /** 换量重定向配置 */
	exchangeRedirectConfig: {
    /** 不支持重定向城市 */
		filterCity: string[];
    /** 支持重定向entry */
		filterEntry: string[];
    /** 定向跳转链接 */
		redirectLink: string;
	};
  /**
   * 福利资源模块排序列表
   */
  resourceCodeList: string[];
  /** 需要打实时标签的任务id列表 */
  needAddTagTaskIdList: string[];
  /** ios极速版客户端支持异步版本 */
  iosLiteOpenQueryAwardVersion: string;
  /** ios主端客户端支持异步版本 */
  iosOpenQueryAwardVersion: string;
}

interface RTAConfig {
  /** ios是否开启 */
  iosOpen: boolean;
  /** android是否开启 */
  androidOpen: boolean;
  /** 安卓应用Key */
  androidAppKey: string;
  /** IOS应用Key */
  iosAppKey: string;
  /** 场景ID */
  sceneId: string;
  /** Android广告位ID */
  androidSlotId: string;
  /** IOS广告位ID */
  iosSlotId: string;
  /** 广告类型 */
  type: string;
  /** 淘宝包名 */
  pkgName: string;
  /** 唤端schema */
  openSchema: string;
  /** Android支持RTA版本 */
  androidSupportVersion: string;
  /** IOS支持RTA版本 */
  iosSupportVersion: string;
  /** 无端用户等级 */
  NuLevel: string;
  /** 促活用户等级 */
  activeLevel: string;
  /** RTA高优任务ID */
  highPriorityTaskId: string[];
  /** 低优任务ID */
  lowPriorityTaskId: string[];
  /** NU是否进行淘宝安装检测 */
  nuTaobaoInstallCheck: boolean;
}

export interface QueryADRewardsRes {
  code: number;
  completeTime: number;
  reward_data: {
    reward_success_id: string;
    reward_info_list: IRewardInfoItem[];
    reward_type: TanxRewardType;
  }
  error?: string;
  errMsg?: string;
  errCode?: number;
  ext?: any;
}
