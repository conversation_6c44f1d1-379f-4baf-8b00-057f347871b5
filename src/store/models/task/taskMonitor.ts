import { store } from '@/store'
import {TaskInfo, TASK_EVENT_TYPE} from "@/store/models/task/types";
import storage from "@ali/weex-toolkit/lib/storage";
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import fact from '@/lib/fact';
import {getParam} from "@/lib/qs";
import { ResCompleteTask } from '@/store/models/task/types';

// 当天点击去完成任务，记录下任务信息
export const storeTaskHandleTimes = async (task:TaskInfo) => {
  const taskIdTodayClick = await storage.get('task_id_today_click')
  if (!taskIdTodayClick) {
    await storage.setDaily('task_id_today_click', [task.id])
    await storage.setDaily(`to_do_${task.id}`, 0)
  } else {
    if (!taskIdTodayClick?.includes(task.id)) {
      taskIdTodayClick.push(task.id)
      await storage.setDaily('task_id_today_click', taskIdTodayClick)
      await storage.setDaily(`to_do_${task.id}`, 0)
    }
  }
}

// 去做任务后回来福利页记录，用于观察任务完成情况上报监控
export const visitRecorderAfterToFinishTask = (resTaskList: TaskInfo[]) => {
  if (!resTaskList?.length) return
  resTaskList.forEach(async (task) => {
    const taskBackTimes = await storage.get(`to_do_${task.id}`)
    if (taskBackTimes !== undefined) {
      await storage.setDaily(`to_do_${task.id}`, taskBackTimes + 1)
    }
  })
}

function getTaskWpkConf (task: TaskInfo) {
  const { taskWPKCategoryConf } = store.getState().app
  let taskEvent = task.event?.includes(TASK_EVENT_TYPE.LINK_TOKEN) ? TASK_EVENT_TYPE.LINK_TOKEN : task.event
  return taskWPKCategoryConf.find(conf => conf.event?.includes(taskEvent))
}

export function logToFinishTask (task: TaskInfo, channelId: string, extraInfo = '') {
  tracker.log({
    category: WPK_CATEGORY_MAP.TO_FINISH_TASK,
    sampleRate: 1,
    msg: `${task.id}_${task.event}_${task.name}`,
    w_succ: 1,
    c1: task.event,
    c2: '' + task.id,
    c3: channelId,
    c4: getParam('entry'),
    bl1: task.url || '',
    bl2: extraInfo
  })
}
export function factToFinishTask (task: TaskInfo) {
  fact.event('task_click', {
    clicl_action: 'gotask',
    task_id: task.id,
    task_name: task.name,
    taskclassify: task?.taskClassify || '',
    groupcode: task?.groupCode || '',
    award_amount: task?.rewardItems[0]?.amount || '',
    task_progress: task?.dayTimes?.progress || '',
  })
}

function logCompleteRate (task: TaskInfo, reloadTimes: number, isComplete: boolean) {
  const taskWpkConf = getTaskWpkConf(task)
  if (!taskWpkConf?.category) return
  const { id, event, name } = task
  tracker.log({
    category: taskWpkConf.category,
    sampleRate: 1,
    w_succ: isComplete ? 1 : 0,
    msg: isComplete ? `${id}_${name}_任务完成_第${reloadTimes}次 ` : `${id}_${name}_未完成`,
    c1: reloadTimes + '',
    c2: event,
    c3: name,
    c4: '' + id
  })
  storage.remove(`to_do_${id}`)
}


export const taskCompleteRateMonitor = (taskList: TaskInfo[], completeTaskIdList: number[]) => {
  taskList.forEach(async (task) => {
    const reloadTimes = await storage.get(`to_do_${task.id}`)
    if (reloadTimes === undefined) return
    const taskWpkConf = getTaskWpkConf(task)
    if (!taskWpkConf) return
    console.log('taskWpkConf:', taskWpkConf)
    if (reloadTimes < (taskWpkConf.limitTimes + 1) && completeTaskIdList.includes(task.id)) {
      return logCompleteRate(task, reloadTimes, true)
    }
    if (reloadTimes > (taskWpkConf.limitTimes - 1)) {
      logCompleteRate(task, reloadTimes, completeTaskIdList.includes(task.id))
    }
  })
}


export const factToClickAndExposure = (completeTask: ResCompleteTask) => {
  const { prizes = [], curTask , state} = completeTask;
  fact.exposure('task_expo', {
    c: 'pop',
    d: 'widget',
    task_id: curTask?.id,
    task_event: curTask?.event,
    task_name: curTask?.name,
    taskclassify: curTask?.taskClassify || '',
    groupcode: curTask?.groupCode || '',
    award_amount: prizes?.[0]?.rewardItem?.amount || '',
    task_progress: curTask?.progress || '',
    task_count: curTask?.progress || '',
    isfinish: state === 2 ? '1' : 0,
  })
  fact.click('task_click', {
    c: 'pop',
    d: 'widget',
    task_id: curTask?.id,
    task_event: curTask?.event,
    task_name: curTask?.name,
    taskclassify: curTask?.taskClassify || '',
    groupcode: curTask?.groupCode || '',
    award_amount: prizes?.[0]?.rewardItem?.amount || '',
    task_progress: curTask?.dayTimes?.progress || '',
    task_count: curTask?.progress || '',
    isfinish: state === 2 ? '1' : 0,
  })
}