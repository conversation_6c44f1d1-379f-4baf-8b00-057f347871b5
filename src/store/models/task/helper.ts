import config from '@/config';
import {dispatch, store} from '@/store';
import network from '@/utils/network';
import ucapi from '@/utils/ucapi';
import dayjs from 'dayjs';
import storage from "@ali/weex-toolkit/lib/storage";
import {execWithLock} from '@/utils/lock';
import {TaskState} from '.';
import Toast from '@/lib/universal-toast/component/toast';
import { getUserInfo } from '@/lib/ucapi';
import {
  INoticeByTokenParam,
  ITaskOpType,
  ITaskV2ReqParam,
  ReqCompleteTask,
  ResCompleteTask,
  SIGNIN_STATE,
  SignInfo,
  TASK_EVENT_TYPE,
  TASK_STATUS,
  TaskInfo,
} from './types';
import {STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY} from '@/constants/storage';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category';
import modal from '@/components/modals/modal';
import {taskCompleteRateMonitor} from './taskMonitor'
import { getMainVersion } from "@/http";
import { checkInstallApp, checkMultipleInstallApp, checkTaskFinished, getExtraInfo, getTaskAppInstallMap } from '@/pages/index/task/help';
import stat from '@/lib/stat';
import { IHighValueTaskState } from '../highValueTask/types';
import { isHuiChuangAdEffectTask } from '@/pages/index/task/help';
import { isIOS } from '@/lib/universal-ua';
import { isIncentiveAdTask } from '@/pages/index/task/help';

export const TIME_EVENT_FLAG = '_prt' // 时长任务

const excludeEventTypes: TASK_EVENT_TYPE[] = [
  // TASK_EVENT_TYPE.EXTRA_AWARD,
  TASK_EVENT_TYPE.STORE_PARENT,
  TASK_EVENT_TYPE.STORE_CHILD,
  TASK_EVENT_TYPE.UCLITE_READ_DOUBLE,
  TASK_EVENT_TYPE.CORP_APP_TASK,
  TASK_EVENT_TYPE.BRAND_TASK,
  TASK_EVENT_TYPE.CORP_APP_TASK_NEW,
  TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND,
];

// 首屏数据返回处理event
export const excludeFirstScreenEventTypes: TASK_EVENT_TYPE[] = [
  TASK_EVENT_TYPE.CORP_APP_TASK,
  TASK_EVENT_TYPE.BRAND_TASK,
  TASK_EVENT_TYPE.CORP_APP_TASK_NEW,
  TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND,
];

/** 获取签到数据 */
export const getSignInfoByParam = (taskState: TaskState) => {
  // const { signin, now, task } = taskState;
  const { signin, now } = taskState;
  // 找到今天签到数据
  let find = signin.find((task, idx, taskList) => {
    // 获取当天的签到数据
    if (task.state === TASK_STATUS.TASK_CONFIRMED && getDayDiff(task.completeTime, now) === 0) {
      if (idx !== taskList.length - 1) {
        task.nextSign = taskList[idx + 1];
      }
      return true;
    }
    // 签到数组第一个未签到，则返回第一个签到任务
    if (idx === 0 && task.signinState === SIGNIN_STATE.NO_SIGN_IN) return true;
    // 上一个签到任务已完成，返回相邻的未完成签到任务
    if (idx > 0 && taskList[idx-1].signinState === SIGNIN_STATE.SIGNED && task.signinState === SIGNIN_STATE.NO_SIGN_IN) return true;
    return false;
  });
  // let find = signin.find(task => getDayDiff(task.beginTime, now) === 0);
  // // 没找到找日常签到任务
  // if (!find) find = task.find(task => task.event === TASK_EVENT_TYPE.ELDER_SIGN_DAILY);
  // // 最后还没找到 那完蛋了 异常
  return find;
};
/** 获取签到数据 */
export const getSignInfo = () => {
  // const { signin, now, task } = store.getState().task;  
  const { signin, now } = store.getState().task;
  // 找到今天签到数据
  let find = signin.find((task, idx, taskList) => {
    // 获取当天的签到数据
    if (task.state === TASK_STATUS.TASK_CONFIRMED && getDayDiff(task.completeTime, now) === 0) {
      if (idx !== taskList.length - 1) {
        task.nextSign = taskList[idx + 1];
      }
      return true;
    }
    // 签到数组第一个未签到，则返回第一个签到任务
    if (idx === 0 && task.signinState === SIGNIN_STATE.NO_SIGN_IN) return true;
    // 上一个签到任务已完成，返回相邻的未完成签到任务
    if (idx > 0 && taskList[idx-1].signinState === SIGNIN_STATE.SIGNED && task.signinState === SIGNIN_STATE.NO_SIGN_IN) return true;
    return false;
  });
  // let find = signin.find(task => getDayDiff(task.beginTime, now) === 0);
  // 没找到找日常签到任务
  // if (!find) find = task.find(task => task.event === TASK_EVENT_TYPE.ELDER_SIGN_DAILY);
  // 最后还没找到 那完蛋了 异常  
  return find;
};
/** 展示新用户7天签到 */
export const showNuTask = () => {
  const { signin, now } = store.getState().task;
  const beginTime = [...signin].pop()?.beginTime;
  return signin.length > 1 && getDayDiff(beginTime, now) >= 0;
};
export const queryTask = ({ kps }) => {
  const { coralHost, appId, moduleCodeTask } = config;
  // const { taskHost, appId, moduleCodeTask } = config;
  const kpsParams = kps ? { kps } : {};
  const params = { appId, moduleCode: moduleCodeTask, fve: getMainVersion(), activeUser: 1, apiVersion: "2", ...kpsParams};
  return network.get(`${coralHost}/uclite/query`, params);
  // return network.get(`${taskHost}/task/query`, params);
};

// todo: 暂时过滤掉现金奖励二方任务
export function filterCompletedTask(list: TaskInfo[]) {
  return list.filter((t) => t.state === TASK_STATUS.TASK_COMPLETED && t.event !== TASK_EVENT_TYPE.STORE_READ_TIME && !isHuiChuangAdEffectTask(t))
}

export function filterDoneTask (list: TaskInfo[]) {
  return list.filter((t) => t.event !== TASK_EVENT_TYPE.TREASURE && (t.state === TASK_STATUS.TASK_COMPLETED || t.state === TASK_STATUS.TASK_CONFIRMED))
}

export function filterDoingTask(list: TaskInfo[]): TaskInfo[] {
  const excludeTaskType = [TASK_EVENT_TYPE.STORE_READ_TIME, TASK_EVENT_TYPE.TREASURE, TASK_EVENT_TYPE.UCLITE_SIGN]
  return list.filter((task) => task.state === TASK_STATUS.TASK_DOING && !excludeTaskType.includes(task.event) ).sort((a, b) => {
    const awardA = getCoinFromDesc(a.desc);
    const awardB = getCoinFromDesc(b.desc);
    return awardB - awardA;
  });
}

export async function checkTaskModalTime () {
  const lastTime = await storage.get('taskModalShowTime')
  if (!lastTime) {
    return  true
  }
  const timeGap = new Date().getTime() - Number(lastTime)
  console.log('timeGap:', timeGap)
  return timeGap > 5 * 60 * 1000
}

export const completeTask = async ({ kps, id, type, useUtCompleteTask =false, publishId, params = {}}: ReqCompleteTask): Promise<ResCompleteTask> => {
  const { taskHost, appId, moduleCodeTask, moduleCodeFukaTask } = config;
  const reqCommonParams = await geneTaskV2Params(id, type);
  const moduleCode = params?.task?.event === TASK_EVENT_TYPE.WUFU_CARD ? moduleCodeFukaTask : moduleCodeTask
  const extParams = params?.ext ? { ext: params.ext } : {};
  const groupParams = { kps, appId, moduleCode, useUtCompleteTask, publishId, fve: getMainVersion(), ...extParams, ...reqCommonParams };
  return network.get(`${taskHost}/task/trigger`, groupParams);
};
export const receiveTask = async ({ kps, id, publishId, useUtCompleteTask = false, params = {}}: Omit<ReqCompleteTask, 'type'>): Promise<ResCompleteTask> => {
  const { taskHost, appId, moduleCodeTask, moduleCodeFukaTask } = config;
  const reqCommonParams = await geneTaskV2ReceiveParams(id);
  const moduleCode = params?.task?.event === TASK_EVENT_TYPE.WUFU_CARD ? moduleCodeFukaTask : moduleCodeTask
  const extParams = params?.ext ? { ext: params.ext } : {};
  const groupParams = { kps, appId, publishId, moduleCode, useUtCompleteTask, fve: getMainVersion(), ...extParams, ...reqCommonParams };
  return network.get(`${taskHost}/task/receiveTask`, groupParams);
};
export const getDayDiff = (beginTime: number | undefined, now: number) => {
  if (!beginTime) return 0;
  const beginD = dayjs(beginTime).endOf('d');
  const nowD = dayjs(now).endOf('d');
  return beginD.diff(nowD, 'd');
};

/** 小游戏任务的 event 标识 */
export const MINIGAME_TASK_TAG = 'minigame';
export const LINK_TOKEN_TASK = 'link_token';

export const handleSigninTask = (list: TaskInfo[], now: TaskState['now']): SignInfo[] => {
  let hasFoundSignDay = false;
  return list
    .sort((a, b) => a.beginTime - b.beginTime)
    .map((it, index, signList) => {
      const sign: SignInfo = { ...it };
      sign.title = `第${index + 1}天`;
      sign.isLastDay = index + 1 === signList.length;
      if (sign.state === TASK_STATUS.TASK_CONFIRMED) sign.signinState = SIGNIN_STATE.SIGNED;
      else sign.signinState = SIGNIN_STATE.NO_SIGN_IN;

      if (sign.state === TASK_STATUS.TASK_CONFIRMED && getDayDiff(sign.completeTime, now) === 0) {
        sign.isSignDay = true;
        hasFoundSignDay = true;
      }
      if (!hasFoundSignDay) {
        if (index === 0 && sign.state !== TASK_STATUS.TASK_CONFIRMED) {
          sign.isSignDay = true;
        } else if (index > 0 && (signList[index-1] as SignInfo).state === TASK_STATUS.TASK_CONFIRMED && sign.state !== TASK_STATUS.TASK_CONFIRMED) {
          sign.isSignDay = true;
        } else {
          sign.isSignDay = false;
        }
      }

      return sign;
    });
};

export function geneTaskRequestId() {
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
}
let ut = '';
let kps = '';

/** 接口使用的入参结构 */
export type TTaskApiReqParam = Pick<ITaskV2ReqParam, 'tid' | 'value' | 'type' | 'requestId' | 'salt' | 'sign'>;
export async function geneTaskV2Params(tid: TaskInfo['id'], type: ITaskOpType, value = 1): Promise<TTaskApiReqParam> {
  if (!ut) {
    const utRes = await ucapi.biz.ucparams({ params: 'ut' });
    // eslint-disable-next-line require-atomic-updates
    ut = utRes.ut || '';
  }
  const requestId = geneTaskRequestId();
  // const salt = `${Date.now() }`;
  const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
  const signOriText = `${decodeURIComponent(ut)}${tid}${type}${value}${requestId}`;
  const sign = await ucapi.spam.sign({ text: signOriText, salt });
  return {
    tid,
    type,
    value,
    requestId,
    sign,
    salt,
  };
}

/** 接口使用的入参结构 */
export type TTaskApiReceiveParam = Pick<ITaskV2ReqParam, 'tid' | 'value' | 'type' | 'requestId' | 'salt' | 'sign'>;
export async function geneTaskV2ReceiveParams(tid: TaskInfo['id']): Promise<any> {
  const { appId } = config;
  if (!ut || !kps) {
    const utRes = await ucapi.biz.ucparams({ params: 'utkp' });
    // eslint-disable-next-line require-atomic-updates
    ut = utRes.ut || '';
  }

  if (!kps) {
    const userInfo: any = await getUserInfo();
    kps = userInfo.kps_wg || '';
  }
  const requestId = geneTaskRequestId();
  // const salt = `${Date.now() }`;
  const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
  const signOriText = `${kps}${decodeURIComponent(ut)}${appId}${tid}${requestId}`;
  const sign = await ucapi.spam.sign({ text: signOriText, salt });
  return {
    tid,
    requestId,
    sign,
    salt,
  };
}

/** 转发二方业务token，完成任务 */
export async function geneSecondTokenParams(queryParams: INoticeByTokenParam) {
  if (!ut) {
    const utRes = await ucapi.biz.ucparams({params: 'ut'});
    ut = utRes.ut || '';
  }
  const { from, token  } = queryParams
  const requestId = geneTaskRequestId();
  const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
  const signOriText = `${decodeURIComponent(ut)}${from}${token}${requestId}`;
  const sign = await ucapi.spam.sign({text: signOriText, salt});
  const params: INoticeByTokenParam = {
    ...queryParams,
    sign,
    salt,
    requestId,
  }
  return params;
}

export function isPureNumber(str) {
  const NUM_REGEXP = /^\d+$/;
  return NUM_REGEXP.test(str);
}

export function getCoinFromDesc(desc) {
  const matchs = desc.match(/(\d+)/g);
  if (matchs) {
    return parseInt(matchs[0], 10);
  }
  return 0;
}

// 判断今天是否已签到
export function checkTodaySignInStatus() {
  const sign = getSignInfo();
  if (sign?.signinState === SIGNIN_STATE.SIGNED) {
    console.log('今天已签到');
    return true;
  }
  return false;
}

// 判断签到数据是否全部完成或今天签到已完成
export function checkSignInCompleted() {
  const signList = store.getState().task.signin;
  const todaySign = getSignInfo();
  const find = signList.find((sign) => {
    // if (sign.completeTime) {
    if (sign.state === TASK_STATUS.TASK_CONFIRMED) {
      return true;
    }
    return false;
  });
  if (!find || todaySign?.signinState === SIGNIN_STATE.NO_SIGN_IN) {
    return false;
  }
  return true;
}

// 判断是否在新人签到期限内
export function checkIsSignInPeriod() {
  const { signin, now } = store.getState().task;
  const nowTimeStr = new Date(now);
  // const lastDayTimeStr = new Date(signin[signin.length - 1].beginTime);
  const lastDayTimeStr = new Date(signin[0].beginTime + 3600 * 24 * 6 * 1000);
  const nowMonth = nowTimeStr.getMonth()+1;
  const nowDate = nowTimeStr.getDate();
  const lastDayMonth = lastDayTimeStr.getMonth()+1;
  const lastDayDate = lastDayTimeStr.getDate();
  if (signin[0]?.beginTime < now) {
    // 开始之后
    if (nowMonth < lastDayMonth || (nowMonth === lastDayMonth && nowDate <= lastDayDate)) {
      // 结束之前
      console.log('当前在新人签到期限内');
      return true;
    }
  }
  console.log('当前不在新人签到期限内');
  return false;
}

// 将今天的签到任务插入到任务列表中
export function handleInsertSignToTaskList(taskList: TaskInfo[], taskState: TaskState) {
  const sign = getSignInfoByParam(taskState);
  if (!sign) return taskList;
  if (sign?.signinState === SIGNIN_STATE.SIGNED) {
    // 今天已签到
    taskList.push(sign);
  } else {
    // 今天未签到
    taskList.unshift(sign);
  }
  return taskList;
}

// 批量领取奖励
export const handleBatchAward = async (taskList: TaskInfo[], highValueTaskIds: string[] = []) => {
  let completeTaskIdList: number[] = [];
  // taskList.forEach(task => {
  //   if (!excludeEventTypes.includes(task.event) && task.state === TASK_STATUS.TASK_COMPLETED) {
  //     completeTaskIdList.push(task.id)
  //   }
  // })
  // 涉及到同步问题，只能放到这里先处理
  for (let i = 0; i < taskList.length; i++) {
    const taskInfo = taskList[i];
    if (taskInfo.event === TASK_EVENT_TYPE.UCLITE_SIGN_MINDER && taskInfo.state === TASK_STATUS.TASK_COMPLETED) {
      const switchStatus = await storage.get(STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY);
      if (switchStatus) continue;
    }
    // 阅读浏览子任务，不需要自动领取
    if(taskInfo.event === TASK_EVENT_TYPE.SUB_STORE_UC_VIDEO_READ_TIME){
      continue;
    }

    if (!taskInfo.event.includes(TIME_EVENT_FLAG) && !excludeEventTypes.includes(taskInfo.event) && taskInfo.state === TASK_STATUS.TASK_COMPLETED) {

      // 高价值任务需要过滤自动领奖
      if(!highValueTaskIds.includes(String(taskInfo.id))){
        completeTaskIdList.push(taskInfo.id)
      }
    }
  }

  taskCompleteRateMonitor(taskList, completeTaskIdList)

  if (!completeTaskIdList.length) return
  const completeTaskIds = completeTaskIdList.join(',')
  execWithLock('batch-award', async (unlockFn) => {
    await dispatch.task.batchAward({
      tids: completeTaskIds
    })
    setTimeout(() => unlockFn(), 1200)
  })
}

export const getInviteCode = async (taskList: TaskInfo[]) => {
  const inviteTask = taskList.find(item => item.event.includes(TASK_EVENT_TYPE.UCLITE_INVITE_TASK))
  if (inviteTask) {
    // 延迟,请求接口，避免抢占带宽
    setTimeout(()=> {
      dispatch.invite.getInviteCode();
    }, 100);
  }
}

export const handleDeleteSearchWordsCacheOne = async (state) => {
  console.log('关键词任务回调', state);
  // tracker 监控
  const readOnceMonitor = tracker.Monitor(WPK_CATEGORY_MAP.READ_ONCE_NOTIFY);
  if (store.getState().task?.isShowTaskUncompletedToast) {
    dispatch.task.setShowTaskUncompletedToast({flag: false, event: ''})
  }
  try {
    const extParams = JSON.parse(state?.detail?.ext_params);
    readOnceMonitor.success({
      msg: '收到关键词完成回调',
      c1: state?.detail?.tid,
      c2: extParams?.q,
      c3: state?.detail?.scene,
      c4: JSON.stringify({
        mark: state?.detail?.reward_mark,
        amount: state?.detail?.reward_amount,
      }),
      bl1: JSON.stringify(state?.detail),
    });

    // has_show_reward_dialog 表示搜索页面展示了奖励弹窗
    if (!state?.detail?.has_show_reward_dialog) {
      // 搜索页面未展示奖励弹窗时返回福利页才出弹窗，这里是记录搜索任务完成的奖励信息
      const searchWordClicked = store.getState().task.searchWordCompleted;
      const searchWordCompleted = {
        taskId: + state?.detail?.tid,
        event: searchWordClicked.event,
        name: extParams?.q,
        prizes: [
          {
            rewardItem: {
              // 兼容旧版本通知jsapi没有奖励字段问题，使用上次点击时的奖励作为此次搜索奖励兜底
              mark: state?.detail?.reward_mark ?? searchWordClicked?.prizes?.[0]?.rewardItem?.mark,
              amount: state?.detail?.reward_amount ?? searchWordClicked?.prizes?.[0]?.rewardItem?.amount,
            },
          }
        ]
      }
      dispatch.task.updateState({
        searchWordCompleted,
      });
    }
  } catch (e) {
    console.log('关键词任务回调上报报错：', e);
    readOnceMonitor.fail({
      msg: '收到关键词完成回调-数据出错',
      c1: state?.detail?.tid,
      c2: state?.detail?.ext_params,
      c3: state?.detail?.scene,
      bl1: JSON.stringify(state?.detail),
    });
  }

  if (state && state.detail && state.detail.tid && state.detail.ext_params) {
    try {
      const taskList = store.getState().task.taskList;
      const task = taskList.find(item => item.id === JSON.parse(state.detail.tid))
      if (task) {
        const extParams = JSON.parse(state.detail.ext_params);
        if (extParams.q) {
          // 判断当前回调通知完成的词语是否存在，存在任务进度progress先+1
          const { searchWordsCache, clickTypeSearchWordsCache, baiduSearchWordsCache } = store.getState().task;
          let wordsCache = task.event === TASK_EVENT_TYPE.UCLITE_READ_CLICK ? clickTypeSearchWordsCache : searchWordsCache
          if (task.event === TASK_EVENT_TYPE.BAIDU_READ_ONCE) {
            wordsCache = baiduSearchWordsCache
          }
          const targetWord = wordsCache.find(word => word?.name === extParams.q);
          if (targetWord) {
            // 删除词语
            dispatch.task.deleteSearchWordsCacheOne({
              event: task.event,
              name: decodeURIComponent(extParams.q)
            });
            searchWordTaskProgressAddOne(task.event);
          }
        }
      }
    } catch (e) {
      console.log(e);
    }
  }
}

/** 搜索任务完成后打开奖励套娃弹窗 */
export function openSearchWordTaskCompletedDialog() {
  const searchWordCompleted = store.getState().task.searchWordCompleted;
  const { needRecommendTaskEvent } = store.getState().app
  if (!searchWordCompleted?.name) return;
  const { rewardItem } = searchWordCompleted?.prizes?.[0];
  const resTaskList = store.getState().task?.resTaskList
  const finishedTask = resTaskList.find(task => task.id === searchWordCompleted.taskId)
  if (needRecommendTaskEvent?.includes(searchWordCompleted.event)) {
    modal.openTreasure(searchWordCompleted?.prizes || [], (rewardItem?.mark || '').includes('cash') ? 'cash' : 'coin', finishedTask);
  } else {
    Toast.show('任务已完成', {
      award: rewardItem,
    });
  }
  dispatch.task.updateState({
    searchWordCompleted: {
      ...searchWordCompleted,
      name: '',
    }
  })
}

/** 更新任务列表搜索关键词任务完成次数 */
export function searchWordTaskProgressAddOne(event: TASK_EVENT_TYPE) {
   // 更新任务列表搜索关键词任务完成次数
   const taskList = store.getState().task.taskList;
   taskList.forEach(task => {
      if (task.event === event) {
        if ((task?.dayTimes?.progress ?? 0) < (task?.dayTimes?.target ?? 0)) {
          if (task?.dayTimes?.progress !== undefined) {
            task.dayTimes.progress++;
          }
        }
      }
   });
   dispatch.task.updateState({
     taskList
   });
}

// 放在逛app赚钱区域的任务
export function isCallAppTask (task: TaskInfo) {
  const callAppModuleEvents = store.getState()?.app.callAppModuleEvents;
  // diamond 有配置就取diamond的
  if (callAppModuleEvents?.length) {
    return callAppModuleEvents.includes(task.event);
  }
  // diamond没有配置则取 callAppEventList 和 LINK_TOKEN_TASK;
  return defaultCallAppEventList.includes(task.event) || task.event?.includes(LINK_TOKEN_TASK);
}

/**
 * 默认放在逛app赚钱区域的任务event列表
 */
export const defaultCallAppEventList = [
  TASK_EVENT_TYPE.APP_LINK,
  TASK_EVENT_TYPE.APP_TOKEN,
  TASK_EVENT_TYPE.CALL_APP_TOKEN,
  TASK_EVENT_TYPE.CALL_APP_LINK,
  TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK,
]

// 一些存在变量的的任务描述，通过接口读取
export function getTaskDesc (event?: TASK_EVENT_TYPE) {
  const { taskDescList } = store.getState().task
  if (!event || !taskDescList?.length) return ''
  const targetDesc = taskDescList.find(descInfo => descInfo.event === event)
  return targetDesc?.desc || ''
}

export const checkNeedRecommendTaskEvent = (id: string) => {
  const taskInfo = getTaskInfo(id);
  const { needRecommendTaskEvent } = store.getState().app

  if (!taskInfo) return false
  return needRecommendTaskEvent?.includes(taskInfo?.event)
}

export const getTaskInfo = (ids: string):TaskInfo | undefined => {
  const idArray = ids.split(',')
  const taskList = store.getState().task.taskListFromServer;
  const resourceList = store.getState().resource.resourceTaskList;

  // 只有一个id
  if (!idArray[1]){
    const taskInfo = [...taskList, ...resourceList]?.find(task => task.id === Number(idArray[0]));
    return taskInfo
  }

  // 多个id找到第一个推荐任务
  const recommendTaskId = idArray.find(id => {
    return checkNeedRecommendTaskEvent(id)
  })

  if (recommendTaskId) {
    return getTaskInfo(recommendTaskId)
  }
  // 多个id但无推荐任务，默认返回
  return getTaskInfo(idArray[0])
}
/**
* 下载类任务App
*/
export const checkAppDownload = async (taskInfo: TaskInfo) => {
  if (!taskInfo?.extra) {
    return true
  }
  if (taskInfo.state === TASK_STATUS.TASK_PRE_TASK_NOT_FINISH) {
    return true;
  }
  const extraObj = getExtraInfo(taskInfo)
  const { scheme, pkgName, andCheckInstall, iosCheckInstall, showUninstallApp } = extraObj;
  const conditions = (isIOS ? iosCheckInstall : andCheckInstall) || showUninstallApp;

  if (conditions && scheme && pkgName) {
    const schemePrefix = scheme.replace(/[^:]+$/, '//');
    const [isInstall] = await checkInstallApp(schemePrefix, pkgName);
    // 未安装不展示补充打点
    if (!isInstall) {
      stat.custom('task_hide_installed', {
        task_id: taskInfo?.id,
        task_name: taskInfo?.name,
        taskclassify: taskInfo?.taskClassify,
        groupcode: taskInfo?.groupCode,
      })
    }
    return !!isInstall
  }
  return true
}

// 判断是否有登录任务
export const checkUcLoginTask = (taskInfo: TaskInfo) => {
  // 任务完成 false
  if (checkTaskFinished(taskInfo)) {
    return {
      hasUcLoginTask: false
    }
  }
  // 任务为登录任务
  if (taskInfo.event === TASK_EVENT_TYPE.UC_LOGIN) {
    return {
      hasUcLoginTask: true,
      taskInfo: taskInfo
    }
  }

  // 子任务包含登录任务
  if (taskInfo?.preTaskList?.length) {
    const task = taskInfo.preTaskList.filter(item => item.event === TASK_EVENT_TYPE.UC_LOGIN && !checkTaskFinished(taskInfo));
    if (task.length) {
      return {
        hasUcLoginTask: true,
        taskInfo: task[0]
      }
    }
  }
  return {
    hasUcLoginTask: false,
  }
}
// 隐藏任务列表中关于高价值的任务，除用户当前需要做的任务除外
export const getHighValueTaskList = (params: {
  needCurrentTask?: boolean,
  resourceTaskList: IHighValueTaskState['resourceTaskList'],
  hiddenTaskIdList: IHighValueTaskState['hiddenTaskIdList'],
  currentTaskInfo: IHighValueTaskState['currentTaskInfo']
} =  {
  needCurrentTask: false,
  resourceTaskList: [],
  hiddenTaskIdList: [],
  currentTaskInfo: null
}) => {
  const {needCurrentTask, resourceTaskList, hiddenTaskIdList, currentTaskInfo } = params;
  let taskId: string[] = [];
  if (resourceTaskList.length) {
   resourceTaskList.forEach(task => {
      taskId.push(String(task.id));
      if (task.preTaskList?.length) {
        task?.preTaskList.forEach(item => {
          taskId.push(String(item.id));
        })
      }
    })
  }
  if (hiddenTaskIdList.length) {
    hiddenTaskIdList.forEach(item => {
      taskId.push(String(item));
    });
  }
  if (currentTaskInfo?.id && !needCurrentTask) {
    taskId = taskId.filter(item => item !== String(currentTaskInfo?.id))
  }
  return taskId;
}

/**
 * 任务列表隐藏下载类任务
 */
export const dealWithAppDownLoadTask = async (taskList: TaskInfo[], firstInit = false) => {
  let list: TaskInfo[] = [];

  const needCheckAppInstall = new Map();
  taskList.forEach(item => {
    const result = getTaskAppInstallMap(item, true);
      result && (needCheckAppInstall[result] = 1);
  });
  
  const checkAPPInstallResult = await checkMultipleInstallApp(Object.keys(needCheckAppInstall), firstInit);
  for (const task of taskList) {
    if (![TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.CORP_APP_TASK, TASK_EVENT_TYPE.CORP_APP_TASK_NEW, TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND].includes(task.event)) {
      list.push(task);
      continue;
    }

    const extraObj = getExtraInfo(task)
    const { scheme, pkgName, andCheckInstall, iosCheckInstall, showUninstallApp } = extraObj;
    const conditions = (isIOS ? iosCheckInstall : andCheckInstall) || showUninstallApp;
    // 如果汇川没有配置下载
    if ([TASK_EVENT_TYPE.CORP_APP_TASK, TASK_EVENT_TYPE.CORP_APP_TASK_NEW, TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND].includes(task.event)){
       // 汇川下载配置扩展参数 (开启检测)
      // 没有开启检测 或则 没有 协议包名
      if (!conditions || !(scheme && pkgName)) {
        list.push(task);
        continue;
      }
    }

    // 配置了扩展参数
    if ([TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.CORP_APP_TASK, TASK_EVENT_TYPE.CORP_APP_TASK_NEW, TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND].includes(task.event)) {
      if([TASK_STATUS.TASK_NOT_COMPLETED,TASK_STATUS.TASK_FINISH,TASK_STATUS.TASK_CONFIRMED, TASK_STATUS.TASK_COMPLETED].includes(task.state)){
        list.push(task);
        continue;
      }
    }
    const pkg = getTaskAppInstallMap(task, false);
    const isInstall = !!checkAPPInstallResult[pkg]?.install;
    const isCheckInstall = isIOS ? iosCheckInstall : andCheckInstall;
    // 展示任务条件: 
    // 1、开启了安装检测并且有安装
    // 2、开了未安装检测并且未安装
    if ( (isCheckInstall && isInstall) || (showUninstallApp && !isInstall)) {
      list.push(task);
      continue;
    }
  }
  return list;
}

// 任务定时上下线 配置了时间段内的任务 在当天这个时间段内可做
export const hiddenTimePeriodTask = (taskList: TaskInfo[], curTime = new Date().getTime()) => {
  let resultTaskList = taskList?.filter(item => {
    if (item?.timeLimit?.length) {
      return isTimestampInTimeRanges(curTime, item?.timeLimit)
    }
    return true
  })
  return resultTaskList?.length ? resultTaskList : []
}
  
// 检测任务是否已经超过时间段
export const checkTaskTimeout = (task, curTime = new Date().getTime()) => {
  if (!task?.timeLimit?.length) {
    return false
  }
  return !isTimestampInTimeRanges(curTime, task?.timeLimit)
}

// 判断时间戳是否在多个时间范围内
const isTimestampInTimeRanges = (timestamp, timeRanges) => {
  const date = new Date(timestamp);
  const hours = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();
  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  for (const range of timeRanges) {
    if (timeString > range.left && timeString < range.right) {
      return true;
    }
  }
  return false;
}

/**
 * 隐藏 未开始的 并且 有配置自定义完成间隔的 激励广告任务
 * @param taskList 
 * @param curTime 
 * @returns 
 */
export const hiddenNotStartTask = (taskList, curTime = new Date().getTime()) => {
  let resultTaskList = taskList?.filter((item: TaskInfo) => {
    const { beginTime, dayTimeIntervalMap = {}} = item;
    if (beginTime > curTime && Object.keys(dayTimeIntervalMap)?.length && isIncentiveAdTask(item)) {
      return false
    }
    return true
  })
  return resultTaskList?.length ? resultTaskList : []
}

export default {
  queryTask,
  handleSigninTask,
  filterCompletedTask,
  filterDoneTask,
  filterDoingTask,
  completeTask,
  receiveTask,
  isPureNumber,
  getCoinFromDesc,
  getSignInfoByParam,
  checkTodaySignInStatus,
  checkSignInCompleted,
  checkIsSignInPeriod,
  handleInsertSignToTaskList,
  handleDeleteSearchWordsCacheOne,
  checkAppDownload,
  checkUcLoginTask,
  getHighValueTaskList,
  hiddenTimePeriodTask
};
