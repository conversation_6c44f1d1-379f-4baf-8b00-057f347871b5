
import {ISlotAd, IAd} from "@/utils/huichuan";
import {TASK_EVENT_TYPE, TASK_STATUS, TaskInfo, ResCompleteTask} from "@/store/models/task/types";
import { MINIGAME_TASK_TAG, getTaskInfo} from "@/store/models/task/helper";

import {dealTaskExtra} from "@/store/models/task/taskCheck";
import { TaoBaoRtaInfo } from '@/utils/rta';
import { TaobaoRtaConfig } from '@/store/models/app';
import { getExtraInfo, isHuiChuangAdEffectTask, isRtaAllTask, checkTaskFinished } from "@/pages/index/task/help";
import { AppState } from "@/store/models/app";
import { TaskState } from ".";
import tracker from "@/lib/tracker";
import { WPK_CATEGORY_MAP } from "@/constants/tracker_category";
import { getParam } from "@/lib/qs";

export const getErrorInfo = (err) => {
  const errCode = err.code || (err.data && err.data.code) || '';
  const msg = err.message || err.msg || '';
  const status = err.status || -1;
  return {
    errCode,
    msg,
    status
  }
}

export const combineRTATask = (list: TaskInfo[], taobaoRtaInfo: TaoBaoRtaInfo | null, taobaoRtaConfig: TaobaoRtaConfig | null) => {
    // 前端配置还没返回，先不展示任务
    if (!taobaoRtaConfig) {
      const renderList = list.filter(task => {
        return !isRtaAllTask(task)
      })
      return renderList;
    }
    const { highPriorityTaskId, lowPriorityTaskId} = taobaoRtaConfig;
    // 是否有未完成的RTA商业任务
    const hasRTATaskNotFinish = list.some((task: TaskInfo) => {
      if (!highPriorityTaskId?.includes(`${task.id}`)) {
        return false;
      }
      let taskExtra ;
      try {
        taskExtra = JSON.parse(task?.extra ? dealTaskExtra(task?.extra) : '{}');
      } catch (e) {
        taskExtra = {};
      }
      return task.state !== TASK_STATUS.TASK_CONFIRMED && taobaoRtaInfo && taskExtra?.category === taobaoRtaInfo?.category
    });
    const renderList = list.filter((task: TaskInfo) => {
      const taskId = `${task.id}`;
      let taskExtra;
      try {
        taskExtra = JSON.parse(task?.extra ? dealTaskExtra(task?.extra) : '{}');
      } catch (e) {
        taskExtra = {};
      }
      // 过滤和当前RTA用户身份不匹配的任务
      if (highPriorityTaskId?.includes(taskId) && (!taobaoRtaInfo || taskExtra?.category !== taobaoRtaInfo?.category)) {
        return false;
      }
      // 有未完成的商业化任务，淘宝换量任务先不展示
      if (hasRTATaskNotFinish && lowPriorityTaskId?.includes(taskId)) {
        return false;
      }
      return true;
    });
    return renderList;
}
export const combineBrandAdTask = (list: TaskInfo[], payload: ISlotAd) => {
  let result;
  result = list.map((task, index) => {
    if (task.event === TASK_EVENT_TYPE.BRAND_TASK) {
      if (payload.ad?.length && payload.ad[0].ad_content?.title && payload.ad[0].ad_content?.img_1) {
        return {
          ...task,
          toDelete: task.state === TASK_STATUS.TASK_CONFIRMED,
          name: payload.ad[0].ad_content.title,
          icon: payload.ad[0].ad_content.img_1.replace('http://', 'https://'),
          sid: payload.sid,
          adId: payload.ad[0].ad_id,
          slotId: payload.slot_id,
        };
      }
      // 无对应广告或者extra、token不对
      return {
        ...task,
        toDelete: task.state === TASK_STATUS.TASK_DOING || task.state === TASK_STATUS.TASK_CONFIRMED, // 未做的，没匹配就干掉
      }
    }
    return task;
  });

  return result.filter(item => {
    return !item.toDelete;
  });
};

// 隐藏高价值任务
export const combineHighValueTask = (list: TaskInfo[], highValueTaskList: string[] = []) =>{
   const newList: TaskInfo[] = [];
   for(const task of list){
       if(highValueTaskList.includes(String(task.id))){
        continue;
      }
      newList.push(task);
   }
   return newList;
}

export const combineCorpAdTask = (list: TaskInfo[], taskCorpAd: { [k: string]: ISlotAd }, clientType: AppState['clientType'], adStoreDataList?: TaskState['adStoreDataList']) => {
  let result;
  result = list?.map((task, index) => {
    if (isHuiChuangAdEffectTask(task)) {
      if (task.extra && task.token) {
        const isLite = clientType === 'UCLite';
        const {ucLite = {}, uc = {}} = getExtraInfo(task);
        const { huichuanAccountIdList = [], huichuanAccountId } = isLite ? ucLite : uc;
        const currenthuichuanAccountIdList = [huichuanAccountId, ...huichuanAccountIdList];

        const huichuanMap: {[k: string]: {ad: IAd, payload: ISlotAd}} = {};
        Object.keys(taskCorpAd).forEach(slotId => {
          const payload = taskCorpAd[slotId];
          if ( payload.ad && Array.isArray(payload.ad)) {
            payload?.ad?.find(ad => {
              const adAccountId = ad.ad_content?.account_id?.toString() || '';
              if(adAccountId && currenthuichuanAccountIdList.includes(adAccountId) && !huichuanMap[adAccountId]){
                huichuanMap[adAccountId] = { ad, payload}
              }
            });
          }
        });

        // 扩展字段配置的没有匹配到汇川返回的则不展示任务该条任务
        if(!Object.keys(huichuanMap).length){
          return {
            ...task,
            toDelete: true,
          }
        }

        const adStoreInfo = adStoreDataList?.find((adItem)=> task?.id === adItem?.taskId);
        // 匹配到缓存的逻辑
        if(adStoreInfo?.accountId && Object.keys(huichuanMap).includes(`${adStoreInfo?.accountId}`)){
          const storeAccountInfo = huichuanMap[adStoreInfo?.accountId];
          return {
            ...task,
            toDelete: false,
            name: task.name || '',
            icon: (storeAccountInfo?.ad.ad_content?.logo_url || task.icon || '').replace('http://', 'https://'),
            adId: storeAccountInfo?.ad.ad_id,
            sid: storeAccountInfo?.payload.sid,
            slotId: storeAccountInfo?.payload.slot_id,
            fromCache: storeAccountInfo?.payload.from_cache,
            packageName: storeAccountInfo?.ad?.ad_content?.package_name,
            accountId: adStoreInfo?.accountId,
          }
        }
        // 没有命中缓存,随机数取
        // 取第一个
        const firstAccountInfo = huichuanMap[Object.keys(huichuanMap)?.[0]];
        return {
          ...task,
          toDelete: false,
          name: task.name || '',
          icon: (firstAccountInfo?.ad.ad_content?.logo_url || task.icon || '').replace('http://', 'https://'),
          adId: firstAccountInfo?.ad?.ad_id,
          sid: firstAccountInfo?.payload?.sid,
          slotId: firstAccountInfo?.payload?.slot_id,
          packageName: firstAccountInfo?.ad.ad_content?.package_name || '',
          fromCache: firstAccountInfo?.payload?.from_cache,
          accountId: firstAccountInfo?.ad?.ad_content?.account_id,
        }
      }
      // 无对应广告或者extra、token不对
      return {
        ...task,
        toDelete: task.toDelete !== false && [TASK_STATUS.TASK_DOING, TASK_STATUS.TASK_NOT_COMPLETED].includes(task.state) // 未做的，没匹配就干掉
      }
    }
    return task;
  });

  return result.filter(item => {
    return !item.toDelete;
  });
};

export const generateLists = (
  resList: TaskInfo[],
  extra: {
    taskBrandAd?: ISlotAd | null,
    taskCorpAd?: { [key: string]: ISlotAd },
    taobaoRtaInfo?: TaoBaoRtaInfo | null,
  },
  taobaoRtaConfig: TaobaoRtaConfig | null,
  clientType: AppState['clientType'],
  needHideHighValueTask: string[],
  callAppModuleEvents: TASK_EVENT_TYPE[],
  adStoreDataList: TaskState['adStoreDataList']
) => {
  const excludeEventTypes: TASK_EVENT_TYPE[] = [
    // TASK_EVENT_TYPE.EXTRA_AWARD,
    TASK_EVENT_TYPE.STORE_PARENT,
    TASK_EVENT_TYPE.STORE_CHILD,
    TASK_EVENT_TYPE.UCLITE_READ_DOUBLE,
    TASK_EVENT_TYPE.CALL_APP_NO_AWARD
  ];
  const excludeEventFlag = '_prt'
  let prtTaskList: TaskInfo[] = []

  let miniGameTaskList: TaskInfo[] = [];
  let newVideoTask: TaskInfo | null = null;
  let videoTaskList: TaskInfo[] = [];
  let videoReadTimeSubTaskList: TaskInfo[] = []; // 浏览视频子任务

  let storeChildTaskList: TaskInfo[] = [];
  let storeParentTask: TaskInfo | null = null;
  let doubleTask: TaskInfo | null = null;
  let fukaSignTask: TaskInfo | null = null;

  let taskInfoList = resList.filter((t:TaskInfo) => {
    if (t.event.includes(excludeEventFlag)) {
      prtTaskList.push(t)
    }
    // 小游戏
    if(t?.event.includes(MINIGAME_TASK_TAG)){
      miniGameTaskList.push(t);
    }
    // 激励视频旧任务
    if(t?.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD){
      newVideoTask = {...t} as TaskInfo;
    }
    // 激励视频任务
    if(t?.event.includes(TASK_EVENT_TYPE.VIDEO_AD)){
      videoTaskList.push(t);
    }

    // 阅读子任务,拿出并不展示在列表中
    if(t?.event === TASK_EVENT_TYPE.SUB_STORE_UC_VIDEO_READ_TIME){
      videoReadTimeSubTaskList.push(t);
      return false;
    }
    // 蓄水子任务
    if(t?.event === TASK_EVENT_TYPE.STORE_CHILD){
      storeChildTaskList.push(t);
    }
    // 单日蓄水父任务
    if(t?.event === TASK_EVENT_TYPE.STORE_PARENT){
      storeParentTask = t;
    }

    // 翻倍任务
    if(t?.event === TASK_EVENT_TYPE.UCLITE_READ_DOUBLE){
      doubleTask = t;
    }
    // 福卡任务
    if(t?.event === TASK_EVENT_TYPE.WUFU_CARD){
      fukaSignTask = t;
    }

    // 过滤掉资源位子任务
    if(t?.event === TASK_EVENT_TYPE.FULI_SUB_STORE_WELFARE){
      return false;
    }

    return excludeEventTypes.indexOf(t.event) < 0 && !t.event.includes(excludeEventFlag) && t.state !== TASK_STATUS.TASK_TIMES_LIMIT
  });

  // 如果有浏览器计时任务
  if(videoReadTimeSubTaskList && videoReadTimeSubTaskList.length){
    // 子任务时长排序
    videoReadTimeSubTaskList = videoReadTimeSubTaskList.sort((a, b) => a.target - b.target);
    taskInfoList = taskInfoList.map(item => {
      if(item.event === TASK_EVENT_TYPE.STORE_UC_VIDEO_READ_TIME){
        return {
          ...item,
          storeChildTaskList: videoReadTimeSubTaskList
        }
      }
      return {
        ...item
      }
    })
  }


  // const miniGameTaskList = taskInfoList.filter(task => task.event.includes(MINIGAME_TASK_TAG))
  // const videoTask = taskInfoList.find(task => task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW)
  // const newVideoTask = taskInfoList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD)
  // if (videoTask) {
  //   const dayTimes = videoTask.dayTimes || { target: 0, progress: 0 }
  //   videoTask.desc = `每天可完成${dayTimes.target}次, 已完成${dayTimes.progress}/${dayTimes.target}次`
  //   // 广告任务状态重写，未全部完成时state改为0
  //   if (dayTimes.progress < dayTimes.target && videoTask.state === TASK_STATUS.TASK_CONFIRMED) {
  //     videoTask.state = 0
  //   }
  // }
  // const videoTaskList = taskInfoList.filter(task => task.event?.includes(TASK_EVENT_TYPE.VIDEO_AD));
  videoTaskList?.forEach((adTask)=>{
    const dayTimes = adTask.dayTimes || { target: 0, progress: 0 }
    if (dayTimes.progress < dayTimes.target && adTask.state === TASK_STATUS.TASK_CONFIRMED) {
      adTask.state = 0
    }
  });

  if (newVideoTask && Object.keys(newVideoTask).length) {
    const dayTimes = newVideoTask?.dayTimes || { target: 0, progress: 0 }
    if (dayTimes.progress < dayTimes.target && newVideoTask.state === TASK_STATUS.TASK_CONFIRMED) {
      newVideoTask.state = 0
    }
  }

  if (miniGameTaskList.length) {
    taskInfoList = taskInfoList.filter(task => {
      if (!task.event.includes(MINIGAME_TASK_TAG)) {
        return true
      } else if (task.id !== miniGameTaskList[0].id) {
        return false
      }
      return true
    })
  }
  // 按照sort字段排序，并且已完成的放最后
  taskInfoList.sort((pre, next) => {
    if (pre.dayTimes?.target) {
      if (pre.state === TASK_STATUS.TASK_FINISH) {
        return 1
      }
    } else if (pre.state === TASK_STATUS.TASK_CONFIRMED) {
      return 1
    }
    return pre.sort - next.sort
  })
  
  storeChildTaskList = storeChildTaskList.length ? storeChildTaskList.sort((a, b) => a.target - b.target): [];
  // const storeParentTask = resList.filter(t => t.event === TASK_EVENT_TYPE.STORE_PARENT).shift();
  // const doubleTask = resList.find(t => t.event === TASK_EVENT_TYPE.UCLITE_READ_DOUBLE)
  // const fukaSignTask = resList.find(t => t.event === TASK_EVENT_TYPE.WUFU_CARD)

  // 这个列表会多次更新，有可能已经有taskBrandAd
  // 有淘宝RTA相关任务
  const taobaoRtaInfo = extra?.taobaoRtaInfo as TaoBaoRtaInfo;
  if(taobaoRtaInfo && Object.keys(taobaoRtaInfo).length && taobaoRtaConfig){
    taskInfoList = combineRTATask(taskInfoList, taobaoRtaInfo, taobaoRtaConfig);
  }
 
  // 有汇川品牌广告任务
  const taskBrandAd = extra.taskBrandAd || {} as ISlotAd;
  if(taskBrandAd && Object.keys(taskBrandAd).length) {
    taskInfoList = combineBrandAdTask(taskInfoList, taskBrandAd);
  }

  // 有汇川效果广告任务
  const taskCorpAd = extra.taskCorpAd;
  if (taskCorpAd && Object.keys(taskCorpAd).length) {
    taskInfoList = combineCorpAdTask(taskInfoList, taskCorpAd, clientType, adStoreDataList);
  }

  // 有高价值相关任务
  if(needHideHighValueTask && needHideHighValueTask.length){
    taskInfoList = combineHighValueTask(taskInfoList, needHideHighValueTask);
  }

  // 广告信息处理必须前置，否则影响二方任务列表
  taskInfoList = taskInfoList.filter(task => {
    // 过滤没有广告返回 导致未执行过滤的问题
    // 没有品牌广告信息返回，过滤需要做的
    if(!Object.keys(taskBrandAd ?? {}).length && task?.event === TASK_EVENT_TYPE.BRAND_TASK) {
      return task.state !== TASK_STATUS.TASK_DOING
    }
    // 没有汇川广告信息返回,过滤需要做的
    if(!Object.keys(taskCorpAd ?? {}).length && isHuiChuangAdEffectTask(task)) {
      return task.state !== TASK_STATUS.TASK_DOING;
    }
    // 没有RTA广告信息返回
    if(!taobaoRtaInfo && isRtaAllTask(task)){
      return task.state !== TASK_STATUS.TASK_DOING
    }

    return true
  });


  const excludeTokenTaskIds: string[] = [];
  let taskDisplayOnTop;
  // let toppingTask;
  taskInfoList.forEach(task => {
    if (task.extra) {
      let taskExtra;
      try {
        const cleanedString = task.extra.replace(/\s/g, '');
        taskExtra = JSON.parse(dealTaskExtra(cleanedString));
      } catch (e) {
        taskExtra = {};
      }
      const excludeTokenTaskId: string = taskExtra?.tokenTaskId
      // extra里有displayOnTop，需要显示在顶部
      if (taskExtra.displayOnTop && task?.state === TASK_STATUS.TASK_DOING) {
        taskDisplayOnTop = {
          ...task,
          extra: taskExtra
        }
      }
      // 置顶任务，切换成资源投放
      // if (taskExtra.toppingTask && task?.state === TASK_STATUS.TASK_DOING) {
      //   toppingTask = {
      //     ...task
      //   }
      // }
      if (excludeTokenTaskId && !excludeTokenTaskIds.includes(excludeTokenTaskId)) {
        excludeTokenTaskIds.push(excludeTokenTaskId)
      }
    }
  });

  // 渲染的二方任务列表
  const callAppTaskList = taskInfoList.filter(task => callAppModuleEvents?.includes(task.event) && !excludeTokenTaskIds.includes('' + task.id) && !checkTaskFinished(task));

  // 逛APP模块任务ID
  const callAppTaskListIds = callAppTaskList.map(item => item.id);
  taskInfoList = taskInfoList.filter(task => {
    // 显示在顶部的任务，不显示在下面列表
    // if (toppingTask && task.id === toppingTask.id) {
    //   return false
    // }
    // 默认拿第一个callAppModuleEvents 任务占位
    if(callAppTaskList.length && task.id === callAppTaskList[0].id){
      return true
    }
    // 过滤在逛APP模块展示的任务
    if(callAppTaskListIds.includes(task.id)){
      return false;
    }
    return true
  });

  // 过滤掉资源位子任务
  // taskInfoList = taskInfoList?.filter((task)=>task?.event !== TASK_EVENT_TYPE.FULI_SUB_STORE_WELFARE);
  getParam('debug')&& console.log('=========taskInfoList', taskInfoList, callAppModuleEvents);

  return {
    taskList: taskInfoList,
    miniGameTaskList,
    callAppTaskList,
    doubleTask: doubleTask || null,
    prtTaskList,
    storeTaskList: storeChildTaskList,
    storeParentTask: storeParentTask || null,
    fukaSignTask,
    taskDisplayOnTop,
    // toppingTask,
    taskListFromServer: resList,
  };
}

export const getTotalRewardAmount = (prizeList: ResCompleteTask[]) => {
  // 检查 element 是否有 prizes 数组且不为空
  return prizeList.reduce((sum, element) => {
    // 检查 element 是否有 prizes 数组且不为空
    if (element.prizes?.length > 0) {
      const firstPrize = element.prizes?.[0] || {};
      // 检查 firstPrize 的 win 属性是否为 true，并且 rewardItem 的 amount 大于 0
      if (firstPrize.win && firstPrize.rewardItem?.amount > 0) {
        return sum + firstPrize.rewardItem.amount;
      }
    }
    return sum;
  }, 0);
}

/**
 *
 * @param prizeList
 */
export const getFinishTaskInfo = (prizeList: ResCompleteTask[], taskList?: TaskInfo[]) => {
  const winList = prizeList?.filter((item)=>{
    return item?.prizes?.[0]?.win && item?.prizes?.[0]?.rewardItem?.amount
  });

  const amountTotal = winList?.reduce((pre, item)=>{
    return pre += item?.prizes?.[0]?.rewardItem?.amount
  }, 0);

  let dialogSubTitle;
  if (winList?.length > 1) {
    dialogSubTitle = '添加和访问小组件奖励已领取'
  }else {
   const curFinishTask = taskList?.find((task)=> task?.id === winList?.[0]?.curTask?.id) as TaskInfo;
   dialogSubTitle = getExtraInfo(curFinishTask)?.dialogSubTitle || '';
  }

  const taskInfo = getTaskInfo(winList?.map((task)=> task?.curTask?.id)?.join(','))

  return {
    amountTotal,
    dialogSubTitle,
    taskInfo,
    isWin: !!winList?.length,
    mark: winList?.[0]?.prizes?.[0]?.rewardItem?.mark || 'coin'
  }
}

/**
 * 完成任务日志上报
 */
export const handleTaskCompleteNewMonitor = (data: {
  resp: ResCompleteTask,
  task?: TaskInfo,
  taskId: number
}) => {
  const taskCompleteNewMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE_NEW);
  const { resp, task, taskId } = data;
  const validPrizes = (resp?.prizes ?? []).filter(prize => prize.win);
  let text = validPrizes.length > 0 ? '领奖成功' : '领奖失败-无奖品';
  let success = !!validPrizes.length;
  // 奖励计算
  let amount = 0;
  if (validPrizes.length > 0) {
    validPrizes.forEach(item => {
      amount += item.rewardItem.amount;
    })
  }
  if (resp.state === TASK_STATUS.TASK_COMPLETED) {
    text = '完成任务-待领取奖励';
    success = true;
  }

  if(success){
    taskCompleteNewMonitor.success({
      msg: String(taskId),
      c1: text,
      c2: task?.name || '',
      c5: resp?.state,
      c6: amount || 0,
      c7: task?.event ?? resp?.curTask?.event,
      bl1: JSON.stringify(resp || {}),
      bl2: JSON.stringify(task || {})
    });
    return;
  }
  taskCompleteNewMonitor.fail({
    msg: String(taskId),
    c1: text,
    c2: task?.name || '',
    c5: resp?.state,
    c6: amount || 0,
    c7: task?.event ?? resp?.curTask?.event,
    bl1: JSON.stringify(resp || {}),
    bl2: JSON.stringify(task || {})
  });
}