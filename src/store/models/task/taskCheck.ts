import {TASK_EVENT_TYPE, TaskInfo} from "@/store/models/task/types";
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category'
import tracker from "@/lib/tracker";

interface IErrorTaskInfo {
  errMsg?: string;
  id: number;
  name: string;
}

/** 兼容服务端引号&quot转义错误 */
export const dealTaskExtra = (extra) => {
  if (!extra) {
    return ''
  }
  const arrEntities = {'quot': '"'};
  return extra?.replace(/&(quot);/ig,function(all,t){return arrEntities[t];});
}

// 检查extra字段配置是否合法
export const extraValidCheck = (task: TaskInfo) => {
  const extra = dealTaskExtra(task?.extra)
  const setErrMsg = () => {
    task.errMsg = 'extra字段配置错误'
  }
  if (typeof(extra) === 'string') {
    try {
      const obj = JSON.parse(extra);
      if (typeof (obj) === 'object' && obj) {
        return true
      } else {
        setErrMsg()
      }
    } catch(e) {
      setErrMsg()
      return false;
    }
  }
  setErrMsg()
  return false
}

// 检查是否配置了链接
const linkTaskValidCheck = (task: TaskInfo) => {
  if (!task.url) {
    task.errMsg = '链接配置错误'
  }
  return !!task.url
}

// 检查奖励配置是否合法
// const rewardItemsValidCheck = (task: TaskInfo) => {
//   let rewardValid = true
//   const rewardItems = task.rewardItems || []
//   if (rewardItems.length) {
//     rewardItems.forEach(rewardItem => {
//       if (rewardItem && !rewardItem.amount) {
//         task.errMsg = '未配置任务奖励'
//         rewardValid = false
//       }
//     })
//   }
//   return rewardValid
// }

const taskChecker = {
  [TASK_EVENT_TYPE.CORP_APP_TASK]: (task) => extraValidCheck(task),
  [TASK_EVENT_TYPE.CORP_APP_TASK_NEW]: (task) => extraValidCheck(task),
  [TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND]: (task) => extraValidCheck(task),
  [TASK_EVENT_TYPE.LINK_TOKEN]: (task) => linkTaskValidCheck(task),
  [TASK_EVENT_TYPE.LINK]: (task) => linkTaskValidCheck(task),
}

export const checkInvalidTask = (taskList: TaskInfo[])=> {
  if (!taskList) return []
  let errTask: (TaskInfo | IErrorTaskInfo)[] = [];
  taskList.forEach(task => {
    if (taskChecker[task.event]) {
      !(taskChecker[task.event](task)) && errTask.push(task)
    }
    // 如果任务奖励通过抽奖发放，查询任务列表时奖励amount为0，暂时去掉这条规则
    // if (!rewardItemsValidCheck(task)) {
    //   errTask.push(task)
    // }
  })
  errTask = errTask.map(task => ({
    errMsg: task.errMsg,
    id: task.id,
    name: task.name
  }))
  return errTask
}

// 过滤配置不正确的任务 1.跳转任务未配链接 2.二方任务扩展字段格式不正确 3.配了任务奖励但未填数量
export const taskFilter = (responseTask: TaskInfo[]) => {
  try {
    let invalidTask = checkInvalidTask(responseTask)
    let errTaskIds
    errTaskIds = invalidTask?.map(task => task.id).join(',')
    tracker.log({
      category: WPK_CATEGORY_MAP.TASK_LIST_CHECK,
      msg: invalidTask.length ? `任务${errTaskIds}配置有误` : '配置正确',
      sampleRate: 1,
      w_succ: invalidTask.length ? 0 : 1,
      c1: '' + responseTask.length,
      c2: '' + invalidTask.length,
      bl1: JSON.stringify(invalidTask)
    })
    if (invalidTask?.length) {
      console.warn(`任务[${errTaskIds}]配置有误, 已过滤`)
      return responseTask.filter(task => {
        return invalidTask.every(invalidTask => invalidTask.id !== task.id)
      })
    }
    return responseTask
  } catch (e) {
    return responseTask
  }
}
