import config from '@/config';
import network from '@/utils/network';
import ucapi from '@/utils/ucapi';

export const queryBalance = ({ moduleCode, kps }) => {
  const { appId, coralHost } = config;
  const params = { appId, moduleCode, kps };
  return network.get(`${coralHost}/currency/v1/queryBalance`, params);
};

export const queryMultiBalance = ({moduleCodeList, kps}) => {
  const { appId, coralHost } = config;
  const params = { appId, moduleCodeList, kps };
  return network.get(`${coralHost}/currency/v1/queryMultiBalance`, params);
}

export const queryUserMultiBalance = ({toModuleCode, moduleCodeList, kps}) => {
  const { appId, coralHost } = config;
  const params = { appId, moduleCodeList, kps, toModuleCode };
  return network.get(`${coralHost}/currency/v1/ucliteHomepage`, params);
}

/** 查询提现配置
 * @doc https://leaf.uc.cn/mock/projects/BJqCQgPlP/piggybank/withdraw/options
 */
export const queryWithdrawOptions = ({ kps }) => {
  const { piggyHost, moduleCodePiggybank } = config;
  const params = { moduleCode: moduleCodePiggybank, kps };
  return network.get(`${piggyHost}/piggybank/withdraw/options`, params);
};

/**
 * 兑换元宝
 * @param param0
 * @returns
 */
export async function exchange({ kps, point }) {
  const { piggyHost, moduleCodePiggybank: moduleCode } = config;
  const utRes = await ucapi.biz.ucparams({ params: 'ut' });
  const ut = utRes.ut || '';
  const timestamp = `${Date.now()}`;
  const text = `${moduleCode}${timestamp}${kps}${ut}${point}`;
  const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
  const sign = await ucapi.spam.sign({ text, salt });
  console.log({ kps, point, sign, timestamp, moduleCode, ut });
  return network.post(`${piggyHost}/piggybank/withdraw/exchange`, { kps, point, sign, timestamp, moduleCode });
}

/**
 * 根据错误码返回错误描述
 * @param code 错误码
 * @returns
 */
export function getExchangeErrorDesc(code: string) {
  const map = {
    // CORAL:SYSTEM_ERROR 系统错误,CORAL:CONCURRENT_OPERATION 并发请求，CORAL:INPUT_PARAM_INVALID 参数错误，EXCHANGE:INVALID_USER 无效用户,，，EXCHANGE:NOT_ENOUGH_BALANCE 余额不足，EXCHANGE:FLOW_EXPIRE 红包流水过期，EXCHANGE:FLOW_EXCHANGEING 红包流水提现中，EXCHANGE:CURRENCY_ERROR 货币系统错误，，EXCHANGE:OVER_MONTH_LIMIT 超过每月500，需要上传身份证 EXCHANGE:OVER_TOTAL_LIMIT 超过每天限制，EXCHANGE:ACT_CONFIG_ERROR 活动配置错误，EXCHANGE:INVALID_EXCHANGE_TIME 无效的提现时间 EXCHANGE:SYSTEM_UPGRADE 系统升级中, EXCHANGE:OVER_MAX_AMOUNT_LIMIT 超过最大提现额，EXCHANGE:OVER_DAILY_MAX_TIMES 超过每日提现次数 EXCHANGE:ONE_APPLYING_LIMIT 已经存在一个在申请中的记录 EXCHANGE:NOT_SUPPORT_ACCOUNT_TYPE 不支持的账号类型 EXCHANGE:NOT_SUPPORT_AMOUNT 不支持的金额选项，EXCHANGE:HAVE_NO_CHANCE 没有提现机会，EXCHANGE:NOT_SUPPORT_USET_TAG 不支持的用户标签  EXCHANGE:OVER_PAY_AMOUNT_LIMIT 当天打款额度已经用户完  EXCHANGE:OVER_PAY_AMOUNT_OPTION_LIMIT 当天选项打款额度已经用完
    ['EXCHANGE:HAVE_NO_CHANCE']: '没有提现机会',
    ['EXCHANGE:NOT_BIND_MOBILE']: '账号没绑定手机',
    ['EXCHANGE:IDENTITY_CARD_NULL']: '身份证空',
    ['EXCHANGE:UNUSUAL_USER']: '用户账号异常',
    ['EXCHANGE:NOT_BIND_ACCOUNT']: '未绑定提现账号',
  }
  return map[code];
}

// 元宝换算成元
export function matrixing(coin: number, exchangeRate: number) {
  if (!exchangeRate) {
    return 0
  }
  let money = Math.floor((coin / exchangeRate)) / 100;
  return money.toFixed(2)
}

export default {
  getExchangeErrorDesc,
  queryBalance,
  exchange,
};
