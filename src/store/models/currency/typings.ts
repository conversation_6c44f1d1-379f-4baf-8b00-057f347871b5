/** 流水列表单个 */
export interface IFlowItem {
  id: number;
  amount: number;
  appId: string;
  title: string;
  state: 'INCOME_CONFIRM' | 'PAYOUT_CONFIRM' | 'PAYOUT_PRE';
  expireAmount: number;
  usedAmount: number;
  validTime: number;
  expireTime: number;
  createTime: number;
  updateTime: number;
  extra: string;
  flowAppId?: string;
  exchangeWay: 'manual' | 'auto';
  exchangeRate: number;
  exchangeAllocation: {
    btnValues: number[],
    notice: string[]
   };
}

export interface IBaseAssetReqParam {
  moduleCode: string;
  appId?: string;
  kps?: string;
  stateList?: string;

  [otherKey: string]: any;
}

export interface IFeFlowItem extends IFlowItem {
  month: number;
  desc: string;
}


export interface IEquityFlowItem {
  planId: number;
  name: string;
  desc: string;
  createTime: number;
  tag: IEquityTagEnum;
}

export enum IEquityTagEnum {
  /** 任务奖励 */
  TASK_AWARD = 'uc_task_award',
  /** 元宝兑换 */
  COIN_EXCHANGE_AWARD = 'uc_coin_exchange_award',
  /** 现金兑换 */
  CASH_EXCHANGE_AWARD = 'uc_cash_exchange_award',
}

export const EQUITY_TAG_TITLE_MAP = {
  [IEquityTagEnum.TASK_AWARD]: '任务奖励',
  [IEquityTagEnum.COIN_EXCHANGE_AWARD]: '元宝兑换',
  [IEquityTagEnum.CASH_EXCHANGE_AWARD]: '现金兑换',
}