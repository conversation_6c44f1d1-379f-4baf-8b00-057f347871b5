import config from '@/config';
import { RootModel } from '@/store';
import tracker from '@/lib/tracker';
import { createModel } from '@rematch/core';
import { queryUserMultiBalance } from './helper';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import network from '@/utils/network';
import { IFlowItem, IBaseAssetReqParam, IFeFlowItem, IEquityFlowItem } from './typings';
import { IMultiBalance } from "@/pages/index/typings";
import { getGlobalFirstData } from '@/lib/render-utils/csr';
import ucapi from '@/utils/ucapi';
import { getErrorInfo } from '@/store/models/task/task_util';

const trackerAjax = tracker.Monitor(WPK_CATEGORY_MAP.CURRENCY_ACTION);

const getFormatDateObj = (timestamp: number) => {
  const d = new Date();
  d.setTime(timestamp);
  return {
    year: d.getFullYear(),
    month: d.getMonth() + 1,
    date: d.getDate(),
    hour: d.getHours(),
    minute: d.getMinutes(),
    second: d.getSeconds(),
  };
};

const prefixZero = (n: number) => {
  return `${n < 10 ? `0${n}` : n}`;
};

const formatFlow2Fe = (flowItem: IFlowItem): IFeFlowItem => {
  const { createTime } = flowItem;
  const flowDateObj = getFormatDateObj(createTime);
  const month = `${flowDateObj.year}${prefixZero(flowDateObj.month)}`;
  const timeStr = [flowDateObj.hour, flowDateObj.minute, flowDateObj.second].map(prefixZero).join(':');
  const desc = `${flowDateObj.year}.${flowDateObj.month}.${flowDateObj.date} ${timeStr}`;
  return {
    ...flowItem,
    month: +month,
    desc,
  };
};

export interface CurrencyState {
  /** 元宝 */
  coin: number;
  /** 金额 单位(分) */
  amount: number;
  /** 提现限制 */
  limit: {
    /** 日上限 */
    daily: number;
    /** 剩余次数 */
    current: number;
  };
  /** 提现配置 */
  cashPageNo: number;
  coinPageNo: number;
  options: Array<{
    /** 元宝 */
    coins: number; // 元宝数
  }>;
  flowPageTabKey: number;
  flowList: IFeFlowItem[];
  // sortedMonthList: number[];
  cashFlowList: IFeFlowItem[];
  totalIncomeAmount: number;
   /** 旧版福利猪现金 */
   oldCashAmount: number;
  equityFlowList: IEquityFlowItem[];
  equityPageNo: number;
  exchangeWay: 'manual' | 'auto';
  exchangeRate: number;
  exchangeAllocation: {
    btnValues: number[];
    notice: string[];
  };
}

const State: CurrencyState = {
  coin: 0,
  amount: 0,
  limit: {
    daily: 0,
    current: 0,
  },
  options: [],
  cashPageNo: 1,
  coinPageNo: 1,
  equityPageNo: 1,
  flowPageTabKey: 2,
  flowList: [],
  cashFlowList: [],
  equityFlowList: [],
  totalIncomeAmount: 0,
  oldCashAmount: 0,
  exchangeWay: 'auto',
  exchangeRate: 0,
  exchangeAllocation: {
    btnValues: [],
    notice: [],
  },
};

const currency = createModel<RootModel>()({
  state: State,
  reducers: {
    updateCurrency: (state: CurrencyState, currency: Partial<CurrencyState>) => {
      return { ...state, ...currency };
    },
    updateMultiBalance: (state: CurrencyState, multiBalance: IMultiBalance) => {
      const { moduleCodeCoin, moduleCodeAmount, moduleCodeOldVersionCash, highVlaueModuleCode } = config;
      const coin = multiBalance[moduleCodeCoin]?.balance || 0;
      const amount = multiBalance[moduleCodeAmount]?.balance || 0;
      const oldCashAmount = multiBalance[moduleCodeOldVersionCash]?.balance || 0;
      const highVlaueAmount = multiBalance[highVlaueModuleCode]?.balance || 0;
      const blendHighValueAmount = highVlaueAmount + amount;

      return {
        ...state,
        coin,
        amount: blendHighValueAmount,
        oldCashAmount,
      };
    },
    setFlowList: (state: CurrencyState, flowList: IFlowItem[], pageNo: number): CurrencyState => {
      const flowWithTypeList: IFeFlowItem[] = flowList.map(formatFlow2Fe);
      console.log('flowWithTypeList:', flowWithTypeList);
      const newList = pageNo > state.coinPageNo ? state.flowList.concat(flowWithTypeList) : flowWithTypeList;
      return {
        ...state,
        flowList: newList,
      };
    },
    setCashFlowList: (state: CurrencyState, flowList: IFlowItem[], pageNo: number): CurrencyState => {
      const flowWithTypeList: IFeFlowItem[] = flowList.map(formatFlow2Fe);
      const newList = pageNo > state.cashPageNo ? state.cashFlowList.concat(flowWithTypeList) : flowWithTypeList;
      return {
        ...state,
        cashFlowList: newList,
      };
    },
    setEquityFlowList: (state: CurrencyState, flowList: IEquityFlowItem[], pageNo: number): CurrencyState => {
      const newList = pageNo > state.equityPageNo ? state.equityFlowList.concat(flowList) : flowList
      return {
        ...state,
        equityPageNo: pageNo,
        equityFlowList: newList,
      };
    },
  },
  effects: (dispatch) => ({
    /** 更新全部资产 */
    async queryAllCurrency(firstInit = false, rootState): Promise<CurrencyState> {
      await dispatch.currency.queryMultiBalance(firstInit);

      // 1/6接口下线
      // if (firstInit) {
      //   await dispatch.currency.transferAssetBalance({});
      // }
      return rootState.currency;
    },
    async queryMultiBalance(firstInit, rootState) {
      const { kps = '' } = rootState.user;
      const { moduleCodeCoin, moduleCodeAmount, moduleCodeOldVersionCash, highVlaueModuleCode } = config;
      try {
        let resData;
        if (firstInit && getGlobalFirstData()?.multiBalance) {
          resData = getGlobalFirstData()?.multiBalance;
        } else {
          resData = await queryUserMultiBalance({
            kps,
            moduleCodeList: `${moduleCodeCoin},${moduleCodeAmount},${moduleCodeOldVersionCash},${highVlaueModuleCode}`,
            toModuleCode: moduleCodeAmount
          });
        }

        trackerAjax.success({ msg: '查询资产成功', bl1: JSON.stringify(resData) });
        dispatch.currency.updateMultiBalance(resData?.balanceInfo);

        dispatch?.currency.updateCurrency({
          exchangeWay: resData?.exchangeWay,
          exchangeRate: resData?.rate,
          exchangeAllocation: resData?.settings,
        });
        return resData;
      } catch (error) {
        // const msg = (error && error.message) || JSON.stringify(error);
        const { errCode, msg } = getErrorInfo(error);
        trackerAjax.fail({ msg: '查询资产失败', c1: `${errCode}_${msg}` });
      }
    },
    async fetchFlowList(params: {type: 'coin' | 'cash', pageNo: number}, rootState) {
      const { highVlaueAppid, highVlaueModuleCode, moduleCodeCoin, moduleCodeAmount, appId } = config;
      const moduleCode = params.type === 'coin' ? moduleCodeCoin : moduleCodeAmount;
      // const { moduleCode, appId } = config.ASSET_PARAMS;
      const { kps = '' } = rootState.user;
      const baseReqParams: IBaseAssetReqParam = {
        appId,
        moduleCode,
        kps,
        stateList: 'PAYOUT_CONFIRM,INCOME_CONFIRM,PAYOUT_PRE',
        pageNum: params.pageNo || 1,
        pageSize: 100,
        queryCutOffTime: new Date(new Date().getTime() - 30 * 24 * 3600 * 1000).getTime(),
      };

      /** 高价值红包现金 */
      const highvalueHttp: IBaseAssetReqParam = {
        ...baseReqParams,
        appId: highVlaueAppid,
        moduleCode: highVlaueModuleCode,
      };
      try {
        // const flowList: IFlowItem[] = await network.get(api, baseReqParams);
        const api = `${config.coralHost}/currency/v1/queryFlowList`;
        let flowHttp = [network.get(api, baseReqParams)]
        if (params?.type === 'cash') {
          flowHttp.push(network.get(api, highvalueHttp));
        }
        const resList = await Promise.all(flowHttp);
        let newFlowList: IFlowItem[] = [];
        resList?.forEach((item) => {
          newFlowList = [...newFlowList, ...item];
        });
        newFlowList.sort((pre, next) => next?.createTime - pre?.createTime);

        newFlowList = (newFlowList ?? []).filter((item) => item?.title !== '迁移');
        if (params.type === 'cash') {
          dispatch.currency.setCashFlowList(newFlowList, params.pageNo);
          dispatch.currency.updateCurrency({
            cashPageNo: params.pageNo,
          });
        } else {
          dispatch.currency.setFlowList(newFlowList, params.pageNo);
          dispatch.currency.updateCurrency({
            coinPageNo: params.pageNo,
          });
        }
        trackerAjax.success({ msg: '查询流水成功', bl1: JSON.stringify(resList) });
      } catch (e) {
        const { errCode, msg } = getErrorInfo(e);
        trackerAjax.success({ msg: '查询流水失败', c1: `${errCode}_${msg}` });
      }
    },
    async fetchTotalCashIncome(params = {}, rootState) {
      const { appId, moduleCodeAmount } = config;
      const { kps } = rootState.user;
      const queryParams: IBaseAssetReqParam = {
        appId,
        moduleCode: moduleCodeAmount,
        beginTime: 0,
        status: 'INCOME_CONFIRM',
        queryAppIds: '',
      };
      if (kps) {
        queryParams.kps = kps;
      }

      const api = `${config.coralHost}/currency/v1/queryPeriodBalance`;
      const periodResData: { amount: number; count: number } = await network.get(api, queryParams);
      dispatch.currency.updateCurrency({
        totalIncomeAmount: periodResData.amount,
      });
    },
    // // 资产迁移，原主端福利资产迁移到极速版 1/6 接口下线
    // async transferAssetBalance(_, rootState) {
    //   const { appId, moduleCodeAmount } = config;
    //   const { kps, ut } = rootState.user;
    //   const timestamp = Date.now();
    //   const nonce = Date.now();
    //   const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
    //   const signOriText = `${timestamp}${nonce}${kps}${ut}${appId}`;
    //   // console.log('signOriText:', signOriText);
    //   const sign = await ucapi.spam.sign({ text: signOriText, salt });
    //   const reqParams = {
    //     appId,
    //     kps,
    //     toModuleCodeList: moduleCodeAmount,
    //     timestamp,
    //     nonce,
    //     sign,
    //   };
    //   try {
    //     const api = `${config.coralHost}/currency/v1/transferAssetBalance`;
    //     const res = await network.get(api, reqParams);
    //     console.log('transferAssetBalance res:', res);
    //     dispatch.currency.queryMultiBalance(false);
    //   } catch (e) {
    //     console.log('transferAssetBalance error:', e);
    //   }
    // },

    /** 查询权益流水列表 */
    async queryAwardList(params: {pageNo: number}, rootState) {
      const { appId, equity_act_id } = config;
      const { kps = '', service_ticket } = rootState.user;
      const equityMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_EQUITY_FLOW, {sampleRate: 1})
      try {
        const api = `${config.coralHost}/award/queryAwardList`;
        const awardListRes = await network.get(api, {
          appId,
          actId: equity_act_id,
          kps,
          serviceTicket: service_ticket,
          tag: 'uc_task_award,uc_coin_exchange_award,uc_cash_exchange_award',
          pageIndex: params.pageNo || 1,
          pageSize: 50,
          queryCutOffTime: new Date(new Date().getTime() - 30 * 24 * 3600 * 1000).getTime(),
          needExpireAward: true,
          useAppId: false,
        })
        if (awardListRes?.items?.length) {
          equityMonitor.success({
            msg: '查询成功',
            bl1: JSON.stringify(awardListRes),
          })
          dispatch.currency.setEquityFlowList(awardListRes?.items, params.pageNo);
        }else {
          equityMonitor.fail({
            msg: '查询失败-空数据',
            bl1: JSON.stringify(awardListRes),
          })
        }
      } catch (error) {
        const { errCode, msg  } = getErrorInfo(error)
        equityMonitor.fail({
          msg: '查询失败-catch',
          c1: `${errCode}_${msg}`,
          bl1: JSON.stringify(error),
        })
      }
    },

    // 查汇率和兑换方式
    async queryExchangeInfo(_, rootState) {
      const exchangeInfoMonitor = tracker.Monitor(WPK_CATEGORY_MAP.EXCHANGE_INFO);
      try {
        const { appId, moduleCodeAmount } = config;
        const { kps } = rootState.user;
        const reqParams = {
          appId,
          kps,
          toModuleCode: moduleCodeAmount,
        };
        const res = await network?.get(`${config.coralHost}/currency/v1/queryExchangeInfo`, reqParams);
        dispatch?.currency?.updateCurrency({
          exchangeWay: res?.exchangeWay,
          exchangeRate: res?.rate,
          exchangeAllocation: res?.settings,
        });
        exchangeInfoMonitor.success({
          msg: '查汇率和兑换方式-成功',
          c1: res?.rate,
          c2: res?.exchangeWay,
          bl1: JSON.stringify(res),
        });
      } catch (error) {
        exchangeInfoMonitor.fail({
          msg: '查汇率和兑换方式-失败',
          bl1: JSON.stringify(error),
        });
      }
    },
    // 切换兑换方式
    async cutExchangeWay(exchangeWay: 'manual' | 'auto', rootState) {
      const cutExchangeWayMonitor = tracker.Monitor(WPK_CATEGORY_MAP.CUT_EXCHANGE_WAY);
      try {
        const { appId, moduleCodeAmount } = config;
        const { kps } = rootState.user;
        const reqParams = {
          appId,
          kps,
          toModuleCode: moduleCodeAmount,
          exchangeWay,
        };
        const res = await network.get(`${config.coralHost}/currency/v1/chooseManualAuto`, reqParams);
        dispatch?.currency?.queryExchangeInfo({});
        cutExchangeWayMonitor.success({
          msg: '切换兑换方式-成功',
          c1: exchangeWay,
          bl1: JSON.stringify(res),
        });
      } catch (error) {
        cutExchangeWayMonitor.fail({
          msg: '切换兑换方式-失败',
          c1: exchangeWay,
          bl1: JSON.stringify(error),
        });
      }
    },
    // 手动兑换元宝
    async transferAsset(fromModuleNum = 0, rootState) {
      const transferAssetMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TRANSFER_ASSET);
      try {
        const { appId, moduleCodeAmount } = config;
        const { kps, ut } = rootState.user;
        const timestamp = Date.now();
        const nonce = Date.now();
        const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
        const signOriText = `${timestamp}${nonce}${kps}${ut}${appId}`;
        const sign = await ucapi.spam.sign({ text: signOriText, salt });
        const reqParams = {
          appId,
          kps,
          fromModuleNum,
          toModuleCode: moduleCodeAmount,
          nonce,
          timestamp,
          sign,
        };
        const res = await network.get(`${config.coralHost}/currency/v1/transferAsset`, reqParams);
        if (res?.fromBalance) {
          transferAssetMonitor.success({
            msg: '手动兑换元宝-成功',
            c1: fromModuleNum,
            bl1: JSON.stringify(res),
          });
          dispatch?.currency?.queryAllCurrency(false);
          Promise.all([
            dispatch?.currency?.fetchFlowList({
              type: 'coin',
              pageNo: 1,
            }),
            dispatch?.currency?.fetchFlowList({
              type: 'cash',
              pageNo: 1,
            }),
          ]);
          dispatch?.currency?.fetchTotalCashIncome({})
          // 更新资产
          return true;
        }
        transferAssetMonitor.fail({
          msg: '手动兑换元宝-失败',
          c1: fromModuleNum,
          bl1: JSON.stringify(res),
        });
        return false;
      } catch (error) {
        transferAssetMonitor.fail({
          msg: '手动兑换元宝-失败',
          c1: fromModuleNum,
          bl1: JSON.stringify(error),
        });
        return false;
      }
    },
  }),
});

export default currency;
