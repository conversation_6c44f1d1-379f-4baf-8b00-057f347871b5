import { RootModel, store } from '@/store';
import tracker from '@/lib/tracker';
import { createModel } from '@rematch/core';
import config from '@/config';
import { getParam, isNovelField } from '@/lib/qs';
import { IQueryResourceInfo, IResourceState, ResourceDescriptions } from './types';
import { completeTask, geneTaskRequestId, hiddenTimePeriodTask, hiddenNotStartTask } from '../task/helper';
import network from '@/lib/network';
import { getMainVersion } from '@/http';
import { getErrorInfo } from '../task/task_util';
import { TASK_EVENT_TYPE, TaskInfo, TASK_STATUS, HAS_RECEVICE_TASK_STATUS } from '../task/types';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { getUserInfo } from '@/lib/ucapi';
import { getGlobalFirstData } from '@/lib/render-utils/csr';
import { checkTaskFinished, getExtraInfo, getTaskAppInstallMap, isIncentiveAdTask } from '@/pages/index/task/help';
import { EventOrder, handleVideoStoreTaskList } from './utils';
import { getAppInstallNotInstallMapByLocalStorage, getAppInstallWithExpiryMinutes } from '@/lib/utils/app_install';
import { isIOS } from '@/lib/universal-ua';
import { LoginStatus } from '../user';

const { TASK_DOING, TASK_COMPLETED, TASK_CONFIRMED } = TASK_STATUS

const State: IResourceState = {
  uc_piggy_high_value: null,
  uc_piggy_novel: null,
  uc_piggy_clouddrive: null,
  uc_piggy_tag_person: null,
  uc_piggy_back_intercept: null,
  uc_pigg_back_tag: null,
  uc_piggy_task_nest: null,
  uc_piggy_limit_time: null,
  uc_piggy_xssvideo_fuli: null,
  resData: null,
  hasCompleteMultiAutoTask: false,
  resourceAdTaskList: [],
  resourceTaskList: [],
  dealWithUninstallTaskList: [],
};

const resource = createModel<RootModel>()({
  state: State,
  reducers: {
    updateState(state: IResourceState, payload: Partial<IResourceState>): IResourceState {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: (dispatch) => ({
    /**
     * 单个资源位列表 接口请求
     */
    async queryResourceInfo(code: string): Promise<IQueryResourceInfo> {
      const { coralHost, appId } = config;
      const { kps = '' } = store.getState().user;
      const requestId = geneTaskRequestId();
      const fve = getMainVersion();
      const entry = getParam('entry') || 'unknown';
      const query = {
        code,
        kps,
        requestId,
        entry,
        appId,
        fve,
        activeUser: 1,
        prioritySize: 1
      };
      return network.get(`${coralHost}/uclite/queryByResource`, query, {
        originalResponse: true
      });
    },
    /**
     * 多个资源位列表 接口请求
     */
    async queryByMultiResourceInfo(codes: string): Promise<ResourceDescriptions> {
      const { coralHost, appId } = config;
      let { kps = '' } = store.getState().user;
      // 如果KPS为空, 等getUserInfo 回来再拿一下
      if(!kps){
        const userInfo: any = await getUserInfo();
        kps = userInfo?.kps_wg;
      }
      const requestId = geneTaskRequestId();
      const fve = getMainVersion();
      const query = {
        codes,
        kps,
        requestId,
        appId,
        fve,
        activeUser: 1,
        prioritySize: 1
      };
      return network.get(`${coralHost}/uclite/queryByMultiResource`, query, {
        originalResponse: true
      });
    },

    /**
     * 批量获取资源数据，并更新
     * 实际最多四个
     */
    async getResourceAllDate(params: { firstInit?: boolean; resData: ResourceDescriptions | null }) {
      const { taskResourceCode, novelResourceCode, clouddriveResourceCode, tagPersonResourceCode, backInterceptResourceCode, backTagResourceCode, taskNestResourceCode, limitTaskResourceCode, videoTimeResourceCode} = config;
      const codes = [taskResourceCode, novelResourceCode, clouddriveResourceCode, tagPersonResourceCode, backInterceptResourceCode, backTagResourceCode, taskNestResourceCode, limitTaskResourceCode, videoTimeResourceCode].join(',');
      const resourceMonitor = tracker.Monitor(184);
      const isNovelSub = isNovelField();
      try {
        let response;
        if (params?.firstInit && params?.resData) {
          response = params.resData;
        } else if(params?.firstInit && getGlobalFirstData()?.resourceCodesInfoRes){
          response = getGlobalFirstData()?.resourceCodesInfoRes;
        } else {
          response = await dispatch.resource.queryByMultiResourceInfo(codes);
        }
        if (!(response?.data && Object.keys(response?.data).length)) {
          resourceMonitor?.fail({
            msg: '批量获取资源失败',
            c1: codes,
            c2: (response as any)?.code || '',
            c4: params.firstInit,
            c5: isNovelSub ? '接口失败-小说分场数据为空': '',
            bl1: JSON.stringify(response),
          });
          return;
        }
        // 任务上下线
        const taskCodeList = [
          backInterceptResourceCode, 
          taskNestResourceCode, 
          taskResourceCode, 
          novelResourceCode,
          clouddriveResourceCode,
          tagPersonResourceCode,
          backTagResourceCode,
          limitTaskResourceCode
        ];
        taskCodeList.forEach(code => {
          if(response?.data?.[code]?.taskList?.length) {
            response.data[code].taskList = hiddenTimePeriodTask(response.data[code].taskList, response.timestamp)
            response.data[code].taskList = hiddenNotStartTask(response.data[code].taskList, response.timestamp)
          }
        });
        // 获取资源位的所有任务
        const allResourceTasks = Object.values(response?.data as ResourceDescriptions)?.map((item)=> item?.taskList)?.flat(Infinity) as TaskInfo[];
        // 过滤未做完的激励广告任务
        const adTaskList = allResourceTasks?.filter((item)=>isIncentiveAdTask(item) && !checkTaskFinished(item));
        // 处理安装检测
        dispatch.task.dealwidthUnistallAppTask({taskList: allResourceTasks, type: 'resource', firstInit: params?.firstInit});
        dispatch.task.updateNow(response.timestamp);

        // 限时任务资源 已做完任务过滤
        if(response?.data[limitTaskResourceCode]?.taskList?.length) {
          let limitTaskList = response.data[limitTaskResourceCode]?.taskList?.filter((item)=> !checkTaskFinished(item));
          limitTaskList = limitTaskList.sort((a, b) => {
            const eventA = EventOrder[a.event] || 4;
            const eventB = EventOrder[b.event] || 4;
            return eventB - eventA;
          });
          response.data[limitTaskResourceCode].taskList = limitTaskList;
        };

        // 合一页子任务处理
        if(response?.data[videoTimeResourceCode]?.taskList?.length) {
          const videoTimeResp = response.data[videoTimeResourceCode];
          const videoTimeResourceCodeObj = handleVideoStoreTaskList(videoTimeResp);
          response.data[videoTimeResourceCode] = videoTimeResourceCodeObj;
        }

        dispatch.resource.updateState({
          ...response.data,
          resData: response,
          resourceAdTaskList: adTaskList,
          resourceTaskList: allResourceTasks,
        });

        const { hasCompleteMultiAutoTask = false } = store.getState().resource;
        // 加个兜底，避免重复触发
        if(params.firstInit && !hasCompleteMultiAutoTask){
          dispatch.resource.updateState({
            hasCompleteMultiAutoTask: true,
          });
          setTimeout(()=> {
            dispatch.resource.completeMultiAutoTask();
          }, 0);
        }

        // 高价值
        if (response.data[taskResourceCode]) {
          dispatch.highValueTask.queryHighValueTask({
            firstInit: true,
            resData: {
              ...response,
              data: response.data[taskResourceCode]
            },
          });
        }

        // 小说分场监控处理
        let novelMsg = '';
        if (isNovelSub) {
          novelMsg = response?.data[novelResourceCode]?.taskList?.length > 0 ? '接口成功-小说分场数据获取成功': '接口成功-小说分场数据获取失败';
        }
        resourceMonitor?.success({
          msg: '批量获取资源成功',
          c1: codes,
          c4: params.firstInit,
          c5: novelMsg,
          bl1: JSON.stringify(response),
        });
      } catch (error) {
        const { errCode, msg } = getErrorInfo(error);
        resourceMonitor?.fail({
          msg: '批量获取资源失败',
          c1: codes,
          c2: errCode,
          c3: msg,
          c4: params.firstInit,
          c5: isNovelSub ? '接口失败-小说分场数据为空': '',
          bl2: JSON.stringify(error),
        });
      }
    },

    /**
     * 串行接口请求
     */
    async serialTaskTrigger(taskList: TaskInfo[]) {
      let { kps } = store.getState().user;
      if(!kps){
        const userInfo: any = await getUserInfo();
        kps = userInfo?.kps_wg;
      }
      const taskCompleteMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE, {sampleRate: 1});
      // 调整完成任务监控，版本覆盖前同时存在
      const taskCompleteNewMonitor = tracker.Monitor(WPK_CATEGORY_MAP.TASK_COMPLETE_NEW);

      const completeTaskApi = async (task: TaskInfo)=> {
        let result;
        try {
          result = await completeTask({ type: 'complete', id: task.id, kps, useUtCompleteTask: task.useUtCompleteTask, publishId: task.publishId, params: {
            task
          } });
          if (result?.state === TASK_COMPLETED || result?.state === TASK_CONFIRMED) {
            taskCompleteMonitor.success({
              msg: '自动完成任务-成功',
              c1: task.id ?? '',
              c2: task.name ?? '',
              c4: 'true',
              c5: result?.state,
              bl1: JSON.stringify(result || {}),
              bl2: JSON.stringify(task),
            });
            taskCompleteNewMonitor.success({
              msg: String(task.id),
              c1: '自动完成任务-成功',
              c2: task.name ?? '',
              c4: 'true',
              c5: result?.state,
              c7: task?.event ?? result?.curTask?.event ?? '',
              bl1: JSON.stringify(result || {}),
              bl2: JSON.stringify(task),
            });
          } else {
            // 完成任务失败
            taskCompleteMonitor.fail({
              msg: '自动完成任务-失败',
              c1: task.id ?? '',
              c2: task.name ?? '',
              c4: 'true',
              c5: result?.state,
              bl1: JSON.stringify(result || {}),
              bl2: JSON.stringify(task),
            });
            taskCompleteNewMonitor.fail({
              msg: String(task.id),
              c1: '自动完成任务-失败',
              c2: task.name ?? '',
              c4: 'true',
              c5: result?.state,
              c7: task?.event ?? result?.curTask?.event ?? '',
              bl1: JSON.stringify(result || {}),
              bl2: JSON.stringify(task),
            });
          }
        } catch (error) {
          taskCompleteMonitor.fail({
            msg: '自动完成任务-catch',
            c1: task.id ?? '',
            c2: task.name ?? '',
            c3: getErrorInfo(error).errCode,
            c4: 'true',
            bl1: JSON.stringify(getErrorInfo(error)),
            bl2: JSON.stringify(task),
          });
          taskCompleteNewMonitor.fail({
            msg: String(task.id),
            c1: '自动完成任务-catch',
            c2: task.name ?? '',
            c3: getErrorInfo(error).errCode,
            c4: 'true',
            c7: task?.event,
            bl1: JSON.stringify(getErrorInfo(error)),
            bl2: JSON.stringify(task),
          });
        }
        return result;
      }

      const results: any[] = [];
      for (const task of taskList) {
        const res = await completeTaskApi(task);
        results.push(res);
      }
      return results;
    },


    /**
     * 批量处理到访任务/见面礼任务接口
     * 实际最多四个
     */
    async completeMultiAutoTask(){
      const {uc_piggy_clouddrive , uc_piggy_novel, uc_piggy_tag_person } = store?.getState()?.resource || {};
      const envSub = getParam('evSub') || '';
      const taskList = store?.getState().task.taskList
      const entry = getParam('entry') || '';
      console.log('evSub', envSub);
      const AUTO_TASK_EVENT = [TASK_EVENT_TYPE.FULI_MEET_GIFT, TASK_EVENT_TYPE.FULI_LIMIT_SIGNIN];
      const taskCompleteList: TaskInfo[] = [];
      // 小说业务
      if(uc_piggy_novel?.taskList?.length && envSub.includes('novel')) {
        const novelTask = uc_piggy_novel.taskList.filter(task => {
          return task?.state === TASK_DOING && AUTO_TASK_EVENT.includes(task.event);
        });
        taskCompleteList.push(...novelTask);
      }


      // 网盘业务
      if(uc_piggy_clouddrive?.taskList?.length && envSub.includes('clouddrive')) {
        const clouddriveTask = uc_piggy_clouddrive.taskList.filter(task => {
          return task?.state === TASK_DOING && AUTO_TASK_EVENT.includes(task.event);
        });
        taskCompleteList.push(...clouddriveTask);
      }

      //标签人群业务
      if(uc_piggy_tag_person?.taskList?.length) {
        const tagPersonList = uc_piggy_tag_person.taskList.filter(task => {
          return task?.state === TASK_DOING && AUTO_TASK_EVENT.includes(task.event);
        });
        taskCompleteList.push(...tagPersonList);
      }
      let hasEntryVisitTask = false
      // 处理固定入口到访任务
      const user = store?.getState()?.user;
      const isLogin = user?.isLogin
      const clientType = store.getState()?.app?.clientType;
      const isLite = clientType === 'UCLite';
      const entryVisitTasks = taskList?.filter(task => {
        if (task?.event === TASK_EVENT_TYPE.FULI_MEET_GIFT && task?.state === TASK_DOING) {
          // 通过任务的扩展参数entry来匹配当前访问的entry
          const extraObj = getExtraInfo(task)
          return extraObj?.[isLite ? 'ucLite' : 'uc']?.entryGuideConfig?.entryList?.includes(entry);
        }
        return false;
      });
      console.log({entryVisitTasks,taskList},'entryVisitTasks')
      if (entryVisitTasks?.length && isLogin) {
        hasEntryVisitTask = true
      }
      isLogin && taskCompleteList.push(...entryVisitTasks);
      console.log({
        taskCompleteList,
        isLogin
      })
      if(!taskCompleteList.length){
        return;
      }

      try {
      console.log(taskCompleteList,'taskCompleteList')
       const result = await dispatch.resource.serialTaskTrigger(taskCompleteList);
       console.log('result', result);
        dispatch.resource.getResourceAllDate({
          firstInit: false,
          resData: null
        });
        hasEntryVisitTask && dispatch.task.queryTask(false)
      } catch (error) {
        console.error('result', error);
        dispatch.resource.getResourceAllDate({
          firstInit: false,
          resData: null
        });
        hasEntryVisitTask && dispatch.task.queryTask(false)
      }
    },

    /**
     * 首屏批量资源位处理
     */
    firstScreenResourceListHandler(taskList: TaskInfo[]) {
      const appInstallMap = store.getState().app.appInstallMap;
      const appNotInstallMap = getAppInstallNotInstallMapByLocalStorage();
      const list: any[] = [];
      for (const item of taskList) {
        // 需要对在做的任务进行安装检测
        const pkg = getTaskAppInstallMap(item);
        if (!pkg) {
          list.push({
            id: item?.id,
            name: item?.name,
            display: true
          });
          continue;
        }
        
        const { showUninstallApp = false, iosCheckInstall = true, andCheckInstall = true } = getExtraInfo(item);
        let install: Boolean | null | undefined = null;
        // 有缓存数据，则取缓存的数据
        if (appInstallMap.get(pkg)) {
          install = !!appInstallMap.get(pkg)?.installed;
        }
        // 缓存的数据，如果不为null,则取缓存的数据 
        if (getAppInstallWithExpiryMinutes(pkg) !== null) {
          install = !!appNotInstallMap.get(pkg)?.installed;
        }
        // 当前任务在列表的显隐, 没有找到缓存，默认展示
        let display = install === null ? true : install;
        const isCheckInstall = isIOS ? iosCheckInstall : andCheckInstall;
        // 未安装不展示
        if (showUninstallApp && install) {
          display = false;
        }
        // 安装后展示, 不安装不展示,
        if (isCheckInstall && !install) {
          display = false;
        }
        list.push({
          id: item?.id,
          name: item?.name,
          display
        });
      }
      dispatch.resource.updateState({
        dealWithUninstallTaskList: list,
      });
    },
    /**
     * 合一页子任务处理
     */
    async getVideoStoreTaskList(firstInit: boolean) {
      // 不是页面初始化请求，不需要，其他走批量资源位
      if (!firstInit) {
        return;
      }
      const resourceMonitor = tracker.Monitor(197);
      const { videoTimeResourceCode } = config;
      try {
        const res: any = await dispatch.resource.queryResourceInfo(videoTimeResourceCode);
        let taskList: TaskInfo[] = [];
        const response: IQueryResourceInfo  = res?.data || {};
        if (response?.taskList && response.taskList.length) {
          const obj = handleVideoStoreTaskList(response);
          dispatch.resource.updateState({
            [videoTimeResourceCode]: {
              ...obj,
            },
          });
          taskList = obj.taskList;
        }
        resourceMonitor?.success({
          msg: '合一页子任务请求成功',
          c1: getParam('entry'),
          c2: String(taskList.length),
          bl1: JSON.stringify(response ?? {}),
        });
      } catch (error) {
        resourceMonitor?.fail({
          msg: '合一页子任务请求失败',
          c1: getParam('entry'),
          c3: getErrorInfo(error).errCode,
          bl1: JSON.stringify(getErrorInfo(error)),
        });
      }
    }
  }),
});

export default resource;
