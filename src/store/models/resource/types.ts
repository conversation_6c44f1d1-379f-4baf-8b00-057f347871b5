import { TaskInfo } from "../task/types";


const RESOURCE_CODE_LIST = [
  'uc_piggy_high_value',
  'uc_piggy_novel',
  'uc_piggy_clouddrive',
  'uc_piggy_tag_person',
  'uc_piggy_back_intercept',
  'uc_pigg_back_tag',
  'uc_piggy_task_nest',
  'uc_piggy_limit_time',
  'uc_piggy_xssvideo_fuli'
] as const;
export type RESOURCE_CODE_LIST_TYPE = (typeof RESOURCE_CODE_LIST)[number];
export interface IQueryResourceInfo {
  attributes: Record<string, string> & {
    displayTaskIdList: string[],
    title: string;
    titleText: string;
    isUnpack: string;
    isSubUnpack: string;
  };
  taskList: TaskInfo[];
  hiddenTaskIdList: number[];
  resp: any;
}

export interface  IQueryResourceExposure{
  code: string;
  taskId: number;
  actionType: 'EXPOSURE' | 'CLICK',
  publishId: number;
}
export interface IResourceExposureResponse {
  code: string;
  msg: string;
  success: boolean;
  timestamp: number;
  traceId: string;
}

export type ResourceDescriptions = {
    [key in RESOURCE_CODE_LIST_TYPE]: IQueryResourceInfo | null;
}
export interface IResourceState extends ResourceDescriptions {
    resData: ResourceDescriptions | null;
    hasCompleteMultiAutoTask: boolean;
    resourceAdTaskList: TaskInfo[];
    resourceTaskList: TaskInfo[];
    dealWithUninstallTaskList: {
      id: number,
      name: string,
      /** 是否展示在任务列表 */
      display: boolean,
      /** 是否展示在套娃任务 */
      recommendDisplay: boolean,
    }[];
}