import { TASK_EVENT_TYPE } from "../task/types";
import { IQueryResourceInfo } from "./types";

// 限时任务 event排序
export const EventOrder = {
  [TASK_EVENT_TYPE.VIDEO_AD_BROWSE]: 3,
  [TASK_EVENT_TYPE.VIDEO_AD]: 3,
  [TASK_EVENT_TYPE.VIDEO_AD_NEW]: 3,
  [TASK_EVENT_TYPE.UCLITE_VIDEO_AD]: 3,
  [TASK_EVENT_TYPE.BRAND_TASK]: 2,
  [TASK_EVENT_TYPE.CORP_APP_TASK]: 1,
  [TASK_EVENT_TYPE.CORP_APP_TASK_NEW]: 1,
  [TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND]: 1,
};

// 处理合一页计时子任务
export const handleVideoStoreTaskList = (response: IQueryResourceInfo)=> {
  let storeTaskList = (response?.taskList || []).filter(item => item.event === TASK_EVENT_TYPE.SUB_STORE_UC_VIDEO_READ_TIME);
  // 子任务时长排序
  storeTaskList = storeTaskList.sort((a, b) => a.target - b.target);
  return {
    ...response,
    taskList: storeTaskList,
    res: response
  }
}