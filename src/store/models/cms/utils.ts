
import { ICMSRes } from "./typings";
import { isIOS } from "@/lib/universal-ua";

export const getCMSResKeyItems = (key: string, cmsData: ICMSRes) => {
  if (!cmsData[key]) {
    return null
  }
  const keyData = cmsData[key]?.items?.length && cmsData[key]?.items[0]; 
  return {
    ...keyData,
    testId: cmsData[key]?.test_id,
    testDataId: cmsData[key]?.test_data_id,
  }
}

export const dateFormatToNum = (date: string) => {
 return date ? new Date(isIOS ? date.replace(/-/g, '/') : date).getTime() : 0;
}




