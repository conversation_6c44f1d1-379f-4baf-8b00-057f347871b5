
export enum CMS_RESOURCE_CODES {
  // ADVERTISING_AND_CHANNELS = 'fuli_video_ad_config', // 激励视频slotKey 不走cms配置, 走任务扩展字段
  FULI_BANNER = 'fuli_banner',
  HUICHUAN_AD_CONFIG = 'fuli_huichuan_advertising_config',
  NOTICE_CONFIG = 'fuli_notice_config',
  SEARCH_TASK_CONFIG = 'fuli_search_channel_config',
  FULI_ANNOUNCE_POP = 'fuli_announce_pop',
  /** 福利屏蔽UC农场施肥弹框entry */
  SHIELD_FARM_POP_ENTRY = 'fuli_shield_farm_pop_entry',
  TANX_ORDER_TASK_DIALOG = 'fuli_tanx_order_task_dialog',
  DELAYED_CALLBACK = 'fuli_delayed_callback', // 换量回调延迟配置,
  /** 福利重定向芭芭农场 */
  FULI_REDIRECT_FARM = 'flz_redirect_bbnc',
  /** 运营合作诉求,APP 检测安装 */
  APP_INSTALL_CHECK = 'app_install_check',
  /** 福利资源位排序 */
  FULI_RESOURCE_SORT = 'fuli_resource_sort'
}

export interface ICmsItem {
  cms_evt: string;
  data_id: string;
  items: any[];
  start_time: string;
  end_time: string;
  test_data_id: string;
  test_id: string;
  [key: string]: any;
}

export type ICMSRes = {
  [key in CMS_RESOURCE_CODES]: ICmsItem
}

export interface ICmsState {
  initd: boolean;
  /** 搜索点击任务渠道 */
  channelEventArray: IChannelEventItem[];
  /** banner广告 */
  bannerAdList: IBannerAdItem[];
  /** 汇川banner2广告slotId */
  huichuanBanner2SlotId: string;
  /** 汇川二方APP下载任务slotId列表 */
  huichuanCorpTaskSlotList: string[];
  /** 汇川三方游戏任务slotId列表 */
  huichuanGameTaskSlotList: string[];
  /** 汇川弹窗广告slotId */
  huichuanPopSlotId: string;
  /** 汇川品牌任务广告slotId */
  huichuanBrandTaskSlotId: string;
  /** 汇川竞价广告缓存时间（小时） */
  huichuanBidAdCacheHr: number;
  /** 汇川品牌广告缓存时间（小时） */
  huichuanBrandAdCacheHr: number;
  /** 首页公告 */
  indexNoticeConfig: {
    noticeStartTime: number;
    noticeEndTime: number;
    noticeList: Array<string>;
  },
  /** 流水页公告 */
  flowNoticeConfig: {
    noticeStartTime: number;
    noticeEndTime: number;
    noticeList: Array<string>;
  },
  /** 公告弹框配置数据 */
  announcePopData: IAnnouncePopData;
  /** 屏蔽农场施肥弹框的entry */
  shieldFarmPopEntryList: string[];
  cmsResAllData: ICMSRes | null;
  cmsAppInstallCheckRes: {
    checkOpen: '0' | '1',
    appList: Array<{
      scheme: string;
      pkgName: string;
    }>
  };
  tanxOrderTaskDialog: {
    maxDailyDisplayCount: number;
    showTimeGap: number;
    countdownSeconds: number;
  },
  // 换量回调延迟配置
  delayCallbackConfig: {
    defaultDelaySeconds: number;
    entryDelayList: {
      entry: string;
      delaySeconds: number;
    }[];
  },
  // 福利资源位展示排序
  resourceCodeListSort: string[];
}

/** 公告弹框配置 */
export interface IAnnouncePopData {
  isShow: boolean;
  title: string;
  content: string;
  /** cms数据id, 每次发布的数据id都不一样 */
  mid: string;
  isIngotShow: boolean;
}

export interface IRecommendTaskPlan {
  taskId: string;
  recommendTimes: string;
  isOneOffTask: boolean | string;
  sort: number;
  name: string;
}

export interface IChannelEventItem {
  channelId: string;
  eventName: string;
}

export interface IBannerAdItem {
  id: string;
  updateFlag: string;
  startTime: number;
  endTime: number;
  img: string;
  link: string;
  android: string;
  ios: string;
  needLogin: boolean;
  isTaobao: boolean;
}
