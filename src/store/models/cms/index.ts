import { RootModel, store} from '@/store';
import tracker from '@/lib/tracker';
import { createModel } from '@rematch/core';
import config from "@/config";
import { getCmsData } from '@/http';
import {
  CMS_RESOURCE_CODES,
  ICmsState,
  ICMSRes,
  IChannelEventItem,
  IBannerAdItem,
  IAnnouncePopData
} from './typings';
import { getCMSResKeyItems, dateFormatToNum } from './utils';
import { WPK_CATEGORY_MAP } from "@/constants/tracker_category";

const State: ICmsState = {
  initd: false,
  channelEventArray: [],
  bannerAdList: [],
  huichuanBanner2SlotId: config.HC_BRAND_BANNER2,
  huichuanCorpTaskSlotList: [],
  huichuanGameTaskSlotList: [],
  huichuanBidAdCacheHr: 6,
  huichuanBrandAdCacheHr: 2,
  huichuanBrandTaskSlotId: config.HC_BRAND_TASK,
  huichuanPopSlotId: config.HC_BRAND_POP,
  indexNoticeConfig: {
    noticeStartTime: 0,
    noticeEndTime: 0,
    noticeList: [],
  },
  flowNoticeConfig: {
    noticeStartTime: 0,
    noticeEndTime: 0,
    noticeList: [],
  },
  announcePopData: {
    isShow: false,
    title: '',
    content: '',
    mid: '',
    isIngotShow: true,
  },
  shieldFarmPopEntryList: [],
  cmsResAllData: null,
  cmsAppInstallCheckRes: {
    checkOpen: '0',
    appList: []
  },
  tanxOrderTaskDialog: {
    maxDailyDisplayCount: 10,
    showTimeGap: 0.5,
    countdownSeconds: 5,
  },
  delayCallbackConfig: {
    defaultDelaySeconds: 2,
    entryDelayList: []
  },
  resourceCodeListSort: ['uc_piggy_limit_time', 'uc_piggy_novel', 'uc_piggy_tag_person', 'uc_piggy_clouddrive']
};

const Cms = createModel<RootModel>()({
  state: State,
  reducers: {
    updateState(state: ICmsState, payload: Partial<ICmsState>): ICmsState {
      return {
        ...state,
        ...payload
      }
    },
  },
  effects: dispatch => ({
    async initCms(_, rootState){
     // 首屏cms数据初始化成功了，不再重新获取，失败情况下重试
     if (rootState.cms.initd) {
      return;
     }
    const cmsResData: ICMSRes | null = await getCmsData();
    dispatch.cms.setCmsData(cmsResData);
    return cmsResData
    },
    async setCmsData(cmsResData: ICMSRes | null) {
      if (!cmsResData) {
        tracker.log({
          category: WPK_CATEGORY_MAP.GET_CMS_ALL_DATA,
          w_succ: 0,
          msg: 'cms资源查询失败-catch',
          c1: Object.values(CMS_RESOURCE_CODES).join(','),
          c2: '0'
        });
        return;
      }
      dispatch.cms.updateState({
        initd: true,
        cmsResAllData: cmsResData,
      });
      if (Object.keys(cmsResData)?.length > 0) {
        dispatch.cms.getResourceCodeListSort(cmsResData);
        dispatch.cms.getBannerAdData(cmsResData);
        dispatch.cms.getHuiChuanAdConfig(cmsResData);
        dispatch.cms.getNoticeConfigData(cmsResData);
        dispatch.cms.getSearchTaskEventData(cmsResData);
        dispatch.cms.getAnnounceConfigData(cmsResData);
        dispatch.cms.getShieldFarmPopEntry(cmsResData);
        dispatch.cms.getAppInstallCheckByResourceCode(cmsResData);
        dispatch.cms.getTanxOrderTaskDialog(cmsResData);
        dispatch.cms.getDelayCallbackConfig(cmsResData);
      } else {
        tracker.log({
          category: WPK_CATEGORY_MAP.GET_CMS_ALL_DATA,
          w_succ: 0,
          msg: 'cms资源查询失败-空数据',
          c1: Object.values(CMS_RESOURCE_CODES).join(','),
        })
      }
    },
    /**
     * 获取banner广告数据
     * @param cmsData
     */
    async getBannerAdData(cmsData: ICMSRes){
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.FULI_BANNER, cmsData);
      if (data) {
        const bannerData: IBannerAdItem = {
          ...data,
          needLogin: data?.needLogin ? JSON.parse(data?.needLogin) : false,
          isTaobao: data?.isTaobao ? JSON.parse(data?.isTaobao) : false,
          startTime: dateFormatToNum(data?.startTime ),
          endTime: dateFormatToNum(data?.endTime),
        }
        dispatch.cms.updateState({
          bannerAdList: [{...bannerData}]
        })
      }

    },
    /**
     * 获取汇川广告资源配置
     * @param cmsData
     */
    async getHuiChuanAdConfig(cmsData){
      const adData = getCMSResKeyItems(CMS_RESOURCE_CODES.HUICHUAN_AD_CONFIG, cmsData);
      if (adData) {
        const {
          huichuanBanner2SlotId,
          huichuanCorpTaskSlotList,
          huichuanGameTaskSlotList,
          huichuanPopSlotId,
          huichuanBrandTaskSlotId,
          huichuanBidAdCacheHr,
          huichuanBrandAdCacheHr
        } = adData;

        dispatch.cms.updateState({
          huichuanBanner2SlotId,
          huichuanCorpTaskSlotList,
          huichuanGameTaskSlotList,
          huichuanPopSlotId,
          huichuanBrandTaskSlotId,
          huichuanBidAdCacheHr: Number(huichuanBidAdCacheHr),
          huichuanBrandAdCacheHr: Number(huichuanBrandAdCacheHr),
        })
      }

    },
    /**
     * 获取公告配置数据
     * @param cmsData
     */
    async getNoticeConfigData(cmsData: ICMSRes){
      const configData = getCMSResKeyItems(CMS_RESOURCE_CODES.NOTICE_CONFIG, cmsData);
      if (configData) {
        const {indexNoticeConfig, flowNoticeConfig} = configData
        const indexData: ICmsState['indexNoticeConfig'] = {
          noticeStartTime: dateFormatToNum(indexNoticeConfig?.noticeStartTime),
          noticeEndTime: dateFormatToNum(indexNoticeConfig?.noticeEndTime) ,
          noticeList: indexNoticeConfig?.noticeList
        }
        const flowData: ICmsState['flowNoticeConfig'] = {
          noticeStartTime: dateFormatToNum(flowNoticeConfig?.noticeStartTime),
          noticeEndTime: dateFormatToNum(flowNoticeConfig?.noticeEndTime) ,
          noticeList: flowNoticeConfig?.noticeList
        }

        dispatch.cms.updateState({
          indexNoticeConfig: indexData,
          flowNoticeConfig: flowData,
        })
      }

    },
    /**
     * 获取搜索任务渠道配置数据
     * @param cmsData
     */
    async getSearchTaskEventData(cmsData: ICMSRes){
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.SEARCH_TASK_CONFIG, cmsData);
      if (data) {
        const eventArray = data?.eventArray as IChannelEventItem[] || [];
        dispatch.cms.updateState({
          channelEventArray: eventArray
        })
      }
    },
    // tanx下单奖励弹窗配置
    async getTanxOrderTaskDialog(cmsData: ICMSRes) {
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.TANX_ORDER_TASK_DIALOG, cmsData);
      if (data) {
        dispatch.cms.updateState({
          tanxOrderTaskDialog: {
            maxDailyDisplayCount: Number(data.maxDailyDisplayCount),
            showTimeGap: Number(data.showTimeGap),
            countdownSeconds: Number(data.countdownSeconds)
          }
        })
      }
    },
    async getDelayCallbackConfig(cmsData: ICMSRes) {
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.DELAYED_CALLBACK, cmsData);
      if (data) {
        dispatch.cms.updateState({
          delayCallbackConfig: {
            defaultDelaySeconds: Number(data.defaultDelaySeconds),
            entryDelayList: data.entryDelayList?.map(item => {
              return {
                entry: item.entry,
                delaySeconds: Number(item.delaySeconds)
              }
            })
          }
        })
      }
    },
    /**
     * 获取公告弹框配置数据
     * @param cmsData
     */
    async getAnnounceConfigData(cmsData: ICMSRes){
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.FULI_ANNOUNCE_POP, cmsData);
      if (data) {
        const configData: IAnnouncePopData = {
          isShow: data?.isShow === 'true' ? true : false,
          title: data?.title,
          content: data?.content,
          mid: data?.mid,
          isIngotShow: data?.isIngotShow === 'true' ? true : false,
        }
        dispatch.cms.updateState({
          announcePopData: configData,
        })
      }
    },

    /**
     * 获取屏蔽UC农场施肥弹框entry配置数据
     * @param cmsData
     */
    async getShieldFarmPopEntry(cmsData: ICMSRes){
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.SHIELD_FARM_POP_ENTRY, cmsData);
      if (data) {
        dispatch.cms.updateState({
          shieldFarmPopEntryList: data?.shieldFarmPopEntryList,
        })
      }
    },

    /**
     * 运营合作诉求,APP 检测安装
     */
    async getAppInstallCheckByResourceCode(cmsData: ICMSRes){
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.APP_INSTALL_CHECK, cmsData);
      if (data) {
        dispatch.cms.updateState({
          cmsAppInstallCheckRes: data
        });
      }
    },
    /**
     * 资源位展示排序
     */
    async getResourceCodeListSort(cmsData: ICMSRes) {
      const defaultResourceCodeListSort = store.getState().cms.resourceCodeListSort ?? [];
      const data = getCMSResKeyItems(CMS_RESOURCE_CODES.FULI_RESOURCE_SORT, cmsData);
      if (data && data?.resourceConfig?.length > 0) {
        const sortMap = data?.resourceConfig.reduce((map, item) => {
          map[item.resourceCodeList] = parseInt(item.sort, 10);
          return map;
        }, {});
        const result = defaultResourceCodeListSort.sort((a, b) => {
          return (sortMap[a] || Infinity) - (sortMap[b] || Infinity);
        });
        dispatch.cms.updateState({
          resourceCodeListSort: result,
        });
      }
    },
  })
})

export default Cms;
