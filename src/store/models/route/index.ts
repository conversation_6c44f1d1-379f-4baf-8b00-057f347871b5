// import { getLock, unlock, lock } from '../../../utils/lock';
// import * as api from './api';

import tracker from '@/lib/tracker';
import { initPageLogParam } from '@/utils/log';
import { history } from '@/lib/nav';
// import event from '@/utils/event';
import { getLock, unlock, lock } from '@/utils/lock';
import toast from '@/lib/universal-toast';
import { dispatch } from '@/store';

export const enum PAGE_ID {
  /** 首页 */
  index = '/',
  /** 元宝流水页 */
  flow = 'flow',
  // 兑换页
  conversion = 'conversion',
}

export const pages = [
  '/', 'flow', 'conversion',
]

export interface PushParams {
  page: PAGE_ID;
  data?: any;
}

export interface SceneInfo {
  sceneId: string;
  sceneName: PAGE_ID | null;
  data: any;
}

export interface RouteState {
  /** 当前页面 */
  page: PAGE_ID;

  /** 窗口唯一标识 */
  sceneId: string;

  /** 窗口渲染管理 */
  sceneList: SceneInfo[];

  /** 最大堆栈深度 */
  maxStackLength: number;

  /** 传递的数据 */
  data: IRouteParam
}

interface IRouteParam {
  // 流水页标题名
  flowHeader: string;
  // 资产流水模块标识
  flowPageId: string;
}

const route = {
  state: {
    sceneId: 'scene_0',
    page: PAGE_ID.index,
    sceneList: getInitialRederList(30, PAGE_ID.index),
    maxStackLength: 30,
    data: {}
  } as RouteState,

  reducers: {
    updateScene(state, payload: Partial<RouteState>) {
      if (payload.page) {
        let page = payload.page;
        // if (page.indexOf('/') === 0) {
        //   page = page.substr(1) as any;
        // }
        if (page === PAGE_ID.index) {
          if (state.page !== page) {
            // event.emit('pageIndexReview');
            initPageLogParam('index', true);
          }
        }
        return { ...state, page };
      }
      return { ...state, ...payload };
    },

  },

  effects: {
    async push(page: PAGE_ID | PushParams, rootState) {
      // history.push('/' + page as string);
      await safeRoute(async () => {
        const targetPage = typeof page === 'string' ? page : page.page;
        const targetPageData = typeof page === 'string' ? {} : page.data || {};

        const nextScene = rootState.route.sceneList.find(item => {
          console.log('item???', item);
          return !item.sceneName
        });
        if (!nextScene) {
          toast.show('已经打开太多窗口啦');
          const error = new Error('route: too many scenes');
          tracker.logError(error, { c1: targetPage });
          return;
        }

        const { sceneId } = nextScene;
        const sceneList = rootState.route.sceneList.map(item => {
          return item.sceneId === sceneId ? { sceneId, sceneName: targetPage, data: targetPageData } : item;
        });
        dispatch.route.updateScene({ sceneList, data: targetPageData });
        await history.push('/' + targetPage);
        dispatch.route.updateScene({ sceneId, page: targetPage, })
      });
    },
    async pop() {
      history.pop();
    },
    async back() {
      history.pop();
    },
  },
};

async function safeRoute(fn) {
  const lockKey = 'SAGE_ROUTE';
  if (getLock(lockKey)) return;
  lock(lockKey);

  try {
    await fn();
  } finally {
    unlock(lockKey);
  }
}

function getInitialRederList(maxLength, defaultPageId) {
  return new Array(maxLength).join(',').split(',').map((_, index) => ({
    sceneId: `scene_${index}`,
    sceneName: index === 0 ? defaultPageId : null,
    data: null,
  }));
}
export default route;
