export interface ITransformer {
  img: string;
  imgStyle?: 'bubble' | 'img';
  title: string;
  type: 'link' | 'searchWords';
  link?: string;
  businessName?: string;
  platform: ('android' | 'ios')[];
}

export interface ITaskWPKCategoryConf {
  category: number,
  event: string[];
  limitTimes: number;
}

// 获取push通知返回
export interface IPushStateResponse {
  state: '0' | '1' | string,
  errCode:  any;
}

// 设置浏览器
export interface IDefaultBrowserResponse {
  state: '0' | '1' | string,
  enable_start_default_setting: '0' | '1' | string;
  errCode: any;
}
export interface ISecondModalConfig {
  visitTimes: number,
  taskId?: string,
  popType: EPopType,
  popImg?: string,
  popTitle?: string,
  popSubtitle?: string,
  awardAmountLimit?: number,
  icon?: string,
  btnText?: string,
  jumpLink?: string;
}

export const enum EPopType {
  Task = '1',
  Act = '2',
  Tanx = '3'
}
