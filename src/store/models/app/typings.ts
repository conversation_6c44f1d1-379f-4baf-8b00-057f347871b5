export interface ITransformer {
  img: string;
  imgStyle?: 'bubble' | 'img';
  title: string;
  type: 'link' | 'searchWords';
  link?: string;
  businessName?: string;
  platform: ('android' | 'ios')[];
}

export interface ITaskWPKCategoryConf {
  category: number,
  event: string[];
  limitTimes: number;
}

// 获取push通知返回
export interface IPushStateResponse {
  state: '0' | '1' | string,
  errCode:  any;
}

// 设置浏览器
export interface IDefaultBrowserResponse {
  state: '0' | '1' | string,
  enable_start_default_setting: '0' | '1' | string;
  errCode: any;
}