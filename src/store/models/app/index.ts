import { RootModel } from '@/store';
import { createModel } from '@rematch/core';
import { DESKTOP_TASK_EVENT, ResCompleteTask, TASK_EVENT_TYPE, TASK_STATUS, TaskInfo} from '../task/types';
import { LoginStatus } from '../user';
import { store } from '@/store/';
import taskHelper, { getSignInfo } from '../task/helper';
// import storage from '@ali/weex-toolkit/lib/storage';
import { getDayDiff } from '@/store/models/task/helper';
import Toast from "@/lib/universal-toast";
import { isAttached } from "@/lib/prerender";
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category'
import { queryCalendarReminders, hadSignCalendarReminder, queryCalendarPermission } from '@/utils/calendar_helper';
import {
  checkCurActPeriod,
  checkFromIFlowBanner,
  checkSearchModal,
  getAppInstallMapByLocalStorage,
  queryUCTaskWorker
} from './utils'
import dailyResource from '@/pages/index/actResource/daily';
import springResource from '@/pages/index/actResource/springFestival';
import { IResource } from "@/pages/index/actResource/daily";
import { inPeriod } from "@/utils/date";
import {checkInstallApp, finishSignTaskAndOpenModal, isReadTypeSearchWordsTask} from "@/pages/index/task/help";
import ucapi from '@/utils/ucapi';
import Modal from '@/components/modals/modal';
import fact from '@/lib/fact';
import storage from '@ali/weex-toolkit/lib/storage';
import { ITransformer, IPushStateResponse, IDefaultBrowserResponse } from './typings'
import { getCmsBannerAdList } from '@/utils/cms';
import { getCdAdProtect, getCdInviteRecordIn3days } from '@/utils/cd';
import { getAndUpdatePersonalizedAdSwitch } from '@/utils/huichuan';
import urlParams from "@/utils/urlParams";
import { WelfareAward, IDiamondData } from '@/store/models/task/types';
import { toModalSecond } from "./utils";
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import { MODAL_ID } from '@/components/modals';
import qs, { getEvSub } from '@/lib/qs';
import { LOCALSTORAGE_KEY } from '@/constants/localStorage';
import { STORAGE_DEFAULT_BROWSER_KEY, STORAGE_PUSH_SWITCH_KEY } from '@/constants/storage';
import { getLocalStorageWithExpiry, setLocalStorageWithExpiry } from '@/utils/localStorage';
import baseModal from '@ali/weex-rax-components/lib/base_modal';

// const isFlowPage = location.hash.includes('flow')
export interface TaobaoRtaConfig {
  androidAppKey: string;
  iosAppKey: string;
  sceneId: string;
  androidSlotId: string;
  iosSlotId: string;
  type: string;
  pkgName: string;
  openSchema: string;
  NuLevel: string; // NU用户等级
  activeLevel: string; // 拉活用户等级
  androidOpen: boolean;
  iosOpen: boolean;
  nuTaobaoInstallCheck: boolean;
  highPriorityTaskId: string[]; // RTA高优任务ID
  lowPriorityTaskId: string[]; // RTA低优任务ID
  androidSupportVersion: string;
  iosSupportVersion: string;
}


export interface IBannerAd {
  id: string;
  updateFlag?: string;
  startTime: number;
  endTime: number;
  img: string;
  link: string;
  android?: string;
  ios?: string;
  needLogin?: boolean;
  isTaobao?: boolean;
}

/** banner新手保护枚举值 */
export enum EBannerNuProtectDay {
  SEVEN = '7',
  ONE = '1',
  ZERO = '0',
}

export interface IeventArray {
  eventName: TASK_EVENT_TYPE;
  channelId: string;
}

export interface AppState extends IDiamondData {
  riskRule: string;
  firstInitd: boolean;
  animationReady: boolean;
  indexResource: IResource;
  /** 今日是否签到 */
  signed: boolean;
  installedTaobao: boolean; // 是否安装了淘宝
  // activeDays: string;
  // 上面参数貌似没用
  // 是否已经设置了签到提醒
  hadSetSignRemind: boolean;
  // 是否支持查询签到日历提醒
  isSupportSignRemind: boolean;
  // 春节活动期间，换头图
  isSpringPeriod: boolean;
  // 独立页面链接
  shakePageLink: string;
  // cms banner 列表
  bannerAdList: IBannerAd[];
  bannerAd2List: IBannerAd[];
  transformers: ITransformer[];
  // cd 参数 boolean 值
  cdFuliAdProtect: boolean,
  cdInviteRecordIn3days: boolean,
  baiduSearchWordSize: number;
  // 福利开关状态
  welfareBallState: boolean;
  bigPrizeEvents: TASK_EVENT_TYPE[];
  newWelfares: WelfareAward[];
  taobaoRtaInfo: {
    category: string;
    adInfo: {
      title: string;
      sid: string;
      price: string;
    } | null;
  } | null;

  /**
   * 客户端标识
   * UCLite - 极速版
   * UCMobile - 主端
   */
  clientType: 'UCLite' | 'UCMobile';
  ucParams: {
    pr: string;
    ve: string;
    sv: string;
    ut: string;
  } | null;
  /** 主端提现页面链接 */
  oldWithdrawlink: string;
  ucLoginTask?: {
    // 是否有UC 登录任务
    hasUcLoginTask: boolean;
    taskInfo?: TaskInfo;
  };
  /** 换量重定向配置 */
  exchangeRedirectConfig: {
    filterCity: string[];
    filterEntry: string[];
    redirectLink: string;
  },
  privacyPolicy: string;
  userAgreement: string;
  // app 是否安装
  appInstallMap: Map<string, {
    installed: boolean;
    res: Record<any, any>
  }>;
  /**
   * 逛app模块的事件列表
   */
  callAppModuleEvents: TASK_EVENT_TYPE[];
  // push通知
  pushStateResponse: IPushStateResponse | null;
  // 设置浏览器
  defaultBrowserResponse: IDefaultBrowserResponse | null;
  needPageVisibleUpdate: boolean;
}

const newSpringStartTime = new Date('2025/01/22 10:00:00').getTime();
const newSpringEndTime = new Date('2025/02/12 23:59:59').getTime();
const now = new Date().getTime();
// 福利主会场才展示春节样式, 分场不展示
const isWelfareIndex = getEvSub() === 'uclite_fuli_index';
const isSpringPeriod =  isWelfareIndex && inPeriod(newSpringStartTime, newSpringEndTime, now);
const defaultResource = isWelfareIndex && isSpringPeriod ? springResource : dailyResource;

const appState: AppState = {
  firstInitd: false,
  animationReady: false,
  indexResource: defaultResource,
  signed: false,
  installedTaobao: true,
  // activeDays: '',
  // 上面参数貌似没用
  bannerNuProtectDay: EBannerNuProtectDay.SEVEN,
  banner2NuProtectDay: EBannerNuProtectDay.SEVEN,
  withdrawLink: '',
  nuPacketLink: '',
  inviteDesc: '',
  inviteTips: '',
  inviteRule: '',
  needRecommendTaskEvent: [],
  flowRateDesc: '',
  readTaskConf: {
    newsLink: '',
    searchLink: '',
    novelLink: '',
    awardAmount: 25000,
    doubleAwardAmount: 60000,
  },
  hadSetSignRemind: false,
  isSupportSignRemind: false,
  newSpringStartTime,
  newSpringEndTime,
  isSpringPeriod,
  shakePageLink: '',
  invitePageLink: '',
  shieldInviteModule: true,
  bannerAdList: [],
  bannerAd2List: [],
  transformers: [],
  cdFuliAdProtect: false,
  cdInviteRecordIn3days: false,
  searchWordSize: 10,
  clickTypeSearchWordSize: 4,
  baiduSearchWordSize: 4,
  showAppTaskNum: 4,
  welfareBallState: (localStorage.getItem('welfareBallState') || 'ON') === 'ON',
  taskWPKCategoryConf: [],
  bigPrizeEvents: [],
  callAppModuleEvents: [
    TASK_EVENT_TYPE.APP_LINK,
    TASK_EVENT_TYPE.APP_TOKEN,
    TASK_EVENT_TYPE.CALL_APP_TOKEN,
    TASK_EVENT_TYPE.CALL_APP_LINK,
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_APP_LINK
  ],
  paramsToken: [],
  alimamaShoppingTargetTime: 30,
  eventFilterUninstalledTaobao: [],
  tanxSlotKey: '',
  iOSTanxSlotKey: '',
  newWelfares: [],
  countDownTime: 0,
  newAdTaskNodes: ['1', '3', '5', '6', '10', '15', '16', '20', '25', '30'],
  skipVideoGetAward: false,
  doubleADTaskMaxAward: 0,
  amateur: {
    taskId: 186013,
    order: 1
  },
  taobaoRtaConfig: null,
  taobaoRtaInfo: null,
  helpCenterlink: '',
  curRulelink: '',
  liteActivityRuleLink: '',
  activityRuleLink: '',
  clientType: 'UCLite',
  ucParams: null, // 全局公参，首屏初始化调用一次JSAPI获取
  oldWithdrawlink: '',
  ucLoginTask: {
    hasUcLoginTask: false
  },
  exchangeRedirectConfig: {
    filterCity: [],
    filterEntry: [],
    redirectLink: '',
  },
  appInstallMap: getAppInstallMapByLocalStorage(),
  pushStateResponse: getLocalStorageWithExpiry(STORAGE_PUSH_SWITCH_KEY),
  defaultBrowserResponse: getLocalStorageWithExpiry(STORAGE_DEFAULT_BROWSER_KEY),
  resourceCodeList: ["uc_piggy_novel", "uc_piggy_clouddrive", "uc_piggy_tag_person"],
  needAddTagTaskIdList: [],
  iosLiteOpenQueryAwardVersion: '',
  iosOpenQueryAwardVersion: '',
  // 是否在页面visible的时候更新接口数据
  needPageVisibleUpdate: true,
  riskRule: 'https://cs-center.uc.cn/index/selfservice?instance=ucby008&uc_param_str=einibicppfmivefrlantcunwsssvjbktchnnsnddds#/detail?id=106332806056'
};

const app = createModel<RootModel>()({
  state: appState,
  reducers: {
    updateState (state: AppState, payload: Partial<AppState>): AppState {
      return {
        ...state,
        ...payload
      }
    },
    updateAnimationReady(state: AppState) {
      return {
        ...state,
        animationReady: true
      }
    },
    updateWelfareBallState(state: AppState, payload: boolean) {
      return {
        ...state,
        welfareBallState: payload
      }
    },
  },
  effects: dispatch => ({
    async init(firstInit = false) {
      if (firstInit) {
        tracker.log({
          category: WPK_CATEGORY_MAP.APP_TASK_INIT,
          msg: '启动入口',
        });
        // 记录初始化开始时间
        window.__app_init_time = Date.now();
      }
      const appInitMonitor = tracker.Monitor(WPK_CATEGORY_MAP.APP_INIT);

      try {
        console.log('init data time 是否预渲染上屏', isAttached());
        const resourceData = store?.getState()?.resource?.resData;
        // 初始化用户信息
        await Promise.all([dispatch.user.setupUserInfo(firstInit), dispatch.cms.initCms({}), dispatch.resource.getResourceAllDate({
          firstInit,
          resData: resourceData
        })])

        const cmsResData = store.getState().cms.cmsResAllData;
        if (firstInit && cmsResData) {
          // 重定向
          dispatch.redirect.handleRedirectEvent(cmsResData);
        }

        // 请求货币
        dispatch.currency.queryAllCurrency(firstInit);

        await dispatch.task.queryTask(firstInit);

        // 检查并设置当前有效的活动周期
        checkCurActPeriod();

        // 请求风控接口
        dispatch.user.getUserRiskScore();

        // RTA不需要await, 客户端结果回来后处理, 202505 暂时下线RTA相关任务
        // dispatch.rta.updateTaobaoRta(firstInit);

        // 请求权益
        dispatch.rightsGift.rightsInit(firstInit);

        // 请求合一页蓄水子任务
        dispatch.resource.getVideoStoreTaskList(firstInit);

        // 检查换端任务开关状态 (2024.02.23无合作暂时下架)
        // dispatch.task.queryCallAppTaskSwitchStatus()
        queryUCTaskWorker()
        const cmsState = store.getState().cms;
        const slotList = [...cmsState.huichuanCorpTaskSlotList, ...cmsState.huichuanGameTaskSlotList];
        // 不要await，并行执行，避免阻塞页面渲染
        getAndUpdatePersonalizedAdSwitch().finally(async ()=> {
          await dispatch.ad.fetchAllTaskCorpAd({slot_ids: slotList, force: false});

          // 首次进来且有汇川的任务,宏任务作为最后执行，等待一下RTA返回
          setTimeout(()=> {
            if(firstInit && store?.getState()?.highValueTask?.hasHuiChuanTask){
              const baseCurrentOpenModal = modal?.getCurrentOpenModalObj() || {};
              if(!baseCurrentOpenModal[MODAL_ID.HIGH_VALUE_TASK]){
                dispatch.highValueTask.handleTaskVisible({showTaskDialog: firstInit});
              }
            }
          }, 0)
        });

        dispatch.ad.fetchTaskBrandAd(false);
        // dispatch.ad.fetchBannerBrandAd(false);
        dispatch.ad.fetchBannerBrandAd2(false);
        // 任务承接
        dispatch.task.scrollContainerByTaskId();
        // 处理初始化弹框
        this.handleShowDialog(firstInit);

        dispatch.user.selecBindtInfo()
        console.time('读取cms/cd耗时');
        Promise.all([
          getCdAdProtect(),
          getCdInviteRecordIn3days(),
          getCmsBannerAdList('cms_fuli_banner'),
        ]).then(values => {
          dispatch.app.updateState({
            cdFuliAdProtect: values[0],
            cdInviteRecordIn3days: values[1],
            bannerAdList: values[2],
          });
          console.timeEnd('读取cms/cd耗时');
          // 福利状态先读取本地值 待主线任务完成后再读取ucApi的值
          dispatch.app.fetchWelfareBallState()
        });

        setTimeout(() => {
          dispatch.app.updateAnimationReady();
          // 任务纯展示，延迟请求
          dispatch.task.queryDesc({});
        }, 100);
        // 获取签到提醒写入日历状态
        // dispatch.app.getSignRemindStatus();
        appInitMonitor.success({
          msg: 'success',
          c1: urlParams.getParams('entry')
        });
        if (firstInit) {
          dispatch.app.updateState({
            firstInitd: true,
          });
        }
        return;
      } catch (e) {
        if (firstInit) {
          dispatch.app.updateState({
            firstInitd: true,
          });
        }
        const msg = (e && e.message) || JSON.stringify(e);
        appInitMonitor.fail({
          msg: 'success',
          c1: urlParams.getParams('entry'),
          bl1: msg});
        Toast.show('网络不佳，请重新进入页面');
        return false;
      }
    },

    /**
     * 时间: 2024.10.24
     * diamond配置数据从yes后台取, 不再从聚合平台取(Yes-Diamond应用)
     * 接口: /uclite/query
     * 字段: frontData
     */
    // async getDiamondConf(firstInit: boolean) {
    //   if (firstInit && (getGlobalFirstData()?.diamondConf || window.__CSRFirstDataPms__)) {
    //     console.log('[store/models/app] getDiamondConf - 初始化, 首屏数据请求已经发出，不重复请求');
    //     let firstData = getGlobalFirstData();
    //     if (!firstData?.diamondConf) {
    //       console.log('[store/models/app] getDiamondConf - getFirstData 可能还没回来，等一下')
    //       firstData = await window.__CSRFirstDataPms__
    //     }
    //     if (firstData) return;
    //     // 如果fistData为null, 表示数据预取出错，需要重试
    //     console.log('[store/models/app] getDiamondConf 首屏数据请求出错，重试');
    //   }
    //   const {kps, sign, vCode} = store.getState().user
    //   const params = {
    //     kps,
    //     sign,
    //     vcode: vCode
    //   }
    //   const api = `${config.coralHost}/aggregation/home?${qs.stringify(params)}`
    //   const req_services = `[{"module":"diamond","key":"ucliteWelfare"}]`
    //   try {
    //     const resData = await network.get(api, {
    //       uc_param_str: 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicggplo',
    //       req_services,
    //     })
    //     if (resData && resData[0]?.data) {
    //       dispatch.app.updateState({
    //         ...resData[0]?.data
    //       })
    //     }
    //   } catch (e) {
    //   }
    //   dispatch.task.generateTaskList({});
    // },
    async handleShowDialog(firstInit = false) {
      const { status } = store.getState().user;
      const { taskList } = store.getState().task;
      const { inviteCode } = store.getState().invite;
      const { shieldInviteModule } = store.getState().app;
      const inviteEntryTask = taskList.find(item => item.event.includes(TASK_EVENT_TYPE.UCLITE_INVITE_ENTRY));

      // modal.openTreasure([{
      //   extra: {},
      //   rewardItem: {name: "奖励10元宝", mark: "coin", amount: 10, icon: ""},
      //   rewardType: "currency",
      //   win: true
      // }], 'coin');

      try {
        const data = await ucapi.biz.getStoreData({
          page: 'uclite-welfare-new', 	// 存储所属的页面
          id: 'invite_code_scan', // 数据存储的 key 标识位
        });
        if (data?.data?.code) {
          if (status === LoginStatus.login) {
            if (inviteEntryTask) {
              // 如果这时候还没有 获取到自己的邀请码，实时发起请求
              let myCode = inviteCode as string;
              if (!myCode) {
                myCode = await dispatch.invite.getInviteCode();
              }
              if (myCode === data?.data?.code) {
                fact.exposure('fuli_expo', {
                  c: 'qrshare_invited',
                  d: 'expo',
                  code: data?.data?.code,
                  status: 'own',
                });
                Toast.show('无法接受自己的邀请哦');
              } else {
                fact.exposure('fuli_expo', {
                  c: 'qrshare_invited',
                  d: 'expo',
                  code: data?.data?.code,
                  status: 'qrqualified',
                });
                if (shieldInviteModule) {
                  Toast.show('活动已下线');
                } else {
                  Modal.openInviteCode(data?.data?.code);
                }
              }
            } else {
              fact.exposure('fuli_expo', {
                c: 'qrshare_invited',
                d: 'expo',
                code: data?.data?.code,
                status: 'unqualified',
              });
              Toast.show('抱歉，你暂不符合活动参与条件，具体见活动规则');
            }
          } else {
            fact.exposure('fuli_expo', {
              c: 'qrshare_invited',
              d: 'expo',
              code: data?.data?.code,
              status: 'unlogged',
            });
            Modal.openLogin('登录后才能扫码接受邀请哦');
          }
        } else {
          dispatch.app.showInitModal({firstInit});
        }
        ucapi.biz.deleteStoreData({
          page: 'uclite-welfare-new', 	// 存储所属的页面
          id: 'invite_code_scan', // 数据存储的 key 标识位
        })
      } catch (err) {
        dispatch.app.showInitModal({firstInit});
      }
    },
    // async getActiveDays(_, rootState) {
    //   const { kps } = rootState.user
    //   const navRes = await network.get(`${config.navHost}/home/<USER>
    //     req_tips: 'activeDays',
    //     kps
    //   });
    //   console.log('activeDays', navRes)
    //   dispatch.app.updateState({
    //     activeDays: navRes?.tips?.activeDays
    //   })
    // },
    // 获取最新的任务状态和资产
    async updateTaskAndCurrency () {
      await Promise.all([
        dispatch.currency.queryAllCurrency(null),
        dispatch.resource.getResourceAllDate({ firstInit: false, resData: null}),
        dispatch.task.queryTask(false)
      ])
    },
    async updateAll() {
      dispatch.app.updateTaskAndCurrency();
       // 更新push开关 & 设置浏览器
      dispatch.app.getJasApiByPush({ firstInit: false});
      dispatch.app.getJasApiByBrowser({ firstInit: false });

      // 202505 暂时下线RTA相关任务
      // dispatch.rta.updateTaobaoRta();
      dispatch.task.queryDesc({})
      const cmsState = store.getState().cms
      const slotList = [...cmsState.huichuanCorpTaskSlotList, ...cmsState.huichuanGameTaskSlotList];

      // 不需要二次更新
      // dispatch.highValueTask.queryHighValueTask({
      //   firstInit: false,
      //   resData: null
      // });

      dispatch.ad.fetchAllTaskCorpAd({slot_ids: slotList, force: false});
      dispatch.user.selecBindtInfo();
    },
    async handleTaskModalsByPriority(): Promise<void> {
      const signTask = getSignInfo();
      const taskList = store.getState().task.taskList;
      // 组件任务
      const doTaskList = taskList?.filter((task)=> DESKTOP_TASK_EVENT?.includes(task?.event) && task?.state === TASK_STATUS.TASK_DOING);
      if(!doTaskList.length) {
        await finishSignTaskAndOpenModal('自动签到', true);
        return;
      }
      const signPriority = signTask?.sort || 0;
      const desktopTaskPriority = doTaskList?.[0]?.sort || 0;
      // 任务优先级比较
      if (signPriority >= desktopTaskPriority) {
        await finishSignTaskAndOpenModal('自动签到', true);
        dispatch.task.batchFinishDesktopTask();
      } else {
        await dispatch.task.batchFinishDesktopTask();
        await finishSignTaskAndOpenModal('自动签到', true);
      }
    },
    /**
     * 展示初始弹框
     */
    async showInitModal({firstInit}, rootState) {
      // TODO: 该弹窗已经不使用了，移除吧
      // await checkSearchModal();
      const { signin } = store.getState().task;
      const { status } = rootState.user;
      const highValueTaskState = store.getState().highValueTask;
      let hasModalToShow = false;

      /**
       * 公告弹框
       */
      const { announcePopData } = rootState.cms;
      const {coin = 0} = rootState?.currency || {};
      // 与上一次的cms数据id进行比较, 不一致则出公告弹框
      const cmsAnnounceId = localStorage.getItem('announce_data_id');
      if (announcePopData?.isShow && announcePopData?.content && announcePopData?.mid !== cmsAnnounceId) {
        // 元宝数不为0的弹窗
        if (announcePopData?.isIngotShow) {
          if (coin) {
            Modal.openAnnouncePop(announcePopData);
            localStorage.setItem('announce_data_id', announcePopData?.mid)
          }
        } else {
          Modal.openAnnouncePop(announcePopData);
          localStorage.setItem('announce_data_id', announcePopData?.mid)
        }
      }

      // 展示公告弹框后，去做高价值任务
      // 如果没有汇川或者RTA的任务，先去展示弹窗
      if(!(highValueTaskState.hasTaobaoRtaTask && highValueTaskState.hasHuiChuanTask)){
        await dispatch.highValueTask.handleTaskVisible({ showTaskDialog: firstInit});
      }
      if (!taskHelper.checkTodaySignInStatus() && signin.length > 0 && !taskHelper.checkSignInCompleted()) {
        // 每个自然日仅自动弹出一次
        const nuSignInDialogTimestamp = parseInt(localStorage.getItem(LOCALSTORAGE_KEY.UC_LITE_WELFARE_NEW_SIGN_DIALOG_TS) || '0');
        if (getDayDiff(rootState.task.now, nuSignInDialogTimestamp) > 0) {
          localStorage.setItem(LOCALSTORAGE_KEY.UC_LITE_WELFARE_NEW_SIGN_DIALOG_TS, `${rootState.task.now}`);
          // 同时需要出组件/签到的 根据sort进行优先级判断
          this.handleTaskModalsByPriority();
        } else {
          dispatch.task.batchFinishDesktopTask()
        }
      } else if(status === LoginStatus.login){
        toModalSecond(firstInit)
        // 
        baseModal.open(MODAL_ID.ENTRY_GUIDE)
        checkFromIFlowBanner()
        dispatch.task.batchFinishDesktopTask()
      }

      if (status !== LoginStatus.login) {
        return
      }

      let hasAd = false;
      const showPopBrandAd = async (adData) => {
        if (adData) {
          // NU当天新手保护
          const isProtect = await getCdAdProtect();
          if (!isProtect) {
            Modal.popBrandAd(adData);
          }
          hasModalToShow = true;
          hasAd = true;
        }
      };
      if (!hasAd) {
        const popBrandAdKey = 'pop_brand_ad';
        const loaded = await storage.get(popBrandAdKey)
        if (!loaded) {
          await getAndUpdatePersonalizedAdSwitch();
          const adData = await dispatch.ad.fetchPopAd(false);
          if (adData) {
            storage.setDaily(popBrandAdKey, '1');
            showPopBrandAd(adData);
          }
        }
      }

      const isShowTaskUncompletedToast = store.getState().task.isShowTaskUncompletedToast;
      if (isShowTaskUncompletedToast?.flag) {
        if (isReadTypeSearchWordsTask(isShowTaskUncompletedToast?.event)) {
          Toast.show('搜索浏览时长不够，任务未完成');
        } else {
          Toast.show('未点击搜索结果，任务未完成');
        }
        dispatch.task.setShowTaskUncompletedToast({flag: false, event: ''});
      }

      // if (!signInDialogShow) {
      //   const { taskList } = store.getState().task;
      //   const completedTaskList = taskHelper.filterCompletedTask(taskList);
      //   const doneTask = taskHelper.filterDoneTask(taskList)
      //   console.log('doneTask:', doneTask)
      //   if (completedTaskList.length) {
      //     modal.openTaskAward();
      //     hasModalToShow = true;
      //   } else {
      //     const showTaskModal = await checkTaskModalTime()
      //     if (doneTask.length < 2 && showTaskModal && !isFlowPage) {
      //       modal.openTask()
      //       storage.set('taskModalShowTime', new Date().getTime())
      //     }
      //   }
      // }

      return hasModalToShow
    },
    // 获取签到日历设置状态
    async getSignRemindStatus() {
      try {
        const permission = await queryCalendarPermission();
        if (permission.errCode) {
          dispatch.app.updateState({
            isSupportSignRemind: false,
          });
          return;
        }
        // const switchSetStatus = await storage.get(STORAGE_SIGN_SWITCH_SET_CALENDAR_KEY);
        // const taskSetStatus = await storage.get(STORAGE_SIGN_MINDER_TASK_SET_CALENDAR_KEY);
        // const dataFromDisk = await getCalendarReminderStoreData();
        // console.log('dataFromDisk: set-calendar-reminder', dataFromDisk)
        // if (permission.hasReadPermission === '1' || switchSetStatus || taskSetStatus || dataFromDisk?.data?.setCAlendarReminder) {
        if (permission?.hasReadPermission === '1') {
          try {
            const data = await queryCalendarReminders();
            if (!data.errCode && data?.events?.length > 0 && hadSignCalendarReminder(data.events)) {
              // 有写入值
              dispatch.app.updateState({
                hadSetSignRemind: true,
                isSupportSignRemind: true,
              });
            } else {
              dispatch.app.updateState({
                hadSetSignRemind: false,
                isSupportSignRemind: true,
              });
            }
          } catch (err) {
            // 不支持api
            console.log('不支持查询日历jsapi');
            dispatch.app.updateState({
              hadSetSignRemind: false,
              isSupportSignRemind: false,
            });
          }
        }
      } catch (err) {
        // 不支持api
        console.log('不支持查询日历权限jsapi');
        dispatch.app.updateState({
          hadSetSignRemind: false,
          isSupportSignRemind: false,
        });
      }
    },
    // 获取福利开关状态
    async fetchWelfareBallState() {
      const welfareBallStateMonitor = tracker.Monitor(WPK_CATEGORY_MAP.WELFAR_BALL_STATE);
      try {
        const state = await ucapi.mission.getSettingSwitch();

        if (!state || state.errCode) {
          welfareBallStateMonitor.fail({
            msg: '未获取到状态',
            bl1: JSON.stringify(state),
          });
          return
        }
        const welfareBallState = state.is_close ? 'OFF' : 'ON'
        welfareBallStateMonitor.success({
          msg: `获取成功-${welfareBallState}`,
          c1: welfareBallState,
          bl1: JSON.stringify(state),
        });
        localStorage.setItem('welfareBallState', welfareBallState)
        dispatch.app.updateWelfareBallState(!state.is_close)
      } catch (error) {
        welfareBallStateMonitor.fail({
          msg: '未获取到状态-error',
          bl1: JSON.stringify(error),
        })
      }
    },
    async checkInstalledTaobao () {
      try {
        const installedTaobao = localStorage.getItem('installedTaobao');
        if (installedTaobao) {
          dispatch.app.updateState({
            installedTaobao: installedTaobao === 'true'
          })
        }
        const [appIsInstalled] = await checkInstallApp('taobao://', 'com.taobao.taobao')
        dispatch.app.updateState({
          installedTaobao: appIsInstalled
        })
        localStorage.setItem('installedTaobao', appIsInstalled ? 'true' : 'false')
      } catch (e) {
        console.error(e);
      }
    },

    // 获取push设置
    async getJasApiByPush(params: { firstInit: boolean}) {
       if(params.firstInit) {
        const checkPushResult = await window?.__APP_PUSH_STATE__;
        dispatch.app.updateState({
          pushStateResponse: checkPushResult
        });
        setLocalStorageWithExpiry(STORAGE_PUSH_SWITCH_KEY, checkPushResult);
        return;
       }
      const pushStateResponse  = store.getState()?.app?.pushStateResponse;
      // 没有设置的时候，每次进来更新查询一次
      if(pushStateResponse?.state !== '1') {
        try {
          const checkPushState = Date.now();
          const result = await ucapi.biz.getPushState();
          dispatch.app.updateState({
            pushStateResponse: result
          });
         setLocalStorageWithExpiry(STORAGE_PUSH_SWITCH_KEY, result);
          tracker.log({
            category: 177,
            msg: '读取是否开启push权限耗时',
            wl_avgv1: Date.now() - checkPushState
           });
        } catch (err) {
          console.error('getPushState error', err);
        }
      }

    },
    // 获取浏览器设置
    async getJasApiByBrowser(params: { firstInit: boolean}) {
      if(params.firstInit) {
       const checkBrowser = await window?.__APP_DEFAULT_BROWSER__;
       dispatch.app.updateState({
         defaultBrowserResponse: checkBrowser
       });
       setLocalStorageWithExpiry(STORAGE_DEFAULT_BROWSER_KEY, checkBrowser);
       return;
      }
      const defaultBrowserResponse  = store.getState()?.app?.defaultBrowserResponse;
      // 没有设置的时候，每次进来更新查询一次
      if(defaultBrowserResponse?.state !== '1') {
        try {
          const checkDefaultBrowser = Date.now();
          const result = await ucapi.biz.getDefaultBrowser();

          dispatch.app.updateState({
            defaultBrowserResponse: result
          });
          setLocalStorageWithExpiry(STORAGE_DEFAULT_BROWSER_KEY, result);
          tracker.log({
            category: 177,
            msg: '读取是否设置默认浏览器耗时',
            wl_avgv1: Date.now() - checkDefaultBrowser
           });
        } catch (err) {
          console.error('getDefaultBrowser error', err);
        }
      }
   },
   //  运营合作诉求,APP 检测安装
   async checkAppInstall() {
    const { checkOpen = '0', appList = []} = store.getState().cms.cmsAppInstallCheckRes;
      try {
        console.log('cms appCheckInfo:', checkOpen, appList);
        if (checkOpen === '1') {
          const checkList = appList.map(item => {
            return checkInstallApp(item.scheme, item.pkgName);
          });
          await Promise.all(checkList);
        }
      } catch (err) {
        console.info(err);
      }
  }
  })
});

export default app;
