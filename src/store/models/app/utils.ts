import { dispatch, store } from '@/store';
import { inPeriod } from "@/utils/date";
// import { uc } from '@ali/weex-toolkit/lib/weex_config';
import springResource from '@/pages/index/actResource/springFestival';
import dailyResource from '@/pages/index/actResource/daily';
import modal from '@/components/modals/modal'
import {TASK_EVENT_TYPE} from "@/store/models/task/types";
import urlParams from '@/utils/urlParams';
import {checkTaskFinished, ifShowAdTask, isSearchWordsTask} from "@/pages/index/task/help";
import ucapi from "@/utils/ucapi";
import config from "@/config";
import { WORKER_EVENT_TYPE } from "@/pages/index/typings";
import {dealTaskExtra ,extraValidCheck} from '@/store/models/task/taskCheck'
import {TASK_STATUS} from '@/store/models/task/types'
import {localStorageGet,localStorageSet,getSecondsSinceMidnight, getLocalStorageWithExpiry} from '@/utils/localStorage'
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import {getParam, getEvSub} from "@/lib/qs";
import { STORAGE_APP_INSTALL_KEY } from '@/constants/storage';
import { LOCALSTORAGE_KEY } from '@/constants/localStorage';


export function checkCurActPeriod () {
  const curTime = store.getState().task?.now;
  const { newSpringStartTime, newSpringEndTime } = store.getState().app;
  const isSpringPeriod = inPeriod(newSpringStartTime, newSpringEndTime, curTime);
  // 福利主会场才展示春节样式, 分场不展示
  const isWelfareIndex = getEvSub() === 'uclite_fuli_index';
  dispatch.app.updateState({
    isSpringPeriod: isWelfareIndex && isSpringPeriod,
    indexResource: isWelfareIndex && isSpringPeriod ? springResource : dailyResource,
  })
}

export const checkFromIFlowBanner = async () => {
  const entry  = urlParams.getParams('entry')
  const { taskList } = store.getState().task
  const adTask = taskList.find(item => item.event === TASK_EVENT_TYPE.VIDEO_AD_NEW || item.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD)
  const wordsTask = taskList.find(item => isSearchWordsTask(item.event))
  const adTaskPercent = (adTask?.dayTimes?.progress || 0) / (adTask?.dayTimes?.target || 1)
  const searchWordsPercent = (wordsTask?.dayTimes?.progress || 0) / (wordsTask?.dayTimes?.target || 1)
  if ( !(adTask || wordsTask) || (adTaskPercent === 1 && searchWordsPercent === 1)) return
  const isFromIFlowBanner = entry === 'xinxiliuzhengwenye';
  const iFlowDialogShow = await localStorage.getItem('iFlowDialogShow')
  if (isFromIFlowBanner && !iFlowDialogShow) {
    if (adTask && adTaskPercent < 1) {
      localStorage.setItem('iFlowDialogShow', '1')
      modal.openTreasure(null, 'iFlow-video')
      return;
    }
    if (wordsTask && searchWordsPercent < 1) {
      localStorage.setItem('iFlowDialogShow', '1')
      modal.openTreasure(null, 'iFlow-words')
      return;
    }
  }
}

export const checkSearchModal = async () => {
  const entry = urlParams.getParams('entry');
  const startsWith: string[] = ['words_', 'video_'];
  const haveCompletedModal = await localStorage.getItem('searchModalShow')
  const searchModalType = startsWith.find(param => {
    return entry?.startsWith(param)
  })
  if (!searchModalType) return;
  if (haveCompletedModal) return;
  localStorage.setItem('searchModalShow', '1')
  modal.openSearchModal(searchModalType.startsWith('words_') ? 'search-words' : 'search-video');
}

const postWorkerQueryMsg = (times) => {
  tracker.log({
    category: WPK_CATEGORY_MAP.TASK_WORKER,
    msg: `查询worker注册信息-第${times}次`,
    sampleRate: 1,
    w_succ: 1,
    c1: '',
    c2: getParam('entry')
  })
  ucapi.base.postmessage({
    data: {
      secret: config.WORKER_SECRET,
      businessKey: config.businessKey,
      type: WORKER_EVENT_TYPE.UC_TASK_WORKER_QUERY
    }
  })
}

// 查询appWorker注册的任务，未查到则2s后重试，最多循环3次，兼容端启动后appWorker启动时机较晚问题
export const queryUCTaskWorker = () => {
  console.log('[queryUCTaskWorker]')
  const MAX_QUERY_TIMES = 5;
  let queryTimes = MAX_QUERY_TIMES;
  const intervalId = setInterval(() => {
    const workerRegisterTaskIds = store.getState().task.workerRegisterTaskIds;
    if (!workerRegisterTaskIds?.length) {
      postWorkerQueryMsg(MAX_QUERY_TIMES + 1 - queryTimes);
    }
    queryTimes--;
    if (queryTimes <= 0) {
      clearInterval(intervalId);
    }
  }, 2000);
}

/**
 *
 * @param visiteTimes
 * "modalConfig": [
            {
              "visitTimes": "int(访问次数)",
              "taskId": "string(任务id)",
              "popType": "1/2/3(推荐类型, 1: 任务 2: 活动 3: tanx下单)",
              "popImg": "img(活动图片)",
              "popTitle": "string(弹窗标题)",
              "popSubtitle": "string(弹窗副标题)",
              "awardAmountLimit": "int(展示的最小奖励数额)",
              "icon": "img(奖励icon)",
              "btnText": "string(按钮文案)"
            }
          ]

 * @returns
 */
export const getSecondPopConfig = (visiteTimes: number) => {
  const { secondPopResourceCode } = config;
  const multiResource = store?.getState()?.resource || {}
  const secondPopResourceData = multiResource?.[secondPopResourceCode];
  if (!secondPopResourceData) {
    return null;
  }
  const popConfig = secondPopResourceData.attributes?.modalConfig;
  let taskList = secondPopResourceData.taskList;
  // 筛选出可以进入弹窗的任务, 未完成/有填充的视频任务
  taskList = taskList.filter(task => {
    return !checkTaskFinished(task) && ifShowAdTask(task);
  })
  const currentWateringTimesPopConfig = popConfig?.find((item) => {
    return Number(item.wateringTimes) === visiteTimes;
  });
  if (!currentWateringTimesPopConfig) {
    return null;
  }
  const task = taskList?.find(item => item.id === Number(currentWateringTimesPopConfig.taskId));
  if (!task) {
    return null;
  }
  return {
    ...currentWateringTimesPopConfig,
    task
  }
}

// 签到后 第二次进入弹资源投放配置弹窗逻辑
export const toModalSecond = (firstInit) => {
  const { shieldFarmPopEntryList } = store.getState().cms;
  const entry = getParam('entry');
  // 如果命中屏蔽entry, 则不出施肥弹框
  if (shieldFarmPopEntryList?.includes(entry)) {
    return
  }

  if (firstInit) {
    const secondDialogVisitData = localStorage.getItem(LOCALSTORAGE_KEY.UC_PIGGY_SECOND_DIALOG_TS_VISIT)
    // 今日已弹/弹窗对应任务已完成 任务已完成不出弹窗，且消耗时机
    // 已经签到
    ucapi?.biz?.getCDParams('second_guide_popup_switch').then((res) => {
      const { value } = res;
      if (value === '1') {

      }
    });
  }
}

export const getAppInstallMapByLocalStorage = () => {
  const value = getLocalStorageWithExpiry(STORAGE_APP_INSTALL_KEY);
  if (value) {
    try {
      const obj = JSON.parse(JSON.stringify(value));
      const localDataMap = new Map(Object.entries(obj));
      return localDataMap
    } catch (error) {
      return new Map();
    }
  }
  return new Map();
};

