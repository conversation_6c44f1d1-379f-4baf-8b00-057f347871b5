import { dispatch, store } from '@/store';
import { inPeriod } from "@/utils/date";
// import { uc } from '@ali/weex-toolkit/lib/weex_config';
import springResource from '@/pages/index/actResource/springFestival';
import dailyResource from '@/pages/index/actResource/daily';
import modal from '@/components/modals/modal'
import {TASK_EVENT_TYPE} from "@/store/models/task/types";
import urlParams from '@/utils/urlParams';
import {isSearchWordsTask} from "@/pages/index/task/help";
import ucapi from "@/utils/ucapi";
import config from "@/config";
import { WORKER_EVENT_TYPE } from "@/pages/index/typings";
import {dealTaskExtra ,extraValidCheck} from '@/store/models/task/taskCheck'
import {TASK_STATUS} from '@/store/models/task/types'
import {localStorageGet,localStorageSet,getSecondsSinceMidnight, getLocalStorageWithExpiry} from '@/utils/localStorage'
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import {getParam, getEvSub} from "@/lib/qs";
import { STORAGE_APP_INSTALL_KEY } from '@/constants/storage';


export function checkCurActPeriod () {
  const curTime = store.getState().task?.now;
  const { newSpringStartTime, newSpringEndTime } = store.getState().app;  
  const isSpringPeriod = inPeriod(newSpringStartTime, newSpringEndTime, curTime);
  // 福利主会场才展示春节样式, 分场不展示
  const isWelfareIndex = getEvSub() === 'uclite_fuli_index';
  dispatch.app.updateState({
    isSpringPeriod: isWelfareIndex && isSpringPeriod,
    indexResource: isWelfareIndex && isSpringPeriod ? springResource : dailyResource,
  })
}

export const checkFromIFlowBanner = async () => {
  const entry  = urlParams.getParams('entry')
  const { taskList } = store.getState().task
  const adTask = taskList.find(item => item.event === TASK_EVENT_TYPE.VIDEO_AD_NEW || item.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD)
  const wordsTask = taskList.find(item => isSearchWordsTask(item.event))
  const adTaskPercent = (adTask?.dayTimes?.progress || 0) / (adTask?.dayTimes?.target || 1)
  const searchWordsPercent = (wordsTask?.dayTimes?.progress || 0) / (wordsTask?.dayTimes?.target || 1)
  if ( !(adTask || wordsTask) || (adTaskPercent === 1 && searchWordsPercent === 1)) return
  const isFromIFlowBanner = entry === 'xinxiliuzhengwenye';
  const iFlowDialogShow = await localStorage.getItem('iFlowDialogShow')
  if (isFromIFlowBanner && !iFlowDialogShow) {
    if (adTask && adTaskPercent < 1) {
      localStorage.setItem('iFlowDialogShow', '1')
      modal.openTreasure(null, 'iFlow-video')
      return;
    }
    if (wordsTask && searchWordsPercent < 1) {
      localStorage.setItem('iFlowDialogShow', '1')
      modal.openTreasure(null, 'iFlow-words')
      return;
    }
  }
}

export const checkSearchModal = async () => {
  const entry = urlParams.getParams('entry');
  const startsWith: string[] = ['words_', 'video_'];
  const haveCompletedModal = await localStorage.getItem('searchModalShow')
  const searchModalType = startsWith.find(param => {
    return entry?.startsWith(param)
  })
  if (!searchModalType) return;
  if (haveCompletedModal) return;
  localStorage.setItem('searchModalShow', '1')
  modal.openSearchModal(searchModalType.startsWith('words_') ? 'search-words' : 'search-video');
}

const postWorkerQueryMsg = (times) => {
  tracker.log({
    category: WPK_CATEGORY_MAP.TASK_WORKER,
    msg: `查询worker注册信息-第${times}次`,
    sampleRate: 1,
    w_succ: 1,
    c1: '',
    c2: getParam('entry')
  })
  ucapi.base.postmessage({
    data: {
      secret: config.WORKER_SECRET,
      businessKey: config.businessKey,
      type: WORKER_EVENT_TYPE.UC_TASK_WORKER_QUERY
    }
  })
}

// 查询appWorker注册的任务，未查到则2s后重试，最多循环3次，兼容端启动后appWorker启动时机较晚问题
export const queryUCTaskWorker = () => {
  console.log('[queryUCTaskWorker]')
  const MAX_QUERY_TIMES = 5;
  let queryTimes = MAX_QUERY_TIMES;
  const intervalId = setInterval(() => {
    const workerRegisterTaskIds = store.getState().task.workerRegisterTaskIds;
    if (!workerRegisterTaskIds?.length) {
      postWorkerQueryMsg(MAX_QUERY_TIMES + 1 - queryTimes);
    }
    queryTimes--;
    if (queryTimes <= 0) {
      clearInterval(intervalId);
    }
  }, 2000);
}

// 签到后 第二次进入弹芭芭农场弹窗
export const toModalFarm = (firstInit) => {
  const { shieldFarmPopEntryList } = store.getState().cms;
  const entry = getParam('entry');
  // 如果命中屏蔽entry, 则不出施肥弹框
  if (shieldFarmPopEntryList?.includes(entry)) {
    return
  } 

  if (firstInit) {
    const openModalFarm = localStorageGet('open-modal-farm')
    if (openModalFarm?.flag) return
    // 已经签到
    ucapi?.biz?.getCDParams('second_guide_popup_switch').then((res) => {
      const { value } = res;
      if (value === '1') {
        const {resTaskList} = store.getState().task
        const taskInfo = resTaskList?.find(item => {
          if (item?.extra) {
            if (!extraValidCheck(item)) return false
            let extraObj = JSON?.parse(dealTaskExtra(item?.extra))
            return extraObj?.openModalSecond
          }
          return false
        })
        if (!taskInfo) return
        if ((taskInfo?.dayTimes && taskInfo?.state !== TASK_STATUS?.TASK_FINISH)
          || taskInfo?.state !== TASK_STATUS?.TASK_CONFIRMED) {
          localStorageSet('open-modal-farm',{flag: true},getSecondsSinceMidnight())
          modal.openFarmModal(taskInfo);
        }
      }
    });
  }
}

export const getAppInstallMapByLocalStorage = () => {
  const value = getLocalStorageWithExpiry(STORAGE_APP_INSTALL_KEY);
  if (value) {
    try {
      const obj = JSON.parse(JSON.stringify(value));
      const localDataMap = new Map(Object.entries(obj));
      return localDataMap
    } catch (error) {
      return new Map();
    }
  }
  return new Map();
}; 

