import network from '@/utils/network';
// import tracker from '@/lib/tracker';
import {dispatch, store} from '@/store';
import ucapi from "@/utils/ucapi";
import config from '@/config';
import { IInviteState, IInviteUser } from './typings'
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from '@/constants/tracker_category'

const inviteState: IInviteState = {
  inviteCode: '',
  inviteRecord: {
    totalNum: 0,
    totalAmount: 0,
    list: []
    // list: [{
    //   avatar: 'https://image.uc.cn/s/uae/g/42/2021d11/cash.png',
    //   nickName: 'asdka',
    //   amount: 120,
    //   totalAmount: 300,
    //   ingAmount: 180,
    //   desc: 'TA还没用UC极速版哦 今天用满5分钟 你的奖励会在次日到账'
    // }, {
    //     avatar: 'https://image.uc.cn/s/uae/g/42/2021d11/cash.png',
    //     nickName: '阿呆',
    //     amount: 180,
    //     totalAmount: 300,
    //     ingAmount: 120,
    //     desc: 'TA还没用UC极速版哦 今天用满5分钟 你的奖励会在次日到账'
    //   }
    // ]
  },
  inviteAmount: 0, // 阶段邀请奖励
  inviteList: [],
  endTime: 0,
  inviteIntro: {
    d1: {
      times: 60,
      amount: 0,
      mark: 'cash'
    },
    d2: {
      times: 60,
      amount: 0,
      mark: 'cash'
    },
    other: {
      times: 0,
      amount: 0,
      mark: 'cash'
    }
  },
  pageNo: 1,
}

const invite = {
  state: inviteState,
  reducers: {
    update(state: IInviteState, payload: Partial<IInviteState>): IInviteState {
      return {
        ...state,
        ...payload,
        inviteCode: payload.inviteCode || state.inviteCode || ''
      };
    },
    updateInviteList(state: IInviteState, inviteList: IInviteUser[], pageNo: number): IInviteState {
      const newList = pageNo > state.pageNo ? state.inviteList.concat(inviteList) : inviteList
      return {
        ...state,
        pageNo,
        inviteList: newList
      }
    }
  },
  effects: {
    async getInviteCode () {
      const { kps = '' } = store.getState().user
      let ut = ''
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      const timestamp = Date.now()
      const utRes = await ucapi.biz.ucparams({ params: 'ut' });
      ut = utRes.ut || '';
      const signOriText = `${timestamp}${config.appId}${config.moduleCodeInvite}${kps}${decodeURIComponent(ut)}${false}`;
      const sign = await ucapi.spam.sign({ text: signOriText, salt });
      const getInviteCodeMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_INVITE_CODE);
      try {
        const inviteInfo = await network.get(`${config.coralHost}/invite/v2/queryUserInviteInfo`, {
          kps,
          appId: config.appId,
          onlyQueryCode: false,
          moduleCode: config.moduleCodeInvite,
          sign,
          timestamp
        })
        const inviteCode = inviteInfo?.codeInfo?.code || ''
        if (inviteCode) {
          getInviteCodeMonitor.success({
            msg: '获取成功',
            c1: inviteCode
          })
        } else {
          getInviteCodeMonitor.fail({
            msg: '获取失败-未返回邀请码',
          })
        }
        dispatch.invite.update({
          inviteCode
        })
        return inviteCode;
      } catch (error) {
        const msg = (error && error.message) || JSON.stringify(error);
        getInviteCodeMonitor.fail({
          msg: '接口失败',
          bl1: msg
        })
      }
    },
    async getInviteDetail () {
      const { kps = '' } = store.getState().user
      const getInviteDetailMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_INVITE_DETAIL);
      try {
        const inviteInfo = await network.get(`${config.coralHost}/uclite/invite/page`, {
          kps,
          appId: config.appId,
        })
        if (inviteInfo.inviteRecord) {
          getInviteDetailMonitor.success({
            msg: '成功',
            c1: inviteInfo.inviteRecord?.totalNum,
            c2: inviteInfo.inviteRecord?.totalAmount,
            c3: inviteInfo.inviteAmount || 0
          })
        } else {
          getInviteDetailMonitor.fail({
            msg: '数据异常',
            c1: '未返回'
          })
        }
        dispatch.invite.update({
          ...inviteInfo
        })
      } catch (error) {
        const msg = (error && error.message) || JSON.stringify(error);
        getInviteDetailMonitor.fail({
          msg: '接口失败',
          bl1: msg
        })
      }
    },
    async getInviteList (pageNo: number) {
      const { kps = '' } = store.getState().user
      const getInviteListMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_INVITE_LIST);
      try {
        const resInviteList = await network.get(`${config.coralHost}/uclite/invite/list`, {
          kps,
          appId: config.appId,
          pageNo,
          pageSize: 10
        })
        if (resInviteList) {
          getInviteListMonitor.success({
            msg: '查询成功',
            c1: resInviteList.length
          })
          dispatch.invite.updateInviteList(resInviteList, pageNo)
        } else {
          getInviteListMonitor.fail({
            msg: '查询失败-无数据',
          })
        }
      } catch (error) {
        const msg = (error && error.message) || JSON.stringify(error);
        getInviteListMonitor.fail({
          msg: '查询失败',
          bl1: msg
        })
      }
    },
    /**
     * 绑定邀请码
     */
    async bindInvite(inviteCode: string) {
      const {kps = ''} = store.getState().user
      let ut = ''
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      const timestamp = Date.now()
      const utRes = await ucapi.biz.ucparams({params: 'ut'});
      ut = utRes.ut || '';
      const signOriText = `${timestamp}${config.appId}${config.moduleCodeInvite}${kps}${decodeURIComponent(ut)}${inviteCode}`;
      const sign = await ucapi.spam.sign({text: signOriText, salt});
      const bindMonitor = tracker.Monitor(WPK_CATEGORY_MAP.INVITE_BIND);
      try {
        const bindCodeRes = await network.post(`${config.coralHost}/invite/v2/bindInviteCode`, {
          kps,
          appId: config.appId,
          inviteCode,
          moduleCode: config.moduleCodeInvite,
          sign,
          timestamp
        })
        bindMonitor.success({
          msg: '绑定成功',
          c1: kps,
          c2: inviteCode,
          bl1: JSON.stringify(bindCodeRes)
        });
        console.log('bindCodeRes:', bindCodeRes)
        return bindCodeRes
      } catch (err) {
        bindMonitor.fail({
          msg: '绑定失败',
          c1: kps,
          c2: inviteCode,
          c3: err?.code || '',
          c4: err?.msg || '',
          bl1: JSON.stringify(err)
        });
        throw err;
      }
    },
  }
}

export default invite
