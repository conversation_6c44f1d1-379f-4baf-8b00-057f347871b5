export interface IInviteReward {
  d1: {
    times: number;
    amount: number;
    mark: string;
  },
  d2: {
    times: number;
    amount: number;
    mark: string;
  },
  other: {
    times: number;
    amount: number;
    mark: string;
  }
}

export interface IInviteUser {
  avatar: string;
  nickName: string;
  amount: number; // 已获得奖励
  ingAmount: number; // 即将到账奖励
  totalAmount: number; // 总奖励
  desc: string;
  active: boolean; // 今日是否活跃
}

export interface IInviteRecord {
  totalNum: number; // 邀请人数
  totalAmount: number; // 邀请奖励
  list: IInviteUser[];
}

export interface IInviteState {
  endTime: number;
  inviteIntro: IInviteReward;
  inviteAmount: number; // 阶段邀请奖励
  inviteCode: string;
  inviteRecord: IInviteRecord;
  inviteList: IInviteUser[];
  pageNo: number;
}
