export interface IEquityGiftState {
  rightsGiftList: IEquityGiftItem[];
  diamondData: IDiamondData | null;
}

export interface IDiamondData {
  alipayFirst: boolean;
  alipayFirstPercent: number;
  wechatPay: boolean;
  wechatPayExceedTime: number;
  csLink: string;
  ruleLink: string;
  agreementName: string;
  goodsImg: string; // 好货特惠 商品图片
  couponRuleLink: string;
  shoppingLink: string;
  orderCenterLink: string;
  shoppingCouponsLink: string;
  hookShoppingLink: string;
  couponEndTime: number;
  balanceRequire: number; // 显示优惠券要求余额数
  showDiscountsPrice: boolean; // 是否显示优惠后价格
  noticeConfig: {
    noticeStartTime: number;
    noticeEndTime: number;
    newNoticeContent: string;
    newNoticeStartTime: number;
    newNoticeEndTime: number;
  };
  equityExchangeTagConfig: {
    planId: number;
    tag: string;
    tagBg: string;
    tagColor: string;
  }[]
}


export interface IEquityGiftItem {
  planId: number;
  accountType: string;
  classify: 'member' | 'privilege';
  name: string;
  icon: string;
  img: string[];
  /** 描述 */
  explain: string;
  /** 使用说明 */
  useExplain: string;
  /** 简介 */
  intro: string;
  getUrl: string;
  useUrl: string;
  startTime: number;
  endTime: number;
  code: string;
  spuId: number;
  skuId: number;
  /** 价值 */
  giftValue: number;
  /** 已兑换库存数量 */
  assigned: number;
  /** 总库存数 */
  quota: number;
  /** 是否限制领取 */
  isLimit: boolean;
  /** 周期限制领取次数 */
  drawLimit: number;
  /** 周期领取时长 */
  drawCycle: number;
  /** 下个库存周期 */
  nextInterval: number;
  /** 当前周期库存 */
  availability: number;
  /** 每个周期增加的库存 */
  refreshQuota: number;
  fixedOrder: number;
  /** 权益是否可以兑换 */
  canDraw: boolean;
  /** 不能领取原因 */
  limitCode: LimitCodeType;
  assetConsumeList: {
    /** 兑换所需的金额 */
    amount: number;
    /** 原价 */
    originAmount: number;
  }[];
  factName: string;
}
export interface IEquityGift {
  list: IEquityGiftItem[];
  title: string;
  classify: 'member' | 'privilege';
  icon: string;
  id: number;
}

export enum IExchangeFlowState {
  /** 确认支出 */
  PAYOUT_CONFIRM = 'PAYOUT_CONFIRM',
  /** 预支出 */
  PAYOUT_PRE = 'PAYOUT_PRE'
}

const LimitCodeArr = [
  /** 已结束 */
  'AWARD:OVER_END_TIME',
  /** 未开始 */
  'AWARD:NOT_START_TIME',
  /** 未发布 */
  'AWARD:NOT_PUBLISHED',
  /** 超过领取次数限制 */
  'AWARD:LIMIT_RECEIVE',
  /** 库存不足 */
  'AWARD:NOT_ENOUGH',
] as const;

export type LimitCodeType = (typeof LimitCodeArr)[number];

/** 兑换错误码code */
export const exchangeReasonMap = {
  'INSUFFICIENT_BALANCE_FAIL': '当前余额不足',
  'AWARD:LIMITED': '当前兑换次数已用完，每天12点更新',
  'AWARD:OUT_OF_STOCK': '当前权益（或会员）已抢完，请明天再来',
  'ALREADY_REDEEMED': '当前兑换次数已用完，每天12点更新',
  'UNUSUAL_USER': '账号异常，请联系客服',
  'RULE_NOT_EXCHANGE': '兑换暂未开放 请后续重试',
  'DEFAULT': '兑换失败，请刷新页面后重试',
}

/** 兑换失败埋点上报的文案 */
export const exchangeFailFactText = {
  'AWARD:OUT_OF_STOCK': '库存不足',
  'UNUSUAL_USER': '风控用户',
  'AWARD:LIMITED': '已兑换过',
  'AWARD:LIMIT_RECEIVE': '已兑换过',
  'INSUFFICIENT_BALANCE_FAIL': '余额不足',
  'RULE_CODE_NOT_FOUND': '投放规则不存在',
  'RULE_NOT_PUBLISHED': '投放规则未上线',
  'RULE_NOT_EXCHANGE': '投放规则不支持兑换',
  'SKU_NOT_FOUND': '商品不存在',
  'OVER_END_TIME': '已结束',
  'AWARD:NOT_START_TIME': '权益未开始',
  'AWARD:OVER_END_TIME': '权益已结束',
  'AWARD:RECEIVE_FAIL': '领取失败',
  'NEED_LOGIN': '需要登陆',
  'DEFAULT': '其他',
};
