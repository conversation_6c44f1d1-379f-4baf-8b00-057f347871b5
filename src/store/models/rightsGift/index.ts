import network from '@/utils/network';
import tracker from '@/lib/tracker';
import config from '@/config';
import { store, dispatch } from '@/store';
import { IEquityGiftState, IEquityGiftItem } from './typings';
import ucapi from "@/utils/ucapi";
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import { getGlobalFirstData } from '@/lib/render-utils/csr';
// import { getErrorInfo } from '../app/utils';

export function geneRequestId() {
  function s4() {
    // eslint-disable-next-line no-bitwise
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return `${s4() + s4()}-${s4()}-${s4()}-${s4()}-${s4()}${s4()}${s4()}`;
}

export const refreshUserGiftState = async (scene: 'exchangeEquityGift' | 'taskComplete') => {
  // note: 监控地址: https://wpk.ucweb.com/#/45w4uv8x-nrs4k62g/h5/custom/index?code=193
  try {
    const res = await ucapi.novel.sendNativeEvent()
    if (res.result === 'success') {
      tracker.log({
        category: WPK_CATEGORY_MAP.REFRESH_USER_GIFT_STATE,
        msg: '刷新用户权益状态成功',
        w_succ: 1,
        c1: scene,
        bl1: JSON.stringify(res)
      })
    } else {
      tracker.log({
        category: WPK_CATEGORY_MAP.REFRESH_USER_GIFT_STATE,
        msg: '刷新用户权益状态失败',
        w_succ: 0,
        c1: scene,
        bl1: JSON.stringify(res)
      })
    }
  } catch (e) {
    tracker.log({
      category: WPK_CATEGORY_MAP.REFRESH_USER_GIFT_STATE,
      msg: '刷新用户权益状态失败-catch',
      w_succ: 0,
      c1: scene,
      bl1: JSON.stringify(e)
    });
  } 
}

export const getErrorInfo = (err) => {
  const errCode = err.code || (err.data && err.data.code) || '';
  const msg = err.message || err.msg || '';
  const status = err.status || -1;
  return {
    errCode,
    msg,
    status
  }
}

const initEquityGiftState: IEquityGiftState = {
  rightsGiftList: [],
  diamondData: null
};

const equityGift = {
  state: { ...initEquityGiftState },
  reducers: {
    update(state: IEquityGiftState,payload: Partial<IEquityGiftState>): IEquityGiftState {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: {
    async rightsInit(firstInit = false) {
       await dispatch.rightsGift.fetchEquityGiftList(firstInit);
    },
    async fetchDiamondData() {
      const queryEquityMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_WALLET_DIAMOND);
      try {
        const kps = store.getState().user.kps;
        const reqParams = [
          {
            "module": "diamond",
            "key":  config.wallet_diamond_key
          }
        ]
        const aggRes = await network.get(`${config.coralHost}/aggregation/wallet`, {
          kps,
          // sceneCode: config.SCENE_CODE,
          req_services: JSON.stringify(reqParams)
        });

        if (aggRes?.[0].data) {
          dispatch.rightsGift.update({
            diamondData: aggRes?.[0]?.data
          });
          queryEquityMonitor.success({
            msg: '查询成功',
          });
        } else {
          queryEquityMonitor.fail({
            msg: '查询失败-没有数据',
          });
        }

      } catch (err) {
        queryEquityMonitor.fail({
          msg: '查询失败',
          bl1: JSON.stringify(err),
        });
        console.log(err);
      }
    },
    /** 查询权益礼品列表 */
    async fetchEquityGiftList(firstInit = false) {
      const { coralHost, walletAppId, rightsExchangeCode, rightsExchangeSceneCode } = config;
      const reqHost = `${coralHost}/uclite/exchange/queryGiftBagList`;
      const kps = store.getState().user.kps;
      const queryEquityMonitor = tracker.Monitor(WPK_CATEGORY_MAP.QUERY_RIGHTS_GIFT);
      try {
        let resData: { planList: IEquityGiftItem[] };
        if(firstInit && getGlobalFirstData()?.rightsGiftList){
          resData = getGlobalFirstData()?.rightsGiftList;
        } else {
          const requestId = geneRequestId();
          resData = await network.get(reqHost, {
            appId: walletAppId,
            kps: kps || '',
            requestId,
            ruleCode: rightsExchangeCode,
            sceneCode: rightsExchangeSceneCode,
          })
        }
        const { planList = [] } = resData;
        if (planList?.length) {
          queryEquityMonitor.success({
            msg: '查询成功',
            bl1: JSON.stringify(planList),
          });
        } else {
          queryEquityMonitor.fail({
            msg: '查询成功-列表为空',
            bl1: JSON.stringify(planList),
          });
        }
        dispatch.rightsGift.update({ rightsGiftList: planList})
      } catch (e) {
        console.log('权益礼品列表查询失败', e);
        const err = getErrorInfo(e)
        queryEquityMonitor.fail({
          msg: '查询失败-catch',
          c1: err?.errCode,
          bl1: JSON.stringify(e),
        })
      }
    },

    /** 兑换权益礼品 */
    async exchangeEquityGift(params: IEquityGiftItem) {
      const { planId = '' } = params;
      const { coralHost, walletAppId, rightsExchangeCode, rightsExchangeSceneCode } = config;
      const reqHost = `${coralHost}/uclite/exchange/drawGiftBag`;
      const kps = store.getState().user.kps;
      const exchangeMonitor = tracker.Monitor(WPK_CATEGORY_MAP.EXCHANGE_RIGHTS_GIFT);      
      try {
        const requestId = geneRequestId();
        const ut = store.getState().user.ut || '';
        const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
        const signOriText = `${kps}${decodeURIComponent(ut)}${walletAppId}${rightsExchangeSceneCode}${rightsExchangeCode}${planId}${requestId}`;
        const sign = await ucapi.spam.sign({ text: signOriText, salt });
        const postParams = {
          appId: walletAppId,
          kps: kps || '',
          requestId,
          planId,
          sign,
          ruleCode: rightsExchangeCode,
          sceneCode: rightsExchangeSceneCode,
        }
        const exchangeRes = await network.post(reqHost, JSON.stringify(postParams) ,{ headers: {'Content-Type': 'application/json'}, timeout: 6e3 })
        exchangeMonitor.success({
          msg: `${params.name}-${params.planId}-兑换成功`,
          c1: `${params.planId}`,
          c2: `${params.name}`,
          c4: `${params.classify}`,
          bl1: JSON.stringify(params),
          bl2: JSON.stringify(exchangeRes),
        })
        dispatch.currency.queryAllCurrency(false);
        dispatch.rightsGift.fetchEquityGiftList();
        return { code: 'OK'}
      } catch (e) {
        console.log('兑换权益礼品失败', e);
        const err = getErrorInfo(e);
        exchangeMonitor.fail({
          msg: `兑换失败`,
          c1: `${params.planId}`,
          c2: `${params.name}`,
          c3: `${err?.errCode}`,
          c4: `${params.classify}`,
          bl1: JSON.stringify(params),
          bl2: JSON.stringify(e),
        })
        dispatch.rightsGift.fetchEquityGiftList();
        return { code: err.errCode}
      }
    },
  },
};

export default equityGift;
