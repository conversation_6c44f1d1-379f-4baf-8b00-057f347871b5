import {dispatch, store } from "@/store/index";
import fact from '@/lib/fact';
import tracker from '@/lib/tracker';
import { isAndroid, isIOS, isLatestVersion, getUCLiteVersion } from '@/lib/universal-ua';
import { getParam } from '@/lib/qs';
import { loadNativeAd, getNoahRtaTag, onNativeAdShow, clickNativeAdAndNotify } from '@/lib/ucapi';
import {checkInstallApp } from "@/pages/index/task/help";
import { TaoBaoRtaInfo } from '@/utils/rta';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import { MODAL_ID } from "@/components/modals";

export interface IRTAState {
  taobaoRtaInfo: TaoBaoRtaInfo | null;
}

const state: IRTAState = {
  taobaoRtaInfo: null,
};

const rta = {
  state,
  reducers: {
    updateState(state: IRTAState, payload: Partial<IRTAState>): IRTAState {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: {
    async updateTaobaoRta(firstInit = false) {
      try {
        const checkStart = Date.now();
        const highValueTask = store.getState().highValueTask;
        const rtaResult = await this.getTaoBaoRTAAdInfo(firstInit);
        console.log('[AD] rtaResult', rtaResult);
        dispatch.rta.updateState({
          taobaoRtaInfo: rtaResult
        });
        dispatch.task.updateRTATaskList({
          taobaoRtaInfo: rtaResult
        });
        // 首次进来且有RTA的任务
        if(firstInit){
          const baseCurrentOpenModal = modal?.getCurrentOpenModalObj() || {};
          if(!baseCurrentOpenModal[MODAL_ID.HIGH_VALUE_TASK] && highValueTask.hasTaobaoRtaTask){
            dispatch.highValueTask.handleTaskVisible({showTaskDialog: firstInit});
          }
        }

        tracker?.log({
          category: 177,
          msg: 'TaobaoRta检测耗时',
          wl_avgv1: Date.now() - checkStart
         });
      } catch (err) {
        console.error('[AD getTaoBaoRTAAdInfo]', err);
      }
    },
    async getTaoBaoRTAAdInfo (firstInit = false) {
      const { taobaoRtaConfig } = store.getState().app;
      const version = store.getState().user.ve || getUCLiteVersion()
      console.log('[AD]] taobaoRtaConfig', taobaoRtaConfig);
      if (!taobaoRtaConfig) {
        return null;
      }
      const { androidAppKey, iosAppKey, androidSlotId, iosSlotId, type, sceneId, NuLevel, activeLevel, pkgName, openSchema, androidOpen, iosOpen, nuTaobaoInstallCheck, highPriorityTaskId,  lowPriorityTaskId, androidSupportVersion, iosSupportVersion} = taobaoRtaConfig;
      if (isAndroid && !androidOpen) {
        return null;
      }
      if (isIOS && !iosOpen) {
        return null;
      }
      // 限定Android版本
      if (isAndroid && !isLatestVersion(version, androidSupportVersion)) {
        return null;
      }
      // 限定IOS版本
      if (isIOS && !isLatestVersion(version, iosSupportVersion)) {
        return null;
      }
      const rtaAdMonitor = tracker.Monitor(160);
      const rtaTagMonitor = tracker.Monitor(161);
      const rtaShowMonitor = tracker.Monitor(164);
      const nuUserLevel = NuLevel?.split(',');
      const activeUserLevel = activeLevel?.split(',')
      let rtaAdResult;
      const aid = getParam('rta_aid') || isAndroid ? androidSlotId : iosSlotId;
      const adParams = {
        appKey: isAndroid ? androidAppKey : iosAppKey,
        aid,
        type: type
      };
      const adStartTime = Math.round(window?.performance?.now());
      try {
        fact.event('taobao_ad_request', {
          event_id: '19999',
          scene: sceneId,
          is_first_init: firstInit
        });
        // 请求淘宝RTA广告
        rtaAdResult = await loadNativeAd(adParams);
        console.log('[AD] rtaAdResult', rtaAdResult);
        rtaAdMonitor.success({
          msg: '淘宝RTA广告请求成功',
          c1: sceneId,
          c2: aid,
          wl_avgv1: Math.round(window?.performance?.now()) - adStartTime,
          bl1: JSON.stringify(rtaAdResult || {}),
        });
        fact.event('taobao_ad_response', {
          event_id: '19999',
          scene: sceneId,
          response_result: 'success',
          sid: rtaAdResult?.sid,
          is_first_init: firstInit
        });
        // 返回了广告ID
        if (!rtaAdResult?.sid) {
          rtaAdMonitor.fail({
            msg: '淘宝RTA广告请求失败',
            c1: sceneId,
            c2: aid,
            wl_avgv1: Math.round(window?.performance?.now()) - adStartTime,
            bl1: JSON.stringify(rtaAdResult || {}),
            bl2: JSON.stringify(adParams)
          });
        }
      } catch (err) {
        fact.event('taobao_ad_response', {
          event_id: '19999',
          scene: sceneId,
          response_result: 'overtime',
          is_first_init: firstInit
        });
        console.log('[AD] 淘宝RTA广告请求失败',adParams, err);
        rtaAdMonitor.fail({
          msg: '淘宝RTA广告请求失败',
          c1: sceneId,
          c2: aid,
          wl_avgv1: Math.round(window?.performance?.now()) - adStartTime,
          bl1: JSON.stringify(err),
          bl2: JSON.stringify(adParams)
        });
      }
      // 返回了广告ID
      if (rtaAdResult?.sid) {
        const [isInstall] = await checkInstallApp(openSchema, pkgName);
        const tagStartTime = Math.round(window?.performance?.now());
        try {
          fact.event('taobao_rta_request', {
            event_id: '19999',
            scene: sceneId,
            sid: rtaAdResult?.sid,
            is_first_init: firstInit
          });
          // 获取用户的淘宝分类
          const rtaTagResult = await getNoahRtaTag({
            scene: sceneId,
          });
          console.log('[AD] rtaTagResult', rtaTagResult);
          const rtaTagPrice = rtaTagResult?.price || '0';
          const userLevels = nuUserLevel.concat(activeUserLevel);
          // 由于没办法配置用户的标签，提供Mock的方式验证
          const mockRtaCategory = getParam('rta_category');
          const category = mockRtaCategory || rtaTagResult?.category;
          rtaTagMonitor.success({
            msg: '淘宝RTA身份请求成功',
            c1: category,
            c2: sceneId,
            c3: aid,
            wl_avgv1: Math.round(window?.performance?.now()) - tagStartTime,
            bl1: JSON.stringify(rtaTagResult ?? {}),
          });
          fact.event('taobao_rta_response', {
            event_id: '19999',
            scene: sceneId,
            taobao_rta_type: category,
            is_installed_taobao: isInstall ? '1' : '0',
            is_success: '1',
            sid: rtaAdResult?.sid,
            rta_price: rtaTagPrice,
            is_first_init: firstInit
          });
          // 不在用户等级中
          if (!userLevels.includes(category)) {
            console.log('[AD] not in userLevels', category);
            fact.event('taobao_rta_show', {
              event_id: '19999',
              scene: sceneId,
              taobao_rta_type: category,
              is_installed_taobao: isInstall ? '1' : '0',
              is_show: 'fail',
              show_failed_reason: 'no_target_users',
              sid: rtaAdResult?.sid,
              rta_price: rtaTagPrice,
              is_first_init: firstInit
            });
            rtaShowMonitor.fail({
              msg: '淘宝RTA用户不在目标用户中',
              c1: category,
              c2: sceneId,
              c3: aid,
              bl1: JSON.stringify(rtaTagResult ?? {}),
              bl2: JSON.stringify({
                scene: sceneId,
              })
            });
            return null;
          }

          // 命中用户标签
          if (mockRtaCategory || (rtaTagResult?.target === '1' && category)) {
            console.log('[AD] isInstall', isInstall);
            // 拉活用户先检测是否安装淘宝
            if (activeUserLevel.includes(category) && !isInstall) {
              fact.event('taobao_rta_show', {
                event_id: '19999',
                scene: sceneId,
                taobao_rta_type: category,
                is_installed_taobao: isInstall ? '1' : '0',
                is_show: 'fail',
                show_failed_reason: 'no_taobao',
                sid: rtaAdResult?.sid,
                rta_price: rtaTagPrice,
                is_first_init: firstInit
              });
              rtaShowMonitor.fail({
                msg: '淘宝RTA拉活用户未安装淘宝',
                c1: category,
                c2: sceneId,
                c3: aid,
                bl1: JSON.stringify(rtaTagResult ?? {}),
                bl2: JSON.stringify({
                  scene: sceneId,
                })
              });
              return null
            }
            if (nuTaobaoInstallCheck && nuUserLevel.includes(category) && isInstall) {
              fact.event('taobao_rta_show', {
                event_id: '19999',
                scene: sceneId,
                taobao_rta_type: category,
                is_installed_taobao: isInstall ? '1' : '0',
                is_show: 'fail',
                show_failed_reason: 'has_taobao',
                sid: rtaAdResult?.sid,
                rta_price: rtaTagPrice,
                is_first_init: firstInit
              });
              rtaShowMonitor.fail({
                msg: '淘宝RTA拉新用户已安装淘宝',
                c1: category,
                c2: sceneId,
                c3: aid,
                bl1: JSON.stringify(rtaTagResult ?? {}),
                bl2: JSON.stringify({
                  scene: sceneId,
                })
              });
              return null;
            }
            fact.event('taobao_rta_show', {
              event_id: '19999',
              scene: sceneId,
              taobao_rta_type: category,
              is_installed_taobao: isInstall ? '1' : '0',
              is_show: 'success',
              sid: rtaAdResult?.sid,
              rta_price: rtaTagPrice,
              is_first_init: firstInit
            });
            rtaShowMonitor.success({
              msg: '淘宝RTA用户符合展示规则',
              c1: category,
              c2: sceneId,
              c3: aid,
              bl1: JSON.stringify(rtaTagResult ?? {}),
              bl2: JSON.stringify({
                scene: sceneId,
              })
            });
            const rtaResult = {
              category,
              sceneId,
              aid,
              adInfo: {
                title: rtaAdResult.title,
                sid: rtaAdResult.sid,
                price: rtaTagPrice,
              },
              highPriorityTaskId,
              lowPriorityTaskId

            };
            return rtaResult;

          }
          rtaTagMonitor.fail({
            msg: '淘宝RTA身份不在目标用户',
            wl_avgv1: Math.round(window?.performance?.now()) - tagStartTime,
            c1: category,
            c2: sceneId,
            c3: aid,
            bl1: JSON.stringify(rtaTagResult ?? {}),
            bl2: JSON.stringify({
              scene: sceneId,
            })
          });
          fact.event('taobao_rta_show', {
            event_id: '19999',
            scene: sceneId,
            taobao_rta_type: category,
            is_installed_taobao: isInstall ? '1' : '0',
            is_show: 'fail',
            show_failed_reason: 'no_target_users',
            sid: rtaAdResult?.sid,
            rta_price: rtaTagPrice,
            is_first_init: firstInit
          });
        } catch (err) {
          fact.event('taobao_rta_response', {
            event_id: '19999',
            scene: sceneId,
            sid: rtaAdResult?.sid,
            taobao_rta_type: '',
            is_installed_taobao: '',
            is_success: '0',
            is_first_init: firstInit
          });
          console.error('[AD] 淘宝RTA身份请求失败', err);
          rtaTagMonitor.fail({
            msg: '淘宝RTA身份请求失败',
            wl_avgv1: Math.round(window?.performance?.now()) - tagStartTime,
            c1: '',
            c2: sceneId,
            c3: aid,
            bl1: JSON.stringify(err),
            bl2: JSON.stringify({
              scene: sceneId,
            })
          });
        }
      }
      return null;
    },
    async ratTaskExposure(_, roootState) {
      const addShowMonitor = tracker.Monitor(162);
      const taobaoRtaInfo = roootState.rta.taobaoRtaInfo;
      const taobaoRtaConfig = roootState.app.taobaoRtaConfig;
      if (!taobaoRtaInfo || !taobaoRtaConfig) {
        return;
      }
      const { androidSlotId, iosSlotId, type } = taobaoRtaConfig;
      const params = {
        sid: taobaoRtaInfo.adInfo?.sid ?? '',
        aid: isAndroid ? androidSlotId : iosSlotId,
        type
      };
      try {
        const showRet = await onNativeAdShow(params);
        addShowMonitor.success({
          msg: '广告曝光成功',
          c1: taobaoRtaInfo.category,
          c2: taobaoRtaInfo.sceneId,
          c3: taobaoRtaInfo.aid,
          bl1: JSON.stringify(showRet || {}),
          bl2: JSON.stringify(params)
        });
        console.log('[AD] onNativeAdShow result', showRet);
      } catch (err) {
        addShowMonitor.fail({
          msg: '广告曝光异常',
          c1: taobaoRtaInfo.category,
          c2: taobaoRtaInfo.sceneId,
          c3: taobaoRtaInfo.aid,
          bl1: JSON.stringify(err),
          bl2: JSON.stringify(params)
        });
        console.error('[AD] onNativeAdShow err', err);
      }

    },
    async rtaAdClick(task, rootState) {
      const clickMonitor = tracker.Monitor(163);
      const taobaoRtaInfo = rootState.rta.taobaoRtaInfo;
      const taobaoRtaConfig = rootState.app.taobaoRtaConfig;
      if (!taobaoRtaInfo || !taobaoRtaConfig) {
        return;
      }
      const { androidSlotId, iosSlotId, type } = taobaoRtaConfig;
      const params = {
        sid: taobaoRtaInfo?.adInfo?.sid ?? '',
        aid: isAndroid ? androidSlotId : iosSlotId,
        thirdid: `${task.token}` || `${task.id}`,
        channel: 2,
        type: type,
      };
      try {
        const clickRet = await clickNativeAdAndNotify(params);
        clickMonitor.success({
          msg: '广告点击正常',
          c1: taobaoRtaInfo.category,
          c2: taobaoRtaInfo.sceneId,
          c3: taobaoRtaInfo.aid,
          bl1: JSON.stringify(clickRet),
          bl2: JSON.stringify(params)
        });
        console.log('[AD] clickNativeAdAndNotify result', params, clickRet);
      } catch (err) {
        console.error('[AD] clickNativeAdAndNotify err', err);
        clickMonitor.fail({
          msg: '广告点击异常',
          c1: taobaoRtaInfo.category,
          c2: taobaoRtaInfo.sceneId,
          c3: taobaoRtaInfo.aid,
          bl1: JSON.stringify(err),
          bl2: JSON.stringify(params)
        });
      }
    }
  }
}

export default rta;
