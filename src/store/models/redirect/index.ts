import { store, dispatch } from '@/store/index';
import fact from '@/lib/fact';
import { addQueryParams, getParam } from '@/lib/qs';
import { openURL } from '@/pages/index/task/help';
import {  ICMSRes } from '../cms/typings';
import { AppState } from '../app';

export interface IRedirectState {
  init: boolean;
  redirectConfigList: Array<{
    filterEntry: string[];
    redirectLink: string;
  }>;
  areaInfo: {
    city: string;
    province: string;
  };
}

const state: IRedirectState = {
  init: false,
  areaInfo: {
    city: '',
    province: '',
  },
  redirectConfigList: [],
};

const redirect = {
  state,
  reducers: {
    updateState(state: IRedirectState, payload: Partial<IRedirectState>): IRedirectState {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: {
    // CMS处理逻辑
    async handleRedirectEvent(cmsData: ICMSRes) {
      const redirectDataList = cmsData?.flz_redirect_bbnc?.items[0]?.redirectConfigList as IRedirectState['redirectConfigList'] || [];      
      const entry = getParam('entry') || 'unknown';
      // hash路由模式下 链接有可能会自动拼上 # 号
      const newEntry = entry?.split('#')?.[0];  
      const { init } = store.getState().redirect;
      
      if (init) {
        return;
      }

      // 根据entry匹配cms对应数据
      const matchConfig = redirectDataList?.find((item) => item?.filterEntry?.includes(newEntry));

      if (!newEntry || !matchConfig) {
        return;
      }

      if (matchConfig?.redirectLink) {
        fact.event('page_redirect_jump', {
          entry: newEntry,
        });

        try {
          openURL(matchConfig?.redirectLink);
        } catch (e) {
          window.location.href = matchConfig?.redirectLink;
        }
        
        // 更新数据
        dispatch.redirect.updateState({
          init: true,
          redirectConfigList: redirectDataList,
        });
      }
    },
    // 服务端城市过滤重定向
    async handleTaskRedirectEvent(params: {
      areaInfo: IRedirectState['areaInfo'];
      exchangeRedirectConfig: AppState['exchangeRedirectConfig'];
    }) {
      const { areaInfo, exchangeRedirectConfig } = params;
      const entry = getParam('entry') || 'unknown';
      const { init } = store.getState().redirect;
      if (init) {
        return;
      }
      if (!exchangeRedirectConfig.filterCity || !exchangeRedirectConfig.filterEntry || !exchangeRedirectConfig.redirectLink) {
        return;
      }

      const { filterCity, filterEntry, redirectLink } = exchangeRedirectConfig;
      // 未命中地域 && 命中入口
      if (filterCity.includes(areaInfo.city || '') || filterCity.includes(areaInfo.province || '')) {
        return;
      }
      if (filterEntry.includes(entry)) {
        fact.event('page_redirect_jump', {
          entry: entry,
          area: areaInfo.city || areaInfo.province,
        });
        const url = addQueryParams(redirectLink, { entry });
        try {
          openURL(url);
        } catch (e) {
          window.location.href = url;
        }
        // 更新数据
        dispatch.redirect.updateState({
          init: true,
          areaInfo,
        });
      }
    },
  },
};

export default redirect;
