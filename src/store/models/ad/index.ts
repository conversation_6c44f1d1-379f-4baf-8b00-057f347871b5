import {ISlotAd, hcAdHelper, generateCacheKey} from '@/utils/huichuan';
import {StoreState, store, dispatch} from "@/store/index";
import fact from '@/lib/fact';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import storage from '@ali/weex-toolkit/lib/storage';
import ucapi from '@/utils/ucapi';
import {isIOS} from "@/lib/universal-ua";
import config from '@/config';
import cacheOnce from "@/utils/cacheOnce";
import {getCorpTaskFromCache} from "@/store/models/ad/helper";
import { setAppInstallWithExpiryMinutes } from '@/lib/utils/app_install';

interface IEmbedNativeAdInfo {
  id: string;
  width: number;
  height: number;
}

export interface IAdState {
  bannerBrandAd: ISlotAd | null;
  bannerBrandAd2: ISlotAd | null;
  popBrandAd: ISlotAd | null;
  bannerAdLoaded: boolean;
  bannerAd2Loaded: boolean;
  taskBrandAd: ISlotAd | null;
  taskCorpAd: {
    [k: string]: ISlotAd
  };
  personalizedAdSwitch: boolean;
  personalizedAdSwitchUpdateFlag: boolean;
  embedNativeAdInfo: IEmbedNativeAdInfo;
}

const state: IAdState = {
  bannerBrandAd: null,
  bannerBrandAd2: null,
  popBrandAd: null,
  bannerAdLoaded: false,
  bannerAd2Loaded: false,
  taskBrandAd: hcAdHelper.getAdCache(config.HC_BRAND_TASK),
  taskCorpAd: {},
  personalizedAdSwitch: true,
  personalizedAdSwitchUpdateFlag: false,
  embedNativeAdInfo: {
    id: '',
    height: 0,
    width: 0,
  },
};

export const fetchPopAd = cacheOnce('popAd', async function () {
  const huichuanPopSlotId = store.getState()?.cms.huichuanPopSlotId;
  return hcAdHelper.requestHuichuanAd(huichuanPopSlotId, false);
});

const ad = {
  state,
  reducers: {
    updateState(state: IAdState, payload: Partial<IAdState>): IAdState {
      return {
        ...state,
        ...payload,
      }
    },
    onTaskCorpAdUpdate(state: IAdState, payload: { taskBrandAd: ISlotAd; taskCorpAd: { [k: string]: ISlotAd } }): IAdState {
      return {
        ...state,
        taskCorpAd: payload.taskCorpAd
      }
    },
    onTaskBrandAdUpdate(state: IAdState, payload: { taskBrandAd: ISlotAd; taskCorpAd: { [k: string]: ISlotAd } }): IAdState {
      return {
        ...state,
        taskBrandAd: payload.taskBrandAd
      }
    },
    onBannerBrandAdUpdate(state: IAdState, payload: ISlotAd): IAdState {
      return {
        ...state,
        bannerAdLoaded: true,
        bannerBrandAd: payload
      }
    },
    onBannerBrandAd2Update(state: IAdState, payload: ISlotAd): IAdState {
      return {
        ...state,
        bannerAd2Loaded: true,
        bannerBrandAd2: payload
      }
    },
    onPopBrandAdUpdate(state: IAdState, payload: ISlotAd): IAdState {
      return {
        ...state,
        popBrandAd: payload
      }
    },
    clearEmbedNativeAd(state: IAdState) {
      return {
        ...state,
        embedNativeAdInfo: {
          id: '',
          height: 0,
          width: 0,
        },
      }
    }
  },
  effects: {
    async fetchPopAd(force: boolean = false, rootState: StoreState) {
      if (rootState.cms.huichuanPopSlotId) {
        const result = await fetchPopAd();
        dispatch.ad.onPopBrandAdUpdate(result)
        return result;
      }
    },
    // async fetchBannerBrandAd(force: boolean = false, rootState: StoreState) {
    //   if (rootState.app.huichuanBannerSlotId) {
    //     const result = await hcAdHelper.requestHuichuanAd(config.HC_BRAND_BANNER, force);
    //     dispatch.ad.onBannerBrandAdUpdate(result);
    //     return result;
    //   }
    // },
    async fetchBannerBrandAd2(force: boolean = false, rootState: StoreState) {
      const { huichuanBanner2SlotId } = rootState.cms;
      if (huichuanBanner2SlotId) {
        const result = await hcAdHelper.requestHuichuanAd(huichuanBanner2SlotId, force);
        dispatch.ad.onBannerBrandAd2Update(result);
        return result;
      }
    },
    async fetchTaskBrandAd(force: boolean = false, rootState: StoreState) {
      const { huichuanBrandTaskSlotId } = rootState.cms;
      if (rootState.cms.huichuanBrandTaskSlotId) {
        const result = await hcAdHelper.requestHuichuanAd(huichuanBrandTaskSlotId, force);
        // 判断是否该品牌广告ad_id是否已完成
        if (result?.ad[0]?.ad_id) {
          const key = `ad_brand_${result.ad[0].ad_id}_completed`;
          console.log(`检查该品牌广告ad_id: ${result?.ad[0]?.ad_id}今天是否已经完成过了`);
          const completed = await storage.get(key);
          if (completed) {
            console.log(`该品牌广告ad_id: ${result?.ad[0]?.ad_id}今天已经完成过了`);
            localStorage.removeItem(generateCacheKey(huichuanBrandTaskSlotId));
            dispatch.ad.onTaskBrandAdUpdate({taskBrandAd: null, taskCorpAd: store.getState().ad.taskCorpAd});
            dispatch.task.updateHuichuanTaskList({taskBrandAd: null, taskCorpAd: store.getState().ad.taskCorpAd});
            return null;
          } else {
            console.log(`该品牌广告ad_id: ${result?.ad[0]?.ad_id}今天还没完成`);
          }
        }
        dispatch.ad.onTaskBrandAdUpdate({taskBrandAd: result, taskCorpAd: store.getState().ad.taskCorpAd});
        dispatch.task.updateHuichuanTaskList({taskBrandAd: result, taskCorpAd: store.getState().ad.taskCorpAd});
        console.log('汇川品牌广告任务返回值', result);
        return result;
      }
    },
    async fetchTaskCorpAd(payload: { force: boolean; slot_id: string; }, rootState: StoreState) {
      const {force = false, slot_id} = payload;
      if (slot_id) {
        const result = await hcAdHelper.requestHuichuanAd(slot_id, force);
        if (result) {
          const currAdStore = store.getState().ad;
          dispatch.ad.onTaskCorpAdUpdate({
            taskBrandAd: currAdStore.taskBrandAd,
            taskCorpAd: {...currAdStore.taskCorpAd, [slot_id]: result}
          });
          dispatch.task.updateHuichuanTaskList({
            taskBrandAd: currAdStore.taskBrandAd,
            taskCorpAd: {...currAdStore.taskCorpAd, [slot_id]: result}
          });
        }
        return result;
      }
    },
    // Promise.all 请求汇川广告
    async fetchAllTaskCorpAd(payload: { force: boolean; slot_ids: Array<string>; }, rootState: StoreState) {
      const {force = false, slot_ids} = payload;
      if (slot_ids.length) {
        const startTime = Date.now();
        // 先从缓存中拿一次渲染，再发请求
        const cacheTaskCorpAd = getCorpTaskFromCache(slot_ids)
        if (Object.keys(cacheTaskCorpAd).length) {
          const currAdStore = store.getState().ad;
          dispatch.ad.onTaskCorpAdUpdate({
            taskBrandAd: currAdStore.taskBrandAd,
            taskCorpAd: {...currAdStore.taskCorpAd, ...cacheTaskCorpAd}
          });
          dispatch.task.updateHuichuanTaskList({
            taskBrandAd: currAdStore.taskBrandAd,
            taskCorpAd: {...currAdStore.taskCorpAd, ...cacheTaskCorpAd}
          });
        }
        let fetchArr: Array<any> = [];
        slot_ids.forEach(slot_id => {
          fetchArr.push(hcAdHelper.requestHuichuanAd(slot_id, force));
        });
        const adMonitor = tracker.Monitor(WPK_CATEGORY_MAP.PAGE_AD_FETCH);
        const data = await Promise.all(fetchArr).then(result => result);
        console.log('汇川广告请求返回数组：', data, slot_ids);
        const taskCorpAd = {};
        data.forEach(item => {
          if (item) {
            taskCorpAd[item.slot_id] = item;
          }
        })
        const currAdStore = store.getState().ad;
        dispatch.ad.onTaskCorpAdUpdate({
          taskBrandAd: currAdStore.taskBrandAd,
          taskCorpAd: {...currAdStore.taskCorpAd, ...taskCorpAd}
        });
        dispatch.task.updateHuichuanTaskList({
          taskBrandAd: currAdStore.taskBrandAd,
          taskCorpAd: {...currAdStore.taskCorpAd, ...cacheTaskCorpAd}
        });
        // const huichuanGameTaskSlotList = store.getState().app.huichuanGameTaskSlotList;
        // const slot_keys = Object.keys(taskCorpAd).filter(key => !huichuanGameTaskSlotList.includes(key)); // 填充 slot_id 数组
        const slot_keys = Object.keys(taskCorpAd); // 填充 slot_id 数组
        // const slot_ids_without_game = slot_ids.filter(key => !huichuanGameTaskSlotList.includes(key));
        const fillSlots = slot_keys.join('`'); // 填充 slot_id 拼接字符串
        // const originSlots = slot_ids_without_game.join('`'); // 请求 slot_id 拼接字符串
        const originSlots = slot_ids.join('`'); // 请求 slot_id 拼接字符串

        if (slot_keys.length > 0) {
          // 有填充
          let fillFactSubmitted = false;  // 表示新请求是否填充
          const cache_slots: Array<string> = [];  // 本地缓存 slot_id 数组
          const new_req_slots: Array<string> = [];  // 新请求 slot_id 数组
          slot_keys.forEach(slotKey => {
            if (taskCorpAd[slotKey].from_cache) {
              cache_slots.push(slotKey);
            } else {
              new_req_slots.push(slotKey);
            }
          })
          const cacheSlots = cache_slots.join('`'); // 本地缓存 slot_id 拼接字符串
          const newFillSlots = new_req_slots.join('`'); // 新请求 slot_id 拼接字符串

          if (new_req_slots.length > 0) {
            // 新请求有广告填充返回
            fact.event('page_huichuan_ad_fetch', {
              is_fill: 1,
              fill_slots: fillSlots,
              origin_Slots: originSlots,
              new_fill_slot: newFillSlots,
            })
            adMonitor.success({
              msg: '新请求有填充',
              c1: fillSlots,
              c2: originSlots,
              c3: cacheSlots,
              c4: newFillSlots,
            });
            fillFactSubmitted = true;
            console.log('页面级别请求-新请求有填充');
          }

          if (!fillFactSubmitted) {
            // 这里表示当前没有新的请求填充
            fact.event('page_huichuan_ad_fetch', {
              is_fill: 0,
              fill_slots: fillSlots,
              origin_slots: originSlots,
              cache_slotes: cacheSlots,
            })
            // if (slot_keys.length === slot_ids_without_game.length) {
            if (slot_keys.length === slot_ids.length) {
              // 没有新请求填充的情况下，页面填充的slot跟发起slot一样，表示都是缓存，没有发起新请求，不打点
              // 12.03 更新，有缓存则有广告展示，打点
              adMonitor.success({
                msg: '无新请求-有广告缓存',
                c1: fillSlots,
                c2: originSlots,
                c3: cacheSlots,
              });
              console.log('页面级别请求-无新请求-有广告缓存')
            } else {
              adMonitor.success({
                msg: '新请求无填充-有广告缓存',
                c1: fillSlots,
                c2: originSlots,
                c3: cacheSlots,
              });
              console.log('页面级别请求-新请求无填充-有广告缓存');
            }
          }
        } else {
          // 无广告填充返回
          fact.event('page_huichuan_ad_fetch', {
            is_fill: 0,
            fill_slots: fillSlots,
            origin_Slots: originSlots,
          })
          adMonitor.fail({
            msg: '新请求无填充-无广告缓存',
            c1: fillSlots,
            c2: originSlots,
          });
          console.log('页面级别请求-新请求无填充-无广告缓存');
        }
        tracker.log({
          category: 177,
          msg: '汇川广告请求耗时',
          wl_avgv1: Date.now() - startTime
        });
        return data;
      }
      return null;
    },
    async exposure(key: string, rootState: StoreState) {
      hcAdHelper.hcAdExpose(rootState.ad[key]?.ad?.[0]);
    },
    async click(key: string, rootState: StoreState) {
      await hcAdHelper.hcAdClick(rootState.ad[key].ad[0]);
    },
    async corpTaskExposure(payload: { accountId: string; slotId: string; }, rootState: StoreState) {
      let targetAd;
      const {accountId, slotId} = payload;
      const slotAd = rootState.ad.taskCorpAd[slotId];
      if (slotAd
        && slotAd?.ad
        && slotAd.ad?.length
      ) {
        slotAd.ad.forEach(adInfo => {
          if (adInfo.ad_content.account_id === accountId) {
            targetAd = adInfo;
          }
        });
      }
      if (targetAd) {
        hcAdHelper.hcAdExpose(targetAd);
      }
    },
    async corpTaskClick(payload: { accountId: string; slotId: string; taskToken: string }, rootState: StoreState) {
      let targetAd;
      const {accountId, slotId, taskToken} = payload;
      const slotAd = rootState.ad.taskCorpAd[slotId];
      if (slotAd
        && slotAd?.ad
        && slotAd.ad?.length
      ) {
        slotAd.ad.forEach(adInfo => {
          if (adInfo.ad_content.account_id === accountId) {
            targetAd = adInfo;
          }
        });
      }
      if (targetAd) {
        const submitResult = await hcAdHelper.submitTaskRecord(slotAd.sid, taskToken, {
          account_id: accountId,
          slot_id: slotId
        });
        if (submitResult) {
          hcAdHelper.hcAdClick(targetAd);
          // storage 记录广告点击打点过期时间，记录点击时是否已经安装 app
          if (!isIOS) { // ios需要用 schema 查询，暂无
            if (targetAd?.ad_content?.package_name) {
              const record = await storage.get(`hc_ad_${slotId}_${accountId}_click_expire`);
              if (!record) {
                const expireTime = Date.now() + 60 * 60 * 1000;
                storage.set(`hc_ad_${slotId}_${accountId}_click_expire`, expireTime);
                const queryRes = await ucapi.biz.queryApp({
                  cache_first: '1',
                  pkgs: [targetAd?.ad_content?.package_name],
                });
                // 是否已安装
                const clickInstallFlag = isIOS ? (queryRes[targetAd.ad_content?.package_name] ? true : false) : (queryRes[targetAd.ad_content?.package_name]?.versionName ? true : false);
                storage.set(`hc_ad_${slotId}_${accountId}_click_install_status`, clickInstallFlag);
                setAppInstallWithExpiryMinutes({
                  pkg: targetAd?.ad_content?.package_name, 
                  install: clickInstallFlag
                });
              }
            }
          }
          return true;
        } else {
          console.error(`汇川任务上报失败${slotId}-${slotAd.sid}-${accountId}`);
        }
      }
      return false;
    }
  }
}

export default ad;
