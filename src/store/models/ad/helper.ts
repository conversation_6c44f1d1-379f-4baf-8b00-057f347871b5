import {generateCacheKey} from "@/utils/huichuan";

export const getCorpTaskFromCache = (slotIds: Array<string>) => {
  const cacheTaskCorpAd = {};
  slotIds.forEach(slotId => {
    const CACHE_KEY = generateCacheKey(slotId);
    // 判断本地广告缓存标识，缓存时间内直接返回缓存数据
    const currentTs = new Date().getTime();
    const hcAdCache = localStorage.getItem(CACHE_KEY);
    if (hcAdCache) {
      const cacheInfo = JSON.parse(hcAdCache);
      if (cacheInfo.expireTime > currentTs) {
        cacheInfo.ad.from_cache = true;
        cacheTaskCorpAd[slotId] = cacheInfo.ad
      } else {
        localStorage.removeItem(hcAdCache);
      }
    }
  });
  console.log('本地缓存的汇川广告:', cacheTaskCorpAd)
  return cacheTaskCorpAd
}
