import { getExtraInfo, isRtaAllTask } from "@/pages/index/task/help";
import { TASK_EVENT_TYPE, TASK_STATUS, TaskInfo } from "../task/types";
import { MODAL_ID } from "@/components/modals";
import { getLocalStorageWithExpiry, setLocalStorageWithExpiry } from "@/utils/localStorage";



export const isRTAHighTask = (taskInfo: TaskInfo) => {
  // return taskInfo.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DAILY || taskInfo.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU || taskInfo.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD;
  return isRtaAllTask(taskInfo);
}

export const needAutoShowReward = (taskInfo: TaskInfo) => {
  const eventList = [
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU,
    TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD,
    TASK_EVENT_TYPE.CALL_APP_DOWNLOAD,
    TASK_EVENT_TYPE.HIGH_VALUE_TASK,
    TASK_EVENT_TYPE.CORP_APP_TASK,
    TASK_EVENT_TYPE.CORP_APP_TASK_NEW,
    TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND,
  ]
  return eventList?.includes(taskInfo.event)
}

// 子任务排序
export const sortPreTaskList = (taskInfo: TaskInfo) => {
  if (!taskInfo?.extra) {
    return taskInfo
  }
  if (!taskInfo.preTaskList?.length) {
    return taskInfo
  }
  const taskExtra = getExtraInfo(taskInfo);
  const taskSort = taskExtra.taskSort;
  let newPreTaskList: TaskInfo[] = [...taskInfo.preTaskList];
  newPreTaskList.sort((a, b) => {
    return taskSort.indexOf(a.id) - taskSort.indexOf(b.id);
  });
  return {
    ...taskInfo,
    preTaskList: newPreTaskList
  }
}

/**
 * 高价值奖励弹窗展示逻辑
 */
export const getHighDialogCookie = (taskInfo: TaskInfo, awardShowLimit: string, bindTaobao: boolean) => {
  const storageKey = `${MODAL_ID.HIGH_VALUE_AWARD}_${taskInfo.id}`;
  const value = taskInfo.taskType.includes('every') ? getLocalStorageWithExpiry(storageKey) : localStorage.getItem(storageKey);

  if (taskInfo.state === TASK_STATUS.TASK_COMPLETED) {
    return Number(value) >= Number(awardShowLimit);
  }
  return value === 'true' || value === true;
}

/**
 * 高价值奖励弹窗展示逻辑
 */
export const setHighDialogCookie = (taskInfo: TaskInfo, bindTaobao: boolean) => {
  const storageKey = `${MODAL_ID.HIGH_VALUE_AWARD}_${taskInfo.id}`;
  if (bindTaobao) {
    taskInfo.taskType.includes('every') ? setLocalStorageWithExpiry(storageKey, true) : localStorage.setItem(storageKey, 'true');
    return;
  }
  const number = taskInfo.taskType.includes('every') ? getLocalStorageWithExpiry(storageKey) : localStorage.getItem(storageKey);
  const newValue = Number(number ?? 0) + 1;
  taskInfo.taskType.includes('every') ? setLocalStorageWithExpiry(storageKey, newValue) : localStorage.setItem(storageKey, String(newValue));
}
