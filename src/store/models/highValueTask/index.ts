import { RootModel, store } from '@/store';
import tracker from '@/lib/tracker';
import { createModel } from '@rematch/core';
import config from '@/config';
import { IHighValueTaskState, IQueryResourceInfo } from './types';
import { checkAppDownload, checkUcLoginTask, geneTaskRequestId, hiddenTimePeriodTask } from '../task/helper';
import network from '@/lib/network';
import { TASK_EVENT_TYPE, TASK_STATUS, TaskInfo } from '../task/types';
import { checkTaskFinished, getExtraInfo, isHuiChuangAdEffectTask, showUninstallAppTask, taskActionHandler } from '@/pages/index/task/help';
import baseModal from '@/components/modals/modal';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import { getHighDialogCookie, isRTAHighTask, needAutoShowReward, setHighDialogCookie, sortPreTaskList } from './utils';
import { TaobaoRtaConfig } from '../app';
import { MODAL_ID } from '@/components/modals';
import { isWeb } from 'universal-env';
import stat from '@/lib/stat';
import { combineCorpAdTask, getErrorInfo } from '../task/task_util';
import { preloadImg } from '@/lib/utils/preloadImg';
import Toast from '@/lib/universal-toast';


const PRE_ONLOAD_HIGH_RESOURCE_KEY = 'preOnloadLimitResourceWithWelfare';
const State: IHighValueTaskState = {
  responseData: null,
  resourceConfig: {
    awardShowLimit: "3"
  },
  resourceTaskList: [],
  hiddenTaskIdList: [],
  currentTaskInfo: null,
  taskPanelNeedHidden: new Map(),
  hasTaobaoRtaTask: false,
  hasHuiChuanTask: false,
  hasShowHighValueDialog: false,
  needFinishShowRewardTaskList: [],
};

const highValueTask = createModel<RootModel>()({
  state: State,
  reducers: {
    updateState(state: IHighValueTaskState, payload: Partial<IHighValueTaskState>): IHighValueTaskState {
      return {
        ...state,
        ...payload,
      };
    },
  },
  effects: (dispatch) => ({
    /**
     * 处理高价值数据
     */
    async handleHighValueTask(resourceData: {
      data: IQueryResourceInfo,
      timestamp: number
    } | null) {
      if (!resourceData?.data) {
        return;
      }
      const { timestamp } = resourceData;
      let { taskList, attributes } = resourceData?.data || {};
      const user = store?.getState()?.user
      const bindTaobao = user.bindTaobao;
      const { hasHuiChuanTask, hasTaobaoRtaTask } = dispatch.highValueTask.checkHasDependOnTask(taskList ?? []);
      // 任务定时上下线隐藏
      taskList = hiddenTimePeriodTask(taskList ?? [], timestamp);
      dispatch.highValueTask.updateState({
        resourceConfig: attributes,
        resourceTaskList: taskList,
        hasShowHighValueDialog: !bindTaobao, // 没有绑定淘宝的时候，默认有登录任务
        hasTaobaoRtaTask,
        hasHuiChuanTask
      });
      if(isWeb){
        dispatch.highValueTask.handleTaskVisible({
          showTaskDialog: false
        });
        dispatch.highValueTask.preOnloadResources(attributes || {});
      }
    },
    /**
     * 查询资源位任务列表
     */
    async queryHighValueTask(params: {
      firstInit?: boolean,
      resData: IQueryResourceInfo | null
    }) {
      const resourceCode = config.taskResourceCode;
      const resourceMonitor = tracker.Monitor(171);

      try {
        let response;
        if (params?.firstInit && params?.resData) {
          response = params.resData;
        } else {
          response = await dispatch.resource.queryResourceInfo(resourceCode);
        }
        dispatch.highValueTask.updateState({
          responseData: response,
        });
        if (!(response?.data?.taskList?.length || response?.data?.hiddenTaskIdList?.length)) {
          resourceMonitor?.fail({
            msg: '获取高价值任务失败-列表数据为空',
            c1: resourceCode,
            c2: (response as any)?.code || '',
            bl1: JSON.stringify(response),
          });
          return;
        }
        await dispatch.highValueTask.handleHighValueTask(response);
        resourceMonitor?.success({
          msg: '获取高价值任务成功',
          c1: resourceCode,
          bl1: JSON.stringify(response),
        });
      } catch (error) {
        const { errCode, msg} = getErrorInfo(error)
        resourceMonitor?.fail({
          msg: '获取高价值任务失败',
          c1: resourceCode,
          c2: msg,
          c3: errCode,
          bl1: JSON.stringify(error),
        });
      }
    },

    // 处理弹窗当前展示的任务
    async handleTaskVisible(params: { showTaskDialog: boolean }) {
      const resourceTaskList = store.getState().highValueTask.resourceTaskList || [];
      const taskCorpAd = store.getState().ad.taskCorpAd;
      const clientType = store.getState().app.clientType;
      const { showTaskDialog = true } = params;
      if (!resourceTaskList.length) {
        dispatch.highValueTask.updateState({
          hasShowHighValueDialog: false
        });
        tracker.log({
          category: 171, // 系统自动生成，请勿修改
          msg: '高价值-没有拿到数据返回', // 将根据msg字段聚合展示在平台的top上报内容中
        });
        return;
      }

       // 接口请求UV
      stat.custom('high-value-task', {
        c: 'task',
        d: 'award',
        status: 'task-query',
      });

      const isLogin =  store.getState().user.isLogin;
      const taskCompletedList = resourceTaskList.filter(item => item.state === TASK_STATUS.TASK_COMPLETED);
      // 登录绑定成功了，然后领取奖励
      if (isLogin && taskCompletedList.length > 0) {
        await dispatch.highValueTask.getAwardForHighTask(taskCompletedList || []);
        return;
      }

      let dialogTaskInfo: TaskInfo | null = null;

      // 需要检查是否已经完成任务的
      const hasTaskFinished = await dispatch.highValueTask.showAutoHighValueDialog(resourceTaskList);
      if (hasTaskFinished) {
        // 任务完成接口
        stat.custom('high-value-task', {
          c: 'task',
          d: 'award',
          status: 'task-finish',
        });
        return;
      }

      let needFinishList = resourceTaskList.filter((item) => !checkTaskFinished(item));
      if (!needFinishList.length) {
        console.warn('高价值没有需要完成的任务');
        tracker.log({
          category: 174, // 系统自动生成，请勿修改
          msg: '高价值没有需要完成的任务', // 将根据msg字段聚合展示在平台的top上报内容中
          c1: '', // 自定义字段c1 对应 任务ID
          c2: '', // 自定义字段c2 对应 任务名称
          c3: '', // 自定义字段c3 对应 投放ID
          c4: '', // 自定义字段c4 对应 任务状态
          bl1: JSON.stringify(resourceTaskList), // 自定义长文本bl1 对应 任务详情
        });
        dispatch.highValueTask.updateState({
          hasShowHighValueDialog: false
        });
        return;
      }
      // 需要在任务完成时候，领取奖励
      const needFinishShowRewardTaskList = needFinishList.filter((item) => !needAutoShowReward(item));
      dispatch.highValueTask.updateState({
        needFinishShowRewardTaskList: needFinishShowRewardTaskList.map((item) => String(item.id)),
      });

      const adStoreDataList =  store.getState().task.adStoreDataList;
      // 处理汇川相关
      needFinishList = combineCorpAdTask(needFinishList, taskCorpAd, clientType, adStoreDataList);

      for (const task of needFinishList) {
        let showTask = true; // 是否展示任务
        // 下载类： 领取过的任务，已经完成了触发任务完成
        if (task.state === TASK_STATUS.TASK_NOT_COMPLETED) {
          const result = await dispatch.task.checkAppDownloadFinish({
            taskInfo: task,
            showToast: false
          });
          if (result) {
            tracker.log({
              category: 174, // 系统自动生成，请勿修改
              msg: '下载任务完成-领取奖励', // 将根据msg字段聚合展示在平台的top上报内容中
              c1: String(task.id), // 自定义字段c1 对应 任务ID
              c2: String(task.name), // 自定义字段c2 对应 任务名称
              c3: String(task.publishId), // 自定义字段c3 对应 投放ID
              c4: '', // 自定义字段c4 对应 任务状态
              bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
            });
            // 更新资源位弹窗
            await dispatch.highValueTask.queryHighValueTask({
              firstInit: false,
              resData: null
            });
            return;
          }
        }

        // RTA 任务
        if (isRTAHighTask(task)) {
          showTask = await dispatch.highValueTask.checkTaobaoRtaTask(task);
          tracker.log({
            category: 174, // 系统自动生成，请勿修改
            msg: showTask ? 'RTA任务满足身份' : 'RTA任务不满足身份', // 将根据msg字段聚合展示在平台的top上报内容中
            c1: String(task.id), // 自定义字段c1 对应 任务ID
            c2: String(task.name), // 自定义字段c2 对应 任务名称
            c3: String(task.publishId), // 自定义字段c3 对应 投放ID
            c4: String(task.state), // 自定义字段c4 对应 任务状态
            bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
          });
          if (showTask) {
            dialogTaskInfo = task;
            break;
          }
          continue;
          // 下载任务
        } else if ([TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.CORP_APP_TASK, TASK_EVENT_TYPE.CORP_APP_TASK_NEW, TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND].includes(task.event)) {
          const { showUninstallApp } = getExtraInfo(task)
          const isInstall = await checkAppDownload(task);
          showTask = showUninstallApp ? !isInstall : isInstall;

          tracker.log({
            category: 174, // 系统自动生成，请勿修改
            msg: showTask ? '下载任务满足条件' : '下载任务不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
            c1: String(task.id), // 自定义字段c1 对应 任务ID
            c2: String(task.name), // 自定义字段c2 对应 任务名称
            c3: String(task.publishId), // 自定义字段c3 对应 投放ID
            c4: String(task.state), // 自定义字段c4 对应 任务状态
            bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
          });
          if (showTask) {
            dialogTaskInfo = task;
            break;
          }
          continue;
          // 多步骤任务
        } else if (task.event === TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
          const newTask = sortPreTaskList(task);
          const { result } = await dispatch.highValueTask.checkPreTask(newTask);
          showTask = result;
          tracker.log({
            category: 174, // 系统自动生成，请勿修改
            msg: showTask ? '多步骤任务满足条件' : '多步骤任务不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
            c1: String(task.id), // 自定义字段c1 对应 任务ID
            c2: String(task.name), // 自定义字段c2 对应 任务名称
            c3: String(task.publishId), // 自定义字段c3 对应 投放ID
            c4: String(task.state), // 自定义字段c4 对应 任务状态
            bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
          });
          if (showTask) {
            // 子任务排序
            dialogTaskInfo = newTask;
            break;
          }
          continue;
        } else {
          // 其他任务
          showTask = await showUninstallAppTask(task);
          tracker.log({
            category: 174, // 系统自动生成，请勿修改
            msg: showTask ? '任务满足条件' : '任务不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
            c1: String(task.id), // 自定义字段c1 对应 任务ID
            c2: String(task.name), // 自定义字段c2 对应 任务名称
            c3: String(task.publishId), // 自定义字段c3 对应 投放ID
            c4: String(task.state), // 自定义字段c4 对应 任务状态
            bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
          });
          if(showTask){
            dialogTaskInfo = task;
            break;
          }
          continue;
        }
      }
      if (dialogTaskInfo && Object.keys(dialogTaskInfo).length) {
        dispatch.highValueTask.updateState({
          currentTaskInfo: dialogTaskInfo,
          hasShowHighValueDialog: true,
        });

        // 判断是否有登录任务
        const ucLoginTask = checkUcLoginTask(dialogTaskInfo);
        if (ucLoginTask.hasUcLoginTask) {
          dispatch.app.updateState({
            ucLoginTask,
          });
        }
        // 打开弹窗
        if(showTaskDialog){
          baseModal.openHighValueTask();
          // 更新任务列表数据
          dispatch.task.generateTaskList({});
          stat.custom('high-value-task', {
            c: 'task',
            d: 'award',
            status: 'task-doing',
            task_id: dialogTaskInfo.id,
            task_name: dialogTaskInfo.name
          });
        }
        return;
      }
      stat.custom('high-value-task', {
        c: 'task',
        d: 'award',
        status: 'task-not-have',
      });
      dispatch.highValueTask.updateState({
        hasShowHighValueDialog: false,
      });
    },
    /**
     * 判断子任务逻辑
     */
    async checkPreTask(taskInfo: TaskInfo) {
      const { preTaskList = [] } = taskInfo;
      const user = store?.getState()?.user
      if (!preTaskList.length) {
        console.error('任务配置错误');
        return {
          result: false,
          hasTaobaoRta: false,
        };
      }

      // 手淘RTA
      const isRTATask = preTaskList.filter((item) => isRTAHighTask(item));
      if (isRTATask.length > 0) {
        const result = await dispatch.highValueTask.checkTaobaoRtaTask(isRTATask[0]);

        // RTA里面有下载任务
        const downloadTask = preTaskList.filter(
          (item) =>
            item.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_NU ||
            item.event === TASK_EVENT_TYPE.RTA_CALL_TAOBAO_DOWNLOAD,
        );
        if (downloadTask.length > 0) {
          await dispatch.task.checkAppDownloadFinish({
            taskInfo: downloadTask[0],
            showToast: false
          });
        }
        // RTA里面有下载任务只有登录任务没有完成
        const needDoPreTaskList = preTaskList.filter(item => !checkTaskFinished(item));
        if (needDoPreTaskList.length === 1) {
          const preTask = needDoPreTaskList[0];
          if (preTask.event === TASK_EVENT_TYPE.UC_LOGIN && user.bindTaobao) {
            await dispatch.task.complete({
              id: preTask.id,
              type: 'complete',
              useUtCompleteTask: !!preTask?.useUtCompleteTask,
              publishId: preTask.publishId,
              params: {
                task: preTask,
                toast: false
              }
            });
            await dispatch.highValueTask.queryHighValueTask({
              firstInit: false,
              resData: null
            });
          }
        }
        tracker.log({
          category: 174, // 系统自动生成，请勿修改
          msg: result ? '多步骤任务-RTA满足条件' : '多步骤任务-RTA不满足条件', // 将根据msg字段聚合展示在平台的top上报内容中
          bl1: JSON.stringify(preTaskList), // 自定义长文本bl1 对应 任务详情
        });
        return {
          result,
          hasTaobaoRta: true,
        };
      }

      // 下载类
      const isAppDownload = preTaskList.filter((item) => [TASK_EVENT_TYPE.CALL_APP_DOWNLOAD, TASK_EVENT_TYPE.CORP_APP_TASK, TASK_EVENT_TYPE.CORP_APP_TASK_NEW, TASK_EVENT_TYPE.CORP_APP_TASK_EXTEND].includes(item.event));
      if (isAppDownload) {
        const result = checkAppDownload(isAppDownload[0]);
        return {
          result: !result,
          hasTaobaoRta: false,
        };
      }
      return {
        result: true,
        hasTaobaoRta: false,
      };
    },
    /**
     * 手淘RTA相关任务处理
     */
    async checkTaobaoRtaTask(taskInfo: TaskInfo) {
      const taobaoRtaInfo = store.getState().rta.taobaoRtaInfo;
      const taobaoRtaConfig = (store.getState().app.taobaoRtaConfig || {}) as TaobaoRtaConfig;
      const { highPriorityTaskId = [] } = taobaoRtaConfig;

      console.log('resourceTaskList checkTaobaoRtaTask', taobaoRtaInfo, highPriorityTaskId);
      const checkShowRtaTask = (task: TaskInfo) => {
        const taskId = `${task.id}`;
        if (!task?.extra) {
          return false;
        }
        const taskExtra = getExtraInfo(task);
        if (!highPriorityTaskId && taskExtra?.defaultHidden === '1') {
          return false;
        }
        if (!taskExtra?.category || !taobaoRtaInfo?.category) {
          return false;
        }
        // 过滤和当前RTA用户身份匹配的任务
        if (highPriorityTaskId?.includes(taskId) && taskExtra?.category === taobaoRtaInfo?.category) {
          return true;
        }
        return false;
      };
      const result = checkShowRtaTask(taskInfo);
      tracker.log({
        category: 174, // 系统自动生成，请勿修改
        msg: `RTA身份校验结果-${result}`, // 将根据msg字段聚合展示在平台的top上报内容中
        c1: String(taskInfo.id || ''),
        c5: String(result),
        bl1: JSON.stringify(taskInfo || {}),
        bl2: JSON.stringify(taobaoRtaInfo || {})
      });
      stat.custom('high-value-task', {
        c: 'task',
        d: 'award',
        status: 'task-check-rta',
        category: result
      });
      return result;
    },
    async resourceExposure(params: { taskInfo: TaskInfo; actionType: 'EXPOSURE' | 'CLICK'; code: string }) {
      const { taskInfo, actionType, code } = params;
      const { coralHost, appId } = config;
      const { kps = '' } = store.getState().user;
      const exposureMonitor = tracker.Monitor(172);
      const query = {
        code,
        appId,
        kps,
        taskId: taskInfo?.id || '',
        actionType,
        publishId: taskInfo?.publishId || '',
      };
      try {
        const result = await network.get(`${coralHost}/uclite/resourceExposure`, query, {
          originalResponse: true
        });
        console.log('resourceExposure', result);
        if (result.code === 'OK') {
          exposureMonitor.success({
            msg: '上报成功',
            c1: String(taskInfo?.id || ''),
            c2: taskInfo?.name,
            c3: actionType,
            bl1: JSON.stringify(taskInfo),
          });
        }
      } catch (error) {
        exposureMonitor.fail({
          msg: '上报失败',
          c1: String(taskInfo?.id || ''),
          c2: taskInfo?.name,
          c3: actionType,
          bl1: JSON.stringify(taskInfo),
          bl2: error,
        });
      }
    },
    // 弹窗确认做任务操作
    dialogConfirmEvent() {
      const currentTaskInfo: TaskInfo | null = store.getState().highValueTask.currentTaskInfo;
      if (!currentTaskInfo) {
        return;
      }

      let taskInfo = currentTaskInfo;
      if (currentTaskInfo?.preTaskList && currentTaskInfo?.preTaskList?.length) {
        const firstTask = currentTaskInfo?.preTaskList?.filter((item) => {
          return !checkTaskFinished(item);
        });

        // 更新任务列表
        if (firstTask.length === 0) {
          dispatch.highValueTask.queryHighValueTask({
            firstInit: false,
            resData: null
          });
          return;
        }
        taskInfo = firstTask[0];
      }

      stat.click('task_click', {
        c: 'home',
        d: 'pop',
        resource_location: 'high_value_pop',
        task_id: taskInfo.id,
        task_name: taskInfo.name,
        taskclassify: taskInfo?.taskClassify,
        groupcode: taskInfo?.groupCode,
      });
      taskActionHandler(taskInfo, {});

      const user = store?.getState()?.user
      if (user.isLogin || user.bindTaobao) {
        // 跳转后，关闭任务弹窗
        if (currentTaskInfo.event !== TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
          baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
        }
      }
    },

    // 弹窗关闭逻辑
    dialogCancelEvent() {},
    /**
     * 展示领奖弹窗
     */
    showAutoHighValueDialog(resourceTaskList: TaskInfo[]) {
      const isLogin = store?.getState()?.user.isLogin;
      const awardShowLimit = store?.getState()?.highValueTask?.resourceConfig?.awardShowLimit || '3';

      const baseCurrentOpenModal = modal?.getCurrentOpenModalObj() || {};
      return new Promise((resolve) => {
        const hasFinishedTaskList = resourceTaskList.filter(item => checkTaskFinished(item) || item.state === TASK_STATUS.TASK_COMPLETED);
        if (!hasFinishedTaskList.length) {
          resolve(false);
          return;
        }
        if (!isLogin && baseCurrentOpenModal[MODAL_ID.HIGH_VALUE_AWARD]) {
          resolve(true);
          return;
        }

        let showAwardDialog = false;
        for (const task of hasFinishedTaskList) {
          const { rewardItems, prizes = [], state } = task;
          const prizeInfo = state === TASK_STATUS.TASK_COMPLETED && !isLogin ? rewardItems[0] : prizes[0]?.rewardItem || {};

          // 移除自动领奖的部分，避免刷接口
          if(isLogin && !needAutoShowReward(task) && checkTaskFinished(task) && !getHighDialogCookie(task, awardShowLimit, isLogin)){
            // 登录情况下，二次校验，同时延迟展示
            setTimeout(()=> {
              if(!getHighDialogCookie(task, awardShowLimit, isLogin)){
                baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
                Toast.show('任务完成，奖励已发放');
                stat.exposure('reward_exposure', {
                  c: 'home',
                  d: 'pop',
                  resource_location: 'high_value_pop',
                  task_id: task.id,
                  task_name: task.name,
                });
                setHighDialogCookie(task, isLogin);
              }
            }, 1000)
            continue;
          }

          // 解决登录问题导致随机奖励展示不正确
          if(isLogin && Object.keys(prizeInfo).length === 0) {
            continue;
          }

          if (!getHighDialogCookie(task, awardShowLimit, isLogin)) {
            baseModal.close(MODAL_ID.HIGH_VALUE_TASK);
            baseModal.openHighValueAward({
              ...prizeInfo,
              // 任务名称
              taskName: task.name,
              taskId: task.id,
              isLogin
            });
            setHighDialogCookie(task, isLogin)
            tracker.log({
              category: 174, // 系统自动生成，请勿修改
              msg: '任务完成-展示弹窗', // 将根据msg字段聚合展示在平台的top上报内容中
              c1: String(task.id), // 自定义字段c1 对应 任务ID
              c2: String(task.name), // 自定义字段c2 对应 任务名称
              c3: String(task.publishId), // 自定义字段c3 对应 投放ID
              c4: String(task.state), // 自定义字段c4 对应 任务状态
              bl1: JSON.stringify(task), // 自定义长文本bl1 对应 任务详情
            });
            showAwardDialog = true;
            // 非登录态展示一个就行了
            if (!isLogin) {
              resolve(showAwardDialog);
              return;
            }
          }
        }
        resolve(showAwardDialog);
      })
    },
    /**
   * 获取任务奖励
   */
  async getAwardForHighTask(taskCompletedList: TaskInfo[]) {
    taskCompletedList.forEach(async item => {
      await dispatch.task.complete({
        id: item?.id,
        type: 'award',
        useUtCompleteTask: !!item?.useUtCompleteTask,
        publishId: item.publishId,
        params: {
          task: item,
          toast: false
        }
      });
    });
    stat.custom('high-value-task', {
      c: 'task',
      d: 'award',
      status: 'task-complete',
    });
    await dispatch.highValueTask.queryHighValueTask({
      firstInit: false,
      resData: null
    });
  },
  /**
   * 弹窗资源预加载
   */
  preOnloadResources(resourceConfig: Record<string, any>) {
    const key = PRE_ONLOAD_HIGH_RESOURCE_KEY;
    if (localStorage.getItem(key)) {
      return;
    }

    let imgList: string[] = [];
    if (Object.keys(resourceConfig?.dialogStyle || {}).length) {
      Object.keys(resourceConfig?.dialogStyle || {}).forEach((style: string) => {
        if (['topBgImg', 'successTopBgImg', 'bottomBgImg'].includes(style)) {
          const value: string = resourceConfig?.dialogStyle[style];
          value && imgList.push(value);
        }
      })
    }
    try {
      preloadImg(imgList);
      localStorage.setItem(PRE_ONLOAD_HIGH_RESOURCE_KEY, 'true');
    } catch (error) {
    // 捕获一下error
      console.error(error);
    }
  },
  /**
   * rta 任务曝光
   */
  rtaTaskExposure() {
    const currentTaskInfo: TaskInfo | null = store.getState().highValueTask.currentTaskInfo;
    const taobaoRtaInfo = store.getState().rta.taobaoRtaInfo;
    if (!currentTaskInfo || !taobaoRtaInfo) {
      return;
    }
    // 列表有任务，曝光上报
    if (isRTAHighTask(currentTaskInfo)) {
      dispatch.rta.ratTaskExposure({});
      return;
    }

    if (currentTaskInfo.event !== TASK_EVENT_TYPE.HIGH_VALUE_TASK) {
      return;
    }
    if (!currentTaskInfo.preTaskList?.length) {
      return;
    }

    // 子任务，曝光上报
    const isRTATask = currentTaskInfo.preTaskList.filter(item => isRTAHighTask(item));
    if (isRTATask.length > 0) {
      dispatch.rta.ratTaskExposure({});
    }
  },
  checkHasDependOnTask(resourceTaskList: TaskInfo[]){
    let hasTaobaoRtaTask = false;
    let hasHuiChuanTask = false;

    resourceTaskList.forEach((task)=> {
      if(isRTAHighTask(task)){
        hasTaobaoRtaTask = true;
      }
      if(isHuiChuangAdEffectTask(task)){
        hasHuiChuanTask = true;
      }
      if(task?.preTaskList && task?.preTaskList.length){
        task.preTaskList.forEach((item)=> {
          if(isRTAHighTask(item)){
            hasTaobaoRtaTask = true;
          }
          if(isHuiChuangAdEffectTask(task)){
            hasHuiChuanTask = true;
          }
        })
      }
    });
    return {
      hasTaobaoRtaTask,
      hasHuiChuanTask
    }
  }
  }),
});

export default highValueTask;
