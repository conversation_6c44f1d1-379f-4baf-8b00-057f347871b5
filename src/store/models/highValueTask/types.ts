import { RewardItem, TaskInfo } from "../task/types";


export interface IQueryResourceInfo {
  attributes: Record<string, string>;
  taskList: TaskInfo[];
  hiddenTaskIdList: number[];
}

export interface  IQueryResourceExposure{
  code: string;
  taskId: number;
  actionType: 'EXPOSURE' | 'CLICK',
  publishId: number;
}
export interface IResourceExposureResponse {
  code: string;
  msg: string;
  success: boolean;
  timestamp: number;
  traceId: string;
}
export interface HighTaskDialogTypes {
  /** 任务相关信息-源数据 */
  taskInfo: TaskInfo;
  /** 弹窗顶部icon */
  dialogTopIcon: string;
  /** 弹窗顶部背景图片 */
  dialogTopBgImg: string;
  /** 弹窗标题 */
  dialogTitle: string;
  /** 弹窗内容： 最高 ｜ 元 ｜ 肥料 */
  dialogContent: RewardItem;
  /** 弹窗默认填充色 */
  dialogContainerBgColor: string;
  /** 弹窗底部背景图片 */
  dialogBottomBgImg: string;
  /** 是否有子任务 */
  hasPreTaskList: boolean;
  /** 子任务列表 */
  preTaskList: TaskInfo[];
  /** 弹窗做任务按钮 */
  dialogConfirmBtnText: string;
  dialogConfirmEvent: (data: TaskInfo) => void;
  /** 弹窗关闭按钮 */
  dialogCancelBtnText: string;
  dialogCancelEvent: (data: TaskInfo) => void;
}


export interface IHighValueTaskState {
  responseData: IQueryResourceInfo | null,
  resourceConfig: Record<string, string>;
  resourceTaskList: TaskInfo[];
  hiddenTaskIdList: number[],
  taskPanelNeedHidden: Map<string, string>;
  // 是否有首淘任务
  hasTaobaoRtaTask: boolean;
  // 是否有汇川大奖任务
  hasHuiChuanTask: boolean;
  // 当前需要弹窗任务数据
  currentTaskInfo: TaskInfo | null;

  // 是否展示高价值弹窗，用于处理弹窗优先级
  hasShowHighValueDialog: boolean;

  // 需要在finish的逻辑展示结果弹窗的任务ID
  needFinishShowRewardTaskList: string[];
}
