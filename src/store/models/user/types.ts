export interface IBindThirdInfo {
  result: 'success' | 'failed';
  /** nologin 表示当前未登录，unbind 表示没有该第三方账号类型的绑定信息。 */
  fail_msg: 'nologin' | 'unbind';
  /**   "taobao"|"alipay" */
  bindThirdType: 'taobao' | 'alipay';
  /** 第三方账号昵称，可能为空  */
  third_nickname: string;
  /**   第三方账号头像，可能为空 */
  third_avatar_url: string;
  /** 第三方账号信息 third_kps=base6 */
  third_kps: string;
  /** 错误信息code */
  fail_event: string; 
  // ios返回的错误码platform_code，android返回fail_event
  platform_code?: string;
}
