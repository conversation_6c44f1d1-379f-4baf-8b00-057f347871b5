import config from '@/config';
import ucapi from '@/utils/ucapi';
import { ClientInfo, LoginStatus, UserState } from '.';
import { store, dispatch } from '@/store/index'
import { TASK_STATUS } from "@/store/models/task/types";

export function getUid(uid?: string, utdid?: string, ut?: string) {
  const str = [uid || '', utdid || 'unknown', encodeURIComponent(ut || '')]
    .filter(i => !!i)
    .join('_');
  return str;
}

export const geneBindParams = async (ut: string) => {
  const { appId, moduleCodeBind: moduleCode } = config;
  const timestamp = Date.now();
  const nonce = Date.now();
  const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
  const signOriText = `${timestamp}${nonce}${appId}${moduleCode}${ut}`;
  const sign = await ucapi.spam.sign({ text: signOriText, salt });
  return { appId, moduleCode, timestamp, nonce, sign };
};
export const transformClientInfo = (clientInfo: Partial<ClientInfo>): Partial<UserState> => {
  const { kps_wg = '', sign_wg = '', uId = '', nickname = '', avatar_url = '', vCode, utdId, loginStatus, sv = '', ve = '' } = clientInfo;
  return {
    kps: kps_wg,
    sign: sign_wg,
    uid: uId,
    isLogin: !!loginStatus,
    nickname,
    avator: avatar_url || '',
    vCode: vCode || 0,
    utdId: utdId || '',
    ut: clientInfo.ut || '',
    status: loginStatus ? LoginStatus.login : LoginStatus.visitor,
    ve,
    sv
  };
};

export const checkAndCompleteFukaTask = () => {
  if (store.getState().task?.fukaSignTask) {
    const fukaSignTask = store.getState().task.fukaSignTask
    if (fukaSignTask?.state === TASK_STATUS.TASK_DOING) {
      localStorage.setItem('bindAlipaySuccess', '1')
      dispatch.task.complete({id: fukaSignTask?.id, type: 'complete', params: {task: fukaSignTask}} )
    }
  }
}

export default {
  geneBindParams,
};
