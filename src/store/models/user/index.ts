import tracker from '@ali/weex-toolkit/lib/tracker';
import ucapi from '@/utils/ucapi';
import { RootModel, store } from '@/store';
import fact from '@/lib/fact';
import config from '@/config';
import network from '@/utils/network';
import { getUid, geneBindParams, transformClientInfo, checkAndCompleteFukaTask } from '@/store/models/user/helper';
import { createModel } from '@rematch/core';
import { isIOS } from "@/lib/universal-ua";
import { isAndroid } from '@ali/weex-toolkit/lib/ua';
// import jsbridge from "@ali/weex-toolkit/lib/ucapi";
import { uc } from '@ali/weex-toolkit/lib/weex_config';
import { IBindThirdInfo } from "@/store/models/user/types";
import Toast from '@/lib/universal-toast';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import { geneTaskRequestId } from '../task/helper';
import baseModal from '@/components/modals/modal';
import { getErrorInfo } from '../task/task_util';
import { getParam } from '@/lib/qs';

const ALIPAY_PKG = isIOS ? 'alipays://' : 'com.eg.android.AlipayGphone'
const checkIfInstalledAlipay = async () => {
  const res = await ucapi.biz.queryApp({
    cache_first: '0',
    pkgs: [ALIPAY_PKG],
  });
  const appInstallInfo = (res || {})[ALIPAY_PKG];
  return appInstallInfo?.appSize > 0 || appInstallInfo?.canOpen
}

export const enum LoginStatus {
  /** 游客 */
  visitor = 1,
  /** 退登 */
  logout = 2,
  /** 登录 */
  login = 3,
  /** 非UC */
  notuc = 4,
}
export interface UserState {
  kps: string;
  sign: string;
  uid: string;
  isLogin: boolean;
  nickname: string;
  avator: string;
  vCode: number;
  utdId: string;
  ut: string;
  status: LoginStatus;
  userType?: UserType;
  bindInfo?: IBindThirdInfo;
  hasBindedAlipay?: boolean;
  bindTaobao: boolean;
  ve: string;
  sv: string;
  service_ticket: string;
}

/** 用户 -1=初始值 0=老用户 1=NU  2=回流 */
export const enum UserType {
  unknown = -1,
  old = 0,
  new = 1,
  back = 2
}

/** 客户端返回用户信息 */
export interface ClientInfo {
  kps_wg: string;
  sign_wg: string;
  uId: string;
  nickname: string;
  avatar_url: string;
  vCode: number;
  utdId: string;
  ut: string;
  ds: string;
  loginStatus: boolean;
  ve: string;
  sv: string;
  service_ticket: string;
}

const State: UserState = {
  kps: '',
  sign: '',
  uid: '',
  isLogin: false,
  nickname: '',
  avator: '',
  vCode: -1,
  ut: '',
  utdId: '',
  status: 1,
  userType: UserType.unknown,
  hasBindedAlipay: false,
  bindTaobao: false, // 是否绑定淘宝账户
  ve: '',
  sv: '',
  service_ticket: '',
};
const user = createModel<RootModel>()({
  state: State,
  reducers: {
    updateUserInfo: (state: UserState, userInfo: Partial<UserState>) => {
      console.log('更新用户态', userInfo);
      return { ...state, ...userInfo };
    },
    updateBindInfo: (state: UserState, bindInfo: UserState['bindInfo']): UserState => {
      return {
        ...state,
        bindInfo,
        hasBindedAlipay: bindInfo?.result === 'success',
      }
    },
    updateBindAlipayStatus(state: UserState, binded: boolean): UserState {
      return {
        ...state,
        hasBindedAlipay: state.isLogin && binded,
      }
    },
  },
  effects: dispatch => ({
    async initTraceId(payload) {
      const { ucParams,  userInfo } = payload;
      const ut = decodeURIComponent((ucParams || { ut: '' }).ut);
      const initUid = getUid(userInfo.uId, decodeURIComponent(userInfo.utdId), decodeURIComponent(ut));
      tracker.config({
        uid: initUid,
      });
    },
    async setupUserInfo(firstInit = false, rootState): Promise<Partial<UserState>> {
      const vCode = Date.now();
      try {
        const clientInfo = await ucapi.account.getUserInfo();
        let ucParams = uc.params
        const appUcParams = rootState.app.ucParams;
        ucParams = appUcParams ? appUcParams : await ucapi.biz.ucparams({ params: 'utddvesv' }, true);
        dispatch.user.initTraceId({
          ucParams,
          userInfo: clientInfo,
        });
        const ut = decodeURIComponent((ucParams || { ut: '' }).ut);
        const userInfo = transformClientInfo({ ...clientInfo, vCode, ut, sv: ucParams?.sv, ve: ucParams?.ve });
        dispatch.user.updateUserInfo(userInfo);
        fact.baseParam({ ucid: clientInfo.uid || '', page_status: userInfo.isLogin ? '1': '0' });
        // 检测退登
        if (!userInfo.isLogin) await dispatch.user.checkLogoutStatus({});
        return userInfo;
      } catch (e) {
        console.log('ucapi.account.getUserInfo error: ', e);
        return rootState.user;
      }
    },
    async checkLogoutStatus(_, rootState) {
      const { bindHost } = config;
      const { utdId } = rootState.user;
      const params = await geneBindParams(utdId);
      try {
        const resData: { binded: boolean } = await network.get(`${bindHost}/tourist/v1/checkUtBindUid`, params);
        if (resData.binded) throw 'NEED_LOGIN';
      } catch (error) {
        // 已绑定和异常情况认定为退登用户
        dispatch.user.updateUserInfo({ status: LoginStatus.logout });
      }
    },
    async login() {
      ucapi.account.login();
    },
    async afterUserLogout() {
      const userInfo = { ...State, status: LoginStatus.logout };
      dispatch.user.updateUserInfo(userInfo);
      await dispatch.app.updateAll();
    },
    /** 查询用户类型 新用户/回流用户/老用户 */
    async queryUserType(kps?: string) {
      const data = await network.get(`${config.coralHost}/uclite/queryUserType`, {
        appId: config.LOGIN_TASK_APP_ID,
      });
      dispatch.user.updateUserInfo({
        userType: data.userType,
      });
      return data;
    },
    // 查询绑定信息
    async selecBindtInfo() {
      await dispatch.user.getBindTaobaoInfo()
      await dispatch.user.initBindAlipayInfo()
    },
    // 查询是否绑定支付宝
    async initBindAlipayInfo() {
      const bindInfoMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_ALIPAY_INFO);
      try {
        let bindInfo: IBindThirdInfo = await ucapi.account.getBindInfo('alipay', true);
        console.log('initBindAlipayInfo -> ', bindInfo);
        if ((bindInfo as any).errCode) {
          bindInfoMonitor.fail({
            msg: '获取绑定信息失败',
            bl1: JSON.stringify(bindInfo)
          })
          console.error('[user] 获取绑定信息失败 ', JSON.stringify(bindInfo));
          return;
        }
        if (bindInfo.result === 'success') {
          const fromAlipay = localStorage.getItem('loginFromAlipay')
          if (fromAlipay) {
            localStorage.removeItem('loginFromAlipay')
            checkAndCompleteFukaTask()
          }
          dispatch.user.updateBindInfo(bindInfo);
          bindInfoMonitor.success({
            msg: '已绑定',
            c1: '1',
            bl1: JSON.stringify(bindInfo)
          })
        } else {
          bindInfoMonitor.success({
            msg: '未绑定',
            c1: '0',
            bl1: JSON.stringify(bindInfo)
          })
        }
      } catch (e) {
        bindInfoMonitor.fail({
          msg: '获取绑定信息失败',
          bl1: JSON.stringify(e)
        })
        console.error('获取绑定支付宝信息异常', JSON.stringify(e || {msg: 'unknown_msg'}));
      }
    },
    // 绑定支付宝
    async bindAlipay() {
      const app = store?.getState()?.app
      if (isAndroid()) {
        const installed = await checkIfInstalledAlipay();
        if (!installed) {
          console.error('[alipay] 用户未安装支付宝，提示安装');
          Toast.show('请先到应用商店下载支付宝哦');
          return;
        }
      }
      return new Promise((resolve, reject) => {

        ucapi.account.bindAlipay()
          .then(res => {
            console.log('[alipay] 绑定弹窗调起完成', res, ' 等待绑定结果回调');
            const invokeSuccess = res.result === 'success';
            if (!invokeSuccess) {
              console.error('[alipay] 调起绑定失败, invoke ', JSON.stringify(res || {}));
              Toast.show('绑定支付宝账号失败，请重试~');
              return;
            }
            Toast.show('正在绑定支付宝账号，请等待~');
            const listenBind = async (state: any) => {
              // console.log('[bind] 监听全局绑定事件 ', state);
              // document.removeEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
              if (state && state.detail) {
                const res: IBindThirdInfo = state.detail;
                Toast.hide();
                const bindSuccess = res.result === 'success';
                console.log('[bind] 事件监听完成~~ bindSuccess ? ', bindSuccess, ' res.result ', res);
                dispatch.user.updateBindAlipayStatus(bindSuccess);
                if (bindSuccess && res.third_nickname) {
                  // 这里绑定的返回结果基本都没有看到昵称，所以这段一般不执行
                  dispatch.user.updateBindInfo(res);
                }
                if (bindSuccess) {
                  resolve(bindSuccess);
                  dispatch.app.updateAll()
                } else {
                  Toast.show('绑定支付宝账号失败，请重试~');
                  reject(res);
                }
                document.removeEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
              }
            }
            document.addEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
          })
          .catch(err => {
            // TODO: wpk
            console.error('[alipay] 调起绑定失败, msg ', JSON.stringify(err || {}))
          })
      });
    },
    // 查询是否绑定淘宝
    async getBindTaobaoInfo () {
      const bindInfoMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_TAOBAO_INFO);
      try {
        let bindInfo = await ucapi.account.getBindInfo('taobao', true);
        if ((bindInfo as any)?.errCode) {
          bindInfoMonitor.fail({
            msg: '获取绑定信息失败',
            c2: (bindInfo as any)?.errCode,
            c3: bindInfo?.fail_msg,
            bl1: JSON.stringify(bindInfo)
          })
          return;
        }
        if (bindInfo?.result === 'success') {
          dispatch.user.updateUserInfo({bindTaobao: true});
          bindInfoMonitor.success({
            msg: '已绑定',
            c1: '1',
            bl1: JSON.stringify(bindInfo)
          })
        } else {
          bindInfoMonitor.success({
            msg: '未绑定',
            c1: '0',
            bl1: JSON.stringify(bindInfo)
          })
        }
      } catch (error) {
        bindInfoMonitor.fail({
          msg: '获取绑定信息失败',
          c2: 'catch-error',
          c3: `${error?.errMsg || ''}`,
          bl1: JSON.stringify(error),
        })
      }
    },
    // 绑定淘宝
    async toBindTaobao() {
      const app = store?.getState()?.app
      const user = store?.getState()?.user
      const {bindTaobao} = user
      if (bindTaobao) {
        dispatch.app.updateAll()
        return;
      }
      const bindTaoBaoMonitor = tracker.Monitor(WPK_CATEGORY_MAP.GET_TAOBAO_BIND);
      return new Promise((resolve, reject) => {
        ucapi.account.bindTaobao('uc_lite_piggy')
          .then(res => {
            const invokeSuccess = res.result === 'success';
            if (!invokeSuccess) {
              Toast.show('绑定淘宝账号失败，请重试~');
              bindTaoBaoMonitor.fail({
                msg: '绑定淘宝失败',
                c1: 0,
                bl1: JSON.stringify(res)
              })
              return;
            }
            Toast.show('正在绑定淘宝账号，请等待~');
            const listenBind = async (state: any) => {
              if (state && state.detail) {
                const res: IBindThirdInfo = state.detail;
                Toast.hide();
                const bindSuccess = res.result === 'success';
                console.log('bindTaobao ', res);
                dispatch.user.updateUserInfo({bindTaobao: bindSuccess});
                if (bindSuccess) {
                  resolve(bindSuccess);
                  bindTaoBaoMonitor.success({
                    msg: '绑定淘宝成功',
                    c1: 1,
                    bl1: JSON.stringify(res)
                  })
                  console.log('bindTaobao bindSuccess', bindSuccess);
                  // 判断是否有高价值绑定任务 并且完成任务
                  if (app?.ucLoginTask?.hasUcLoginTask) {
                    const task = app?.ucLoginTask?.taskInfo
                    dispatch.task.complete({
                      id: task?.id as number,
                      type: 'complete',
                      useUtCompleteTask: !!task?.useUtCompleteTask,
                      publishId: task?.publishId,
                      params: { task }
                    });
                  }
                  dispatch.app.updateAll()
                } else {
                  Toast.show('绑定淘宝宝账号失败，请重试~');
                   // ios返回的错误码platform_code，android返回fail_event
                  const failCode = String(res?.platform_code || res?.fail_event)
                  bindTaoBaoMonitor.fail({
                    msg: '绑定淘宝失败',
                    c1: 0,
                    c2: failCode,
                    c3: res?.fail_msg,
                    bl1: JSON.stringify(res)
                  })
                  reject(res);
                }

                document.removeEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
              }
            }
            document.addEventListener('UCEVT_Global_BindThirdPartyAccountComplete', listenBind);
          })
          .catch(err => {
            bindTaoBaoMonitor.fail({
              msg: '绑定淘宝失败',
              c1: 0,
              c2: 'catch-error',
              c3: err?.msg,
              bl1: JSON.stringify(err)
            })
            console.error('[taobao] 调起绑定失败, msg ', JSON.stringify(err || {}))
          })
      });
    },
    // 获取用户是否是风控
    async getUserRiskScore() {
      const { taskHost, appId } = config;
      const requestId = geneTaskRequestId();
      const salt = 'sy5th908xb9bmgiz2ssy0cykzezkq1jf';
      let { kps, ut } = store.getState().user;
      if (!ut) {
        const utRes = await ucapi.biz.ucparams({ params: 'ut' });
        ut = utRes.ut || '';
      }
      const signOriText = `${appId}${decodeURIComponent(ut)}${requestId}`;
      const sign = await ucapi.spam.sign({ text: signOriText, salt });
      const riskMonitor = tracker.Monitor(196);
      try {
        const resp = await network.get(`${taskHost}/task/queryUserInfo`, {
          appId,
          kps,
          requestId,
          sign,
          salt,
        });
        const frontData = store.getState().app;
        // 如果是风控用户
        if (resp?.accountUnusual) {
          baseModal.openRiskControl({ riskRule: frontData.riskRule });
        }
        riskMonitor.success({
          c1: resp?.accountUnusual ? '是' : '否',
          bl1: JSON.stringify(resp)
        });
      } catch (error) {
        riskMonitor.success({
          c2: getErrorInfo(error).errCode,
          bl2: JSON.stringify(getErrorInfo(error))
        });
      }
    },
  }),
});

export default user;
