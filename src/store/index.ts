import { init, Models, RematchDispatch, RematchRootState } from '@rematch/core';

import app from './models/app';
import user from './models/user';
import currency from './models/currency';
import task from './models/task';
import route from './models/route';
import ad from './models/ad';
import invite from "@/store/models/invite";
// import shop from '@/store/models/shop';
import rta from '@/store/models/rta';
import cms from '@/store/models/cms';
import highValueTask from '@/store/models/highValueTask';
import redirect from '@/store/models/redirect/index';
import resource from '@/store/models/resource/index';
import rightsGift from '@/store/models/rightsGift/index';

export interface RootModel extends Models<RootModel> {
  app: typeof app;
  user: typeof user;
  currency: typeof currency;
  task: typeof task;
  route: typeof route;
  ad: typeof ad;
  // shop: typeof shop;
  invite: typeof invite;
  rta: typeof rta;
  cms: typeof cms;
  redirect: typeof redirect;
  highValueTask: typeof highValueTask;
  resource: typeof resource;
  rightsGift: typeof rightsGift;
}

const rootModel: RootModel = {
  app,
  user,
  route,
  currency,
  task,
  ad,
  // shop,
  invite,
  rta,
  cms,
  redirect,
  highValueTask,
  resource,
  rightsGift
};

export const store = init({
  models: rootModel,
});

export type Store = typeof store;
export type StoreDispatch = RematchDispatch<RootModel>;
export type StoreState = RematchRootState<RootModel>;
export const { dispatch } = store;
