import ua from '@ali/weex-toolkit/lib/ua';
import uc from '@ali/weex-toolkit/lib/weex_config/uc';
import { isWeex } from 'universal-env';
// import ucapi from "@/utils/ucapi";

const userAgent = window.navigator.userAgent.toLowerCase();

export enum APP_NAME {
  UC = 'UC',
  QUARK = 'QUARK',
  DDLEARN = 'BBD',
  UNKNOWN = 'unknown',
}

function getUCParamStr(target) {
  const reg = new RegExp(`&${target}=([^&]*)`);
  const match = reg.exec(window.location.search);

  return match && match[1] ? decodeURIComponent(match[1]) : '';
}

export function getUCLiteVersion() {
  const ve = getUCParamStr('ve');
  if (ve) return ve;

  const match = /(ucbrowser|uclite)\/([^\s]*)/.exec(userAgent);
  return match && match[2] ? match[2] : '';
}

export const isAndroid = ua.isAndroid();

export const isIOS = ua.isIOS();

export const appName = getAppName();

export const appVersion = getUCLiteVersion();

export const bizVersion = MAIN_VERSION;

export const isUc = ua.isUC();

export const isQuark = appName === APP_NAME.QUARK;

export const osName = isAndroid ? 'Android' : 'iOS';

export const isLatestVersion = ua.isLatestVersion;

export const statusBarHeight = isIOS ? uc.env.statusBarHeight / uc.env.windowWidth * 750 : 0;

function getAppName() {
  if (isWeex) {
    const rawAppName = weex.config.env.appName.toLowerCase();
    if (rawAppName.indexOf('quark') > -1 || rawAppName.indexOf('夸克') > -1) {
      return APP_NAME.QUARK;
    } else if (rawAppName.indexOf('ddlearn') > -1) {
      return APP_NAME.DDLEARN;
    } else if (rawAppName.indexOf('ucmobile') > -1 || rawAppName.indexOf('ucbrowser') > -1) {
      return APP_NAME.UC;
    }
    return APP_NAME.UNKNOWN;
  }
  return APP_NAME.UNKNOWN;
}

// export const getAppVersion = async () => {
//   let version = ua.getUCVersion()
//   if (!version) {
//     const ucParams = await ucapi.biz.ucparams({ params: 've' }, true);
//     version = (ucParams || { ve: '' }).ve
//   }
//   return version
// }
//
// export let appVersion
//
// getAppVersion().then((version) => {
//   appVersion = version
// })

