import qs from '@/lib/qs';

function request(options) {
  return new Promise((resolve, reject) => {
    let isDone = false;
    const timer: any = null;

    const callback = (ret: any) => {
      if (isDone) return;
      if (timer) clearTimeout(timer);
      isDone = true;

      if (options.type === 'text' || options.originalResponse) {
        resolve(ret);
      } else if (ret && (ret.code === 'OK' || ret.code === 0 || ret.message === 'ok')) {
        resolve(ret.data);
      } else if (options.url.indexOf('wh_page_only=true') > -1) {
        resolve(ret);
      } else {
        reject(ret);
      }
    };

    setTimeout(() => {
      if (isDone) return;
      isDone = true;
      reject({ success: false, code: 'network timeout' });
    }, options.timeout);

    const { url, ...args } = options;
    fetch(url, normalizeNetworkOptions(args))
      .then((data) => data.json())
      .then(callback, reject);
  });
}

/**
 * 兼容 fetch 数据格式
 */
function normalizeNetworkOptions(options) {
  const { method, headers, body, ...others } = options;
  let bodyData = body;
  const contentType = headers && headers['Content-Type'] || '';
  if (method === 'POST' && body && (contentType === 'application/x-www-form-urlencoded' || contentType === '') && isObject(body)) {
    bodyData = qs.stringify(body);
  }
  return { method, headers, body: bodyData, ...others };
}

function isObject(arg) {
  return Object.prototype.toString.call(arg).indexOf('Object') !== -1;
}

export default request;
