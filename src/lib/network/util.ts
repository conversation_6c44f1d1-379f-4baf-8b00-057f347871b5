import qsUtil from '@/lib/qs';
import { NetworkOption } from './typings.d';
import fetch from './fetch';
import { isNode } from 'universal-env';

export const qs = qsUtil;

// node端接口调用超时时间
const NODE_REQUEST_TIMEOUT = 800;

/**
 * 发起请求
 * @param params 请求参数
 */
export function request(params: NetworkOption): Promise<any> {
  const defaultOption = {
    type: 'json',
    method: 'GET',
    timeout: 4e3,
    json: true,
  };

  const options = { ...defaultOption, ...params };

  if (isNode) {
    options.timeout = Math.min(options.timeout, NODE_REQUEST_TIMEOUT);

  }

  return fetch(options);
}