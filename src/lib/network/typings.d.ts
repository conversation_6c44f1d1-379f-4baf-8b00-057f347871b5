export interface NetworkOption {
  type?: 'json' | 'text';
  method?: 'GET' | 'POST';
  timeout?: number;
  json?: boolean;
  url?: string;
  headers?: object;
  body?: object;
  originalResponse?: boolean,
  retry?: boolean;
  closeAppendTimestamp?: boolean;
}

export interface InitOptions {
  baseURL?: string;
  avgCategory?: number;
  errCategory?: number;
  sampleRate?: number;
  timeout?: number;
}
