import tracker from '@/lib/tracker';
import { InitOptions, NetworkOption } from './typings.d';
import { qs, request } from './util';
// import debug from '../debug';

const UC_PARAM_STR = 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicgodcadebcaaoclbwf';

let baseURL = '';
let avgCategory = 101;
let errCategory = 102;
let sampleRate = 0.1;
let timeout = 8e3;

function init(options: InitOptions) {
  baseURL = options.baseURL || baseURL;
  sampleRate = options.sampleRate || sampleRate;
  avgCategory = options.avgCategory || avgCategory;
  errCategory = options.errCategory || errCategory;
  timeout = options.timeout || timeout;
}

function parseURL(url: string, params?: any) {
  if (url.indexOf('://') === -1) {
    url = baseURL + url;
  }

  if (params) {
    url = url + (url.indexOf('?') === -1 ? '?' : '&') + qs.stringify(params);
  }

  if (!url.includes('uc_param_str')) {
    url = `${url + (url.indexOf('?') === -1 ? '?' : '&') }uc_param_str=${UC_PARAM_STR}`;
  }
  return url;
}

function getUCCode(resp) {
  const ucDataMsg = resp['uc-data'];
  let ret = '';
  if (ucDataMsg) {
    const m2 = /Code=((-)?(\d+))/.exec(ucDataMsg);
    if (m2) {
      ret += m2[1];
    }
  }
  return ret;
}

function getUCErrorCode(resp) {
  const ucDataMsg = resp['uc-data'];
  let ret = '';
  if (ucDataMsg) {
    const m1 = /ErrorCode=((-)?(\d+))/.exec(ucDataMsg);
    if (m1) {
      ret = m1[1];
    }
  }
  return ret;
}

function getUCErrorCodeDesc(resp) {
  let ret = '';
  const ucCode = getUCCode(resp);
  if (ucCode) {
    ret += `Code=${ucCode} `;
  }
  const ucErrorCode = getUCErrorCode(resp);
  if (ucErrorCode) {
    ret += `ErrorCode=${ucErrorCode} `;
  }
  return ret.trim();
}

/**
 * GET 请求
 * @param url 链接
 * @param data 参数
 */
function get(url: string, data?: any, config: NetworkOption = {}): Promise<any> {
  const urlParams = config.closeAppendTimestamp ? data : { __t: Date.now(), entry: qs.getParam('entry') || '', evSub: qs.getEvSub(), ...data };
  const targetUrl = parseURL(url, urlParams);
  const { retry, ...options } = config;
  const params: NetworkOption = {
    url: targetUrl,
    method: 'GET',
    timeout,
    ...options,
  };

  // 请求正常响应之前，每隔 2s 发送一个请求，并以最早成功响应、或最后异常的数据为准。重试 2 次，周期 8s
  return new Promise((resolve, reject) => {
    let isDone = false;
    let timer: any = null;
    let count = retry ? 2 : 1;

    const callback = (isFulfilled: boolean, ret: any) => {
      if (isDone) return;
      isDone = true;
      clearTimeout(timer);

      if (isFulfilled) {
        resolve(ret);
      } else {
        reject(ret);
      }
    };

    const send = () => {
      count -= 1;
      const isLastOne = count === 0;

      // const handler = {};
      console.log('[network request]', params);

      request(params)
        .then((ret) => {
          callback(true, ret);
          // console.log('[network response]', params, ret);
        })
        .catch((ret) => {
          if (isLastOne) {
            callback(false, ret);
            ret.ucErrorCode = getUCErrorCode(ret);
            ret.ucCode = getUCCode(ret);
            // console.error('[network catch]', params, ret);
          }

          reportFail(ret, params);
        });

      if (!isLastOne) timer = setTimeout(send, 2e3);
    };

    send();
  });
}

function reportFail(ret, params: NetworkOption) {
  const targetUrl = params.url as string;
  const url = targetUrl.substring(0, targetUrl.indexOf('?') !== -1 ? targetUrl.indexOf('?') : targetUrl.length);
  let respData;
  try {
    respData = ret && ret.code ? ret : (typeof ret.data === 'object' ? ret.data : JSON.parse(ret.data || '{}'));
  } catch {
    respData = {};
  }

  const ucErrorCodeDesc = getUCErrorCodeDesc(ret);

  const respErrCode = respData.code;
  const respErrMsg = respData.msg || JSON.stringify(respData);

  const httpMethod = params.method;
  const statusCode = ret.status;

  const msg = `${httpMethod} ${url} status: ${statusCode} respErrorCode ${respErrCode}`;

  // 全量上报接口异常
  tracker.log({ category: errCategory, msg, c1: url, c2: targetUrl, c3: respErrMsg, c4: ucErrorCodeDesc });
  console.error(msg);
}

/**
 * POST 请求
 * @param url 链接
 * @param data 参数
 * @param option 配置
 */
function post(url: string, data?: any, option: NetworkOption = {}): Promise<any> {
  const params: NetworkOption = {
    url: parseURL(url, {
      entry: qs.getParam('entry') || '',
      evSub: qs.getEvSub()
    }),
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: data,
    ...option,
  };

  console.log('[network request]', params);
  return request(params).then((resData) => {
    // data为 coral响应对象中的data值
    console.log('[network response]', params, resData);
    return resData;
  }).catch((ret) => {
    ret.ucErrorCode = getUCErrorCode(ret);
    ret.ucCode = getUCCode(ret);
    // console.error('[network catch]', params, ret);
    reportFail(ret, params);
    return Promise.reject(ret);
  });
}

export default {
  init,
  get,
  post,
  qs,
};
