import { getParam } from '@/lib/qs';
import { isNode } from 'universal-env';
import ucapi from '@/utils/ucapi';
import { TaskInfo } from '@/store/models/task/types';

function safeObject(obj) {
  if (Object.prototype.toString.call(obj) === '[object Object]') return obj;
  try {
    obj = JSON.parse(obj);
  } catch (e) { }
  return obj;
}
export async function getUCParams() {
  if (isNode) {
    return {
      pr: getParam('pr'),
      ve: getParam('ve')
    };
  }
  if (!ucapi?.biz?.ucparams) {
    return null;
  }
  let result;
  try {
    result = await ucapi.biz.ucparams({ params: 'prvesvutdd' }, true);
  } catch (err) {
    console.error('[first-data-api] getUCParams error', err);
  }
  return result;
}

export function getUserInfo() {
  return new Promise((resolve, reject) => {
    if (isNode) {
      const kps_wg = getParam('kps_wg')
        .replace(/(\<|\>|\&)/g, '');
      resolve({
        kps_wg,
      });
    } else if (window.ucapi) {
      const vCode = +new Date();
      window.ucapi.invoke('account.getUserInfo', {
        vCode,
        success: data => {
          data = safeObject(data);
          resolve({
            ...data,
            vCode,
          });
        },
        fail: data => {
          reject(safeObject(data));
        },
      });
    } else {
      resolve({})
    }
  });
}

/**
 * 获取广告接口
 * @param options
 * @returns
 */
export const loadNativeAd = (options: {
  appKey: string;
  aid: string;
  type: string;
  requestInfo?: Record<string, any>;
}) => {
  return new Promise((resolve: (data: {

    title: string;
    sid: string;
    assetId: string;
  }) => void, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.loadNativeAd', {
        ...options,
        success: (ret) => {
          if (ret.success) {
            resolve(ret);
          } else {
            reject(ret);
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    } else {
      reject(new Error('loadNativeAd window.ucpai not exist'));
    }
  });
}
/**
 *  获取RTA标签信息
 * @param options
 * @returns
 */
export const getNoahRtaTag = (options: {
  scene: string;
  rta_ids?: string;
}) => {
  return new Promise((resolve: (data: {
    success: string;
    show_order: string;
    target: string;
    category: string;
    price: string;
  }) => void, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.getNoahRtaTag', {
        ...options,
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('getNoahRtaTag window.ucapi not exist'));
    }
  });
}
/**
 * 展示广告通知接口
 * @param options
 * @returns
 */
export const onNativeAdShow = (options: {
  sid: string;
  aid: string;
  type: string;
}) => {
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.onNativeAdShow', {
        ...options,
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('onNativeAdShow window.ucapi not exist'));
    }
  });
}
/**
 * 点击并绑定任务通知接口
 * @param options
 * @returns
 */
export const clickNativeAdAndNotify = (options: {
  sid: string;
  type: string;
  aid: string;
  thirdid: string;
  channel: number;
}) => {
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('biz.clickNativeAdAndNotify', {
        ...options,
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('clickNativeAdAndNotify window.ucapi not exist'));
    }
  });
}

/**
 * 东风检测接口
 * url: 检测的链接
 * action: 传expose则是曝光监测，click是点击监测
 */
export function dongfengMonitoring(url: string, action: 'expose' | 'click') {
  if (!url) {
    console.error('dongfeng.request error:  url is empty!');
    return;
  }
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('dongfeng.request', {
        url,
        action,
        type: "dongfeng",
        success: data => {
          resolve(data);
        },
        fail: err => {
          reject(err);
        }
      })
    } else {
      reject(new Error('dongfeng.request window.ucapi not exist'));
    }
  });
}

// 获取桌面组件安装信息
export function getInstallInfo(addtype, type, widgetReceiverName, extraParams: {taskInfo: TaskInfo | null, resource_location: string}): Promise<{isInstalled: boolean}>{
  const { taskInfo, resource_location } = extraParams;
  console.log('widget.getInstallInfo', extraParams);
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('widget.getInstallInfo',{
      addType: addtype,   	// 添加类型，
      channel: 'guide',			// 必须，来源，默认是 guide
      typeId: type,  	// 必须：类型 id，需要跟模板的ID一致
      widgetReceiverName,  	// 必须，找客户端同学获取，eg：com.uc.business.widget.dynamic.receiver.
      extra: {
        resource_location,
        task_id: taskInfo?.id || '',
        task_name: taskInfo?.name || '',
      },
      success: function(data) {
        resolve(data)
      },
      fail: function(data) {
        console.error(data);
      }})
    } else {
      resolve({})
    }
  })
}

// 通知客户端更新 桌面组件状态
export function notifyChanged(type,widgetReceiverName){
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('widget.notifyChanged',{
        typeId: type,  	// 必须：样式 id，需要跟模板的ID一致
        widgetReceiverName, 	// 必须，找客户端同学获取，eg：com.uc.business.widget.dynamic.receiver.DynamicWidget1x1Receiver
      success: function(data) {
        resolve(data)
      },
      fail: function(data) {
        console.error(data);
      }})
    } else {
      resolve({})
    }
  })
}

// 添加桌面小组件
export function installWidget(addType, type, widgetReceiverName, extraParams: {taskInfo: TaskInfo, resource_location: string}): Promise<{data: any; fail?: boolean}> {
  const { taskInfo, resource_location } = extraParams;
  console.log('widget.install', extraParams);
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('widget.install',{
        addType: addType,
        channel: 'guide',
        typeId: type,
        forceNotShowGuide: true,
        widgetReceiverName,
        extra: {
          resource_location,
          task_id: taskInfo?.id || '',
          task_name: taskInfo?.name || '',
        },
        success: function(data) {
          resolve({data})
        },
        fail: function(data) {
          console.error(data);
          resolve({fail: true,data})
        }
      });
    } else {
      resolve({fail: true,data: {}})
    }
  })
}

// 通知客户端更新 视频挂件状态更新
export function videoNotifyChanged(mission: string){
  return new Promise((resolve, reject) => {
    if (window.ucapi) {
      window.ucapi.invoke('mission.notifyTodayMissionComplete',{
      mission,
      success: function(data) {
        resolve(data)
      },
      fail: function(data) {
        console.error(data);
      }})
    } else {
      resolve({})
    }
  })
}