import Rax, { createElement, useState, useRef, useEffect, useMemo } from 'rax';
import cloneElement from 'rax-clone-element';
import Children from 'rax-children';
import History from './history';
import EventEmitter from 'eventemitter3';
import './index.css';
import { Action } from 'history';

const event = new EventEmitter();

const historyManager = new History();

window._his = historyManager

interface IProps {
  children?: Rax.RaxElement[],
  default: string;
}

enum RouterStatus {
  // 准备push下一页、pushing下一页（进场动画）、正常（进到默认位置）、离开ing下一页（退场动画）、卸载页面unmountPage
  readyPush = 'readyPush',
  prepareAnimatePush = 'prepareAnimatePush',
  pushing = 'pushing',
  normal = 'normal',
  leaving = 'leaving',
  // out unmountPage
}

interface Router {
  scene: string,
  status: RouterStatus,
}


function Nav(props: IProps) {
  const [ routerQueue, setRouterQueue ] = useState<Router[]>([{
    scene: props.default,
    status: RouterStatus.normal,
  }]);
  const [ curPageScene, setPageScene ] = useState<string>(props.default);
  const navRef = useRef(null);
  // let history: History | null

  useMemo(() => {
    // didmount
    historyManager.init({
      type: 'hash',
      page: props.default,
    });
  }, []);

  const push = (scene: string) => {
    setRouterQueue(routerQueue.concat({
      scene,
      status: RouterStatus.readyPush,
    }));
    setPageScene(scene);
    console.warn('pushing ', scene)
    event.emit('updateScene', { action: Action.Push, scene });
  }
  const pop = (scene: string) => {
    console.log('nav pop', routerQueue);
    if (routerQueue.length > 1) {
      const lastRouterIndex = getLastRouterIndex(routerQueue, scene);
      if (lastRouterIndex !== -1) {
        const oldRouter = routerQueue[lastRouterIndex];
        // const newQueue = routerQueue.slice(0, lastRouterIndex).concat({
        //   ...oldRouter,
        //   status: RouterStatus.leaving,
        // }).concat(routerQueue.slice(lastRouterIndex + 1));
        const newQueue = spliceCopy(routerQueue, lastRouterIndex, {
          ...oldRouter,
          status: RouterStatus.leaving,
        });
        const nextRouter = routerQueue[lastRouterIndex - 1];
        setRouterQueue(newQueue);
        setPageScene(nextRouter.scene);

        setTimeout(function() {
          // 不能在这儿直接处理，routeQueue在快速点击时是旧的，导致状态异常
          event.emit('popFinish', scene);
        }, 400);
        event.emit('updateScene', { action: Action.Pop, scene: nextRouter.scene });
      } else {
        lock = false;
        console.error('发生不可预期异常, pop了一个不存在的scene');
      }
    } else {
      console.log('quit quit quit');
      // iOS 下在福利猪动画崩溃时会刷新页面并产生自动的物理返回行为，导致/index页无法退出，又未进行解锁
      lock = false;
    }
  }

  // useEffect(() => {
  //   const a: string[] = [];
  //   document.querySelectorAll('.pg').forEach(item => a.push(item.className))
  //   console.log('className', a);
  // });

  useEffect(() => {
    const handlePopFinish = (scene: string) => {
      const lastRouterIndex = getLastRouterIndex(routerQueue, scene);
      const finalRouterQueue = spliceCopy(routerQueue, lastRouterIndex);
      console.log('pop', scene, routerQueue, lastRouterIndex, finalRouterQueue);
      setRouterQueue(finalRouterQueue);
    }
    event.on('popFinish', handlePopFinish);
    return () => {
      event.off('popFinish', handlePopFinish);
    }
  }, [ routerQueue ]);


  useEffect(() => {
    const handle = ({ action, page }) => {
      if (action === Action.Push) {
        lock = true;
        push(page);
      } else if (action === Action.Pop) {
        // 用户可能快速按物理返回键，导致前面的pop未完成，后面又pop了
        // lock = true是为了阻止用户在物理返回未完成时，又手动调了push、pop方法，导致stack异常
        lock = true;
        pop(page);
      } else {
        console.error('nav not support action ', action);
      }
    };
    historyManager.addListen(handle);
    return () => {
      historyManager.removeListen(handle);
    }
  }, [push, pop]);

  useEffect(() => {
    const lastRouter = routerQueue[routerQueue.length - 1];
    if (lastRouter.status === RouterStatus.readyPush) {
      let newQueue = routerQueue.slice(0, routerQueue.length - 1);
      newQueue = newQueue.concat({
        ...lastRouter,
        status: RouterStatus.prepareAnimatePush,
      })
      setRouterQueue(newQueue);
      console.warn('pushing1 ', lastRouter.scene, RouterStatus.prepareAnimatePush)
    }
    if (lastRouter.status === RouterStatus.prepareAnimatePush) {
      setTimeout(() => {
        console.warn('pushing2 ', lastRouter.scene, RouterStatus.pushing)
        let newQueue = routerQueue.slice(0, routerQueue.length - 1);
        newQueue = newQueue.concat({
          ...lastRouter,
          status: RouterStatus.pushing,
        })
        setRouterQueue(newQueue);
        setTimeout(() => {
          event.emit('pushFinish', lastRouter.scene);
          console.warn('pushing3 finish ', lastRouter.scene)
        }, 400);
      }, 30)
    }
  }, [ routerQueue ]);

  return (
    <div className="nav" ref={navRef}>
      {
        routerQueue.map(({ scene, status }) => {
          let page: any = null;
          Children.map(props.children, (child) => {
            if (scene === child?.props.scene) {
              page = child;
            }
            return child;
          })
          let className = getClass(status)
          return cloneElement(page, {
            key: scene,
            className,
            style: {},
          });
        })
      }
    </div>
  );
}

function getClass(status: RouterStatus) {
  switch (status) {
    case RouterStatus.readyPush:
      return 'pg-next'
    case RouterStatus.prepareAnimatePush:
      return 'pg-next pg-animate'
    case RouterStatus.pushing:
      return 'pg-next pg-animate pg-in'
    case RouterStatus.normal:
      return ''
    case RouterStatus.leaving:
      return 'pg-leave pg-animate'
    default:
      return '';
      break;
  }
}

function getLastRouterIndex(routerQueue: Router[], scene: string) {
  let index = routerQueue.length;
  while(--index >= 0) {
    if (routerQueue[index].scene === scene) {
      return index;
    }
  }
  return -1;
}

function spliceCopy(queue: any[], index, item?) {
  const preQueue = queue.slice(0, index);
  if (item) {
    preQueue.push(item)
  }
  return preQueue.concat(queue.slice(index + 1));
}

let lock = false;
let forceUnLockTimeout

export default Nav;

event.on('popFinish', () => {
  lock = false;
  clearTimeout(forceUnLockTimeout);
})

event.on('pushFinish', () => {
  lock = false;
  clearTimeout(forceUnLockTimeout);
})

// historyManager.on('change', data => {
//   event.emit('change', data)
// })

export const history = {
  event,
  push: (page: string) => {
    if (historyManager.isInit) {
      console.warn('push start ', page, lock);
      if (lock) return;
      lock = true;
      forceUnLockTimeout = setTimeout(() => {
        lock = false;
      }, 600);
      historyManager.push(page);
    } else {
      console.warn('未初始化historyManger');
    }
  },
  pop: () => {
    if (historyManager.isInit) {
      console.warn('pop start ', lock);
      if (lock) return;
      lock = true;
      forceUnLockTimeout = setTimeout(() => {
        lock = false;
      }, 600);
      historyManager.pop();
    } else {
      console.warn('未初始化historyManger');
    }
  },
  getStack() {
    return historyManager.stack;
  }
}

window._his1 = history
