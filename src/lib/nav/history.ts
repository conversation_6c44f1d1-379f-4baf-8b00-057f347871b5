import EventEmitter from 'eventemitter3';
import history from '@/lib/universal/histroy-hash';
import { Action } from 'history';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';

export interface HistoryOptions {
  type: 'hash' | 'history';
  page: string;
}

interface ListenCbParams {
  action: string,
  page: string,
}

interface ActionInfo {
  action: string,
  page: string,
}

// export enum Action {
//   pop = 'pop',
//   push = 'push',
//   replace = 'replace',
// }
// TODO delete
// window.onhashchange = () => {
//   console.warn('hashchange', location.hash)
// }

export default class History extends EventEmitter {

  options: HistoryOptions;
  stack: string[] = []
  isInit = false

  private listenCb: ((params: ListenCbParams) => void)[] = [];
  private lock = false;
  private lastActionInfo: ActionInfo
  private history = history
  private lastPage: string = this.getCurrentPage()

  init(options: HistoryOptions) {
    this.isInit = true;
    this.options = options;
    const { type, page } = this.options;
    console.log('history init ', this.getCurrentPage());
    if (typeof window !== 'undefined') {
      if (type === 'hash') {
        this.history.listen(this.handleListen)
      } else {
        console.error('options not support type: history')
      }
    }
    if (page ) {
      this.replace(page);
    }
  }

  getCurrentPage() {
    return this.history.location.pathname;
  }

  handleListen = ({ location, action }) => {
    if (!this.lock) {
      const targetPathname = location.pathname;
      // 用户按了物理返回或直接操作了hash。只能处理物理返回场景，所以默认为pop行为，对前进场景无法解决，但是在uc自有业务下无该场景问题
      console.warn('请使用标准nav的history工具进行路由切换，不要直接操作hash或history堆栈，如果是物理返回则无视当前告警')
      // 预测用户是pop还是push
      if (targetPathname === this.stack[this.stack.length - 2]) {
        // pop
        this.lastActionInfo = {
          page: this.lastPage,
          action: Action.Pop,
        }
        if (this.stack.length > 1) {
          // iOS 下在福利猪动画崩溃时会刷新页面并产生自动的物理返回行为，此时不能退出第一个路由栈
          this.stack.pop();
        } else {
          console.warn('发生了异常的物理返回，可能是ios自动物理返回，或其他原因')
        }
      } else {
        // push
        this.stack.push(targetPathname);
        this.lastActionInfo = {
          page: targetPathname,
          action: Action.Push,
        }
      }
    } else {
      const pathname = location.pathname;
      // 手动操作push、pop、replace
      if (this.lastPage === pathname) {
        if (this.stack.length === 0) {
          this.stack.push(pathname);
        }
        this.commonHandleEnd({ location });
        // 只有这种场景才中断，其他情况继续执行后续的push或者pop
        if (this.stack.indexOf(pathname) !== -1) return;
      }
      switch (action) {
        case Action.Push:
          this.stack.push(pathname);
          this.lastActionInfo = {
            page: pathname,
            action: Action.Push,
          }
          break;
        case Action.Pop:
          this.stack.pop();
          const prePage = this.lastPage;
          this.lastActionInfo = {
            page: prePage,
            action: Action.Pop,
          }
          break;
        case Action.Replace:
          this.stack = this.stack.slice(0, this.stack.length - 1).concat(pathname);
          this.lastActionInfo = {
            page: pathname,
            action: Action.Replace,
          };
        default:
          break;
      }
    }
    this.listenCb.forEach(cb => {
      cb(this.lastActionInfo);
    })
    this.commonHandleEnd({ location });
  }

  private commonHandleEnd({ location }) {
    console.log('history change ', this.lastActionInfo, this.stack, location.pathname)
    this.unlock();
    this.lastPage = this.getCurrentPage();
    // 需要响应实时的当前page给change事件，this.lastActionInfo可能为空，此时为初始化状态
    this.emit('change', { page: location.pathname, action: this.lastActionInfo && this.lastActionInfo.action || Action.Replace });
  }

  unlock = () => {
    this.lock = false;
  }

  addListen(cb: (params: ListenCbParams) => void) {
    this.listenCb.push(cb);
  }

  removeListen(cb: (params: ListenCbParams) => void) {
    this.listenCb = this.listenCb.filter((cacheCb) => {
      if (cacheCb === cb) return false;
      return true;
    });
  }

  push(page: string) {
    if (this.lock) return;
    console.warn('history change push before', page, ' current page ', this.getCurrentPage(), this.stack, location.href, history.location.pathname, window.history.length);
    this.lock = true;
    // this.lastActionInfo = {
    //   page,
    //   action: Action.Push,
    // };
    this.history.push(page);
    console.warn('history push after', page, ' current page ', this.getCurrentPage(), this.stack, location.href, history.location.pathname, window.history.length);
  }

  pop() {
    if (this.lock) return;
    this.lock = true;
    if (this.stack.length <= 1) {
      this.exit();
      this.unlock();
    } else {
      this.history.back();
    }
  }

  exit() {
    // location.href = 'ext:back';
    jsbridge.exec('biz.openPageUrl', { url: 'ext:back' });
  }

  replace(page: string) {
    if (this.lock) return;
    this.lock = true;
    this.history.replace(page);
  }
}
