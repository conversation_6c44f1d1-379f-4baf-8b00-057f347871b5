import idx from 'idx';
import withUniversal from '@/lib/render-utils/with-universal';
import fact from '@ali/fact-stat';

const ev_ct = 'uclite_fuli';

const statExport = {
  init(obj = {}, a = 'uclite_fuli') {
    fact.setup({
      ev_ct,
      ev_sub: 'uclite_fuli_index',
      a: a,
      b: 'index',
      bro_test_id: (window as any).__broTestId || '',
      bro_test_data_id: (window as any).__broTestDataId || ''
    });
  },
  updateParam(options = {}) {
    fact.baseParam(options);
  },
  pv(page = 'page_fuli_index', { b = '12517722' } = {}) {
    fact.pageview(page, { b });
  },
  exposure(arg1, options = {}) {
    fact.exposure(arg1, options);
  },
  click(arg1, options = {}) {
    fact.click(arg1, options);
  },
  custom(arg1, options = {}) {
    fact.event(arg1, options);
  },
  getCustomStatParams() {
    return idx(fact, _ => _.wa.customStatParams) || {};
  },
};

export default withUniversal(statExport);
