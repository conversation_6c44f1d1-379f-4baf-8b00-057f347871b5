## async-component

> 异步组件，依赖 code split



## Getting Started

```jsx
import AsyncComponent from './lib/async_component';

function render() {
  return (
    <AsyncComponent
      resolve={() => import('./LAZY_COMPONENT')}
      LoadingComponent={props => <div>Loading {props.foo}</div>}
      ErroComponent={props => <div>Error {props.foo}</div>}
      foo={"CUSTOM PROPS"}
    />
  );
}
```



## Documentation

### AsyncComponent

| 属性             | 说明                                                         | 默认值 |
| ---------------- | ------------------------------------------------------------ | ------ |
| resolve          | 异步加载的组件，返回 Promise，可以通过 import 或者 require.ensure 实现 | 无     |
| LoadingComponent | 组件加载过程展示                                             | null   |
| ErrorComponent   | 组件加载异常展示                                             | null   |



