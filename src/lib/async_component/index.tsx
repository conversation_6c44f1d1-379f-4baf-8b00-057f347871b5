import { createElement, Component } from 'rax';
import debug from '@ali/weex-toolkit/lib/debug';

export interface AsyncComponentProps {
  resolve: () => Promise<any>;
  LoadingComponent?: typeof Component;
  ErrorComponent?: typeof Component;
  [key: string]: any;
}

enum STATUS {
  LOADING,
  READY,
  ERROR,
}

interface AsyncComponentState {
  status: STATUS;
}

class AsyncComponent extends Component<AsyncComponentProps, AsyncComponentState> {

  El: typeof Component = null;

  state = {
    status: STATUS.LOADING,
  };

  constructor(props: AsyncComponentProps) {
    super(props);

    props.resolve()
      .then((ret: any) => {
        this.El = ret && ret.default || ret;
        if (this.El) {
          this.setState({ status: STATUS.READY });
        } else {
          throw new Error('[AsyncComponent] unexpected Component chunk');
        }
      })
      .catch(error => {
        debug.error(error);
        this.setState({ status: STATUS.ERROR });
      });
  }

  render() {
    const { El } = this; // tslint:disable-line:no-this-assignment
    const { status } = this.state;
    const { LoadingComponent, ErrorComponent, children, ...props } = this.props;

    if (El) {
      return <El {...props}>{children}</El>;
    }

    if (LoadingComponent && status === STATUS.LOADING) {
      return <LoadingComponent {...props} />;
    }

    if (ErrorComponent && status === STATUS.ERROR) {
      return <ErrorComponent {...props} />;
    }

    return null;
  }
}

export default AsyncComponent;
