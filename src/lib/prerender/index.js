import prerenderEvent from './event';
import event from '@/utils/event';
import tracker from '@/lib/tracker';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';
import { uc } from '@ali/weex-toolkit/lib/weex_config';
import { parseQueryStr } from '@/utils/url';

let cachePT;
let isAppeared = false;

/**
 * 是否预渲染容器
 */
export function isPrerender() {
  const pt = window.ucweb?.window?.performance?.pt || 0;
  return pt > 0 && pt <= 3;

}

/**
 * 获取预渲染状态
 */
export function getPTStatus() {
  if (cachePT) return cachePT;
  const pt = window.ucweb && window.ucweb.window && window.ucweb.window.performance && window.ucweb.window.performance.pt;
  if (typeof pt !== 'undefined') {
    // 0、1、2、3
    cachePT = pt;
  } else {
    // 不支持该值的版本，小于13.2.2
    cachePT = -1;
  }
  console.log('=====cachePT=====:', cachePT);
  return cachePT;
}


export function getPRTTime() {
  return window.ucweb && window.ucweb.window && window.ucweb.window.performance ? window.ucweb.window.performance.prt : 0;
}

if (isPrerender()) {
  console.warn('命中预渲染', `pt: ${getPTStatus()}`, `prt: ${getPRTTime()}`);
  // 预渲染容器
  prerenderEvent.on('prerendercommit', e => {
    // fromEvent2Prt = (performance.timing.navigationStart + parseInt(e.timeStamp)) - getPRTTime();
    // window.__launchtime = Date.now();
    let realUrl = e && e.detail && e.detail.url;
    console.warn('上屏事件', e, `pt: ${getPTStatus()}`, `prt: ${getPRTTime()}`, `上屏的接口耗时：${Date.now() - e.detail.timestamp}`);
    if (realUrl) {
      // 替换hash
      if (realUrl.includes('disable_back')) {
        event.emit('disableBack');
      }
      realUrl = realUrl.replace(/#.*/g, '');
      const url = `${realUrl}${location.hash}`;
      window.history.replaceState('', document.title, url);
    }
    isAppeared = true;
  });
}

let attachedEvent = null;
let pageAttached = false;
let notPrerenderReport = false;

const attachMonitor = tracker.Monitor(WPK_CATEGORY_MAP.PRERENDER_DISPLAY);
let attachType = '';
let cbs = [];

function runCbs() {
  for (let i = 0; i < cbs.length; i++) {
    cbs[i](attachedEvent);
  }
}

window.onprerendercommit = function(e) {
  if (!attachType) {
    attachedEvent = e;
    attachType = 'onprerendercommit';
  }
  if (attachType !== 'onprerendercommit') {
    return;
  }
  let realUrl = e && e.detail && e.detail.url;
  let entry = '';
  if (realUrl) {
    entry = parseQueryStr(realUrl).entry;
  }
  if (!pageAttached) {
    pageAttached = true;
    attachMonitor.success({
      msg: '预渲染页面-触发新预渲染上屏',
      c1: 'prerendercommit',
      c2: entry,
      bl1: realUrl,
    });
    runCbs();
  }
};

prerenderEvent.on('prerendercommit', e => {
  if (!attachType) {
    attachedEvent = e;
    attachType = 'prerendercommit';
  }
  if (attachType !== 'prerendercommit') {
    return;
  }
  let realUrl = e && e.detail && e.detail.url;
  let entry = '';
  if (realUrl) {
    entry = parseQueryStr(realUrl).entry;
  }
  if (!pageAttached) {
    pageAttached = true;
    attachMonitor.success({
      msg: '预渲染页面-触发预渲染上屏',
      c1: 'prerendercommit',
      c2: entry,
      bl1: realUrl,
    });
    runCbs();
  }
});

// 上屏兜底
document.addEventListener('onPageFirstShow', (value) => {
  if (!attachType) {
    attachedEvent = value;
    attachType = 'onPageFirstShow';
  }
  if (attachType !== 'onPageFirstShow') {
    return;
  }
  if (!pageAttached) {
    pageAttached = true;
    attachMonitor.success({
      msg: '预渲染页面-触发onPageFirstShow',
      c1: 'onPageFirstShow',
      c2: '',
    });
    runCbs();
  }
});


export function doOnAttach(cb) {
  if (isPrerender()) {
    if (attachedEvent) {
      cb(attachedEvent);
    } else {
      cbs.push(cb);
    }
  } else {
    cb({});
    const { entry = '' } = uc.params;
    if (!notPrerenderReport) {
      notPrerenderReport = true;
      attachMonitor.success({
        msg: '非预渲染页面',
        c1: '',
        c2: entry,
      });
    }
  }
}

export function isAttached() {
  return isAppeared;
}
