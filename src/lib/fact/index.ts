import { Fact, PlainObject } from './index.d';
const pageT0 = Date.now();
import { getParam } from '@/lib/qs';

const factStat = require('@ali/fact-stat');
export interface Options {
  a: string;
  b: string;
  ev_ct: string;
  ev_sub: string;
  biz_ver: string;
  [k: string]: string | number | boolean;
}

const baseParams: Options = {
  biz_ver: MAIN_VERSION,
} as any;

function setup(options: Partial<Options>) {
  const { a, b, ev_ct, ev_sub, ...ret } = options;
  Object.assign(baseParams, ret);
  Object.assign(baseParams, ret);
  factStat.setup({a, b, ev_ct, ev_sub});
  factStat.baseParam(getBaseParams());
}

function baseParam(params: PlainObject) {
  Object.assign(baseParams, params);
  factStat.baseParam(params);
}

function getBaseParams() {
  const entry = getParam('entry');
  return {
    ...baseParams,
    entry, is_weex: false, dt: Date.now() - pageT0,
  };
}

function pageview(pageName: string, params?: object) {
  factStat.pageview(pageName, { ...getBaseParams(), ...params });
}

function click(arg1: string, params?: object) {
  factStat.click(arg1, { ...getBaseParams(), ...params });
}
function exposureAsync(arg1: string, params?: object,) {
  execAsync(exposure)(arg1, params)
}

function execAsync(method, ts = 0) {
  return (...args) => {
    setTimeout(() => {
      method(...args);
    }, ts);
  }
}

function exposure(arg1: string, params?: object) {
  factStat.exposure(arg1, { ...getBaseParams(), ...params });
}

function event(arg1: string, params?: object) {
  factStat.event(arg1, { ...getBaseParams(), ...params });
}

export default { baseParam, setup, pageview, click, exposure, event, exposureAsync } as Fact;
