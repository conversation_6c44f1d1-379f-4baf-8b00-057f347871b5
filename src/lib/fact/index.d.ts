type BaseType = string | number | boolean | null;

export interface PlainObject {
  [key: string]: BaseType;
}

interface Config {
  /** 是否开启UT打点，默认为true */
  needUT?: boolean;
  /** 是否开启WA打点，默认为true */
  needWa?: boolean;
  /** 是否开启WA在线时长打点，默认为fasle */
  onlinetimeOn?: boolean;
  /** WA在线时长采集比率，默认为30% */
  onlineTimeChance?: number;
  /** 是否开启自动曝光，默认为false */
  exposureStatOn?: boolean;
  /** 设置WA统计的集群，会使用对应集群的默认waUrl及appLogUrl，设置为空需要业务提供waUrl和appLogUrl */
  clusterName?: 'india' | 'internation' | 'china' | 'sanfrancisco' | 'other', // WA集群，默认为国内
  /** 自定义WA日志上报地址，clusterName不在可选项里时生效 */
  waUrl?: string;
  /** 自定义WA日志appLog地址，clusterName不在可选项里时生效 */
  appLogUrl?: string;
}

interface Option {
  /** a 位 */
  a: string;
  /** b 位 */
  b?: string;
  /** 业务标识 */
  ev_ct: string;
  /** 业务方，用户区分日志，比如活动组为activity */
  ev_biz?: string;
  /** 活动业务线， 如市场、主客 */
  ev_biz_sub?: string;
  /** 页面入口 */
  entry?: string;
  config?: Config;
  [key: string]: string | Config;
}

interface PageviewParams extends PlainObject {
  /** b 位 */
  b: string;
  /** 扩展参数 */
  [key: string]: BaseType;
}

interface ClickParams extends PlainObject {
  /** c 位 */
  c: string;
  /** d 位 */
  d: string;
  /** 扩展参数 */
  [key: string]: BaseType;
}

interface ExposureParams extends ClickParams {}

export class Fact {

  /** 公共参数 */
  readonly params: PlainObject;

  /** 初始化设置 */
  setup: (option: Option) => void;

  /** 访问日志 */
  pageview: (page: string, params: PageviewParams) => void;

  /** 点击日志 */
  click: (arg1: string, params: ClickParams) => void;

  /** 曝光日志 */
  exposure: (arg1: string, params: ExposureParams) => void;

  /** 自定义日志 */
  event: (arg1: string, params?: PlainObject) => void;

  /** 设置公共参数 */
  baseParam: ((key: string, value: BaseType) => void) & ((params: PlainObject) => void);

  /** 曝光日志 */
  exposure: (arg1: string, params: ExposureParams) => void;

  /** 异步曝光日志 */
  exposureAsync: (arg1: string, params: ExposureParams, ts?:number) => void;
}

// export default new Fact();
