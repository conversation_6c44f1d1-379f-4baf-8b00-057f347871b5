## Getting Start

```js
import factStat from '@ali/weex-toolkit/lib/fact_stat';

// 初始化
factStat.setup({
  a: 'SPM规范的A位',
  b: 'SPM规范的B位',
  ev_ct: '业务标识',
  ...其他扩展字段
});


// 访问日志
factStat.pageview('pageName', {
  b: 'spm规范的b位', // 可选，不设置默认使用初始化时的B位或初始化时的B位
  ...其他扩展字段
});

// 点击日志
// 注意，UT NATIVE库在处理ARG1时，会把基础参数page和参数arg1进行拼接。
// eg: page='home', arg1='myarg1'。最终上传arg1='home_myarg1'
factStat.click('arg1', {
  c: 'SPM规范的C位',
  d: 'SPM规范的D位',
  ...其他扩展字段
});

// 曝光日志
factStat.exposure('arg1', {
  c: '我是C',
  d: '我是D',
  ...其他扩展字段
});

// 自定义事件

factStat.event({
  arg1: 'arg1',
  params: {
    c: '我是C',  // 选填
    d: '我是D', // 选填
    ...其它扩展字段
  }
  
})
```

# 验证

- [UT埋点验证平台](<https://usertrack.alibaba-inc.com/validate/qrcode>)

> Android：ut.21711551
> 
> IOS正式版：ut.21803344
