/**
 * 激励广告相关处理函数文件
 */

import ucapi from "@/utils/ucapi";
import tracker from "@ali/weex-toolkit/lib/tracker";
import { TaskInfo } from "@/store/models/task/types";
import { store } from "@/store";
import { isIOS, isAndroid, isLatestVersion } from "@/lib/universal-ua";
import { getExtraInfo } from "@/pages/index/task/help";

/**
 * 通知客户端激励广告发奖成功
 * @param task 
 * @param options 
 */
export const notifyAdAwardSuccess = async (task: TaskInfo, options: { slotKey: string; rewardId: string; appId: string; requestId: string; businessCode?: string }, source: string) => {
  const monitorParams = {
    c1: `${task.id}`,
    c2: options?.slotKey,
    c3: options?.rewardId,
    c4: `${task?.name}`,
    c5: `${source}`,
    bl1: JSON.stringify(task),
    bl2: JSON.stringify(options),
  }
  const consumeMonitor = tracker.Monitor(173, {sampleRate: 1});
  try {
    const res = await ucapi.biz.consumeSuccessRewards({ ...options })
    const consumeSuccess = res?.success?.toString() === '1' || res?.success?.toString() === 'true';
    if (consumeSuccess) {
      consumeMonitor.success({
        msg: `${options?.slotKey}-成功`,
        w_succ: 1,
        ...monitorParams,
        bl3: JSON.stringify(res)
      })
    } else {
      consumeMonitor.fail({
        msg: `${options?.slotKey}-失败`,
        w_succ: 0,
        ...monitorParams,
        bl3: JSON.stringify(res)
      })
    }
    return consumeSuccess;
  } catch (error) {
    consumeMonitor.fail({
      msg: `${options?.slotKey}-${task.name}-失败-catch`,
      w_succ: 0,
      ...monitorParams,
      bl3: JSON.stringify(error)
    })
    return false;
  }
}

/**
 * 获取激励广告slot数据
 * @param task 
 * @returns 
 */
export const getIncentiveAdSlotData = (task: TaskInfo) => {
  const clientType = store.getState()?.app?.clientType;
  const isLite = clientType === 'UCLite';
  const {
    iosSlotKey = '',
    androidSlotKey = '',
    androidAppId = '',
    iosAppId = '',
    androidCheckAdFill = false, 
    iosCheckAdFill = false,
  } = isLite ? getExtraInfo(task)?.ucLiteAdConfig || {} : getExtraInfo(task)?.ucAdConfig || {};

  return {
    slotKey: isIOS ? iosSlotKey : androidSlotKey,
    appId: isIOS ? iosAppId : androidAppId,
    checkAdFill: isIOS ? iosCheckAdFill : androidCheckAdFill,
  }
}

/**
 * 是否开启异步查奖
 * 1、ios需要指定版本以上才可以开启异步查询奖励, 否则会导致广告调起失败;
 * 2、例如: 主端ios需要在版本17.2.0.2476后才开启, 在之前版本开启大概率会导致广告调起失败
 * @param curVer - 当前客户端版本
 * @param isLite - 是否为极速版
 */
export const isOpenQueryAward = (curVer: string, isLite: boolean) => {
  if (isAndroid) {
    return isLatestVersion(curVer, '16.4.0.0000')
  }
  const frontData = store.getState().app;
  const iosSupVer = isLite ? frontData?.iosLiteOpenQueryAwardVersion : frontData?.iosOpenQueryAwardVersion;
  return isLatestVersion(curVer, iosSupVer || '17.2.0.2476');
};