import { STORAGE_APP_NOT_INSTALL_KEY } from "@/constants/storage";

export const getAppInstallNotInstallMapByLocalStorage = () => {
    const value = localStorage.getItem(STORAGE_APP_NOT_INSTALL_KEY);
    if (value) {
        try {
            const obj = JSON.parse(value);
            const localDataMap = new Map(Object.entries(obj));
            return localDataMap
        } catch (error) {
            return new Map();
        }
    }
    return new Map();
};

const appInstallAllData = getAppInstallNotInstallMapByLocalStorage();
export function setAppInstallWithExpiryMinutes(data: {
    pkg: string, install: boolean, updateLater?: boolean, minutes?: number;
}) {
    const { pkg, install, updateLater = false, minutes = 30 } = data;
    if (!pkg) {
        return;
    }
    const now = new Date();
    const expiryTime = now.getTime() + minutes * 60 * 1000;
    appInstallAllData.set(pkg, {
        installed: Boolean(install),
        expiry: expiryTime,
    });
    // 设置延迟更新，避免在循环重复写入
    !updateLater && localStorage.setItem(STORAGE_APP_NOT_INSTALL_KEY, JSON.stringify(Object.fromEntries(appInstallAllData ?? {})));
}

// 更新所有app安装
export function updateAllAppInstallWithExpiryMinutes() {
   localStorage.setItem(STORAGE_APP_NOT_INSTALL_KEY, JSON.stringify(Object.fromEntries(appInstallAllData ?? {})));
}

export function getAppInstallWithExpiryMinutes(pkg: string) {
    const value = appInstallAllData.get(pkg);
    if (!value) {
        return null;
    }
    const now = new Date();
    const { installed = false, expiry = now.getTime() } = value;
    // 检查是否过期
    if (Date.now() > expiry) {
        // 数据过期，删除该包名的数据
        appInstallAllData.delete(pkg);
        // 更新缓存（写回去）
        localStorage.setItem(STORAGE_APP_NOT_INSTALL_KEY, JSON.stringify(Object.fromEntries(appInstallAllData ?? {})));
        return null;
    }
    // 数据有效，返回 installed 的值
    return installed;

}