import { store } from "@/store";
import { isIOS  } from '@/lib/universal-ua';

const CALL_TAOBAO_CONFIG_MAP = {
  UCMobile: {
    ios: {
      pgkName: 'com.ucweb.iphone.lowversion',
      backURL: 'uclink://www.uc.cn/f11f7cb5d16fa0969d670c1eccc6053d'
    },
    android: {
      pgkName: 'com.UCMobile',
      backURL: 'uclink://www.uc.cn/cc77796ca7c25dff9607d31b29effc07'
    },
  },
  UCLite: {
    ios: {
      pgkName: 'com.ucweb.iphone.pro',
      backURL: 'ucliteioslink://www.uc.cn/8c09be3332aa631fcbad0c5834ef68fd'
    },
    android: {
      pgkName: 'com.ucmobile.lite',
      backURL: 'uclink://www.uc.cn/19b64348381e629f44f43b8506f24e92'
    },
  },
}

export const getTaobaoUrlParams = () => {
  const clientType = store?.getState()?.app?.clientType;
  const fr = isIOS ? 'ios' : 'android';
  const urlObj = CALL_TAOBAO_CONFIG_MAP[clientType][fr];
  return {
    packageName: urlObj?.pgkName,
    backURL: urlObj?.backURL,
  }
}
