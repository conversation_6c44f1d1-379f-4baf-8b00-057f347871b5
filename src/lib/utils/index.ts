export const sleep = (duration = 3000) => new Promise(rs => setTimeout(rs, duration));

export function debounce(fn, wait) {
  let timeout = null;
  return function() {
      let context = this;
      let args = arguments;
      if (timeout) clearTimeout(timeout);
      let callNow = !timeout;
      timeout = setTimeout(() => {
          timeout = null;
      }, wait);
      if (callNow) fn.apply(context, args);
  };
}