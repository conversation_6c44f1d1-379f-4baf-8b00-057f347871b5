import { getInstallInfo, notifyChanged, installWidget } from '@/lib/ucapi/index';
import { appVersion, isLatestVersion, isUc} from '@/lib/universal-ua';
import { TaskInfo } from '@/store/models/task/types';

export const widget1 = {
  typeId: 'widget_welfare_1x1',
  addtype: 'widget_dynamic_welfare_add_1x1',
  widgetReceiverName: 'com.uc.business.widget.dynamic.welfare.receiver.WelfareWidgetReceiver1x1',
};
export const widget2 = {
  typeId: 'widget_welfare_2x2',
  addtype: 'widget_dynamic_welfare_add_2x2',
  widgetReceiverName: 'com.uc.business.widget.dynamic.welfare.receiver.WelfareWidgetReceiver2x2',
};

// 更新小组件状态
export function updateWidget({ typeId, addtype, widgetReceiverName }) {
  if (isUc && isLatestVersion(appVersion, '16.1.8')) {
    getInstallInfo(addtype, typeId, widgetReceiverName, {taskInfo: null, resource_location: 'refresh' }).then((res: { isInstalled: boolean }) => {
      if (res?.isInstalled) notifyChanged(typeId, widgetReceiverName);
    });
  }
}

// 判断小组件是否安装
export async function whetherWidget({ typeId, addtype, widgetReceiverName }, extraParams: {taskInfo: TaskInfo, resource_location: string }) {
  return await getInstallInfo(addtype, typeId, widgetReceiverName, extraParams);
}

// 安装小组件
export async function installDesktopWidget({ typeId, addtype, widgetReceiverName }, extraParams: {taskInfo: TaskInfo, resource_location: string }) {
  return await installWidget(addtype, typeId, widgetReceiverName, extraParams);
}