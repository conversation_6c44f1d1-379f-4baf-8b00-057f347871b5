export interface WpkReporterOptions {
  bid: string;
  uid: string | number;
  utdid: string | number;
  rel: string;
  spa: boolean;
  debug: boolean;
  checkHidden: boolean;
  plugins: any[];
  sampleRate: number;
  /** 是否关闭系统监控项数据上报，只保留自定义监控上报。若应用同时有“内核接入”方式和“JS探针接入”方式时，建议在android系统下设置此项为 true, 兼容性好 */
  onlyCustom?: boolean;
  /** 新参数 和 onlyCustom 一致，不过暂时依赖版本没支持 */
  onlyCustomInUCCore?: boolean;
  beforeSend?: any;
}

export interface ILogErrorParams {
  c1?: string | number | boolean;
  c2?: string | number | boolean;
  c3?: string | number | boolean;
  c4?: string | number | boolean;
  c5?: string | number | boolean;
  c6?: string | number | boolean;
  c7?: string | number | boolean;
  c8?: string | number | boolean;
  c9?: string | number | boolean;
  c10?: string | number | boolean;
  bl1?: string | number | boolean;
  bl2?: string | number | boolean;
  bl3?: string | number | boolean;
  bl4?: string | number | boolean;
  bl5?: string | number | boolean;
  sampleRate?: number;
}

export interface ILogParams extends ILogErrorParams {
  category: number;
  msg?: string | number | boolean;
  w_msg?: string;
  stack?: string;
  w_file?: string;
  w_line?: string;
  w_col?: string;
  w_succ?: 0 | 1;
  wl_avgv1?: number;
  wl_avgv2?: number;
  wl_avgv3?: number;
  wl_avgv4?: number;
  wl_avgv5?: number;
  wl_avgv6?: number;
  wl_avgv7?: number;
  wl_avgv8?: number;
  wl_avgv9?: number;
  wl_avgv10?: number;
  wl_avgv11?: number;
  wl_avgv12?: number;
  sampleRate?: number;
  w_trace_reqid?: string;
}
