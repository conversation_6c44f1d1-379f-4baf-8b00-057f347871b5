import tracker from '@ali/weex-toolkit/lib/tracker';
import config from '@/config';
import withUniversal from '@/lib/render-utils/with-universal';
// import ItraceFluency from '@ali/itrace-fluency';
import ItraceInterface from '@ali/itrace-interface'
import { isIOS } from '@/lib/universal-ua';
import { inittialUserId } from '@/utils/wpk';

function initClient() {
  console.log('PUBLISH_VERSION', MAIN_VERSION);
  tracker.init({
    bid: config.WPK_BID,
    sampleRate: 1,
    rel: MAIN_VERSION,
    uid: inittialUserId,
    debug: false,
    plugins: [
      'flow', 
      'perf',
      'resource',
      {
        id: 'interface',
        plugin: ItraceInterface,
        enableMtop: false,
        withReqHeader: true,
        withRespHeader: true,
        enableCorsTrace(url) {
          // 跨域的API请求，仅对开启农场服务开启
          const APIHost = [config.coralHost, config.taskHost];
          return APIHost.some((host) => url.includes(host));
        }
      },
      // {
      //   id: 'fluency',
      //   plugin: ItraceFluency,
      //   sampleRate: 1, // 采样率，默认为 1，同时控制响应延迟和长任务,
      //   inputDelayOpt: { // 响应延迟的配置
      //     enable: true, // 是否启用，默认为 true
      //     sampleRate: 1, // 响应延迟采样率，不设置时，用上面一个
      //     minThreshold: 100, // 响应延迟时间的最小阈值，即：超过此阈值才上报，默认 100ms
      //     maxThreshold: 60000 // 响应延迟时间的最大阈值，即：低于此阈值才上报，默认 60000ms
      //   },
      //   longTaskOpt: {// 长任务的配置
      //     enable: true, // 是否启用，默认为 true
      //     sampleRate: 1, // 响应延迟采样率，不设置时，用上面一个

      //     minThreshold: 150, // 长任务时间的最小阈值，即：超过此阈值才上报，默认 50ms
      //     maxThreshold: 60000 // 长任务时间的最大阈值，即：低于此阈值才上报，默认 60000ms
      //   },
      //   scrollBlockOpt: {// 滚动、滑动卡顿监控配置
      //     enable: false, // 是否启用
      //   },
      //   zoomBlockOpt: {
      //     enable: false, // 是否启用
      //   }
      // },
      'blank',
    ],
    beforeSend: (logData: {type: string; w_res: string; w_type: number }) => {
      if (logData?.type === 'api') {
        // IOS 存在一个API请求重复上报:fetch/xhr各一条, xhr 不上报
        if (isIOS && logData?.w_type === 16 && logData?.w_res) {
          const resUrl = logData?.w_res ?? '';
          const disAllowedHosts = [
            config.taskHost,
            config.coralHost,
            config.HC_AD_API_URL,
            config.HC_TASK_API
          ];
          return !disAllowedHosts.some((host) => resUrl.includes(host));
        }
        // 相关的素材资源忽略日志上报, 这部分应该属于资源不属于API
        if (logData?.w_res) {
          const resUrl = logData?.w_res ?? '';
          const disAllowedURL = ['https://g.alicdn.com', 'https://image.uc.cn', 'https://huichuan.sm.cn', 'https://broccoli-static.uc.cn'];
          return !disAllowedURL.some((host) => resUrl.includes(host));
        }

        return true;
      }
      return true;
    }
  });
}

export default withUniversal(initClient);
