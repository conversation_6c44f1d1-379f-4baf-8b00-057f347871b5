import uc from '@ali/weex-toolkit/lib/weex_config/uc';


export interface IRect {
  width: number;
  height: number;
  bottom: number;
  top: number;
  left: number;
  right: number;
}

/**
 * 算出屏幕宽度为 750 下的节点rect
 * @param ref {React.Node} - 对应节点
 */
export function getComponentRect(ref): Promise<IRect> {
  return new Promise(resolve => {
    // 这里的值受 meta 中的 initial-scale 影响，和 window.innerWidth 是同一个计量单位
    const result = ref.getBoundingClientRect();
    const ratio = window.innerWidth / 750;

    const viewportWidth750Result = {
      width: result.width / ratio,
      height: result.height / ratio,
      top: result.top / ratio,
      bottom: result.bottom / ratio,
      left: result.left / ratio,
      right: result.right / ratio,
    };

    resolve(viewportWidth750Result);
  });
}

/**
 * @param len 屏幕宽度转为750之后的值转回px
 */
export function trans2PxLen(len: number) {
  const res = `${len * window.innerWidth / 750}px`;
  return res;
}

export function getScreenSize() {
  const screenWidth = 750;
  const screenHeight = Math.ceil(uc.env.windowHeight / uc.env.windowWidth * screenWidth);
  return { screenWidth, screenHeight };
}
