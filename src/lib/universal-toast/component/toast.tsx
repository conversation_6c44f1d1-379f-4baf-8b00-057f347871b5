import { LONG_DELAY, SHORT_DELAY } from '../utils/index';
import { ToastOption } from '../types';
import Driver from '@ali/pcom-driver';
import CoinIcon from './images/<EMAIL>';
import CashIcon from './images/<EMAIL>';

interface QueueOption {
  message: string;
  duration?: number;
  award: IAward;
  more: IAward;
}

interface IAward {
  mark: string;
  icon?: string;
  name?: string;
  amount: number;
}

let queue: QueueOption[] = [];
let isProcessing = false;
let toastWin: HTMLElement;
let imgIconEle: HTMLElement;
let messageEle: HTMLElement;
let awardEle: HTMLElement;
let moreAwardEle: HTMLElement;
let lastToastType: string;

const styles = {
  container: {
    left: '50%',
    bottom: '46%',
    marginRight: '-50%',
    transform: 'translate(-50%)',
    position: 'fixed',
    zIndex: 9999,
    // display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    boxSizing: 'border-box',
    maxWidth: '80%',
    color: '#ffffff',
    paddingTop: '16rpx',
    paddingBottom: '16rpx',
    paddingLeft: '32rpx',
    paddingRight: '32rpx',
    fontSize: '32rpx',
    fontWeight: '600',
    borderRadius: '16rpx',
    textAlign: 'center',
    transition: 'all 0.4s ease-in-out',
    webkitTransition: 'all 0.4s ease-in-out',
    display: 'none',
    backgroundImage: 'linear-gradient(180deg, rgba(0,0,0,0.3), rgba(0,0,0,0.6))',
  },
  icon: {
    width: '380rpx',
    height: '190rpx',
    marginTop: '-30rpx',
  },
  award: {
    color: '#FF726B',
    fontSize: '36rpx',
  }
};

// function showToastWindow(message: string): void {
function showToastWindow(info: QueueOption): void {
  if (!toastWin) {
    toastWin = document.createElement('div');
    toastWin.setAttribute('role', 'alert');
    // support for ARIA, add tabindex for focus
    // https://developer.mozilla.org/zh-CN/docs/Web/HTML/Global_attributes/tabindex
    toastWin.setAttribute('tabindex', '-1');
    Driver.setStyle(toastWin, styles.container);
    document.body.appendChild(toastWin);
  }

  if (info.award) {
    Driver.setStyle(toastWin, { display: 'none' });

    if (lastToastType === '') {
      toastWin.textContent = '';
    }

    const isCash = info.award ? info.award?.mark?.indexOf('cash') > -1 : false;
    const awardAmount = isCash ? info.award?.amount / 100 : info.award?.amount || 0;
    const awardName = isCash ? '元' : '元宝';
    let awardIcon = isCash ? CashIcon : CoinIcon;
    // toastWin.innerHTML = `
    //   <img src="${awardIcon}" style="width: 190px; margin-top: -24px;" />
    //   <div style="margin-top: -10px;">${info.message}</div>
    //   <div style="color: #FF726B; margin-bottom: 10px;">+${awardAmount}${awardName}</div>
    // `;

    if (info.more) {
      awardIcon = CashIcon;
    }

    if (!imgIconEle) {
      imgIconEle = document.createElement('img');
      Driver.setStyle(imgIconEle, styles.icon);
    }
    imgIconEle.setAttribute('src', awardIcon);

    if (!messageEle) {
      messageEle = document.createElement('div');
    }
    messageEle.textContent = info.message;

    if (!awardEle) {
      awardEle = document.createElement('div');
      Driver.setStyle(awardEle, styles.award);
    }
    awardEle.textContent = awardAmount ? `+${awardAmount}${awardName}` : '';

    toastWin.appendChild(imgIconEle);
    toastWin.appendChild(messageEle);
    toastWin.appendChild(awardEle);

    if (info.more) {
      if (!moreAwardEle) {
        moreAwardEle = document.createElement('div');
        Driver.setStyle(moreAwardEle, styles.award);
      }
      const isCash = info.more ? info.more?.mark?.indexOf('cash') > -1 : false;
      const moreAwardAmount = isCash ? info.more?.amount / 100 : info.more?.amount || 0;
      const moreAwardName = isCash ? '元' : '元宝';
      moreAwardEle.textContent = moreAwardAmount ? `+${moreAwardAmount}${moreAwardName}` : '';
      toastWin.appendChild(moreAwardEle);
    }

    lastToastType = 'award';
    Driver.setStyle(toastWin, {
      display: 'block',
      borderRadius: '40rpx',
      padding: '0 0 40rpx 0',
      boxShadow: '0 40rpx 80rpx 0 rgba(0,0,0,0.25)',
      transform: 'translate(-50%,-50%)',
      webkitTransform: 'translate(-50%,-50%)'
    });
  } else {
    if (lastToastType === 'award') {
      toastWin.innerHTML = '';
    }

    toastWin.textContent = info.message;
    lastToastType = '';
    Driver.setStyle(toastWin, { display: 'block', borderRadius: '16rpx', padding: '16rpx 32rpx', transform: 'translate(-50%,-50%)', webkitTransform: 'translate(-50%,-50%)' });
  }
  // toastWin.textContent = message;
  // Driver.setStyle(toastWin, { transform: 'translate(-50%,-50%)', webkitTransform: 'translate(-50%,-50%)' });
}

function hideToastWindow(): void {
  setTimeout((): void => {
    if (toastWin && toastWin.style) {
      Driver.setStyle(toastWin, {
        transform: 'translate(-50%,-50%) scale(0.8)',
        webkitTransform: 'translate(-50%,-50%) scale(0.8)',
      });
    }
  }, 0);
}

const innerToast = {
  hideTimer: null,
  show() {
    // All messages had been toasted already, so remove the toast window,
    if (!queue.length) {
      if (toastWin) {
        // eslint-disable-next-line
        (toastWin as any).parentNode.removeChild(toastWin);
      }
      (toastWin as any) = null;
      return;
    }

    // the previous toast is not ended yet.
    if (isProcessing) return;
    isProcessing = true;

    const toastInfo: QueueOption = queue.shift() as QueueOption;
    // showToastWindow(toastInfo.message);
    showToastWindow(toastInfo);
    innerToast.hideTimer = setTimeout(() => innerToast.switchToNext(), toastInfo.duration);
  },
  // push(message: string, duration: number): void {
  //   queue.push({
  //     message,
  //     duration,
  //   });
  //   innerToast.show();
  // },
  push(message: string, options: any): void {
    queue.push({
      message,
      ...options,
    });
    innerToast.show();
  },
  // Switch to next message
  // This function will hide current, and call `show()` to display next
  // If queue is empty, DOM will be clear in `show()`
  switchToNext() {
    hideToastWindow();
    isProcessing = false;
    setTimeout(() => innerToast.show(), 500);
    if (innerToast.hideTimer) {
      clearTimeout(innerToast.hideTimer);
      innerToast.hideTimer = null;
    }
  },
};


const Toast: ToastOption = {
  SHORT: SHORT_DELAY,
  LONG: LONG_DELAY,

  /*
   * @param message {String}
   * @param duration {Number}
   * @param userStyle {Object} user defined style
   */
  // show(message: string, duration: number = SHORT_DELAY): void {
  //   innerToast.push(message, duration);
  // },
  show(message: string, options: QueueOption): void {
    options = { duration: this.SHORT, ...options };
    innerToast.push(message, options);
  },

  hide() {
    // remove all queued messages
    queue = [];
    innerToast.switchToNext();
  },
};

export default Toast;
