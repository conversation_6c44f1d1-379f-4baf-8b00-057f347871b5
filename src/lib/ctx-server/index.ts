interface ILogger {
  info(msg: any, ...args: any[]): void;
  debug(msg: any, ...args: any[]): void;
  error(msg: any, ...args: any[]): void;
  warn(msg: any, ...args: any[]): void;
}

let ctx: {
  logger: ILogger;
} | undefined;

if (typeof __ctx__ !== 'undefined') {
  ctx = __ctx__;
} else {
  ctx = {
    logger: {
      info() {},
      debug() {},
      error() {},
      warn() {},
    }
  } as any;
}

export default ctx;
