import { getParam } from '@/lib/qs';
import wormholeData from '@/lib/wormhole-data';
import { getPkgIdfromWormholeData } from './normal';
import { isNode } from 'universal-env';

export enum DOCUMENT_TYPE {
  SYNC_SSR = 'SYNC_SSR',
  ASYNC_SSR = 'ASYNC_SSR',
  CSR = 'CSR',
  STREAM_SSR = 'STREAM_SSR',
  STREAM_CSR = 'STREAM_CSR',

}

/**
  SSR服务渲染时，从URL获取参数，决定渲染结果。
 skeletonMode=asyncSsr：并行SSR骨架模版
 skeletonMode=csr：CSR骨架模版
 skeletonMode=stream_csr 流式CSR
 skeletonMode=stream_ssr 流式SSR
*/
function ssrGetDocumentTypeFromUrl() {
  let documentType = (getParam('x_render_type') || getParam('skeletonMode') || '').toUpperCase();
  const isStream = getParam('x_stream') === '1';
  if (documentType === 'ASYNCSSR') {
    documentType = DOCUMENT_TYPE.ASYNC_SSR;
  } else if (isStream && documentType !== DOCUMENT_TYPE.STREAM_CSR && documentType !==  DOCUMENT_TYPE.STREAM_SSR) {
    documentType = documentType === DOCUMENT_TYPE.CSR ? DOCUMENT_TYPE.STREAM_CSR : DOCUMENT_TYPE.STREAM_SSR;
  }
  return documentType || DOCUMENT_TYPE.SYNC_SSR;

}

// SSR判断是否为骨架模式
export function ssrIsSkeletonMode() {
  const documentType = ssrGetDocumentTypeFromUrl();
  const flag = [DOCUMENT_TYPE.ASYNC_SSR, DOCUMENT_TYPE.CSR].some(s => s === documentType);
  console.log('ssrIsSkeletonMode -> ', documentType, 'isNode', isNode, ' flag ', flag)
  const ret = isNode && flag;
  return ret;

}

// SSR时，装饰getFirstData接口返回的数据
export function ssrWrapFirstData(firstData, duration?) {
  const documentType = window?.__INITIAL_DATA__?.pageInitialProps?.__documentType;
  return {
    firstData,
    __ver: MAIN_VERSION,
    __duration: duration,
    __broPackId: getPkgIdfromWormholeData(wormholeData),
    __documentType: documentType || ssrGetDocumentTypeFromUrl(),
  }
}

export function ssrGetInitialProps(getFirstData: () => any) {
  return async function (ctx) {
    console.log('[render-utils] ssrGetInitialProps invoke ...', ctx)

    let firstData,duration;
    // 非骨架渲染时，获取首屏数据
    // 增加监控
    const flag = ctx?.forceGet || !ssrIsSkeletonMode()
    console.log('[render-utils] ssrGetInitialProps !ssrIsSkeletonMode ', flag)

    if (flag) {
      try {
        const startTime = Date.now();
        firstData = await getFirstData();
        duration = Date.now() - startTime;
        console.log(`[render-utils] ssrGetInitialProps - getFirstData | time used ${duration}ms`);
      } catch(err) {
        console.error(`[render-utils] ssrGetInitialProps getFirstData error | ${JSON.stringify(err)}`, err);
      }
    }
    return ssrWrapFirstData(firstData, duration);
  }
}
