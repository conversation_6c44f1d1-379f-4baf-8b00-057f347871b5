import { ssrWrapFirstData, DOCUMENT_TYPE } from './ssr';
import { getParam } from '@/lib/qs';

export function getGlobalFirstData() {
  return window?.__INITIAL_DATA__?.pageInitialProps?.firstData || getInitialDataFromDocument();
}

export function getGlobalInitialProps() {
  return window?.__INITIAL_DATA__?.pageInitialProps;
}

export function isCsrDocument() {
  return getGlobalInitialProps()?.__documentType === DOCUMENT_TYPE.CSR;
}

export function isAsyncSsrDocument() {
  return getGlobalInitialProps()?.__documentType === DOCUMENT_TYPE.ASYNC_SSR || getParam('x_render_type') === 'async-ssr';;
}

export function isWithSkeleton() {
  return [DOCUMENT_TYPE.ASYNC_SSR, DOCUMENT_TYPE.CSR, DOCUMENT_TYPE.SYNC_SSR].indexOf(getGlobalInitialProps()?.__documentType) > -1;
}

export function isGlobalFirstDataEmpty() {
  const firstData = getGlobalFirstData();
  return !firstData;
}

export function setGlobalFirstData(data) {
  if (!data) return;
  if (!window.__INITIAL_DATA__) {
    window.__INITIAL_DATA__ = {};
  }
  const { renderType, skeletonOnly } = window.__INITIAL_DATA__.pageInitialProps || {};
  window.__INITIAL_DATA__.pageInitialProps = { renderType, skeletonOnly, ...ssrWrapFirstData(data) };
}


export function checkSkeleton() {
  // 检测asyncSsr骨架是否有问题。
  if (
    window.location.href.indexOf('skeletonMode=asyncSsr') < 0
    && getGlobalInitialProps()?.__documentType === DOCUMENT_TYPE.SYNC_SSR
  ) {
    const connectSign = window.location.href.indexOf('?') > -1 ? '&' : '?';

    fetch(`${window.location.href}${connectSign}skeletonMode=asyncSsr`)
      .then(res => res.text())
      .then(data => {
        if (data.indexOf(DOCUMENT_TYPE.ASYNC_SSR) < 0) {
          throw new Error();
        }
      })
      .catch(() => {
        console.error(`!!! WARNING：检测到并行SSR骨架屏有异常，请在链接上加上 skeletonMode=asyncSsr 参数进行确认。\n（如果不使用并行SSR能力，忽略此警告，并在 mini-js 中将 NEED_CHECK_SKELETON 设置为 false 以关闭骨架检测， 否则会影响开发体验）`);
      })
  }
}

export function getDocumentType() {
  return getRenderType() || getGlobalInitialProps()?.__documentType;
}

// ---------------- 流式渲染相关  ------------- //

export function isStreamMode() {
  const renderType = (getRenderType() || '').toUpperCase();
  return renderType === 'STREAM_SSR' || renderType === 'STREAM_CSR';
}

export function getRenderType() {
  return (window as any)?.__INITIAL_PROPS__?.renderType || window.__INITIAL_DATA__?.pageInitialProps?.renderType;
}

export function getInitialDataTimeoutFromDocument() {
  return (window as any)?.__STREAM_CSSR_FIRST_DATA_TIMEOUT__ || 1000;
}

export function getInitialDataFromDocument() {
  return (window as any)?.__INITIAL_PROPS__?.initialData  || (window as any)?.__INITIAL_PROPS__?.firstData;
}
/**
 * 在流式CSR场景下，设置一个超时阈值，当服务端请求耗时超过该阈值，再发起重试
 */
export function waitAndFetchInitialData(getInitialData: () => Promise<any>, timeout = getInitialDataTimeoutFromDocument()) {
  window.__CSRFirstDataPms__ = new Promise((rs, rj) => {
    let tid;
    const doGetInitData = () => {
      clearTimeout(tid);
      Promise.resolve(getInitialData()).then(rs).catch(rj);
    }
    detectStreamFirstData().then(rs).catch(doGetInitData);
    tid = setTimeout(() => {
      if (getInitialDataFromDocument()) {
        console.log('已经有初始化数据了, 即将上屏');
        return;
      }
      // 做个标记，便于业务统计流式CSR 成功率
      (window as any).__STREAM_CSR_FALLBACK__ = 1;
      console.log('[waitAndFetchInitialData] timeout %sms,  retry getInitialData', timeout)
      doGetInitData();
    }, timeout);
  });
  return window.__CSRFirstDataPms__;
}


export function detectStreamFirstData(timeout = -1) {
  return new Promise((rs, rj) => {
    let tid;
    console.log('[detectStreamFirstData] document.body listen STREAM_CSSR_FIRST_DATA event');
    document.body.addEventListener('STREAM_CSSR_FIRST_DATA', (event) => {
      const e = event as any;
      console.log('[detectStreamFirstData] receive STREAM_CSSR_FIRST_DATA event ~~', e.detail)
      if (e.detail?.error) {
        rj(e.detail.error);
      } else {
        clearTimeout(tid);
        rs(e.detail?.initialData);
      }
    });
    document.body.dispatchEvent(new CustomEvent('MIN_JS_INIT'));
    (window as any).__MINI_JS_INIT__ = 1;
    if (timeout > 0) {
      tid = setTimeout(() => rj(new Error('timeout')), timeout);
    }
  })
}

export function preFetchInitialData(pms) {
  if (!(pms instanceof Promise)) {
    console.error('首屏接口非promise，请检查是否有误getInitialData方法是否有误');
    return;
  }
  window.__CSRFirstDataPms__ = pms;
}
