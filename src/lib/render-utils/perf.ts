import { getDocumentType } from "./csr";

export const enum renderAction {
  miniJsExecute = 'miniJsExecute',  // miniJS开始执行
  asyncSsrCacheOnScreen = 'asyncSsrCacheOnScreen',  // 并行SSR缓存上屏
  asyncSsrApiStart = 'asyncSsrApiStart',  // 并行SSR接口开始时间
  asyncSsrApiEnd='asyncSsrApiEnd', // 并行SSR接口结束时间
  asyncSsrApiCost='asyncSsrApiCost', // 并行SSR接口结束时
  asyncSsrIsPrefetch = 'asyncSsrIsPrefetch',
  asyncSsrOnScreen = 'asyncSsrOnScreen',  // 并行SSR上屏
  asyncSsrDemote = 'asyncSsrDemote',  // 并行SSR上屏
  nodeFirstData = 'nodeFirstData', // node端首屏接口获取数据耗时
  bootstrapExecute = 'bootstrapExecute',  // bootstrap执行
  csrGotData = 'csrGotData',  // CSR获取到数据
  hydrate = 'hydrate',  // hydrate
  hydrated = 'hydrated',  // hydrate完成，根组件didmount中记录比较合适。
  t2 = 't2',  // t2时间，根据业务情况自行记录
}

interface PerfTime {
  documentType: string;
  documentStart?: number;
  [renderAction.miniJsExecute]?: number;
  [renderAction.asyncSsrCacheOnScreen]?: number;
  [renderAction.asyncSsrApiStart]?: number;
  [renderAction.asyncSsrIsPrefetch]?: '0' | '1';
  [renderAction.asyncSsrOnScreen]?: number;
  [renderAction.asyncSsrDemote]?: '0' | '1';
  [renderAction.nodeFirstData]?: number;
  [renderAction.bootstrapExecute]?: number;
  [renderAction.csrGotData]?: number;
  [renderAction.hydrate]?: number;
  [renderAction.hydrated]?: number;
  [renderAction.t2]?: number;
}

export const perfTime: PerfTime = {
  documentType: getDocumentType(),
  documentStart: window?.__documentStartTime,
};

// 标记性能
export function perfMark(key: renderAction, force = false) {
  switch (key) {
    case renderAction.asyncSsrDemote:
    case renderAction.asyncSsrIsPrefetch:
      return;
    case renderAction.asyncSsrApiCost:
        const { asyncSsrApiStart }: any = perfTime;
        perfTime[key] =  Math.floor(window?.performance?.now()) - asyncSsrApiStart ;
        return;
    default:
      if (!perfTime[key] || force) {
        perfTime[key] = Math.floor(window?.performance?.now());
      }
  }
}

// 标记SSR或并行SSR，Node端调用firstData消耗的时间。
export function perfMarkNodeFirstDataTime(duration = window?.__INITIAL_DATA__?.pageInitialProps?.__duration) {
  if (duration) {
    perfTime[renderAction.nodeFirstData] = duration;
  }
}

// 标记并行SSR是否降级
export function perfMarkAsyncSsrDemote(bool) {
  perfTime[renderAction.asyncSsrDemote] = bool ? '1' : '0';
}

// 标记并行SSR是否为数据预取
export function perfMarkPrefetch(bool) {
  perfTime[renderAction.asyncSsrIsPrefetch] = bool ? '1' : '0';
}

// 获取性能数据
export function getPerf() {
  console.log(perfTime)
  return perfTime;
}

const DocumentTypeMap = {
  CSR_TPL: 'CSR',
  CSR: 'CSR（带骨架）',
  SYNC_SSR: '串行SSR',
  ASYNC_SSR: '并行SSR',
}

// GUIDANCE: 获取啄木鸟上报使用的数据
// 建议业务标记T2后进行上报。
// 啄木鸟配置指引：

// 监控名称：渲染性能监控
// 均值指标：
// wl_avgv1: 主文档开始
// wl_avgv2: 最小JS执行
// wl_avgv3: 并行SSR缓存上屏
// wl_avgv4: 并行SSR请求开始
// wl_avgv5: 并行SSR上屏
// wl_avgv6: node首屏接口耗时
// wl_avgv7: bootstrap
// wl_avgv8: CSR获取到数据
// wl_avgv9: 开始hydrate
// wl_avgv10: hydrate完成
// wl_avgv11: t2
// 自定义指标：
// c1: 命中数据预取
// c2: 并行SSR降级
export function getPerfWpkLogParams() {
  const {
    documentType,
    documentStart: wl_avgv1,
    miniJsExecute: wl_avgv2,
    asyncSsrCacheOnScreen: wl_avgv3,
    asyncSsrApiStart: wl_avgv4,
    asyncSsrOnScreen: wl_avgv5,
    nodeFirstData: wl_avgv6,
    bootstrapExecute: wl_avgv7,
    csrGotData: wl_avgv8,
    hydrate: wl_avgv9,
    hydrated: wl_avgv10,
    t2: wl_avgv11,

    asyncSsrIsPrefetch: c1,
    asyncSsrDemote: c2,
  } = perfTime;

  return {
    msg: DocumentTypeMap[documentType],
    wl_avgv1,
    wl_avgv2,
    wl_avgv3,
    wl_avgv4,
    wl_avgv5,
    wl_avgv6,
    wl_avgv7,
    wl_avgv8,
    wl_avgv9,
    wl_avgv10,
    wl_avgv11,
    c1,
    c2,
  };
}



/**
 * 获取 第一张图片开始加载的时间点
 */
function getFirstImageLoadStartTime() {
  const imgEntries = window.performance.getEntriesByType("resource").filter((e: PerformanceResourceTiming) => e.initiatorType === 'img');
  const firstImgEntry = (imgEntries.sort((e1, e2) => e1.startTime - e2.startTime))[0];
  console.log('[perf] getFirstImageLoadStartTime ', firstImgEntry)
  return firstImgEntry?.startTime;
}
export function rectify(num = 0) {
  const n = num > 18000 ? 18000 : num;
  if (n !== null && n !== undefined && !isNaN(n)) {
    return Math.round(n);
  } else {
    return n;
  }
}

/**
 * https://web.dev/articles/critical-rendering-path/measure-crp?hl=zh-cn#navigation-timing
 *
 * https://wpk.ucweb.com/#/uc-welfare-piggy/flutter/jssdkidx/index?code=145
 * @returns
 */
export function getPerfDomProcessingParams() {
  const documentType = getDocumentType();
  const documentStart = window?.__documentStartTime;
  const domLoading = window.performance.timing.domLoading - window.performance.timing.navigationStart;
  const domInteractive = window.performance.timing.domInteractive - window.performance.timing.navigationStart;
  const domContentLoadedEventStart = window.performance.timing.domContentLoadedEventStart - window.performance.timing.navigationStart;
  const domComplete = window.performance.timing.domComplete - window.performance.timing.navigationStart;
  const chunk2ArrivedTime = window.performance.timing.responseEnd - window.performance.timing.navigationStart;
  const firstImageLoadStartTime = getFirstImageLoadStartTime();
  const ferfParams = {
    msg: documentType,
    wl_avgv1: rectify(domLoading),
    wl_avgv2: rectify(documentStart),
    wl_avgv3: rectify(domInteractive),
    wl_avgv4: rectify(domContentLoadedEventStart),
    wl_avgv5: rectify(domComplete),
    wl_avgv6: rectify(firstImageLoadStartTime),
    wl_avgv7: rectify(window.__CHUNK2_LOAD_TIME__),
    wl_avgv8: rectify(window.__DISPATCH_CHUNK2_EVENT_TIME__),
    wl_avgv9: rectify(perfTime.miniJsExecute),
    wl_argv10: rectify(chunk2ArrivedTime),
    wl_argv11: rectify(perfTime.bootstrapExecute),
    wl_argv12: rectify(perfTime.hydrate),
  };
  console.log('[perf] getPerfDomProcessingParams ->', ferfParams)
  return ferfParams;
}
