import qs from 'qs';
/**
 * 获取 url 上的 search 参数值
 * @param {string} key - 参数名
 * @param {string} url - 被查找的 url，默认为当前的 location
 */
 export function getParam(key, url?) {
  url = url || location.search;
  const hashIndex = url.indexOf('#');
  if (hashIndex > 0) {
    url = url.substr(0, hashIndex);
  }
  const keyMatches = url.match(new RegExp(`[?|&]${ encodeURIComponent(key) }=([^&]*)(&|$)`));
  if (keyMatches && keyMatches[1] === '%s') {
    return keyMatches[1];
  } else {
    return keyMatches ? decodeURIComponent(keyMatches[1]) : '';
  }
}

export function getQueryUCParamsObj(ucParamsStr = 'dsdnfrpfbivessbtbmnilauputogpintnwmtsvcppcprsnnnchmicgodmekplobdmicg') {
  const qsObj = qs.parse(location.search, { ignoreQueryPrefix: true});
  let ret = Object.keys(qsObj).filter(key => ucParamsStr !== key && ucParamsStr.includes(key)).reduce((acc, key) => {
    acc[key] = qsObj[key];
    return acc;
  }, {})
  console.log('[lib/qs/index] location.search ', location.search, ' ret', ret);
  return ret;
}

export function stringify(data = {}) {
  const stack: string[] = [];
  Object.keys(data).forEach((key) => {
    const value = data[key];
    stack.push(`${encodeURIComponent(key) }=${ encodeURIComponent(value === undefined ? '' : value)}`);
  });
  const query = stack.join('&').replace(/%20/g, '+');

  return query;
}

export function addQueryParams(url, queryParams = {}) {
  let targetUrl = url;
  if (/^http(s?):\/\//.test(targetUrl) && queryParams && Object.keys(queryParams).length > 0) {
    const i = targetUrl.indexOf('?');
    let qsObj = i >= 0 ? qs.parse(targetUrl.substring(i), { ignoreQueryPrefix: true }) : {};
    qsObj = { ...qsObj, ...queryParams};
    let prefixPath = i >= 0 ? targetUrl.slice(0, i) : targetUrl;
    targetUrl = `${prefixPath}?${qs.stringify(qsObj)}`;
  }
  return targetUrl;
}

export enum EvSubType {
  UCLITE_FULI_INDEX = 'uclite_fuli_index',
  NOVEL_FULI = 'novel_fuli',
  CLOUDDRIVE_FULI = 'clouddrive_fuli',
  VIDEO_FULI = 'video_fuli',
}

// 获取链接参数透传给服务端
export const getEvSub = ()=> {
  const evSub = getParam('evSub') || '';
  let result = EvSubType.UCLITE_FULI_INDEX;

  if(evSub.includes('novel')){
    result = EvSubType.NOVEL_FULI;
  }else if(evSub.includes('video')){
    result = EvSubType.VIDEO_FULI;
  }else if(evSub.includes('clouddrive')){
    result = EvSubType.CLOUDDRIVE_FULI;
  }else {
    result = EvSubType.UCLITE_FULI_INDEX;
  }
  return result;
}

// 判断主场
export const isMainField = ()=> {
  return getEvSub() === EvSubType.UCLITE_FULI_INDEX;
}

// 是否是小说分场
export const isNovelField = ()=> {
  return getEvSub() === EvSubType.NOVEL_FULI;
}

// 是否是网盘分场
export const isCloudDriveField = ()=> {
  return getEvSub() === EvSubType.CLOUDDRIVE_FULI;
}

export default {
  addQueryParams,
  getParam,
  stringify,
  getEvSub
};
