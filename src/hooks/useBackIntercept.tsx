/**
 * @fileoverview 返回拦截处理的自定义Hook
 * 
 * 该Hook主要用于处理UC和Quark浏览器中的返回拦截逻辑，包括:
 * 1. 物理返回键拦截
 * 2. 手势返回拦截
 * 3. 拦截弹窗展示
 * 4. 拦截频次控制
 * 
 * 主要功能:
 * - 控制返回拦截的频次(每天最大拦截次数和生命周期最大拦截次数)
 * - 管理拦截状态(是否允许拦截、是否正在拦截等)
 * - 处理拦截后的弹窗展示和交互
 * - 提供返回按钮点击和返回事件的处理函数
 * 
 * @example
 * ```tsx
 * const { leftIconClickHandler, useInterceptEffect } = useBackIntercept({
 *   // 获取要在拦截弹窗中展示的任务信息
 *   getBackInterceptTask,
 *   // 处理实际的返回操作
 *   goBackHandler,
 *   // 打开拦截弹窗的方法
 *   openBackInterceptPopup,
 *   // 处理任务完成的回调
 *   taskActionHandler,
 *   // 判断是否允许拦截的方法
 *   shouldAllowIntercept,
 *   // 获取拦截次数限制的配置
 *   getResourceCountInfo
 * });
 * ```
 */

import { useEffect } from 'rax';
import { isSameDay } from "@/utils/date";
import { store } from '@/store';

/**
 * useBackIntercept hook的参数接口
 * @template T 任务信息的类型
 */
interface UseBackInterceptProps<T> {
  /** 获取要在拦截弹窗中展示的任务信息 */
  getBackInterceptTask: () => T | null;
  /** 处理实际的返回操作 */
  goBackHandler: () => void;
  /** 打开拦截弹窗的方法 */
  openBackInterceptPopup: (params: {
    onConfirm: () => void;
    onClose: () => void;
    [key: string]: any;
  }) => void;
  /** 处理任务完成的回调 */
  taskActionHandler: (task: T) => void;
  /** 判断是否允许拦截的方法 */
  shouldAllowIntercept?: () => boolean;
  /** 获取拦截次数限制的配置 */
  getResourceCountInfo?: () => {
    /** 每天最大拦截次数 */
    maxDailyInterceptCount?: number;
    /** 生命周期内最大拦截次数 */
    maxLifecycleInterceptCount?: number
  }
  /** localStorage中存储拦截次数的key */
  interceptCountKey?: string;
  /** localStorage中存储拦截日期的key */
  InterceptDateKey?: string;
}

/**
 * useBackIntercept hook的返回值接口
 */
interface UseBackInterceptReturn {
  /** 处理返回按钮点击的函数 */
  leftIconClickHandler: () => void;
  /** 设置返回拦截的effect hook */
  useInterceptEffect: () => void;
}

/**
 * UC浏览器API调用的参数接口
 */
interface JsApiParams {
  [key: string]: any;
}

/**
 * 调用UC浏览器API的通用方法
 * @param apiName API名称
 * @param params API参数
 * @returns 返回Promise<boolean>表示API调用是否成功
 */
const invokeUcApi = (apiName: string, params: JsApiParams): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    if (!window.ucapi) {
      reject(false);
      return;
    }

    window.ucapi.invoke(apiName, {
      ...params,
      success: (res: any) => {
        if (apiName === 'base.interceptBackKeyEvent') {
          resolve(res.code === 0);
        } else {
          resolve(true);
        }
      },
      fail: () => {
        reject(false);
      }
    });
  });
};

/**
 * 设置物理返回键拦截状态
 * @param enable 是否启用拦截
 */
const interceptBackKeyEvent = (enable: boolean): Promise<boolean> => {
  return invokeUcApi('base.interceptBackKeyEvent', { enable });
};

/**
 * 设置手势返回拦截状态
 * @param disabled 是否禁用手势返回
 */
const gestureBackDisable = (disabled: boolean): Promise<boolean> => {
  return invokeUcApi('biz.gestureDisabled', { disabled });
};

/**
 * 同时设置物理返回键和手势返回的拦截状态
 * @param disabled 是否禁用返回
 */
const setBackIntercept = (disabled: boolean): Promise<boolean> => {
  return Promise.all([
    interceptBackKeyEvent(disabled),
    gestureBackDisable(disabled)
  ]).then(() => true).catch(() => false);
};

// 记录生命周期内的拦截次数
let lifecycleInterceptCount = 0;
// 标记是否正在拦截中，防止重复拦截
let interceptingTag = false

/**
 * 返回拦截处理的自定义Hook
 * 用于在UC和Quark浏览器中处理返回拦截逻辑，包括物理返回键和手势返回的拦截
 * 
 * @template T 任务信息的类型
 */
export function useBackIntercept<T>({
  getBackInterceptTask,
  goBackHandler,
  openBackInterceptPopup,
  taskActionHandler,
  shouldAllowIntercept = () => true,
  getResourceCountInfo,
  interceptCountKey = 'back_intercept_count',
  InterceptDateKey = 'back_intercept_date'
}: UseBackInterceptProps<T>): UseBackInterceptReturn {
  const { maxDailyInterceptCount = 5, maxLifecycleInterceptCount = 1 } = getResourceCountInfo?.() || {}
  const MAX_DAILY_INTERCEPT_COUNT = maxDailyInterceptCount;
  const MAX_LIFECYCLE_INTERCEPT_COUNT = maxLifecycleInterceptCount;
  const INTERCEPT_COUNT_KEY = interceptCountKey;
  const INTERCEPT_DATE_KEY = InterceptDateKey;

  /**
   * 获取当天的拦截次数
   * 如果是新的一天，会重置拦截次数
   * @returns 返回当天已经拦截的次数
   */
  const getInterceptCount = (): number => {
    const lastDate = localStorage.getItem(INTERCEPT_DATE_KEY);
    const now = new Date().getTime().toString();
    if (!lastDate || !isSameDay(lastDate, now)) {
      localStorage.setItem(INTERCEPT_COUNT_KEY, '0');
      localStorage.setItem(INTERCEPT_DATE_KEY, now);
      return 0;
    }
    
    return +(localStorage.getItem(INTERCEPT_COUNT_KEY) || '0');
  };

  /**
   * 增加拦截计数
   * 同时增加生命周期内的拦截次数和当天的拦截次数
   */
  const incrementInterceptCount = (): void => {
    lifecycleInterceptCount++;
    const count = getInterceptCount();
    localStorage.setItem(INTERCEPT_COUNT_KEY, (count + 1).toString());
  };

  /**
   * 判断是否应该设置返回拦截
   * 需要同时满足以下条件：
   * 1. 业务允许拦截（shouldAllowIntercept返回true）
   * 2. 未达到当天最大拦截次数
   * 3. 未达到生命周期最大拦截次数
   * 4. 存在需要展示的任务
   * @returns 是否应该设置返回拦截
   */
  const shouldSetIntercept = (): boolean => {
    if (!shouldAllowIntercept()) {
      return false;
    }
    if (getInterceptCount() >= MAX_DAILY_INTERCEPT_COUNT) {
      return false;
    }
    if (lifecycleInterceptCount >= MAX_LIFECYCLE_INTERCEPT_COUNT) {
      return false;
    }
    return !!getBackInterceptTask();
  };

  /**
   * 处理返回拦截
   * 如果满足拦截条件，会显示拦截弹窗
   * 否则直接执行返回操作
   */
  const handleBackIntercept = (): void => {
    if (interceptingTag) {
      return
    }
    if (!shouldSetIntercept()) {
      goBackHandler();
      return
    }
    incrementInterceptCount();
    const taskInfo = getBackInterceptTask();
    interceptingTag = true
    if (lifecycleInterceptCount >= MAX_LIFECYCLE_INTERCEPT_COUNT) {
      interceptBackKeyEvent(false);
    }
    openBackInterceptPopup({
      ...(taskInfo || {}),
      taskInfo,
      onConfirm: () => {
        interceptingTag = false
        taskInfo && taskActionHandler(taskInfo);
      },
      onClose: () => {
        interceptingTag = false
        goBackHandler();
      }
    });
  };

  /**
   * 处理返回按钮点击事件
   */
  const leftIconClickHandler = (): void => {
    handleBackIntercept();
  };

  /**
   * 处理物理返回键事件
   */
  const handleBackKeyEvent = (): void => {
    handleBackIntercept();
  };

  /**
   * 初始化返回拦截
   * 如果需要拦截，会设置拦截状态并添加事件监听
   */
  const initBackIntercept = (): void => {
    const needBackIntercept = shouldSetIntercept();
    if (needBackIntercept) {
      setBackIntercept(true)
      document.addEventListener('onBackKeyEvent', handleBackKeyEvent);
    } else {
      cleanupBackIntercept()
    }
  };

  /**
   * 清理返回拦截
   * 移除事件监听并关闭拦截状态
   */
  const cleanupBackIntercept = (): void => {
    interceptingTag = false
    setBackIntercept(false);
    document.removeEventListener('onBackKeyEvent', handleBackKeyEvent);
  };

  /**
   * 返回拦截的effect hook
   * 在组件挂载时初始化拦截，在组件卸载时清理拦截
   * 当store状态变化时会重新初始化拦截
   */
  const useInterceptEffect = () => {
    const state = store.getState();
    useEffect(() => {
      initBackIntercept();
      return () => {
        cleanupBackIntercept();
      };
    }, [state]);
  };

  return {
    leftIconClickHandler,
    useInterceptEffect
  };
} 