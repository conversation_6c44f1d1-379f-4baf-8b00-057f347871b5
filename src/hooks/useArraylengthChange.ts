import { useEffect, useRef, useState } from 'rax';

function useArrayLengthChanged(arr) {
  const prevArrLength = useRef(arr.length);

  const [lengthChanged, setLengthChanged] = useState(false);

  useEffect(() => {
    if (prevArrLength.current !== arr.length) {
      setLengthChanged(true);
    } else {
      setLengthChanged(false);
    }

    // 更新前一个数组长度为当前数组长度
    prevArrLength.current = arr.length;
  }, [arr]);

  return lengthChanged;
}

export default useArrayLengthChanged;
