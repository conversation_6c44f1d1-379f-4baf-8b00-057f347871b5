import { createElement, Component } from 'rax';
import { IStoreDispatch, IStoreState } from '@/store';
import { connect } from 'rax-redux';
import View from 'rax-view';
import Text from 'rax-text';

import './index.scss';

class SubScreen extends Component<Props> {
  componentDidMount() {
  }

  render() {
    return <View>
      <View><Text>这里是次屏组件</Text></View>
      <View><Text>这里是次屏组件</Text></View>
      <View><Text>这里是次屏组件</Text></View>
    </View>;
  }
}


const mapState = (state: IStoreState) => ({
});

const mapDispatch = (dispatch: IStoreDispatch) => ({
  dispatch,
});

interface Props extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {}

export default connect(mapState, mapDispatch)(SubScreen);
