import {createElement, Component} from 'rax';
import CommonModal, {ModalProps} from '../modal_common';
import fact from '@/lib/fact';
import Image from '@/components/image';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import closeIcon from '../assets/<EMAIL>';
import {IAd} from "@/utils/huichuan";
import {dispatch} from "@/store/index";
import Text from 'rax-text';
import './index.scss';

export interface IProps extends ModalProps {
  adInfo: IAd;
}

export default class ModalBrandAd extends CommonModal<IProps> {
  static defaultProps: Partial<IProps> = {
    wrapperClass: 'modal-ad-wrap'
  };

  componentDidMount() {
    fact.exposure('brand_adpop_show', {
      c: 'brand_adpop',
      d: 'show',
      adid: this.props.adInfo.ad_id,
    });
    dispatch.ad.exposure('popBrandAd');
  }

  renderCloseBtn() {
    return null
  }

  renderTopOrnament() {
    return null;
  }

  renderContent() {
    return <Image
      style={{
        width: '750rpx', height: '874rpx',
      }}
      onClick={this.onClick}
      source={(this.props.adInfo.ad_content.img_1 || '').replace('http://', 'https://')}
    />;
  }

  renderBottomOrnament() {
    return <>
      <Text className="ad-icon">广告</Text>
      <Image
        style={{
          width: 100, height: 100,
        }}
        onClick={this.onCancel}
        source={closeIcon}
      />
    </>;
  }

  onClick = () => {
    fact.click('brand_ad_modal_click', {
      c: 'brand_ad',
      d: 'modal',
      adid: this.props.adInfo.ad_id,
    });
    dispatch.ad.click('popBrandAd');
    modal.close(this.props.id);
  }
  onCancel = () => {
    fact.click('brand_ad_modal_close_click', {
      c: 'brand_ad',
      d: 'modal',
      adid: this.props.adInfo.ad_id,
    });
    let result = false;
    if (this.props.onCancel) {
      result = this.props.onCancel();
    }
    if (!result) {
      modal.close(this.props.id);
    }
  }

}
