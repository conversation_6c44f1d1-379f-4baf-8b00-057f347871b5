const DEFAULT_INTERVAL_HOURS = 2;    // 默认间隔小时数
const DEFAULT_MAX_TIMES_PER_DAY = 3; // 默认每日最大次数

export function shouldShowPopup(modalId, intervalHours = DEFAULT_INTERVAL_HOURS, maxTimesPerDay = DEFAULT_MAX_TIMES_PER_DAY) {
  const CONTROL_KEY = `${modalId}_popup_control`; // 存储历史记录的键
  // 获取历史记录
  const history = localStorage.getItem(CONTROL_KEY);
  if (!history) {
    // 如果没有历史记录，直接显示弹窗
    localStorage.setItem(CONTROL_KEY, JSON.stringify([new Date().getTime()]));
    return true;
  }

  let historyList = JSON.parse(history);
  const currentTime = new Date().getTime();

  // 1. 检查间隔条件
  const lastTime = historyList[historyList.length - 1];
  const timeDiff = currentTime - lastTime;
  const intervalMs = intervalHours * 60 * 60 * 1000;

  if (timeDiff < intervalMs) {
    // 间隔时间不足，不显示弹窗
    console.log('[modalControl]间隔时间不足');
    return false;
  }

  // 2. 检查每日条件
  const today = new Date(currentTime).setHours(0, 0, 0, 0); // 获取当天 00:00:00 的时间戳
  const todayCount = historyList.filter(time => {
    const timeDate = new Date(time);
    return Number(timeDate) >= today; // 统计当天的弹窗次数
  }).length;
  if (todayCount >= maxTimesPerDay) {
    // 当天次数已用完，不显示弹窗
    console.log('[modalControl]当天展示次数达到上限');
    return false;
  }
  
  // 移除掉今天之前的记录，避免存储过多的数据, 更新今天的记录。
  historyList = historyList.filter(timestamp => timestamp >= today);
  historyList.push(currentTime);
  localStorage.setItem(CONTROL_KEY, JSON.stringify(historyList));

  return true;
}