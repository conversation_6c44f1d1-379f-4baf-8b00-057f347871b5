import { createElement, useEffect } from 'rax';
import View from 'rax-view';
import './index.scss';
import ucapi from '@/utils/ucapi';
import { useSelector } from 'rax-redux';
import { StoreState } from "@/store/index";

import modal from '@ali/weex-rax-components/lib/base_modal';
import { MODAL_ID } from '../index';
import { IEquityGiftItem } from '@/store/models/rightsGift/typings';
import { IDiamondData } from '@/store/models/rightsGift/typings';
import tracker from '@/lib/tracker';
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import { UserState } from '@/store/models/user';
import '@/components/ModalBtn/index.scss';

interface IProps {
  data: IEquityGiftItem
}
const Index = (props: IProps)=> {
  const {data} = props;
  const diamondData: IDiamondData = useSelector((state: StoreState) => state.rightsGift.diamondData);
  const user: UserState = useSelector((state: StoreState) => state.user);

  useEffect(()=> {
    tracker.log({
      category: WPK_CATEGORY_MAP.EXCHANGE_ERROR,
      sampleRate: 1,
      w_succ: 1,
      msg: `风控用户`,
      c1: `${data.planId}`,
      c2: `${data.name}`,
      c3: `${data.classify}`,
      c4: `${user.utdId}`,
      c5: `${user.uid}`,
      bl1: JSON.stringify(data),
      bl2: JSON.stringify(user),
    })
  }, [])

  const handleConfirm = ()=> {
    modal.close(MODAL_ID.EXCHANGE_FAIL);
    ucapi.base.openURL({url: diamondData.csLink});
  }

  const handleClose = () => {
    modal.close(MODAL_ID.EXCHANGE_FAIL);
  }

  return (
    <View className="exchange-fail-modal">
      <View className="right-close" onClick={handleClose} />
      <View className="title">账号异常，兑换失败</View>
      <View className="modal-confirm-btn flex-center" onClick={handleConfirm}>查看原因</View>
    </View>
  )
}

export default Index
