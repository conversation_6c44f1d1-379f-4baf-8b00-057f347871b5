import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import {StoreState} from '@/store';

import './index.scss';
import CommonModal from "@/components/modals/modal_common";
import { connect } from 'rax-redux';
import modal from '../modal';
import TopImg from '../assets/pic_dialog_redpocket.png';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
}
class ModalInviteAward extends CommonModal<IProps> {
  topImg = TopImg;
  static defaultProps = {
    wrapperClass: 'modal-invite-award',
  };

  renderCloseBtn(): Rax.RaxElement | null {
    return null
  }


  onConfirm = () => {
    modal.close(this.props.id);
  }
  getContentStyle() {
    return {
      'position': 'absolute',
      'margin-top': 0,
      'top': '68rpx'
    };
  }

  getOkText() {
    return '开心收下';
  }

  getCancelText() {
    return null;
  }

  renderMain() {
    const { inviteAmount } = this.props
    return <View className="main-content">
      <View className="title">你不在的这段时间里</View>
      <View className="desc">你邀请的好友</View>
      <View className="desc desc-bottom">帮你赚了<Text className="award-amount din-num">{inviteAmount / 100}</Text>元</View>
    </View>
  }
}

const mapState = (state: StoreState) => ({
  inviteAmount: state.invite.inviteAmount
});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalInviteAward);
