.modal-task-search {
  position: relative;
  .modal-content {
    .btn-group {
      margin-top: 14rpx;
      margin-bottom: 20rpx;
      .btn1 {
        box-shadow: 0 15px 15px -5px rgba(184,0,0,0.10);
        border-radius: 54rpx;
      }
    }
    .title-container {
      padding: 0 15%;
      margin: 28rpx auto;
    }
    .title-wrap {
      margin: 28rpx auto;
      .title {
        color: #01255D;
      }
      .din-num {
        color: #F02920;
      }
    }
    .award-task-item-wrap {
      width: 510rpx;
      height: 152rpx;
      background: #fff;
      border-radius: 16rpx;
      box-shadow: 0 5px 20px 0 #DEE5EE;
      padding: 36rpx 26rpx 16rpx 40rpx;
      margin-top: 32rpx;
      .task-title {
        font-family: PingFangSC-Medium;
        font-weight: bold;
        font-size: 28rpx;
        color: #01255D;
        width: 440rpx;
      }
      .task-desc {
        color: #F02920;
        font-size: 24rpx;
        font-weight: 700;
        margin-top: 4rpx;
      }
    }
    .modal-treasure-close {
      position: absolute;
      z-index: 2;
      right: 0;
      top: 0;
      padding: 24rpx;
      width: 48rpx;
      height: 48rpx;
      box-sizing: content-box;
      .close-btn {
        width: 100%;
        height: 100%;
      }
      .close-btn-bg {
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
  }
  .search-words-wrapper {
    width: 450rpx;
    height: 72rpx;
    margin-bottom: 26rpx;
    margin-top: 10rpx;
    background: #EEF2F6;
    border-radius: 48rpx;
    flex-direction: row;
    align-items: center;
    padding: 0 10rpx 0 24rpx;
    .text-wrapper {
      width: 320rpx;
      height: 72rpx;
      overflow: hidden;
      margin-left: 14rpx;
      .search-text {
        line-height: 72rpx;
        font-size: 30rpx;
        color: #405A86;
        letter-spacing: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
