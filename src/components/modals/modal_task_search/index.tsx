import { createElement } from 'rax';
import baseModal from '@ali/weex-rax-components/lib/base_modal';

import './index.scss';
import { StoreState } from '@/store';
import { IWordType, ResCompleteTask, TASK_EVENT_TYPE, TASK_STATUS, TaskInfo } from '@/store/models/task/types';
import { taskActionHandler, isSearchWordsTask } from '@/pages/index/task/help';
import { connect } from 'rax-redux';
import fact from '@/lib/fact';
import CommonModal from '@/components/modals/modal_common';

import { execWithLock } from '@/utils/lock';


import RenderMain from './component/render-main';
import RenderBtnGroup from './component/render-btn-group'

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  prizes: ResCompleteTask['prizes'] | null;
  modalType: 'search-video' | 'search-words';
  scene: string;
}
interface IState {
  wordsIndex: number;
}
class ModalTask extends CommonModal<IProps, IState> {
  static defaultProps = {
    wrapperClass: 'modal-task-search',
  };
  state: IState = {
    wordsIndex: 0,
  };

  componentDidMount() {
    const searchTypeWords = this.getSearchType() === 'word';
    const word = this.props.searchWordsCache[this.state.wordsIndex];
    fact.exposure(searchTypeWords ? 'searchpop_fulihome_show' : 'servideopop_fulihome_show', {
      c: searchTypeWords ? 'searchpop_fulihome' : 'servideopop_fulihome',
      d: 'search',
      xxs: this.isFromXXS() ? 'true' : 'false',
      query: word?.name,
      scene: this.props.scene,
      commercial: word?.type === IWordType.highbiz ? 'true' : 'false',
    });
  }

  getSearchType() {
    const { modalType, searchWordsComplete } = this.props;
    const isSearchWords = modalType === 'search-words' && !searchWordsComplete;
    if (isSearchWords) {
      return 'word'
    }
    return 'video'
  }

  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }
  // 来自信息流正文页
  isFromXXS = () => {
    return this.props?.modalType?.includes('search');
  };

  handleCloseModal = () => {
    baseModal.close(this.props.id);
  };

  finishTask = async () => {
    const { searchWordsComplete, searchVideoComplete, modalType, doingStateWordsTask, doingStateAdTask } = this.props;
    const word = this.props.searchWordsCache[this.state.wordsIndex];
    let recommendTask: TaskInfo | undefined;
    const searchTypeWords = this.getSearchType() === 'word';
    if (searchWordsComplete && searchVideoComplete) {
      this.handleCloseModal();
    } else {
      fact.click(searchTypeWords ? 'search_words_click' : 'servideopop_video_click', {
        c: searchTypeWords ? 'searchpop_fulihome' : 'servideopop_fulihome',
        d: 'go',
        xxs: this.isFromXXS() ? 'true' : 'false',
        query: word?.name,
        scene: this.props.scene,
        commercial: word?.type === IWordType.highbiz ? 'true' : 'false',
      });
      if (modalType === 'search-words') {
        recommendTask = searchWordsComplete ? doingStateAdTask : doingStateWordsTask;
        await execWithLock(
          'finish_task_lock',
          async () => {
            await taskActionHandler(recommendTask as TaskInfo, { word });
          },
          3000,
        );
      } else {
        recommendTask = searchVideoComplete ? doingStateWordsTask : doingStateAdTask;
        await execWithLock(
          'finish_task_lock',
          async () => {
            await taskActionHandler(recommendTask as TaskInfo);
          },
          3000,
        );
      }
    }
    this.handleCloseModal();
  };

  onConfirm = () => {
    this.finishTask();
  };

  getContentStyle() {
    return {
      position: 'absolute',
      'margin-top': 0,
      top: '68rpx',
    };
  }

  getCancelText() {
    return null;
  }

  renderBtnGroup(): JSX.Element | null {
    return <RenderBtnGroup
            onConfirm={this.onConfirm}
            modalType={this.props.modalType}
           />
  }

  changeWords = () => {
    if (this.state.wordsIndex < this.props.searchWordsCache.length - 1) {
      this.setState({
        wordsIndex: this.state.wordsIndex + 1,
      });
    } else {
      this.setState({
        wordsIndex: 0,
      });
    }
  };

  renderMain() {
    const { searchWordsCache, modalType } = this.props;
    const { wordsIndex } = this.state;
    return (
    <RenderMain
      handleCloseModal={this.handleCloseModal}
      changeWords={this.changeWords}
      searchWordsCache={searchWordsCache}
      modalType={modalType}
      wordsIndex={wordsIndex}
    />)
  }
}
const mapState = (state: StoreState) => {
  const taskList = state.task.taskList;
  const doingStateAdTask = taskList.find(task => {
    return (task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD || task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW) && task.state !== TASK_STATUS.TASK_FINISH;
  });
  const doingStateWordsTask = taskList.find(task => {
    return (
      isSearchWordsTask(task.event) &&
      task.state !== TASK_STATUS.TASK_FINISH
    );
  });

  let searchVideoComplete = false;
  const searchVideo = taskList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD || task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW);
  if (!searchVideo) {
    searchVideoComplete = true
  }
  if (searchVideo?.dayTimes) {
    const { progress = 0, target = 1 } = searchVideo!.dayTimes as {progress: number, target: number};
    searchVideoComplete = progress === target;
  }

  let searchWordsComplete = false;
  const searchWords = taskList.find(task => isSearchWordsTask(task.event));
  if (!searchWords) {
    searchWordsComplete = true;
  }
  if (searchWords?.dayTimes) {
    const { progress = 0, target = 1 } = searchWords!.dayTimes as {progress: number, target: number};
    searchWordsComplete = progress === target;
  }
  return {
    doingStateAdTask,
    searchWordsCache: state.task?.searchWordsCache,
    doingStateWordsTask,
    searchVideoComplete,
    searchWordsComplete
  };
};

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalTask);
