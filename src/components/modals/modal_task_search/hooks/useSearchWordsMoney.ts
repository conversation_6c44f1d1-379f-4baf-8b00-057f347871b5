import { useSelector } from 'rax-redux';
import { StoreState } from '@/store';
import { rewardDesc, isSearchWordsTask } from '@/pages/index/task/help';

const useSearchWordsMoney = (): string => {
  const money: string = useSelector((state: StoreState) => {
    const taskList = state.task.taskList;
    const searchWords = taskList.find(task => isSearchWordsTask(task.event));
    if (searchWords) {
      return rewardDesc(searchWords.rewardItems[0]);
    }
    return ''
  })
  return money
}

export default useSearchWordsMoney;
