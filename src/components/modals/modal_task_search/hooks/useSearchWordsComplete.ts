import { useSelector } from 'rax-redux';
import { StoreState } from '@/store';
import { isSearchWordsTask } from "@/pages/index/task/help";

const useSearchWordsComplete = (): boolean => {
  const wordsComplete: boolean = useSelector((state: StoreState) => {
    const taskList = state.task.taskList;
    const searchWords = taskList.find(task => isSearchWordsTask(task.event));
    if (!searchWords?.dayTimes) return false;
    const { progress = 0, target = 1 } = searchWords!.dayTimes as {progress: number, target: number};
    return progress === target
  });
  return wordsComplete;
}

export default useSearchWordsComplete
