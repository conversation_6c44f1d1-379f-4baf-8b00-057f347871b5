import { useSelector } from 'rax-redux';
import { StoreState } from '@/store';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';

const useSearchVideoComplete = (): boolean => {
  const videoComplete: boolean = useSelector((state: StoreState) => {
    const taskList = state.task.taskList;
    const searchVideo = taskList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD || task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW);
    if (!searchVideo?.dayTimes) return false;
    const { progress = 0, target = 1 } = searchVideo!.dayTimes as {progress: number, target: number};
    return progress === target
  });
  return videoComplete;
}

export default useSearchVideoComplete;
