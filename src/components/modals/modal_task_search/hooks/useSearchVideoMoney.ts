import { useSelector } from 'rax-redux';
import { StoreState } from '@/store';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';
import { rewardDesc } from '@/pages/index/task/help';

const useSearchVideoMoney = (): string => {
  const money: string = useSelector((state: StoreState) => {
    const taskList = state.task.taskList;
    const searchVideo = taskList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD || task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW);
    if (searchVideo) {
      return rewardDesc(searchVideo.rewardItems[0]);
    }
    return ''
  })
  return money
}
export default useSearchVideoMoney;
