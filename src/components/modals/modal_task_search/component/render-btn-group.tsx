import { createElement, FC } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';

import useSearchWordsComplete from '../hooks/useSearchWordsComplete';
import useSearchVideoComplete from '../hooks/useSearchVideoComplete';
import useSearchWordsMoney from '../hooks/useSearchWordsMoney';
import useSearchVideoMoney from '../hooks/useSearchVideoMoney';

interface RenderBtnGroupProps {
  onConfirm: () => void;
  modalType: "search-video" | "search-words";
};

const RenderBtnGroup: FC<RenderBtnGroupProps> = ({
  onConfirm,
  modalType
}) => {
  const searchWordsComplete = useSearchWordsComplete();
  const searchVideoComplete = useSearchVideoComplete();
  const searchWordsMoney = useSearchWordsMoney();
  const searchVideoMoney = useSearchVideoMoney();

  const GetOkText = () => {
    if (searchWordsComplete && searchVideoComplete) return '好的'
    if (modalType === 'search-words') {
     return searchWordsComplete ? `看视频再领${ searchVideoMoney }` : `去搜索 领${ searchWordsMoney }`
    } 
    return searchVideoComplete ? `去搜索再领${ searchWordsMoney }` : `看广告 领${ searchVideoMoney }`
  };
 
  return (
    <View className={`btn-group`}> 
      <View className="btn btn1 pure-bg" onClick={onConfirm}>
        <Text className="btn-text btn1-text">{GetOkText()}</Text>
      </View>
    </View>
  )
}

export default RenderBtnGroup