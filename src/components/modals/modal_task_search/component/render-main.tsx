import { createElement, FC } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';

import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';

import SearchWords, {SearchWordsProps} from './search-words';
import useSearchWordsComplete from '../hooks/useSearchWordsComplete';
import useSearchVideoComplete from '../hooks/useSearchVideoComplete';

interface RanderMainProps extends SearchWordsProps {
  handleCloseModal: () => void;
}

const RenderMain: FC<RanderMainProps> = ({
  modalType,
  searchWordsCache,
  wordsIndex,
  changeWords,
  handleCloseModal,
}) => {
  const wordsComplete = useSearchWordsComplete();
  const searchVideoComplete = useSearchVideoComplete();

  return (
    <>
      <View className="close-icon-wrap modal-treasure-close" onClick={handleCloseModal}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>
      <View className="tit-desc" style={{ margin: modalType === 'search-video' ? '40rpx' : '0' }}>
      {
        modalType === 'search-words'
        ?
          ( <>
            <SearchWords
              x-if={!wordsComplete}
              wordsIndex={wordsIndex}
              modalType={modalType}
              searchWordsCache={searchWordsCache}
              changeWords={changeWords}
            />
            <View x-else className="title-container">
              <Text className="title">今日搜索任务已完成明天再来吧</Text>
            </View>                
          </> )
        :
          (<>
            <Text x-if={!searchVideoComplete} className="title">嘿！这里有元宝</Text>
            <View x-else className="title-container">
              <Text className="title">今日视频任务已完成明天再来吧</Text>
            </View>
          </>)
      }
      </View>
    </>
  )
}

export default RenderMain