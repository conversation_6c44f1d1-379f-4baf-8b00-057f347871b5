import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from '@/components/image';

import { IWord } from '@/store/models/task/types'
import SearchIcon from '@/assets/search-icon.png';
import RefreshIcon from '@/pages/index/task/images/<EMAIL>';

export interface SearchWordsProps {
  searchWordsCache: IWord[],
  modalType: "search-video" | "search-words";
  wordsIndex: number;
  changeWords: () => void;
}

const SearchWords = ({
    searchWordsCache, modalType, wordsIndex, changeWords
  }: SearchWordsProps
) => {
  const searchStyle = {
    'margin-top': modalType === 'search-words' ? '40rpx' : '10rpx',
    'margin-bottom': modalType === 'search-words' ? '40rpx' : '26rpx',
  };

  return (
    <View className="search-words-wrapper" style={searchStyle}>
      <Image source={SearchIcon} style={{ width: '32rpx', height: '32rpx' }} />
      <View className="text-wrapper">
        <Text className="search-text">{searchWordsCache[wordsIndex].name}</Text>
      </View>
      <Image onClick={changeWords} source={RefreshIcon} style={{ width: '48rpx', height: '48rpx' }} />
    </View>
  );
}

export default SearchWords
