export const enum MODAL_ID {
  /* 签到弹窗 */
  SIGN_IN = 'SIGN_IN',
  /** 宝箱 */
  TREASURE = 'TREASURE',
  /** 提现 */
  EXCHANGE = 'EXCHANGE',
  /** 退登 */
  LOGOUT = 'LOGOUT',

  /** 领取奖励弹窗 */
  TASK_AWARD = 'TASK_AWARD',

  CLOSE_READ_TASK = 'CLOSE_READ_TASK',


  DOUBLE_AWARD = 'DOUBLE_AWARD',

  WELFARE_CARD = 'WELFARE_CARD',
  WECHAT_SHARE = 'WECHAT_SHARE',

  /** 邀请码填写弹窗 */
  INVITE_CODE = 'INVITE_CODE',
  /** 邀请扫码登录提醒 */
  LOGIN = 'LOGIN',

  INVITE_AWARD = 'INVITE_AWARD',
  /** 汇川品牌广告弹窗 */
  POP_BRAND_AD = 'POP_BRAND_AD',
  /** 商搜承接弹窗 */
  TASK_SEARCH = 'TASK_SEARCH',
  // 新版签到弹窗
  SIGN_IN_NEW = 'SIGN_IN_NEW',
  /** 五福权益领取弹窗 */
  WELFARE_AWARD = 'WELFARE_AWARD',
  // 二次弹窗
  MODAL_SECOND = 'MODAL_SECOND',
  // 公告弹框
  ANNOUNCE_POP = 'ANNOUNCE_POP',
  // 高价值奖励
  HIGH_VALUE_AWARD = 'HIGH_VALUE_AWARD',
  // 高价值任务
  HIGH_VALUE_TASK = 'HIGH_VALUE_TASK',
  // 合规弹窗
  MODAL_HEGUI = 'HEGUI',
  // 返回拦截弹窗
  BACK_INTERCEPT = 'BACK_INTERCEPT',
  // 权益兑换
  RIGHTS_EXCHANGE='RIGHTS_EXCHANGE',
  EXCHANGE_SUCCESS = 'EXCHANGE_SUCCESS', // 权益兑换成功
  EXCHANGE_FAIL = 'EXCHANGE_FAIL', // 权益兑换失败
  TANX_AD = 'TANX_AD', // tanx下单广告弹窗
  RISK_CONTROL = 'RISK_CONTROL' // 风控弹窗
}

export interface ModalProps {
  id: string;
  style?: any;
  onConfirm?: any;
  onCancel?: any;
  okText?: string;
  cancelText?: string;
  title?: string;
  desc?: string;
  amount?: number;
  topImg?: string;
  contentImg?: string;
  wrapperClass?: string;
}

export interface ITreasureExtraParams {
  /** 弹框副标题 */
  dialogSubTitle?: string;
  /** 来源 - 埋点用 */
  pop_source: string;
}


export const TreasureTypeArray = [
  'treasure',
  'iFlow-video',
  'iFlow-words',
  'coin',
  'cash',
  'tanx',
  'equity',
] as const

export type TreasureModalType = (typeof TreasureTypeArray)[number];
