import Modal from '@ali/weex-rax-components/lib/base_modal/modal';
import { createElement, Component } from 'rax';
import View from 'rax-view';
import { MODAL_ID } from '.';
import ModalTreasure from './modal_treasure';
import ModalSignIn from './modal_sign_in';
import ModalDoubleAward from "@/components/modals/modal_double_award";
import ModalWechatShare from "@/components/modals/modal_wechat_share";
import ModalInviteCode from "@/components/modals/modal_invite_code";
import ModalLogin from "@/components/modals/modal_login";
import ModalPopBrandAd from "@/components/modals/modal_pop_brand_ad";
import ModalInviteAward from "@/components/modals/modal_invite_award";
import ModalTaskSearch from "@/components/modals/modal_task_search";
import ModalSignInNew from './modal_sign_in_new';
import ModalFarm from '@/components/modals/modal_farm';
import ModalAnnounce from './modal_announce_pop';
import ModalHighValueAward from './modal_high_value_award';
import ModalHighValueTask from './modal_high_value_task';
import LoginProtocol from './../common/LoginProtocol';
import ModalHegui from '@/components/modals/modal_hegui';
import ModalRightsExchange from '@/components/modals/modal_rights_exchange';
import ModalExchangeSuccess from '@/components/modals/modal_exchange_success';
import ModalExchangeFail from '@/components/modals/modal_exchange_fail';
import ModalBackIntercept from './modal_back_intercept';
import ModalTanxAd from "@/components/modals/modal_tanx_ad";
import ModalRiskControl from './modal_risk_control';
import ModalEntryGuide from './modal_entry_guide';
import { isInsetPage } from '@/utils/url';

const maskStyle = () => {
  const isInset = isInsetPage();
  return {
    backgroundColor: isInsetPage() ?  'rgba(231,235,238,0.80) ': 'rgba(0, 0, 0, 0.6)',
    backdropFilter: isInset ? 'blur(0.5px)' : 'none',
    opacity: 1,
  };
}
class ModalWrap extends Component {
  render() {
    return (
      <View>
        <Modal id={MODAL_ID.SIGN_IN} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalSignIn id={MODAL_ID.SIGN_IN} />
        </Modal>
        <Modal id={MODAL_ID.TREASURE} isMaskCanClose={false} disableEnterAnimation maskStyle={maskStyle()}>
          <ModalTreasure id={MODAL_ID.TREASURE} />
        </Modal>
        <Modal id={MODAL_ID.DOUBLE_AWARD} isMaskCanClose maskStyle={maskStyle()}>
          <ModalDoubleAward />
        </Modal>
        <Modal id={MODAL_ID.WECHAT_SHARE} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalWechatShare />
        </Modal>
        <Modal id={MODAL_ID.INVITE_CODE} isMaskCanClose maskStyle={maskStyle()}>
          <ModalInviteCode />
        </Modal>
        <Modal id={MODAL_ID.LOGIN} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalLogin id={MODAL_ID.LOGIN} />
        </Modal>
        <Modal id={MODAL_ID.POP_BRAND_AD} isMaskCanClose maskStyle={maskStyle()}>
          <ModalPopBrandAd />
        </Modal>
        <Modal id={MODAL_ID.INVITE_AWARD} isMaskCanClose maskStyle={maskStyle()}>
          <ModalInviteAward />
        </Modal>
        <Modal id={MODAL_ID.TASK_SEARCH} isMaskCanClose maskStyle={maskStyle()}>
          <ModalTaskSearch />
        </Modal>
        <Modal id={MODAL_ID.SIGN_IN_NEW} isMaskCanClose maskStyle={maskStyle()}>
          <ModalSignInNew id={MODAL_ID.SIGN_IN_NEW} />
        </Modal>
        <Modal id={MODAL_ID.MODAL_FARM} isMaskCanClose maskStyle={maskStyle()}>
          <ModalFarm />
        </Modal>
        <Modal id={MODAL_ID.ANNOUNCE_POP} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalAnnounce id={MODAL_ID.ANNOUNCE_POP} />
        </Modal>
        <Modal id={MODAL_ID.HIGH_VALUE_AWARD} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalHighValueAward />
        </Modal>
        <Modal id={MODAL_ID.HIGH_VALUE_TASK} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalHighValueTask />
        </Modal>
        <LoginProtocol/>
        <Modal id={MODAL_ID.MODAL_HEGUI} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalHegui />
        </Modal>
        <Modal id={MODAL_ID.RIGHTS_EXCHANGE}  isMaskCanClose={false} maskStyle={maskStyle()}>
           <ModalRightsExchange/>
        </Modal>
        <Modal id={MODAL_ID.EXCHANGE_SUCCESS} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalExchangeSuccess/>
        </Modal>
        <Modal id={MODAL_ID.EXCHANGE_FAIL} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalExchangeFail/>
        </Modal>
        <Modal
          id={MODAL_ID.BACK_INTERCEPT}
          isMaskCanClose={false}
          maskStyle={maskStyle()}
        >
          <ModalBackIntercept />
        </Modal>
        <Modal id={MODAL_ID.TANX_AD} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalTanxAd />
        </Modal>
        <Modal id={MODAL_ID.RISK_CONTROL} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalRiskControl />
        </Modal>
        <Modal id={MODAL_ID.ENTRY_GUIDE} isMaskCanClose={false} maskStyle={maskStyle()}>
          <ModalEntryGuide />
        </Modal>
      </View>
    );
  }
}

export default ModalWrap;
