import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from '@/components/image';
import baseModal from '@ali/weex-rax-components/lib/base_modal';

import './index.scss';
import { StoreState } from '@/store';
import CommonModal, { ModalProps } from '../modal_common';
import { connect } from 'rax-redux';
import {INGOT_ICON } from '@/constants/static_img';
// import fact from '@/lib/fact';

import CloseIcon from '../modal_common/assets/<EMAIL>'
import ArrowIcon from './assets/<EMAIL>'

interface IProps extends ModalProps {
  id: string;
}
type ITotalProps = ReturnType<typeof mapState> & IProps;
class ModalDoubleAward extends CommonModal<ITotalProps> {
  static defaultProps = {
    wrapperClass: 'modal-double-award',
  };

  renderTopOrnament = () => {
    return null
  }

  getContentStyle() {
    return {
      'padding-top': '96rpx',
      'padding-bottom': '80rpx'
    };
  }

  getOkText(): string | undefined {
    return '知道了';
  }

  handleCloseModal = () => {
    baseModal.close(this.props.id);
  }

  renderCloseBtn() {
    return null;
  }


  renderMain() {
    const { awardAmount, doubleAwardAmount  } = this.props.readTaskConf
    return <View className="modal-double-award-main">
      <View className="close-icon-wrap modal-treasure-close" onClick={this.handleCloseModal}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>
      <View className="title">翻倍此任务奖励</View>
      <View className="desc">翻倍状态7天内有效</View>
      <View className="desc">失效后记得来激活哦</View>
      <View className="main">
        <View className="double-desc double-before">
          <View className="title">翻倍前</View>
          <View className="award">
            <Text className="award-desc">每60分钟最高约</Text>
            <View className="amount din-num row j-center align-c">
              <Image source={INGOT_ICON} style={{width: '48rpx', height: '48rpx'}}/>
              {awardAmount}</View>
          </View>
        </View>
        <Image className="arrow" source={ArrowIcon} style={{width: '18rpx', height: '32rpx'}} />
        <View className="double-desc double-after">
          <View className="title">翻倍后</View>
          <View className="award">
            <Text className="award-desc">每60分钟最高约</Text>
            <View className="amount din-num row j-center align-c">
              <Image source={INGOT_ICON} style={{width: '48rpx', height: '48rpx'}}/>{doubleAwardAmount}
            </View>
          </View>
        </View>
      </View>
    </View>
  }
}
const mapState = (state: StoreState) => ({
  readTaskConf: state.app.readTaskConf
});

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalDoubleAward);
