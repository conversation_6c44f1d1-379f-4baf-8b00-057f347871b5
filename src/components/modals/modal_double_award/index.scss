.modal-double-award {
  .modal-treasure-close {
    position: absolute;
    z-index: 2;
    right: 0;
    top: 0;
    padding: 24rpx;
    width: 48rpx;
    height: 48rpx;
    box-sizing: content-box;
    .close-btn {
      width: 100%;
      height: 100%;
    }
    .close-btn-bg {
      background-repeat: no-repeat;
      background-size: contain;
    }
  }
  .title {
    color: #01255D;
    font-size: 48rpx;
    font-weight: 700;
    margin-bottom: 20rpx;
    text-align: center;
  }
  .desc {
    color: #7E93B7;
    font-size: 32rpx;
    font-weight: 700;
    line-height: 44rpx;
    text-align: center;
  }
  .double-desc {
    width: 248rpx;
    height: 240rpx;
    border-radius: 32rpx;
    padding: 24rpx 16rpx 16rpx;
  }
  .main {
    flex-direction: row;
    align-items: center;
    margin-top: 40rpx;
    margin-bottom: 32rpx;
  }
  .double-before {
    background-image: linear-gradient(270deg, #E0E5E9 0%, #F1F5F7 100%);
    .title {
      font-size: 32rpx;
      color: #405A86;
      text-align: center;
      margin-bottom: 20rpx;
    }
    .award {
      width: 216rpx;
      height: 136rpx;
      background-color: rgba(255, 255, 255, 0.5);
      padding: 22rpx 0;
      border-radius: 24rpx;
      .award-desc {
        font-size: 24rpx;
        color: #7E93B7;
        text-align: center;
        margin-bottom: 12rpx;
      }
      .amount {
        color: #000;
        font-size: 48rpx;
        text-align: center;
        img {
          margin-right: 4rpx;
          margin-left: -8rpx;
        }
      }
    }
  }
  .arrow {
    margin: 0 6rpx;
  }
  .double-after {
    background-image: linear-gradient(270deg, #F01208 0%, #FF6B64 98%);
    .title {
      font-size: 32rpx;
      color: #FFF;
      text-align: center;
      margin-bottom: 20rpx;
    }
    .award {
      width: 216rpx;
      height: 136rpx;
      background: rgba(255, 255, 255, 0.1);
      padding: 22rpx 20rpx;
      border-radius: 24rpx;
      .award-desc {
        font-size: 24rpx;
        color: rgba(255,255,255,0.75);
        text-align: center;
        margin-bottom: 12rpx;
      }
      .amount {
        color: #FFE16C;
        font-size: 48rpx;
        text-align: center;
        img {
          margin-right: 4rpx;
          margin-left: -8rpx;
        }
      }
    }
  }
}
