import cz from 'classnames';
import './index.scss';
import { Carousel } from '@/components/Carousel';
import stat from '@/lib/stat';
import {createElement, memo, useCallback, useMemo, useRef, useState } from 'rax';
import modal from '../modal';
import { MODAL_ID } from '..';
import View from 'rax-view';
import Image from '@/components/image';
import { useTracker } from './useTracker';
import { PRIZE_CODE, TaskInfo } from '@/store/models/task/types';


interface IProps {
  onClose?: () => void;
  onConfirm?: () => void;
  imgs?: { img: string }[];
  title?: string;
  leftIcon?: string;
  mainName?: string;
  subName?: string;
  taskInfo: TaskInfo & {
    autoPlay?: number;
    duration?: number;
  }
}

const ModalBackIntercept = (props: IProps) => {
  const {
    onClose,
    onConfirm,
    mainName,
    subName,
    imgs = [],
    title = '先别走! 还有肥料等你领!',
    leftIcon = 'https://gw.alicdn.com/imgextra/i4/O1CN01CSVVlS24jcbfwjNwk_!!6000000007427-2-tps-750-654.png',
    taskInfo,
  } = props;
  const [isLeaving, setIsLeaving] = useState(false);
  const closeTypeRef = useRef<'close' | 'confirm' | ''>('');
  const {
    btnName = '',
    id = ''
  } = taskInfo as TaskInfo
  const { trackClick, trackClose } = useTracker(taskInfo);

  const handleClose = useCallback(() => {
    closeTypeRef.current = 'close';
    trackClick('close');
    setIsLeaving(true);
  }, [trackClick]);

  const handleConfirm = useCallback(() => {
    closeTypeRef.current = 'confirm';
    trackClick('function');
    setIsLeaving(true);
  }, [trackClick]);

  const handleAnimationEnd = useCallback(() => {
    if (!isLeaving) return;

    const type = closeTypeRef.current;
    trackClose(type)
    stat.click('back_intercept_click', {
      c: 'modal',
      d: type,
      // task_id: id,
    });
    modal.close(MODAL_ID.BACK_INTERCEPT);

    if (type === 'close') {
      onClose?.();
    } else if (type === 'confirm') {
      onConfirm?.();
    }
  }, [isLeaving, id, trackClose, onClose, onConfirm]);

  const modalClassName = useMemo(
    () =>
      cz('modal-back-intercept', {
        leave: isLeaving,
      }),
    [isLeaving],
  );
  const getTaskAward = (taskInfo) => taskInfo?.rewardItems[0]?.amount || 0
  const taskAmount = useMemo(() => {
    const rawAmount = getTaskAward(taskInfo);
    const amountType = taskInfo?.rewardItems[0]?.mark;
    if (rawAmount > 0) {
      const amount = amountType === PRIZE_CODE.CASH ? (rawAmount / 100).toFixed(2) : rawAmount;
      return `${mainName}，得${amount}${amountType === PRIZE_CODE.CASH ? '元' : '元宝'}`;
    }
    return '';
  }, [taskInfo, mainName]);

  const closeButtonProps = useMemo(
    () => ({
      className: 'intercept-close',
      source: 'https://gw.alicdn.com/imgextra/i4/O1CN01ePmdAL1RB1fv8Z1Qs_!!6000000002072-2-tps-32-32.png',
      onClick: handleClose,
    }),
    [handleClose],
  );

  const buttonProps = useMemo(
    () => ({
      text: btnName || '去完成',
      onClick: handleConfirm,
    }),
    [btnName, handleConfirm],
  );
  const carouselProps = useMemo(() => ({
    className: 'preview-img',
    imgs,
    autoplay: !!(+(taskInfo.autoPlay ?? 1)),
    interval: taskInfo.duration || 3000,
  }), [imgs, taskInfo]);

  return (
    <View className="modal-back-intercept-wrapper">
      <View className={modalClassName} onAnimationEnd={handleAnimationEnd}>
        <Image className="intercept-logo" source={leftIcon || 'https://gw.alicdn.com/imgextra/i4/O1CN01CSVVlS24jcbfwjNwk_!!6000000007427-2-tps-750-654.png'} alt="" />
        <Image {...closeButtonProps} />
        <View className="intercept-header">
          <View className="intercept-header-title">{title || '先别走! 还有肥料等你领!'}</View>
        </View>
        <View className="slideshow">
          <Carousel {...carouselProps} />
        </View>
        <View className="intercept-content">
          <View className="intercept-title">{taskAmount}</View>
          {!!subName && <View className="intercept-desc">{subName}</View>}
        </View>
        <View className="intercept-btn" onClick={buttonProps.onClick}>
          <View className="btn-text">{buttonProps.text}</View>
        </View>
      </View>
    </View>
  );
};

ModalBackIntercept.displayName = 'ModalBackIntercept';

export default memo(ModalBackIntercept);
