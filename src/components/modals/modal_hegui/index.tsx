import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import {dispatch, StoreState} from '@/store';
import { openURL } from "@/pages/index/task/help";

import './index.scss';
import CommonModal from "@/components/modals/modal_common";
import CloseIcon from "@/components/modals/modal_common/assets/<EMAIL>";
import { connect } from 'rax-redux';
import modal from '../modal';
import {isIOS} from "@/lib/universal-ua";
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import { getEvSub } from '@/lib/qs';
import { isInsetPage } from '@/utils/url';
import config from '@/config';

const goBack = () => {
  if (isIOS) {
    // 小说分场内嵌模式下
    if (getEvSub() === 'novel_fuli' && isInsetPage()) {
      openURL(config.link.novelIosHomepage);
      return;
    }
    jsbridge.exec('biz.openPageUrl', { url: 'ext:back' });
  } else {
     // 小说分场内嵌模式下
     if (getEvSub() === 'novel_fuli' && isInsetPage()) {
      openURL(config.link.novelAndroidHomepage);
      return;
    }
    jsbridge.exec('biz.closeWebPage');
  }
};

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  desc: string;
}
class ModalHegui extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal-hegui',
  };

  renderCloseBtn(): Rax.RaxElement | null {
    return null
  }

  renderTopOrnament = () => {
    return null
  }

  handleCloseModal = () => {
    modal.close(this.props.id);
  };

  handleToPolicy = () => {
    localStorage.setItem('to_hegui_privacy', '1')
    openURL('https://terms.alicdn.com/legal-agreement/terms/privacy/20230410163448721/20230410163448721.html')
  }

  onConfirm = () => {
    localStorage.setItem('hegui_agree', '1')
    localStorage.removeItem('to_hegui_privacy')
    dispatch.app.init(true)
    this.handleCloseModal();
  }

  onCancel = () => {
    goBack()
  }

  getContentStyle() {
    return {
      'position': 'absolute',
      'margin-top': 0,
      'top': '180rpx'
    };
  }

  getOkText() {
    return '同意并进入';
  }

  getCancelText() {
    return '退出';
  }

  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    const cancelText = this.getCancelText();
    const len = [okText, cancelText].filter((i) => !!i).length;
    return (
      <View className={`btn-group len-${len}`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
        {cancelText && (
          <View className="btn btn2 pure-bg" onClick={this.onCancel}>
            <Text className="btn-text btn2-text">{this.getCancelText()}</Text>
          </View>
        )}
      </View>
    );
  }


  renderMain() {
    return ([
      <View className="close-icon-wrap modal-login-close" onClick={this.onCancel}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="modal-hegui-content">
        <View className="title">温馨提示</View>
        <View className="content-desc">
          UC送现金活动需要获取您的UC账号、任务信息、领取的元宝信息等，用于福利猪的现金兑现等服务，请您在使用前阅读并同意<Text className="policy" onClick={this.handleToPolicy}>《UC送现金活动隐私政策》</Text>
        </View>
      </View>,
      ]
    );
  }
}

const mapState = (state: StoreState) => ({});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalHegui);
