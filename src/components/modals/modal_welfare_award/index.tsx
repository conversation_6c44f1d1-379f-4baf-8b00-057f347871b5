import { createElement } from 'rax';
import Image from '@/components/image';
import View from "rax-view";
import Text from 'rax-text';
import { connect } from 'rax-redux';
import tracker from '@/lib/tracker';

import fact from '@/lib/fact';
import { StoreDispatch, StoreState} from "@/store/index";
import { WelfareAward } from '@/store/models/task/types';
import CommonModal, { ModalProps } from '../modal_common';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import { ALIPAY_WUFU_WELFARES }  from '@/constants/index';

import CheckIcon from './assets/<EMAIL>';
import NovelIcon from './assets/<EMAIL>';
import WangpanIcon from './assets/<EMAIL>';
import HongbaoIcon from './assets/<EMAIL>';
import LogoIcon from './assets/<EMAIL>';
import CloseIcon from './assets/<EMAIL>';

import './index.scss';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';

export type IProps = ModalProps & ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {
  type: 'new_welfares' | 'cash_hongbao';
  amount?: number;
}

class ModalWelfareAward extends CommonModal<IProps> {
  componentDidMount() {
    fact.exposure('wufuuclite-fulihexiaotanchuang-expo', {
      c: '2023wfuclitehexiaochengjie',
      d: 'fulitanchuang',
    });
    if ((window as any).__start_open_welfare_award__ && this.props.type === 'new_welfares') {
      tracker.log({
        category: WPK_CATEGORY_MAP.MODAL_WELFARE_AWARD,
        wl_avgv1: Date.now() - (window as any).__start_open_welfare_award__,
      });
      (window as any).__start_open_welfare_award__ = 0;
    }
  }

  getIcon = (e: WelfareAward) => {
    let ret;
    const item = ALIPAY_WUFU_WELFARES.find(o => o.code === e.code);
    if (item) {
      if (item.type === 'wangpan') {
        ret = WangpanIcon;
      } else if (item.type === 'novel') {
        ret = NovelIcon;
      }
    }
    return ret;
  }

  onClose = () => {
    fact.click('get_btn_click', {
      c: 'wufu_welfare_modal',
      d: 'click',
    });
    modal.close(this.props.id)
  }

  renderCloseBtn() {
    return null
  }

  renderTopOrnament() {
    return null
  }

  getAmountDesc = () => {
    const { amount = 0} = this.props;
    let m;
    if (amount % 100 === 0) {
      m = 0
    } else if (amount % 10 === 0) {
      m = 1
    } else {
      m = 2;
    }
    return (amount/ 100).toFixed(m);
  }

  renderContent() {
    const { welfareList , type } = this.props;
    return (
      <View className="welfare_award_content">
          <Image className="welfare_award__logo" source={LogoIcon} />
          {
            type === 'new_welfares' ?
            <Text className="modal_title">您的五福权益待使用</Text>
            :
            <>
            <Text className="modal_title">五福现金红包已放入</Text>
            <Text className="modal_title">您的支付宝账号</Text>
            </>
          }
          {
            type==='new_welfares' ?
            <View className="welfare_list">
              {welfareList.map(e => {
                return (
                  <View key={e.code} className="welfare_award">
                    <Image className="welfare_award__icon" source={this.getIcon(e)} />
                    <Text className="welfare_award__title">{e.name}</Text>
                    <Image className="welfare_award__check_icon" source={CheckIcon}/>
                  </View>
                )
              })}
            </View>:
            <View className="welfare_list">
                <View className="welfare_award">
                  <Image className="welfare_award__icon" source={HongbaoIcon} />
                  <Text className="welfare_award__title">五福现金红包{this.getAmountDesc()}元</Text>
                  <Image className="welfare_award__check_icon" source={CheckIcon}/>
                </View>
            </View>
          }
          {
            type === 'cash_hongbao' &&
            <Text className="cash_hongbao_hints">支付宝账号为您现在登录的账号</Text>
          }
          <View className="get_btn" onClick={this.onClose}>
            <Text className="get_btn__desc">我知道了</Text>
          </View>
          <Image className="close_btn" source={CloseIcon} onClick={this.onClose}/>
      </View>
    )
  }
}

const mapState = (state: StoreState) => ({
  welfareList: state.app.newWelfares,
});

const mapDispatch = (dispatch: StoreDispatch) => ({
});

export default connect(mapState, mapDispatch)(ModalWelfareAward);
