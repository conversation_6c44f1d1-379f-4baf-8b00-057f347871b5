import { createElement, useEffect } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import './index.scss';

import modal from '@ali/weex-rax-components/lib/base_modal';
import { MODAL_ID } from '../index';
import { IEquityGiftItem } from '@/store/models/rightsGift/typings';
import { dispatch } from '@/store';
import { execWithLock } from '@/utils/lock';
import toast from '@/lib/universal-toast/component/toast';
import { exchangeReasonMap, exchangeFailFactText } from '@/store/models/rightsGift/typings';
import fact from '@/lib/fact';
import '@/components/ModalBtn/index.scss';
import { refreshUserGiftState } from '@/store/models/rightsGift';

interface IProps {
  data: IEquityGiftItem;
  resourceCode: string;
}

const Index = (props: IProps) => {
  const { data, resourceCode } = props;
  console.log('data:', data);

  useEffect(() => {
    fact.exposure('exchange_pop_exposure', {
      c: 'list',
      d: 'pop',
      exchange_name: data?.name || '',
      planid: data?.planId,
      count: data.assetConsumeList?.[0]?.amount || '',
      code: 'coin',
      resource_location: resourceCode,
      planId: data.planId,
    });
  }, []);

  const handleConfirm = () => {
    fact.click('exchange_pop__click', {
      c: 'list',
      d: 'pop',
      exchange_name: data?.name || '',
      count: data.assetConsumeList?.[0]?.amount || '',
      code: 'coin',
      resource_location: resourceCode,
      click_position: '确定',
      planId: data.planId,
    });
    modal.close(MODAL_ID.RIGHTS_EXCHANGE);
    execWithLock('equityExchange', async (unlock) => {
      const res = await dispatch.rightsGift.exchangeEquityGift(data);
      // 兑换成功
      if (res?.code === 'OK') {
        modal.open(MODAL_ID.EXCHANGE_SUCCESS, { data, resourceCode });
        refreshUserGiftState('exchangeEquityGift')
      } else if (res?.code === 'UNUSUAL_USER') {
        // 账号异常
        modal.open(MODAL_ID.EXCHANGE_FAIL, { data, resourceCode  });
      } else {
        // 兑换失败
        toast.show(exchangeReasonMap[res?.code] || exchangeReasonMap['DEFAULT']);
      }
      fact.event('exchange_pop__click', {
        c: 'list',
        d: 'pop',
        exchange_name: data?.name || '',
        count: data.assetConsumeList?.[0]?.amount || '',
        code: 'coin',
        resource_location: resourceCode,
        click_position: '确定',
        if_success: res?.code === 'OK' ? '兑换成功' : '兑换失败',
        fail_reason: exchangeFailFactText[res?.code] || exchangeFailFactText['DEFAULT'],
        planId: data.planId,
      });
      unlock();
    });
  };

  const handleCancel = () => {
    fact.click('exchange_pop__click', {
      c: 'list',
      d: 'pop',
      exchange_name: data?.name || '',
      count: data.assetConsumeList?.[0]?.amount || '',
      code: 'coin',
      resource_location: resourceCode,
      click_position: '取消',
      planId: data.planId,
    });
    modal.close(MODAL_ID.RIGHTS_EXCHANGE);
  };

  return (
    <View className="equity-exchange-modal">
      <View className="title">权益兑换</View>
      <View className="content-text">
        确定消耗<Text className="coin-num">{data?.assetConsumeList?.[0]?.amount }元宝</Text>
        兑换<Text className="award-name">{data?.name}</Text>吗？
      </View>
      <View className="modal-confirm-btn" onClick={handleConfirm}>
        确认
      </View>
      <View className="cancel-btn flex-center" onClick={handleCancel}>
        取消
      </View>
    </View>
  );
};

export default Index;
