import {createElement} from 'rax';
import CommonModal, {ModalProps} from '../modal_common';
import fact from '@/lib/fact';
import Image from '@/components/image';
import Text from 'rax-text';
import { connect } from 'rax-redux';
import View from "rax-view";
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import ucapi from "@/utils/ucapi";
import { StoreDispatch, StoreState} from "@/store/index";
import tracker from '@/lib/tracker';
import DialogBg from './assets/<EMAIL>';
import TitleIcon from './assets/<EMAIL>';
import BtnBg from './assets/<EMAIL>';
import CloseIcon from './assets/<EMAIL>';
import Avatar from './assets/<EMAIL>';

import { LoginStatus } from "@/store/models/user";

import './index.scss';
import { WPK_CATEGORY_MAP } from '@/constants/tracker_category';

export interface IProps extends ModalProps {
  isLogin: boolean;
  hasBindedAlipay: boolean;
  bindInfo: {
    third_nickname: string;
  },
  number: number;
  bindAlipay: () => any;
  loginStatus: number;
}

class ModalWelfareCard extends CommonModal<IProps> {
  static defaultProps: Partial<IProps> = {
    wrapperClass: 'modal-welfare-card'
  };

  componentDidMount() {
    let zfb_state = ''
    const { isLogin, hasBindedAlipay } = this.props
    if (isLogin) {
      zfb_state = hasBindedAlipay ? 'login_bind' : 'login_notbind'
    } else {
      zfb_state = 'notlogin'
    }
    fact.exposure('fuka_window', {
      c: 'fuka',
      d: 'window',
      zfb_state
    });
    if ((window as any).__start_open_welfare_card__) {
      tracker.log({
        category: WPK_CATEGORY_MAP.MODAL_WELFARE_CARD,
        wl_avgv1: Date.now() - (window as any).__start_open_welfare_card__,
      });
      (window as any).__start_open_welfare_card__ = 0;
    }
  }

  handleBtnClick = async () => {
    fact.click('bangdingbtn_click', {
      c: 'bangdingbtn',
      d: 'click',
      is_login: this.props.isLogin ? 1 :0,
      type: this.props.hasBindedAlipay ? '1' : '0'
    })
    if (this.props.loginStatus !== LoginStatus.login) {
      // 退登 或 游客
      localStorage.setItem('toAlipayLogin', '1')
      return ucapi.account.openLoginWindow('alipay')
    }

    if (this.props.hasBindedAlipay) {
      // 已登录且绑定支付宝
      modal.close(this.props.id)
    } else {
      // 已登录，但未绑定支付宝
      localStorage.setItem('toAlipayLogin', '1')
      this.props.bindAlipay();
    }
  }

  renderCloseBtn() {
    return null
  }

  renderTopOrnament() {
    return null
  }

  renderContent() {
    const { bindInfo, hasBindedAlipay, isLogin, number } = this.props
    return (
      <View className="welfare-card-content">
        <View className="welfare-card-dialog-top">
          恭喜你获得{number > 0 ? ` ${number} 张福卡` : '福卡'}
        </View>
        <View className="modal-main">
          <Image className="modal-bg" onClick={this.onClick} source={DialogBg}/>
          <Image className="title-icon" source={TitleIcon} />
          <Text className="modal-title">UC 祝你兔年吉祥</Text>
          <View x-if={hasBindedAlipay} className="alipay-info">
            <Text className="text">福卡已存入支付宝账号</Text>
            <View className="alipay-user">
              <Image source={Avatar} className="alipay-user-icon" style={{width: '36rpx', height: '36rpx'}}/>
              <Text className="alipay-user-name">{bindInfo?.third_nickname || ''}</Text>
            </View>
          </View>
          <View x-else className="alipay-info">
            <Text className="text">{ isLogin ? '福卡已存入UC账号' : '随机福卡已到账'}</Text>
            <Text className="text">需绑定支付宝才能使用</Text>
          </View>
          <View className="get-btn-desc" onClick={this.handleBtnClick}>
            {hasBindedAlipay ? '开心收下' : '绑定支付宝'}
          </View>
          <Image className="get-btn" source={BtnBg} />
          <Image className="close-btn" source={CloseIcon} onClick={this.onCancel} />
        </View>
      </View>
    );
  }

  renderBottomOrnament() {
    return null
  }

  onClick = () => {
    fact.click('brand_ad_modal_click', {
      c: 'brand_ad',
      d: 'modal',
    });
    modal.close(this.props.id);
  }
  onCancel = () => {
    fact.click('brand_ad_modal_close_click', {
      c: 'brand_ad',
      d: 'modal',
    });
    let result = false;
    if (this.props.onCancel) {
      result = this.props.onCancel();
    }
    if (!result) {
      modal.close(this.props.id);
    }
  }
}

const mapState = (state: StoreState) => ({
  isLogin: state.user.isLogin,
  hasBindedAlipay: state.user.hasBindedAlipay,
  bindInfo: state.user.bindInfo,
  loginStatus: state.user.status
});

const mapDispatch = (dispatch: StoreDispatch) => ({
  bindAlipay: dispatch.user.bindAlipay,
});

export default connect(mapState, mapDispatch)(ModalWelfareCard);
