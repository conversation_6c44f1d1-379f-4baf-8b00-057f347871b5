import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { StoreState } from '@/store';
import { getTaskRewardItem } from '@/pages/index/task/help';

import './index.scss';
import CommonModal from '@/components/modals/modal_common';
import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';
import { connect } from 'rax-redux';
import modal from '../modal';
import fact from '@/lib/fact';
import Image from '@/components/image';
import IMG from './image.png';
import { TaskInfo } from '@/store/models/task/types';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  title: string;
  desc: string;
  btnText: string;
  image: string;
  task: TaskInfo
  entryList: string[]
}

class ModalEntryGuide extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal_entry_guide',
  };
  getTask() {
    return this.props?.task
  }
  componentDidMount() {
    fact.exposure('guidepop_expo', {
      c: 'entrytask',
      d: 'guide',
      task_id: this.props?.task?.id,
      award_amount: this.props?.task?.rewardItems[0]?.amount || ''
    });
  }
  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }
  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  handleCloseModal = () => {
    modal.close(this.props.id);
  };

  onConfirm = () => {
    fact.click('guidepop_click', {
      c: 'entrytask',
      d: 'guide',
      click_position: '1',
    });
    modal.close(this.props.id);
    if (this.props?.entryList) {
      jsbridge.exec('biz.backToHomepage');
    }
  };

  onCancel = () => {
    fact.click('guidepop_click', {
      c: 'entrytask',
      d: 'guide',
      click_position: '2',
    });
    modal.close(this.props.id);
  };

  getContentStyle() {
    return {
      position: 'absolute',
      'margin-top': 0,
      top: '50%',
      transform: 'translateY(-50%)'
    };
  }

  getOkText() {
    const task = this.props?.task
    const taskAward = getTaskRewardItem(task)
    const btnText = this.props?.btnText || ''
    return btnText ? `${btnText}${taskAward.name}` : `去访问领${taskAward.name}`;
  }
  getDesc() {
    const desc = this.props?.desc
    return desc || '每日从首页可进入领元宝';
  }
  getTitle() {
    const title = this.props?.title
    return title || '从这里可以找到我';
  }
  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    return (
      <View className={`btn-group len-1`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
      </View>
    );
  }

  renderMain() {
    const descText = this.getDesc();
    const title = this.getTitle();
    return [
      <View className="close-icon-wrap modal-login-close" onClick={this.onCancel}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="modal_entry_guide-content">
        <View className="title">{title}</View>
        {descText && <View className="desc">{descText}</View>}
        <Image className="content-img" source={this.props?.image || IMG}></Image>
      </View>,
    ];
  }
}

const mapState = (state: StoreState) => ({});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalEntryGuide);
