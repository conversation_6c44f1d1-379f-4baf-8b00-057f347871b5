import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { StoreState } from '@/store';
import { openURL } from '@/pages/index/task/help';

import './index.scss';
import CommonModal from '@/components/modals/modal_common';
import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';
import { connect } from 'rax-redux';
import modal from '../modal';
import { isIOS } from '@/lib/universal-ua';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import { getEvSub, getParam } from '@/lib/qs';
import { isInsetPage } from '@/utils/url';
import fact from '@/lib/fact';
import config from '@/config';
import Image from '@/components/image';
import IMG from './image.png';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  title: string;
  desc: string;
  btnText: string;
  image: string;
}
class ModalEntryGuide extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal_entry_guide',
  };

  componentDidMount() {
    fact.exposure('', {
      c: '',
      d: '',
    });
  }

  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }
  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  handleCloseModal = () => {
    modal.close(this.props.id);
  };

  onConfirm = () => {
    fact.click('entry_guide_confirm', {
      c: 'entry_guide',
      d: 'pop',
      click_position: 'confirm',
    });
    modal.close(this.props.id);
  };

  onCancel = () => {
    fact.click('entry_guide_cancel', {
      c: 'entry_guide',
      d: 'pop',
      click_position: 'cancel',
    });
    modal.close(this.props.id);
  };

  getContentStyle() {
    return {
      position: 'absolute',
      'margin-top': 0,
      top: '180rpx',
    };
  }

  getOkText() {
    const btnText = this.props?.btnText
    return btnText || '去访问领元宝';
  }
  getDesc() {
    const desc = this.props?.desc
    return desc || '每日从首页可进入领元宝';
  }
  getTitle() {
    const title = this.props?.title
    return title || '从这里可以找到我';
  }
  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    return (
      <View className={`btn-group len-1`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
      </View>
    );
  }

  renderMain() {
    const descText = this.getDesc();
    const title = this.getTitle();
    return [
      <View className="close-icon-wrap modal-login-close" onClick={this.onCancel}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="modal_entry_guide-content">
        <View className="title">{title}</View>
        {descText && <View className="desc">{descText}</View>}
        <Image className="content-img" source={this.props?.image || IMG}></Image>
      </View>,
    ];
  }
}

const mapState = (state: StoreState) => ({});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalEntryGuide);
