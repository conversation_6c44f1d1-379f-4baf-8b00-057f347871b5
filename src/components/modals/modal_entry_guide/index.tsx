import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { StoreState } from '@/store';
import { openURL } from '@/pages/index/task/help';

import './index.scss';
import CommonModal from '@/components/modals/modal_common';
import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';
import { connect } from 'rax-redux';
import modal from '../modal';
import { isIOS } from '@/lib/universal-ua';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import { getEvSub, getParam } from '@/lib/qs';
import { isInsetPage } from '@/utils/url';
import fact from '@/lib/fact';
import config from '@/config';
import Image from '@/components/image';
import IMG from './image.png';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  riskRule: string;
}
class ModalEntryGuide extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal_entry_guide',
  };

  componentDidMount() {
    fact.exposure('', {
      c: 'risk_pop',
      d: 'pop',
    });
  }

  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }
  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  handleCloseModal = () => {
    modal.close(this.props.id);
  };

  onConfirm = () => {
    fact.click('', {
      c: 'risk_pop',
      d: 'pop',
      click_position: 2,
    });
  };

  onCancel = () => {
    fact.click('', {
      c: 'risk_pop',
      d: 'pop',
      click_position: 3,
    });
  };

  getContentStyle() {
    return {
      position: 'absolute',
      'margin-top': 0,
      top: '180rpx',
    };
  }

  getOkText() {
    return '去访问领元宝';
  }
  getDesc() {
    return '每日从首页可进入领元宝';
  }
  getTitle() {
    return '从这里可以找到我';
  }
  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    return (
      <View className={`btn-group len-1`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
      </View>
    );
  }

  renderMain() {
    const descText = this.getDesc();
    const title = this.getTitle() || '从这里可以找到我';
    return [
      <View className="close-icon-wrap modal-login-close" onClick={this.onCancel}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="modal_entry_guide-content">
        <View className="title">{title}</View>
        {descText && <View className="desc">{descText}</View>}
        <Image className="content-img" source={IMG}></Image>
      </View>,
    ];
  }
}

const mapState = (state: StoreState) => ({
  riskRule: state.app.riskRule,
});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalEntryGuide);
