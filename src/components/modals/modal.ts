import fact from '@/lib/fact';
import {ResCompleteTask, RewardItem, TaskInfo} from '@/store/models/task/types';
import logoutCheck from '@/utils/logoutCheck';
import baseModal from '@ali/weex-rax-components/lib/base_modal';
import { MODAL_ID, ITreasureExtraParams, TreasureModalType } from './index';
import modalTopImg from './assets/pic_dialog_normal.png';
import TreasureImg from './assets/pic_dialog_chest.png';
import PocketImg from './assets/pic_dialog_redpocket.png'
import event from '@/utils/event';
import { ISlotAd } from "@/utils/huichuan";
import { IAnnouncePopData } from '@/store/models/cms/typings';

export default {
  openSignIn: async (signAward?, isVoluntarily = false) => {
    baseModal.open(MODAL_ID.SIGN_IN, {
      signAward,
      isVoluntarily
    });
    // fact.exposure('signpop_show', { c: 'signpop', d: 'show' });
  },
  openTreasure: async (prizes: ResCompleteTask['prizes'] | null, type: TreasureModalType, finishedTask?: TaskInfo, excludeAdPlay = false, showTask = true, extraParams?: ITreasureExtraParams) => {
    const imgMap = {
      treasure: TreasureImg,
      coin: modalTopImg,
      cash: PocketImg,
      'iFlow-video': modalTopImg,
      'iFlow-words': modalTopImg,
    }
    if (type === 'treasure') {
      fact.exposure('boxpop_show', { c: 'boxpop', d: 'show' });
    }
    baseModal.open(MODAL_ID.TREASURE, {
      topImg: imgMap[type || 'coin'],
      prizes,
      modalType: type,
      finishedTask,
      excludeAdPlay,
      showTask,
      extraParams,
    });
  },
  openSearchModal: async (type: 'search-video' | 'search-words', scene = 'visit') => {
    baseModal.open(MODAL_ID.TASK_SEARCH, {
      topImg: modalTopImg,
      prizes: null,
      modalType: type,
      scene
    });
  },
  closeLogout: async () => {
    baseModal.close(MODAL_ID.LOGOUT);
  },
  close: (id) => {
    baseModal.close(id);
    event.emit('modalClose');
  },
  openDoubleAward: () => {
    baseModal.open(MODAL_ID.DOUBLE_AWARD)
  },
  openInviteCode: (code?: string) => {
    baseModal.open(MODAL_ID.INVITE_CODE, {
      code,
    });
  },
  openLogin: (desc: string) => {
    event.emit('modalOpen');
    baseModal.open(MODAL_ID.LOGIN, {
      desc,
    });
  },
  // 去芭芭农场弹窗
  openFarmModal: (popConfig) => {
    baseModal.open(MODAL_ID.MODAL_FARM, {
      popConfig,
    });
  },
  popBrandAd: (slotInfo: ISlotAd) => {
    if (slotInfo.ad?.length && slotInfo.ad[0].ad_content.img_1) {
      baseModal.open(MODAL_ID.POP_BRAND_AD, {
        style: {
          width: 750,
          alignItems: 'center',
        },
        adInfo: slotInfo.ad[0]
      });
    }
  },
  openSignInNew: (rewardItem: RewardItem | null, isVoluntarily: boolean) => {
    if (logoutCheck()) {
      return;
    }
    baseModal.open(MODAL_ID.SIGN_IN_NEW, {
      rewardItem,
      isVoluntarily
    });
  },
  // 打开公告弹窗
  openAnnouncePop: (data: IAnnouncePopData) => {
    baseModal.open(MODAL_ID.ANNOUNCE_POP, {data});
  },
  // 打开高价值奖励弹窗
  openHighValueAward: (data: RewardItem & { taskName?: string; taskId: number; isLogin: boolean}) => {
    baseModal.open(MODAL_ID.HIGH_VALUE_AWARD,{data});
  },
  // 打开高价值任务弹窗
  openHighValueTask: () => {
    baseModal.open(MODAL_ID.HIGH_VALUE_TASK);
  },
  // 合规弹窗
  openHeguiModal: async () => {
    baseModal.open(MODAL_ID.MODAL_HEGUI);
  },
  openTanxAd: async (params) => {
    baseModal.open(MODAL_ID.TANX_AD, params);
  },
  // 风控弹窗
  openRiskControl: async (params) => {
    baseModal.open(MODAL_ID.RISK_CONTROL, params);
  },
  // 返回拦截弹窗
  openBackIntercept: (params?: {
    onClose?: () => void;
    onConfirm?: () => void;
    title?: string;
    desc?: string;
    btnText?: string;
    taskId?: string;
    position?: 'center' | 'bottom';
  }) => {
    baseModal.open(MODAL_ID.BACK_INTERCEPT, {
      ...params,
      disableEnterAnimation: true,
      showBodyLeaveAnimation: false,
    });
  }
};
