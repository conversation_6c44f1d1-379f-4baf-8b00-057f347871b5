import { createElement } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import modal from '@/components/modals/modal';
// import ucapi from "@ali/weex-toolkit/lib/ucapi";
import ucapi from '@/utils/ucapi'
import './index.scss';
import { StoreState } from '@/store';
import CommonModal, { ModalProps } from '../modal_common';
import { connect } from 'rax-redux';
import { isIOS  } from '@/lib/universal-ua';
import fact from '@/lib/fact';

import CloseIcon from '../modal_common/assets/<EMAIL>'
import ShareCase from './assets/<EMAIL>';
import Step1 from './assets/<EMAIL>';
import Step2 from './assets/<EMAIL>';
import WechatShare from '@/assets/<EMAIL>';
import Toast from "@/lib/universal-toast";
interface IProps extends ModalProps {
  id: string;
}
type ITotalProps = ReturnType<typeof mapState> & IProps;
class ModalWechatShare extends CommonModal<ITotalProps> {
  static defaultProps = {
    wrapperClass: 'modal-wechat-share',
  };

  renderTopOrnament = () => {
    return null
  }

  getContentStyle() {
    return {
      'padding-top': '96rpx',
      'padding-bottom': '80rpx'
    };
  }

  handleCloseModal = () => {
    modal.close(this.props.id);
  }

  renderCloseBtn() {
    return null;
  }

  handleToWechat = async () => {
    fact.click('invite_wx_click', {
      c: 'task',
      d: 'wx2'
    })
    const url = isIOS ? 'weixin://' : 'com.tencent.mm'
    const startRes = await ucapi.biz.startApp(url);
    if (startRes?.result?.toString() !== 'true') {
      Toast.show('请先安装微信哦');
    }
  }

  renderBtnGroup () {
    return <View className="share-btn" onClick={this.handleToWechat}>
      <Image source={WechatShare} style={{width: '72rpx', height: '72rpx'}} />
      去微信粘贴给好友
    </View>
  }

  renderMain() {
    return <View className="modal-double-award-main">
      <View className="close-icon-wrap modal-treasure-close" onClick={this.handleCloseModal}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>
      <View className="title">你的口令已复制</View>
      <View className="share-step">
        <Image source={Step1} style={{width: '34rpx', height: '34rpx'}} />
        <View className="share-step-desc">
          选择微信好友或微信群
        </View>
      </View>
      <View className="share-step">
        <Image source={Step2} style={{width: '34rpx', height: '34rpx'}} />
        <View className="share-step-desc">
          粘贴发送给好友
        </View>
      </View>
      <View className="main">
        <Image source={ShareCase} style={{width: '450rpx', height: '240rpx'}}/>
      </View>
    </View>
  }
}
const mapState = (state: StoreState) => ({
});

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalWechatShare);
