import Image from '@/components/image';
import { createElement } from 'rax';

export interface IProps {
  onClick: () => void;
  style?: Rax.CSSProperties;
}
const BtnClose: Rax.FC<IProps> = ({ onClick, style }) => {
  return (
    <Image
      className="btn-close"
      style={{ width: '64rpx', height: '64rpx', ...style }}
      onClick={onClick}
      source={'https://img.alicdn.com/imgextra/i2/O1CN01K8oDh31jmj0YCdLAB_!!6000000004591-2-tps-96-96.png'}
    />
  );
};
export default BtnClose;
