import { createElement, useEffect } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import './index.scss';
import TitleImg from './images/title-img.png';
import CloseIcon from './../assets/modal-close.png';
import ColouredRibbonIconImg from './images/coloured-ribbon-Icon.png';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import { convertCentsToYuan } from './../modal_high_value_task/index';
import stat from '@/lib/stat';
import { StoreState, store } from '@/store';
import { connect } from 'rax-redux';
import HighValueAwardImg from './images/high_value_award-bg.png';
import logoutCheck from '@/utils/logoutCheck';
import { MODAL_ID } from '../modal_common';
import { openURL } from "@/pages/index/task/help";
import { addParams } from '@/utils/url';

// 高价值奖励弹窗
const Index = (props) => {
  const { id, highValueTask, data} = props;
  const {icon, name, mark, amount, taskName, randomAmount, taskId, isLogin } = data
  const user = store?.getState()?.user;
  const app = store?.getState()?.app;

  // 点击按钮
  const confirm = () => {
    stat.click('reward_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      click_area: 'withdrawal',
      task_id: taskId,
      taskName: taskName,
      isLogin: user?.isLogin,
    });
    if (logoutCheck()) {
      modal.removeCacheModal(MODAL_ID.HIGH_VALUE_AWARD);
      modal.removeCacheModal(MODAL_ID.HIGH_VALUE_TASK);
      modal.close(id);
      return;
    }
    // 如果是红包奖励 跳转到提现页面
    if (isLogin && mark === 'cash') {
      const link = addParams(app?.withdrawLink, {
        tabName: 'REDPACKET',
      })
      openURL(link);
    }
    modal.close(id);
  };
  // 关闭
  const handleClose = () => {
    stat.click('reward_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      click_area: 'close',
      task_id: taskId,
      taskName: taskName,
      isLogin: user?.isLogin,
    });
    modal.close(id);
  };

  useEffect(()=> {
    stat.exposure('reward_exposure', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
    });
  }, [])
  return (
    <View>
      <View
        style={{ 
          backgroundImage: `url(${
            highValueTask?.resourceConfig?.dialogStyle?.successTopBgImg
              ? highValueTask?.resourceConfig?.dialogStyle?.successTopBgImg
              : HighValueAwardImg
          })`,
        }}
        className="modal-high-value-award"
      >
        <View className="container-bg" />
        <Image className="title-img" source={TitleImg} alt="" />
        <Image className="coloured-ribbon" source={ColouredRibbonIconImg} alt="" />
        <View className="task-name">{taskName}</View>
        <Image className="award-icon" source={icon} alt="" />
        <View className="award-name">
          {!isLogin && randomAmount ? '最高' : '' }
          {mark === 'cash' ? convertCentsToYuan(Number(amount)) : amount}
          {name}
        </View>
        <View onClick={confirm} className="confirm">
          {!isLogin ? '登录并': ''}{highValueTask?.resourceConfig?.dialogSuccessBtnTextMap?.[mark || 'point']}
        </View>
        <View className="close-btn" onClick={handleClose}>
          <Image source={CloseIcon} className="close-icon" alt="" />
        </View>
      </View>
    </View>
  );
};

const mapState = (state: StoreState) => ({
  highValueTask: state.highValueTask,
});

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(Index);
