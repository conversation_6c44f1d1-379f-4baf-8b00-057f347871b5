.modal-high-value-award {
  position: relative;
  box-sizing: border-box;
  width: 590rpx;
  height: 680rpx;
  border-radius: 44rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url(./images/high_value_award-bg.png);
  uc-perf-stat-ignore: image;
  background-repeat: no-repeat;
  background-size: 100% 180rpx;
  box-sizing: border-box;
  img {
    uc-perf-stat-ignore: image;
  }
  .container-bg {
    position: absolute;
    top: 176rpx;
    width: 100%;
    height: 504rpx;
    background-color: #fff;
    z-index: -1;
    border-bottom-left-radius: 40rpx;
    border-bottom-right-radius: 40rpx;
  }
  .title-img {
    position: absolute;
    top: -23rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 369rpx;
    height: 69rpx;
    background-size: cover;
    z-index: 1;
  }
  .coloured-ribbon {
    position: absolute;
    top: -153rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 716rpx;
    height: 226rpx;
    background-size: cover;
  }
  .task-name {
    margin-top: 65rpx;
    margin-bottom: 17rpx;
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #364047;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    line-height: 45rpx;
  }
  .award-icon {
    width: 300rpx;
    height: 300rpx;
    background-size: cover;
  }
  .award-name {
    margin-top: -30rpx;
    width: 283rpx;
    height: 80rpx;
    line-height: 80rpx;
    background: #ffeab8;
    border-radius: 24rpx;
    font-family: PingFangSC-Semibold;
    font-size: 42rpx;
    color: #8b553a;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }
  .confirm {
    margin-top: 38rpx;
    width: 470rpx;
    height: 100rpx;
    background-image: linear-gradient(94deg, #ff8c49 0%, #e92016 100%);
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
    line-height: 100rpx;
  }
  .close-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -112rpx;
    .close-icon {
      width: 100%;
    }
  }
}

