.modal-common {
  display: flex;
  flex-direction: column;
  width: 630rpx;

  .modal-close {
    position: absolute;
    z-index: 2;
    right: 0;
    top: 226rpx;
    // padding-top: 24rpx;
    // padding-right: 24rpx;
    padding: 24rpx;
    width: 48rpx;
    height: 48rpx;
    box-sizing: content-box;

    .close-btn {
      width: 100%;
      height: 100%;
    }
    .close-btn-bg {
      background-repeat: no-repeat;
      background-size: contain;
    }
  }

  .modal-top {
    position: relative;
    z-index: 1;
    width: 630rpx;
    .modal-top-img {
      width: 100%;
      height: 100%;
      uc-perf-stat-ignore: image;
    }

    & + .modal-content {
      margin-top: -182rpx;
    }

    & ~ .modal-close {
      margin-top: -80rpx;
    }
  }

  .modal-top-prize-img{
    position: relative;
    width: 256rpx;
    height: 256rpx;
    z-index: 1;
    .prize-img{
      width: 100%;
    }
  }

  .modal-content {
    position: relative;
    // padding: 70rpx 0;
    background-color: #f9f3e4;
    border-radius: 40rpx;
  }

  .pig-content {
    width: 630rpx;
    min-height: 384rpx;
    align-items: center;
    position: relative;
    background-image: linear-gradient(0deg, #FFFDF9 0%, #F8FBFF 100%);
    // background-image: radial-gradient(50% 100%, rgba(255,246,176,0.38) 50%, rgba(255,246,176,0.00) 100%);
    border-radius: 40rpx;
    padding-top: 182rpx;
    padding-bottom: 56rpx;

    &::before,
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      background-size: contain;
      background-repeat: no-repeat;
    }

    &::before {
      right: 0;
      bottom: 0;
      background-position: 0 100%;
    }
  }

  .tit-desc {
    position: relative;
    z-index: 1;
    align-items: center;
    .title {
      // font-family: PingFangSC-Semibold;
      font-weight: 700;
      font-size: 48rpx;
      line-height: 66rpx;
      color: #01255D;
      letter-spacing: 0;
      text-align: center;
    }

    .desc {
      // font-family: PingFangSC-Semibold;
      font-weight: 700;
      font-size: 32rpx;
      color: rgba(1,37,93,0.50);
      letter-spacing: 0;
      text-align: center;

      &.ingot-desc {
        margin-top: 4rpx;
        font-weight: 800;
        font-size: 64rpx;
        line-height: 90rpx;
        color: #F02920;
        letter-spacing: 0;
        text-align: center;
      }
    }
  }

  .sub-desc {
    // font-family: PingFangSC-Semibold;
    font-weight: 700;
    font-size: 32rpx;
    color: #ffd7b7;
    letter-spacing: 0;
    text-align: center;
  }

  .ft-desc {
    margin-top: 36rpx;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    color: #A38986;
    text-align: center;
  }

  .btn-group {
    position: relative;
    z-index: 1;
    margin-top: 32rpx;
    // padding: 36rpx 64rpx 0;
    display: flex;
    flex-direction: column;

    &.len-2 {
      flex-direction: column;
    }
  }

  .btn {
    min-width: 450rpx;
    max-width: 550rpx;
    padding: 0 20rpx;
    height: 108rpx;
    // border-radius: 108rpx;
    justify-content: center;
    align-items: center;
    background-repeat: no-repeat;

    & + .btn {
      margin-top: 24rpx;
    }
  }

  .btn1 {
    background-image: url('../../../assets/modal_btn_bg.png');
    background-size: cover;
    uc-perf-stat-ignore: image;
    // filter: drop-shadow(0 30rpx 30rpx rgba(184, 0, 0, 0.35));
  }
  .btn1-text {
    color: #fff;
  }

  .btn2 {
    background: #DEE5EE;
    border-radius: 108rpx;
    .btn-text {
      color: rgba(1,37,93,0.75);
    }
  }

  .btn-text {
    font-weight: 700;
    // font-family: PingFang-SC-Heavy;
    font-size: 40rpx;
    letter-spacing: 0;
    text-align: center;
  }

  .modal-common-ft {
    margin-top: 32rpx;
  }
}
