import { createElement, Component } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import NORMAL_CLOSE_ICON from './assets/<EMAIL>'

import './index.scss';

import Image from '@/components/image';

import { ModalProps } from '../index';
export * from '../index';

import IMG_NORMAL_PIG from '../assets/pic_dialog_normal.png';
import fact from '@/lib/fact';

class ModalCommon<P extends ModalProps, S = {}> extends Component<P, S> {
  topImg?: string;
  stat_c?: string;
  modalTopHeight?: string
  /** 子类可重写 */
  logExposure = () => {
    fact.exposureAsync('pop_common_display', {
      c: 'modal_common',
      d: 'display',
      modal_id: this.props.id,
    });
  };

  logConfirmClk = () => {};

  /** 子类可重写 */
  logCancelClk = () => {
    fact.click('pop_cancel_click', {
      c: 'modal_common',
      d: 'click',
      modal_id: this.props.id,
    });
  };

  onConfirm = (e?: any) => {
    this.logConfirmClk();
    let result = false;
    if (this.props.onConfirm) {
      result = this.props.onConfirm();
    }
    if (!result) {
      modal.close(this.props.id);
    }
  };

  onCancel = () => {
    this.logCancelClk();
    let result = false;
    if (this.props.onCancel) {
      result = this.props.onCancel();
    }
    if (!result) {
      modal.close(this.props.id);
    }
  };

  getStyle() {
    const { style } = this.props;
    return style || {};
  }

  getContentStyle() {
    return {};
  }

  /** 关闭按钮的位置，自动包含padding */
  getCloseBtnStyle(): Rax.CSSProperties {
    return {};
  }

  renderTopOrnament(): JSX.Element | null {
    return (
      <View className="modal-top" style={{
        height: this.modalTopHeight || undefined
      }}>
        <Image source={this.topImg || this.props.topImg || IMG_NORMAL_PIG} className="modal-top-img" />
      </View>
    );
  }

  renderBottomOrnament(): JSX.Element | null {
    return null;
  }

  renderMain(): any {
    const { desc, title } = this.props;
    return (
      (title || desc) && (
        <View className="tit-desc">
          {title && <Text className="title">{title}</Text>}
          {desc && <Text className="desc">{desc}</Text>}
        </View>
      )
    );
  }

  getCancelText() {
    const { cancelText } = this.props;
    return cancelText;
  }

  getOkText() {
    const { okText } = this.props;
    return okText;
  }

  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    const cancelText = this.getCancelText();
    const len = [okText, cancelText].filter((i) => !!i).length;
    return (
      <View className={`btn-group len-${len}`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
        {cancelText && (
          <View className="btn btn2 pure-bg" onClick={this.onCancel}>
            <Text className="btn-text btn2-text">{this.getCancelText()}</Text>
          </View>
        )}
      </View>
    );
  }

  renderCloseBtn(): Rax.RaxElement | null {
    return (
      <View className="modal-close" onClick={this.onCancel} style={this.getCloseBtnStyle()}>
        {/* <Image source={ICON_CLOSE} className="close-btn" /> */}
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${NORMAL_CLOSE_ICON})` }} />
      </View>
    );
  }

  renderContent() {
    return (
      <View className="modal-content pig-content" style={this.getContentStyle()}>
        {this.renderMain()}
        {this.renderBtnGroup()}
      </View>
    );
  }

  componentDidMount() {
    this.logExposure();
  }

  render() {
    return (
      <View className={`modal-common ${this.props.wrapperClass || ''}`} style={this.getStyle()}>
        {this.renderTopOrnament()}
        {this.renderContent()}
        {this.renderBottomOrnament()}
        {this.renderCloseBtn()}
      </View>
    );
  }
}

export default ModalCommon;
