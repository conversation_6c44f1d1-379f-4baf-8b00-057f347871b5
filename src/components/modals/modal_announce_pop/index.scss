.modal—announce-comp{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 630rpx;
  min-height: 384rpx;
  background-image: linear-gradient(0deg, #FFFDF9 0%, #F8FBFF 100%);
  border-radius: 40rpx;
  padding: 0 90rpx;

  .announce-title{
    margin-top: 96rpx;
    font-family: PingFangSC-Semibold;
    font-size: 48rpx;
    color: #01255D;
    letter-spacing: 0;
    text-align: center;
    font-weight: 600;
  }

  .announce-content{
    max-height: 400rpx;
    overflow-y: scroll;
    margin-top: 18rpx;
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    color: #7E93B7;
    letter-spacing: 0;
    text-align: justify;
    line-height: 50rpx;
    font-weight: 400;
    // note: 设置最大高度50%，防止内容超出
    max-height: 50vh;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      // note: 隐藏滚动条，因为滚动条就在内容旁边太丑了，做到部分浏览器不显示
      display: none;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }
  }

  .announce-btn{
    margin-top: 50rpx;
    margin-bottom: 80rpx;
    width: 450rpx;
    height: 108rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: PingFangSC-Semibold;
    font-size: 40rpx;
    color: #FFFFFF;
    font-weight: 600;
    background-image: url('../../../assets/modal_btn_bg.png');
    background-size: cover;
    box-shadow: 0 30rpx 30rpx -10rpx rgba(184,0,0,0.10);
    border-radius: 60rpx;
    uc-perf-stat-ignore: image;
  }
}
