import { createElement, Component } from 'rax';
import View from 'rax-view';
import './index.scss';
import { IAnnouncePopData } from '@/store/models/cms/typings';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import fact from '@/lib/fact';

interface IProps {
  id: string;
  data: IAnnouncePopData;
}

class ModalAnnounce extends Component<IProps> {

  componentDidMount(): void {
    console.log('ModalAnnounce', this.props);
    fact.exposure('notice_pop_expo', {
      c: 'welfare',
      d: 'notice',
      content: this.props?.data?.content || '',
    })
  }

  handleClose =() => {
    fact.click('notice_pop_close', {
      c: 'welfare',
      d: 'notice',
      content: this.props?.data?.content || '',
    })
    modal.close(this.props?.id)
  }

  render() {
    const { title = '', content = '' } = this.props?.data
    return (
      <View className="modal—announce-comp">
        <View className="announce-title">{title}</View>
        <View className="announce-content">{content}</View>
        <View className="announce-btn" onClick={this.handleClose} >我知道了</View>
      </View>
    );
  }
}

export default ModalAnnounce;
