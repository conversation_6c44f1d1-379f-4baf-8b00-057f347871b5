import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { StoreState } from '@/store';
import { openURL } from '@/pages/index/task/help';

import './index.scss';
import CommonModal from '@/components/modals/modal_common';
import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';
import { connect } from 'rax-redux';
import modal from '../modal';
import { isIOS } from '@/lib/universal-ua';
import jsbridge from '@ali/weex-toolkit/lib/ucapi';
import { getEvSub, getParam } from '@/lib/qs';
import { isInsetPage } from '@/utils/url';
import fact from '@/lib/fact';
import config from '@/config';

const IOS_NOVEL_HOME_PAGE = config.link.novelIosHomepage;
const ANDROID_NOVEL_HOME_PAGE = config.link.novelAndroidHomepage;

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  riskRule: string;
}
class ModalRiskControl extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal-risk-control',
  };

  componentDidMount() {
    fact.exposure('pig_risk_exposure', {
      c: 'risk_pop',
      d: 'pop',
    });
  }

  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }
  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  goBack = () => {
    // 小说分场内嵌模式下
    if (getEvSub() === 'novel_fuli' && isInsetPage()) {
      openURL(isIOS ? IOS_NOVEL_HOME_PAGE : ANDROID_NOVEL_HOME_PAGE);
      return;
    }

    // 极速版工具栏
    const entry = getParam('entry');
    if (['toolbar', 'toolbar#/'].includes(entry)) {
      jsbridge.exec('biz.backToHomepage');
      return;
    }
    jsbridge.exec('biz.closeWebPage');
  };

  handleCloseModal = () => {
    modal.close(this.props.id);
  };

  handleToLearnMore = () => {
    openURL(this.props.riskRule);
    fact.click('pig_risk_click', {
      c: 'risk_pop',
      d: 'pop',
      click_position: 1,
    });
  };

  onConfirm = () => {
    this.goBack();
    fact.click('pig_risk_click', {
      c: 'risk_pop',
      d: 'pop',
      click_position: 2,
    });
  };

  onCancel = () => {
    this.goBack();
    fact.click('pig_risk_click', {
      c: 'risk_pop',
      d: 'pop',
      click_position: 3,
    });
  };

  getContentStyle() {
    return {
      position: 'absolute',
      'margin-top': 0,
      top: '180rpx',
    };
  }

  getOkText() {
    return '我知道了';
  }

  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    return (
      <View className={`btn-group len-1`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
      </View>
    );
  }

  renderMain() {
    return [
      <View className="close-icon-wrap modal-login-close" onClick={this.onCancel}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="modal-risk-control-content">
        <View className="title">温馨提示</View>
        <View className="content-desc">
          您的账号存在异常行为,无法参与活动
          <Text className="learn" onClick={this.handleToLearnMore}>
            了解详情
          </Text>
        </View>
      </View>,
    ];
  }
}

const mapState = (state: StoreState) => ({
  riskRule: state.app.riskRule,
});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalRiskControl);
