.modal—farm-rendermain {
  display: flex;
  justify-content: center;
  align-items: center;

  .modal—farm-rendermain-text {
    font-family: PingFangSC-Semibold;
    font-size: 48rpx;
    color: #01255d;
    text-align: center;
    font-weight: 600;
    margin: 28rpx 0 50rpx 0;
  }

  .modal—farm-rendermain-btn {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-bottom: 22rpx;
    width: 450rpx;
    height: 108rpx;
    background-image: url(../../../assets/modal_btn_bg.png);
    uc-perf-stat-ignore: image;
    background-size: cover;
    z-index: 99;

    .modal—farm-btn-text {
      font-size: 40rpx;
      font-family: PingFangSC-Semibold;
      color: #ffffff;
      font-weight: 600;
      line-height: 56rpx;
    }

    .gold-color {
      font-size: 40rpx;
      font-family: D-DIN-Bold;
      color: #ffef2f;
      font-weight: 700;
      line-height: 56rpx;
    }

    .modal—farm-rendermain-arrows {
      width: 40rpx;
      height: 40rpx;
      background-size: cover;
      uc-perf-stat-ignore: image;
    }
  }
}

.modal-watering {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  .modal-watering-activity {
    position: relative;
    width: 590rpx;
    img {
      width: 100%;
    }
  }
  .modal-watering-task {
    position: relative;
    width: 590rpx;
    height: 775rpx;
    background-image: url('./images/bg.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    padding-top: 44rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      font-size: 48rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 700;
      line-height: 56rpx;
    }
    .sub-title {
      font-family: PingFangSC-Regular;
      font-size: 32rpx;
      color: #ffffff;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      line-height: 45rpx;
      margin-top: 12rpx;
    }
    .award-content {
      position: relative;
      display: flex;
      justify-content: center;
      width: 380rpx;
      height: 380rpx;
      margin-top: 32rpx;
      overflow: hidden;
    }
    .award-icon {
      width: 100%;
    }
    .award-amount {
      position: absolute;
      bottom: 14rpx;
      background: #ffeab8;
      border-radius: 24rpx;
      height: 80rpx;
      padding: 0 40rpx;
      line-height: 80rpx;
      text-align: center;
      font-family: PingFangSC-Semibold;
      font-size: 42rpx;
      color: #8b553a;
      letter-spacing: 0;
      font-weight: 600;
      left: 50%;
      transform: translateX(-50%);
      white-space: nowrap;
    }
  }
  .confirm-btn {
    position: absolute;
    bottom: 65rpx;
    width: 470rpx;
    height: 100rpx;
    line-height: 100rpx;
    background: #2ac638;
    border-radius: 50rpx;
    font-family: PingFangSC-Semibold;
    font-size: 36rpx;
    color: #ffffff;
    text-align: center;
    font-weight: 700;
    .tip {
      position: absolute;
      top: -22rpx;
      right: 0;
      padding: 0 12rpx;
      width: fit-content;
      height: 44rpx;
      background-image: linear-gradient(90deg, #ff8a4c 0%, #ff422a 100%);
      border: 1rpx solid #ffffff;
      border-radius: 100rpx 100rpx 100rpx 2rpx;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      font-family: PingFangSC-Semibold;
      font-size: 24rpx;
      color: #ffffff;
      letter-spacing: 0;
      font-weight: 600;
      .icon {
        width: 27rpx;
        object-fit: contain;
      }
    }
  }
  .close-img {
    position: absolute;
    top: -84rpx;
    right: 0;
    width: 60rpx;
    height: 60rpx;
    z-index: 1;
  }
}
