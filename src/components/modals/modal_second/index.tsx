import { createElement } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import { StoreState } from '@/store';
import './index.scss';
import CommonModal from '@/components/modals/modal_common';
import { connect } from 'rax-redux';
import fact from '@/lib/fact';
import TopImg from './images/top-img.png';
import RightArrowsImg from './images/right-arrows.png';
import { execWithLock } from '@/utils/lock';
import { taskActionHandler } from '@/pages/index/task/help';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import Text from 'rax-text';
import { EPopType, ISecondModalConfig } from '@/store/models/app/typings';
import { PRIZE_CODE, TaskInfo } from '@/store/models/task/types';
import ICON_CLOSE from '@/components/modals/assets/modal-close.png';
import NORMAL_CLOSE_ICON from '@/components/modals/modal_common/assets/<EMAIL>';
import ucapi from '@/utils/ucapi';
import { INGOT_ICON } from '@/constants/static_img';
interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  desc: string;
  popConfig: ISecondModalConfig & { task: TaskInfo };
}

class ModalFarm extends CommonModal<IProps> {
  topImg = '';
  modalTopHeight = '248rpx';
  // 曝光
  logExposure = () => {
    const { popConfig } = this.props;
    const { task: taskInfo } = popConfig;
    fact.exposureAsync('center_pop', {
      c: 'task',
      d: 'center',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
    });
  };

  // 关闭按钮点击
  logCancelClk = () => {
    const { popConfig } = this.props;
    const { task: taskInfo } = popConfig;
    fact.click('task_pop_click', {
      c: 'task',
      d: 'button',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      click_pop: 0,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
    });
  };
  // 点击按钮去完成任务
  handleConfirm(taskInfo, popConfig?: ISecondModalConfig) {
    const { id } = this.props;
    // const { popType, jumpLink } = popConfig || {};
    // if (popType === EPopType.Act && jumpLink) {
    //   ucapi.base.openURL({ url: jumpLink });
    //   modal.close(id);
    //   return;
    // }
    fact.click('task_pop_click', {
      c: 'task',
      d: 'button',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      click_pop: 1,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
    });
    execWithLock(
      'finish_task_lock',
      async () => {
        console.log('taskActionHandler', taskInfo);
        await taskActionHandler(taskInfo);
        modal.close(id);
      },
      3000,
    );
  }
  // 关闭按钮位置
  getCloseBtnStyle(): Rax.CSSProperties {
    return {
      top: '146rpx',
    };
  }
  renderCloseBtn(): Rax.RaxElement | null {
    const { popType } = this.props?.popConfig || {};
    if (popType === EPopType.Task) {
      return (
        <View className="modal-close" onClick={this.onCancel} style={this.getCloseBtnStyle()}>
          <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${NORMAL_CLOSE_ICON})` }} />
        </View>
      );
    }
    return (
      <View
        className="modal-close"
        onClick={this.onCancel}
        style={{
          top: '-105rpx',
          right: '0',
        }}
      >
        <Image source={ICON_CLOSE} className="close-btn" />
      </View>
    );
  }
  renderBtnGroup() {
    return null;
  }
  renderTopOrnament() {
    const { popType, icon } = this.props?.popConfig || {};
    if (popType === EPopType.Task) {
      return (
        <View
          className="modal-top"
          style={{
            height: this.modalTopHeight || undefined,
          }}
        >
          <Image
            source={icon ? icon : TopImg}
            className="modal-top-img"
            style={{
              objectFit: 'contain',
            }}
          />
        </View>
      );
    }
    return null;
  }
  renderContent() {
    const { popType } = this.props?.popConfig || {};
    if (popType === EPopType.Task) {
      return (
        <View className="modal-content pig-content" style={this.getContentStyle()}>
          {this.renderMain()}
          {this.renderBtnGroup()}
        </View>
      );
    }
    return <>{this.renderMain()}</>;
  }
  renderMain() {
    const { popConfig } = this.props;
    const {
      task: taskInfo,
      btnText,
      popTitle,
      icon = TopImg,
      popImg,
      popType,
      popSubtitle,
      orderTask,
    } = popConfig;
    const getAwardDesc = (task) => {
      if (!rawAmount) return '大额奖励'
      return task && amountType === PRIZE_CODE.CASH
      ? `${(rawAmount / 100).toFixed(2)}元现金`
      : `${rawAmount}元宝`
    }
    const getTaskAward = (taskInfo) => taskInfo?.rewardItems[0]?.amount || 0;
    const rawAmount = getTaskAward(taskInfo);
    const amountType = taskInfo?.rewardItems[0]?.mark;
    const awardDesc = orderTask
      ? '大额奖励'
      : getAwardDesc(taskInfo);

    if (popType === EPopType.Task) {
      return (
        <View className="modal—farm-rendermain">
          <View className="modal—farm-rendermain-text">{popTitle || taskInfo?.name}</View>
          <View className="modal—farm-rendermain-btn" onClick={() => this.handleConfirm(taskInfo)}>
            {btnText ? (
              <Text className="modal—farm-btn-text">{btnText}</Text>
            ) : (
              <>
                <Text className="modal—farm-btn-text">{'做任务可得'}</Text>
                <Text className="gold-color">{taskInfo?.rewardItems[0]?.amount}</Text>
                <Text className="modal—farm-btn-text">元宝</Text>
              </>
            )}
            <Image source={RightArrowsImg} className="modal—farm-rendermain-arrows" />
          </View>
        </View>
      );
    }
    this.topImg = '';
    return (
      <View className="modal-watering">
        {popImg ? (
          <View className="modal-watering-activity" onClick={() => this.handleConfirm(taskInfo, popConfig)}>
            <Image source={popImg} />
          </View>
        ) : (
          <View className="modal-watering-task">
            <View className="title">{popTitle || taskInfo?.name}</View>
            <View className="sub-title">{popSubtitle}</View>
            <View className="award-content">
              <Image className="award-icon" source={icon || ''} />
              <View className="award-amount">{awardDesc}</View>
            </View>
            {orderTask ? (
              <View onClick={() => this.handleConfirm(taskInfo, popConfig)} className="confirm-btn">
                {rawAmount ? (
                  <View className="tip">
                    浏览也可领
                    <Image className="icon" source={INGOT_ICON} alt="元宝" />
                    {rawAmount}
                  </View>
                ) : null}
                {btnText || '去下单领'}
                {getAwardDesc(orderTask)}
              </View>
            ) : (
              <View onClick={() => this.handleConfirm(taskInfo, popConfig)} className="confirm-btn">
                {btnText}
              </View>
            )}
          </View>
        )}
      </View>
    );
  }
}

const mapState = (state: StoreState) => ({
  app: state.app,
});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalFarm);
