import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { dispatch } from '@/store';

import './index.scss';
import CommonModal from "@/components/modals/modal_common";
import CloseIcon from "@/components/modals/modal_common/assets/<EMAIL>";
import { connect } from 'rax-redux';
import modal from '../modal';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  desc: string;
}
class ModalLogin extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal-login',
  };

  renderCloseBtn(): Rax.RaxElement | null {
    return null
  }

  handleCloseModal = () => {
    modal.close(this.props.id);
  };

  onConfirm = () => {
    dispatch.user.login();
    this.handleCloseModal();
  }

  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  getContentStyle() {
    return {
      'position': 'absolute',
      'margin-top': 0,
      'top': '68rpx'
    };
  }

  getOkText() {
    return '好的';
  }

  getCancelText() {
    return null;
  }

  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    const cancelText = this.getCancelText();
    const len = [okText, cancelText].filter((i) => !!i).length;
    return (
      <View className={`btn-group len-${len}`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
        {cancelText && (
          <View className="btn btn2 pure-bg" onClick={this.onCancel}>
            <Text className="btn-text btn2-text">{this.getCancelText()}</Text>
          </View>
        )}
      </View>
    );
  }

  renderMain() {
    return ([
      <View className="close-icon-wrap modal-login-close" onClick={this.handleCloseModal}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="modal-login-title">
        <Text className="title-text">请先登录</Text>
        <Text className="sub-title-text">{this.props.desc || ''}</Text>
      </View>,
      ]
    );
  }
}

const mapState = () => ({});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalLogin);
