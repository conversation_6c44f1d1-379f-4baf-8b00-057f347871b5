.modal-tanx-ad {
  background-image: linear-gradient(0deg, #FFFDF9 0%, #F8FBFF 100%);
  border-radius: 40rpx;
  .pig-content {
    background-image: url("./assets/top-bg.png");
    background-size: 100% 180rpx;
    background-position: top;
    background-repeat: no-repeat;
    background-color: #F8FBFF;
  }
  .top-bg {
    position: absolute;
    top: -40rpx;
    left: -43rpx;
    z-index: 1;
  }
}
.modal-tanx-ad-content {
  .title {
    font-family: PingFangSC-Semibold;
    font-size: 48rpx;
    color: #E6714E;
    letter-spacing: 0;
    font-weight: 700;
    line-height: 67rpx;
    margin-bottom: 16rpx;
    text-align: center;
  }
  .desc {
    font-family: PingFangSC-Regular;
    font-size: 32rpx;
    line-height: 45rpx;
    color: #12161A;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
  }
  .award-container {
    border-radius: 32rpx;
    padding: 24rpx 16rpx 16rpx;
  }
  .award-main {
    flex-direction: row;
    align-items: end;
    margin-top: 58rpx;
    margin-bottom: 32rpx;
    width: 524rpx;
    position: relative;
    justify-content: space-between;
  }
  .award-first {
    background-image: linear-gradient(270deg, #E0E5E9 0%, #F1F5F7 100%);
    width: 216rpx;
    height: 204rpx;
    .award-title {
      font-size: 32rpx;
      color: #405A86;
      text-align: center;
      line-height: 45rpx;
      margin-bottom: 19rpx;
    }
    .award {
      width: 184rpx;
      height: 100rpx;
      background-color: rgba(255, 255, 255, 0.5);
      border-radius: 32rpx;
      box-sizing: border-box;
      align-items: center;
      justify-content: center;
      .award-desc {
        font-size: 24rpx;
        color: #7E93B7;
        text-align: center;
        margin-bottom: 12rpx;
      }
      .amount {
        color: #000;
        font-size: 48rpx;
        text-align: center;
        img {
          margin-right: 4rpx;
          margin-left: -8rpx;
        }
      }
    }
  }
  .arrow {
    margin: 0;
    position: absolute;
    left: 222rpx;
    bottom: 90rpx;
  }
  .award-second {
    background-image: linear-gradient(270deg, #F01208 0%, #FF6B64 100%);
    width: 280rpx;
    height: 230rpx;
    .award-title {
      font-size: 32rpx;
      color: #FFF;
      text-align: center;
      line-height: 50rpx;
      margin-bottom: 21rpx;
    }
    .award {
      width: 248rpx;
      height: 112rpx;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 32rpx;
      box-sizing: border-box;
      justify-content: center;
      align-items: center;
      .award-desc {
        font-size: 24rpx;
        color: rgba(255,255,255,0.75);
        text-align: center;
        margin-bottom: 12rpx;
      }
      .amount {
        color: #FFE16C;
        font-size: 54rpx;
        text-align: center;
        img {
          margin-right: 4rpx;
          margin-left: -8rpx;
        }
      }
    }
  }
}
