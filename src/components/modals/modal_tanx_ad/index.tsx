import { createElement } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import Text from 'rax-text';
import baseModal from '@ali/weex-rax-components/lib/base_modal';
import './index.scss';
import { StoreState } from '@/store';
import CommonModal, { ModalProps } from '../modal_common';
import { connect } from 'rax-redux';
import {TASK_EVENT_TYPE} from "@/store/models/task/types";
import { TaskInfo } from "@/store/models/task/types";
import { finishIncentiveAdTask } from "@/pages/index/task/util";
import fact from '@/lib/fact';
import { IPreloadRes } from "@/pages/index/task/ad_video_browse";

import {INGOT_ICON } from '@/constants/static_img';
import ArrowIcon from './assets/<EMAIL>'
import TopStar from './assets/<EMAIL>';

interface IProps extends ModalProps {
  adTask: TaskInfo; // 激励视频任务
  adTaskPreloadResult: IPreloadRes;
  countdownSeconds: number; // 倒计时s
}
type ITotalProps = ReturnType<typeof mapState> & IProps;

// tanx下单广告提示弹窗
class ModalTanxAd extends CommonModal<ITotalProps> {
  timer;
  state = {
    countDownTime: this.props.countdownSeconds || 5,
  }
  static defaultProps = {
    wrapperClass: 'modal-tanx-ad',
  };
  getAdRewardInfo() {
    const rewardInfo = this.props.adTaskPreloadResult.reward_info_list?.[0] || {};
    return {
      adn_id: rewardInfo?.adn_id || '',
      sid: rewardInfo?.sid || '',
      price: rewardInfo?.price || '',
      pid: rewardInfo?.pid || '',
    }
  }
  componentDidMount() {
    const orderTask = this.props.progressiveOrderTask;
    fact.event('tanx_pop_exposure', {
      task_id: orderTask.id,
      task_name: orderTask.name,
      taskclassify: orderTask.taskClassify,
      groupcode: orderTask.groupCode,
      award_amount: orderTask.rewardItems?.[0].amount,
      task_progress: this.props.adTask.dayTimes?.progress || 0,
      ...this.getAdRewardInfo(),
    })
    this.timer = setInterval(() => {
      if (this.state.countDownTime > 0) {
        this.setState({
          countDownTime: this.state.countDownTime - 1
        });
      } else {
        clearInterval(this.timer);
        this.onConfirm('countdown');
      }
    }, 1000);
  }

  renderTopOrnament = () => {
    return <View className="top-bg">
      <Image source={TopStar} style={{width: '716rpx', height: '226rpx'}} />
    </View>
  }
  
  getContentStyle() {
    return {
      'padding-top': '46rpx',
      'padding-bottom': '80rpx'
    };
  }

  getOkText(): string | undefined {
    return `去领取(${this.state.countDownTime}s)`;
  }
  
  onConfirm = async (click_area: 'pickup' | 'countdown') => {
    const orderTask = this.props.progressiveOrderTask;
    console.log('orderTask:', orderTask);
    fact.event('tanx_pop_click', {
      task_id: orderTask.id,
      task_name: orderTask.name,
      taskclassify: orderTask.taskClassify,
      groupcode: orderTask.groupCode,
      award_amount: orderTask.rewardItems?.[0].amount,
      task_progress: this.props.adTask.dayTimes?.progress || 0,
      click_area,
      ...this.getAdRewardInfo(),
    })
    finishIncentiveAdTask(this.props.adTask);
    baseModal.close(this.props.id);
  }

  renderCloseBtn() {
    return null;
  }


  renderBtnGroup(): JSX.Element | null {
    return (
      <View className={`btn-group len-1`}>
        <View className="btn btn1 pure-bg" onClick={() => this.onConfirm('pickup')}>
          <Text className="btn-text btn1-text">{this.getOkText()}</Text>
        </View>
      </View>
    );
  }

  renderMain() {
    const orderTaskAward = this.props.progressiveOrderTask?.rewardItems?.[0].amount || 0;
    const { adTask } = this.props;
    const adTaskAward = adTask?.rewardItems?.[0].amount || 0;
    return <View className="modal-tanx-ad-content">
      <View className="title">膨胀福利砸中你啦</View>
      <View className="desc">淘宝广告专属福利</View>
      <View className="award-main">
        <View className="award-container award-first">
          <View className="award-title">浏览得</View>
          <View className="award">
            <View className="amount din-num row j-center align-c">
              <Image source={INGOT_ICON} style={{width: '48rpx', height: '48rpx'}}/>
              {adTaskAward}</View>
          </View>
        </View>
        <Image className="arrow" source={ArrowIcon} style={{width: '18rpx', height: '32rpx'}} />
        <View className="award-container award-second">
          <View className="award-title">下单再得</View>
          <View className="award">
            <View className="amount din-num row j-center align-c">
              <Image source={INGOT_ICON} style={{width: '48rpx', height: '48rpx'}}/>{orderTaskAward}
            </View>
          </View>
        </View>
      </View>
    </View>
  }
}
const mapState = (state: StoreState) => {
  const taskList = state.task.taskList;
  const progressiveOrderTask: TaskInfo = taskList.find(task => task.event === TASK_EVENT_TYPE.PROGRESSIVE_INCENTIVE_ORDER);
  return {
    progressiveOrderTask
  }
};

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalTanxAd);
