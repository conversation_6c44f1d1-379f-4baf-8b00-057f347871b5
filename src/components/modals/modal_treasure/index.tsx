import {createElement} from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from '@/components/image';
import baseModal from '@ali/weex-rax-components/lib/base_modal';

import './index.scss';
import { StoreState, dispatch } from '@/store';
import {IPrize, IWordType, ResCompleteTask, TASK_EVENT_TYPE, TASK_STATUS, TaskInfo} from '@/store/models/task/types';
import {
  getPrizeConfig,
  isClickTypeSearchWordsTask,
  isSearchWordsTask,
  taskActionHandler,
  previousNTimesReward,
  checkTaskFinished,
  isIncentiveAdTask,
} from '@/pages/index/task/help';
import {connect} from 'rax-redux';
import fact from '@/lib/fact';
import config from "@/config";
import CommonModal from '@/components/modals/modal_common';
import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';
import {execWithLock} from '@/utils/lock';
import SearchIcon from '@/assets/search-icon.png';
import RefreshIcon from '@/pages/index/task/images/<EMAIL>';
import tracker from "@ali/weex-toolkit/lib/tracker";
import {WPK_CATEGORY_MAP} from "@/constants/tracker_category";
import {isCallAppTask} from '@/store/models/task/helper';
import {dongfengTaskReport} from '@/pages/index/task/help'
import { ifShowAdTask } from '@/pages/index/task/help';
import IMG_NORMAL_PIG from '../assets/pic_dialog_normal.png';
import { ITreasureExtraParams, TreasureModalType } from '@/components/modals/modal_common';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  topImg: string;
  prizes: ResCompleteTask['prizes'] | null;
  modalType: TreasureModalType;
  finishedTask?: TaskInfo;
  excludeAdPlay: boolean;
  // 是否展示套娃任务
  showTask: boolean;
  extraParams: ITreasureExtraParams;
}
interface IState {
  wordsIndex: number;
}
class ModalTask extends CommonModal<IProps, IState> {
  static defaultProps = {
    wrapperClass: 'modal-treasure',
  };
  state: IState = {
    wordsIndex: 0,
  };

  componentDidMount() {
    this.expoFact();
  }
  
  // 资源投放任务曝光
  resourceExposure = async () => {
    const recommendTask = this.getRecommendTask();
    if (recommendTask) {
      await dispatch.highValueTask.resourceExposure({
        taskInfo: recommendTask,
        actionType: 'EXPOSURE',
        code: config.taskNestResourceCode
      });
      if (isIncentiveAdTask(recommendTask)) {
        // 曝光后重新请求资源位，更新任务推荐列表
        dispatch.resource.getResourceAllDate({ firstInit: false, resData: null})
      }
    }
    return
  }

  expoFact = async () => {
    const { pop_source = 'task_list'} = this.props?.extraParams || {};
    const { finishedTask } = this.props;
    const recommendTask = this.getRecommendTask();
    if (recommendTask) {
      dongfengTaskReport(recommendTask ,'expose')
    }
    let msg = `套娃弹窗-${recommendTask?.name || '无推荐任务'}${this.props.excludeAdPlay ? '-无视频填充推荐' : ''}`
    tracker.log({
      category: WPK_CATEGORY_MAP.RECOMMEND_DIALOG_TIMES,
      msg,
      sampleRate: 1,
      w_succ: 1,
      c1: recommendTask? `${recommendTask.id}_${recommendTask.event}` : '',
      c2: `${recommendTask?.dayTimes?.progress || 0}`,
      c3: this.props.modalType,
      c4: finishedTask ? `${finishedTask.id}_${finishedTask.name}` : '',
      bl1: JSON.stringify(this.props.recommendTaskList)
    })

    fact.exposure('task_expo', {
      c: 'taskpop',
      d: 'taskbutton',
      xxs: this.isFromXXS() ? 'true' : 'false',
      task_id: recommendTask?.id || '',
      module: 'pop',
      task_name: recommendTask?.name || '',
      taskclassify: recommendTask?.taskClassify || '',
      groupcode: recommendTask?.groupCode || '',
      award_amount: recommendTask?.rewardItems[0]?.amount || '',
      resource_location: 'matrioska_pop',
      pop_source,
    });
    if (recommendTask && isSearchWordsTask(recommendTask?.event)) {
      fact.exposure('fuli_expo', {
        c: 'searchpop',
        d: 'search',
        xxs: this.isFromXXS() ? 'true' : 'false',
        frequency: recommendTask?.dayTimes?.progress + '',
      });
    }
  };

  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }
  // 来自信息流正文页
  isFromXXS = () => {
    return this.props.modalType.includes('iFlow');
  };

  handleCloseModal = async (isFact = true) => {
    if (isFact) {
      const { pop_source = 'task_list'} = this.props?.extraParams || {};
      const recommendTask = this.getRecommendTask();
      fact.click('close_click', {
        c: 'taskpop',
        d: 'closebuttun',
        xxs: this.isFromXXS() ? 'true' : 'false',
        task_id: recommendTask?.id || '',
        module: 'pop',
        task_name: recommendTask?.name || '',
        taskclassify: recommendTask?.taskClassify || '',
        groupcode: recommendTask?.groupCode || '',
        pop_source,
      });
    }
    this.resourceExposure();
    baseModal.close(this.props.id);
  };

  finishTask = async () => {
    const { pop_source = 'task_list'} = this.props?.extraParams || {};
    const recommendTask = this.getRecommendTask();
    fact.click('task_click', {
      c: 'taskpop',
      d: 'taskbutton',
      xxs: this.isFromXXS() ? 'true' : 'false',
      task_id: recommendTask?.id || '',
      module: 'pop',
      task_name: recommendTask?.name || '',
      taskclassify: recommendTask?.taskClassify || '',
      groupcode: recommendTask?.groupCode || '',
      award_amount: recommendTask?.rewardItems[0]?.amount || '',
      task_progress: recommendTask?.dayTimes?.progress || '',
      resource_location: 'matrioska_pop',
      pop_source,
    });
    if (recommendTask) { 
      dongfengTaskReport(recommendTask , 'click')
    }
    if (recommendTask) {
      if (this.isRecommendSearchTask()) {
        const wordsCache = this.getSearchWords()
        const word = wordsCache[this.state.wordsIndex];
        fact.click('search_words_click', {
          c: 'searchpop',
          d: 'go',
          xxs: this.isFromXXS() ? 'true' : 'false',
          from: recommendTask.id,
          query: word?.name,
          commercial: word?.type === IWordType.highbiz ? 'true' : 'false',
          number: recommendTask?.dayTimes?.progress || 0,
        });
        await execWithLock(
          'finish_task_lock',
          async () => {
            await taskActionHandler(recommendTask, { word });
          },
          3000,
        );
      } else {
        await execWithLock(
          'finish_task_lock',
          async () => {
            await taskActionHandler(recommendTask);
          },
          3000,
        );
      }
    }
    this.handleCloseModal(false);
  };
  // 获取套娃任务
  getNestTask = () => {
    const { recommendTaskList, finishedTask, excludeAdPlay} = this.props;
    let availableTaskList = recommendTaskList;
    // 没有上一次完成的任务，默认取推荐的第一个
    if (!finishedTask) {
      return recommendTaskList?.[0]
    }
    // 无视频填充，不再推荐上次点的任务
    if (excludeAdPlay) {
      availableTaskList = recommendTaskList.filter(task => task.id !== finishedTask?.id)
    }
    const sameTask = availableTaskList.find(task => task.id === finishedTask?.id);
    // 完成了每日次数任务，推荐同样的任务
    if (finishedTask?.taskType === 'everydayTimes' && sameTask) {
      return sameTask
    }
    // 完成了逛app任务，优先继续推荐逛app任务
    if (isCallAppTask(finishedTask)) {
      const targetTask = availableTaskList.find(task => isCallAppTask(task));
      return targetTask || availableTaskList?.[0];
    }
    return availableTaskList?.[0]
  }

  getRecommendTask = () => {
    const { showTask } = this.props;
    // 不展示套娃任务
    if (!showTask) {
      return null;
    }
    return this.getNestTask()
  };

  isRecommendSearchTask = () => {
    const recommendTask = this.getRecommendTask();
    return recommendTask && isSearchWordsTask(recommendTask.event);
  };

  onConfirm = () => {
    execWithLock('click_modal_btn', async () => {
      this.finishTask();
    }, 1200);
  };

  getOkText() {
    const recommendTask = this.getRecommendTask();
    const { modalType, nestTaskConfigList} = this.props;
    // 权益类型
    if (modalType === 'equity') {
      return '开心收下'
    }

    if (!recommendTask) {
      return '好的';
    }
    
    const nestTaskConfig = nestTaskConfigList.find(config => Number(config.taskId) === recommendTask.id);
    
    if (nestTaskConfig) {
      const { mark = 'coin', amount = 0 } = recommendTask.rewardItems?.[0] || {};
      const unit = mark === 'cash' ? '现金' : '元宝';
      const showAmount = mark === 'cash' ? amount / 100 : amount;
      return amount > 0 ?
        <View className="modal-task-btn">{nestTaskConfig.abbreviation}再得<Text className="gold-color">{showAmount}</Text>{unit}</View>
        : <View className="modal-task-btn">{nestTaskConfig.abbreviation}</View>
    }
    
    return recommendTask.name
  }

  // 套娃按钮
  renderBtnGroup(): JSX.Element | null {
    return (
      <View className={`btn-group len-1`}>
        <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
          <View className="btn-text btn1-text">{this.getOkText()}</View>
        </View>
      </View>
    );
  }

  getSearchWords = () => {
    const { searchWordsCache, clickTypeSearchWordsCache } = this.props;
    const recommendTask = this.getRecommendTask()
    if (!recommendTask) return []
    return isClickTypeSearchWordsTask(recommendTask.event) ? clickTypeSearchWordsCache : searchWordsCache
  }

  changeWords = () => {
    fact.click('fuli_click', {
      c: 'searchpop',
      d: 'refresh',
      xxs: this.isFromXXS() ? 'true' : 'false',
    });
    if (this.state.wordsIndex < this.getSearchWords().length - 1) {
      this.setState({
        wordsIndex: this.state.wordsIndex + 1,
      });
    } else {
      this.setState({
        wordsIndex: 0,
      });
    }
  };

  renderSearchWords = () => {
    const { modalType } = this.props;
    const wordsCache = this.getSearchWords()
    const searchStyle = {
      'margin-top': modalType === 'iFlow-words' ? '40rpx' : '10rpx',
      'margin-bottom': modalType === 'iFlow-words' ? '40rpx' : '26rpx',
    };
    const { wordsIndex } = this.state;
    return (
      <View x-if={wordsCache[wordsIndex]} className="search-words-wrapper" style={searchStyle}>
        <Image source={SearchIcon} style={{ width: '32rpx', height: '32rpx' }} />
        <View className="text-wrapper">
          <Text className="search-text">{wordsCache[wordsIndex].name}</Text>
        </View>
        <Image onClick={this.changeWords} source={RefreshIcon} style={{ width: '48rpx', height: '48rpx' }} />
      </View>
    );
  };

  // 覆盖掉继承CommonModal组件的头图
  renderTopOrnament = () => {
    return null;
  };

  // 奖品图内容
  renderImg =()=>{
    const {prizes, modalType, extraParams} = this.props;
    const prizeImg = prizes?.[0]?.rewardItem.icon;
    const showPrizeImg = modalType === 'equity' && prizeImg;

    return (
     <View className="content-wrap row">
      <View x-if={extraParams?.dialogSubTitle} className="sub-title">{extraParams?.dialogSubTitle}</View>
      <View className="prize-img">
        <Image source={showPrizeImg ? prizeImg : this.topImg || this.props.topImg || IMG_NORMAL_PIG} className="img" />
      </View>
     </View>
    );
  }

  renderContent() {
    return (
      <View className="modal-content pig-content" style={this.getContentStyle()}>
        {this.renderMain()}
        {this.renderImg()}
        {this.renderBtnGroup()}
      </View>
    );
  }

  getRewardItem = (prizes: IPrize[] | null) => {
    let rewardItem = prizes?.[0]?.rewardItem || {amount: 0 , mark: ''}
    const coinPrizes = prizes?.filter(prize => prize.rewardItem?.mark?.includes('coin')) || []
    let coinNum = 0
    // 有两份元宝奖励，需要合并
    if (coinPrizes.length > 1) {
      coinPrizes.forEach(item => {
        if (item.rewardItem?.amount) {
          coinNum += item.rewardItem?.amount
        }
      })
      rewardItem = Object.assign({}, rewardItem, {
        amount: coinNum
      })
    }
    return rewardItem
  }

  isNewAdTask = () => {
    const finishedTask = this.props.finishedTask
    return finishedTask?.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD
  }

  getTimes = () => {
    return this.props.taskList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD)?.dayTimes?.progress || 0
  }
  isAwardNode = () => {
    return this.props.prizes && this.props.newAdTaskNodes.includes('' + this.getTimes())
  }

  renderMain() {
    const prizeConf = getPrizeConfig(this.getRewardItem(this.props.prizes));
    const amount = prizeConf.amount
    const { modalType, excludeAdPlay } = this.props;
    return [
      <View className="close-icon-wrap modal-treasure-close" onClick={this.handleCloseModal}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View x-if={this.isNewAdTask() && this.isAwardNode()} className="main-content">
        <View className="sub-title">您已看{this.getTimes()}次视频</View>
        <View className="title-wrap row">
          <Text className="title">累计获得</Text>
          <Text className="title din-num">{previousNTimesReward(this.getTimes(), this.props.newAdTask)}</Text>
          <Text className="title">元宝</Text>
        </View>
        <View className="cur-award row">
          <Text className="title">本次获得</Text>
          <Text className="title din-num">{amount}</Text>
          <Text className="title">元宝</Text>
        </View>
      </View>,
      <View x-else className="tit-desc" style={{ margin: this.props.modalType === 'iFlow-video' ? '40rpx' : '0' }}>
        <View className="title-wrap row" x-if={ modalType === 'equity'}>
          <Text className="title">恭喜获得</Text>
          <Text className="title din-num" style={{'margin-top': '-4rpx'}}>{this.props.prizes?.[0]?.rewardItem?.name}</Text>
          <Text className="title">!</Text>
        </View>
        <View className="title-wrap row" x-if={ amount && modalType !== 'equity'}>
          <Text className="title">恭喜获得</Text>
          <Text className="title din-num">{amount}</Text>
          <Text className="title">{modalType === 'cash' ? '元!':'元宝!'}</Text>
        </View>
        <View x-if={modalType === 'iFlow-video'}>
          <Text className="title">嘿！这里有元宝</Text>
        </View>
        <View x-if={excludeAdPlay}>
          <Text className="title" style={{margin: '20rpx 0', 'font-size': '36rpx'}}>暂无视频填充, 换个任务试试</Text>
        </View>
        { this.isRecommendSearchTask() && this.renderSearchWords()}
      </View>
    ];
  }
}
const mapState = (state: StoreState) => {
  const taskList = state.task.taskList;
  const newAdTask = taskList.find(task => task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD)

  // 过滤激励广告任务加载失败的任务
  const recommendTaskList = state.task.recommendTaskList?.filter((task)=>ifShowAdTask(task));
  let nestTaskList = state.resource?.[config.taskNestResourceCode]?.taskList || [];
  nestTaskList = nestTaskList.filter(task => {
    // 换量任务已完成未领奖时, state=1, 需要过滤掉
    return !checkTaskFinished(task) && task.state !== TASK_STATUS.TASK_COMPLETED && recommendTaskList.some(taskItem => taskItem.id === task.id)
  });
  const nestTaskConfigList = state.resource?.[config.taskNestResourceCode]?.attributes?.recommendTaskPlan || [];

  return {
    now: state.task.now,
    taskList: state.task.taskList,
    recommendTaskList: nestTaskList,
    nestTaskConfigList,
    newAdTask,
    searchWordsCache: state.task?.searchWordsCache,
    clickTypeSearchWordsCache: state.task?.clickTypeSearchWordsCache,
    newAdTaskNodes: state.app?.newAdTaskNodes,
  };
};

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalTask);
