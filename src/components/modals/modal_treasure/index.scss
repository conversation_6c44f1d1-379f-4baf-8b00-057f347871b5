.modal-treasure {
  position: relative;
  .modal-top-prize-img {
    position: absolute !important;
    top: -60rpx !important;
    left: 50%;
    transform: translateX(-50%);
  }
  .pig-content {
    margin-top: 0 !important;
    padding-top: 66rpx !important;
  }
  .modal-task-btn {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    line-height: 56rpx;
    .gold-color {
      font-size: 40rpx;
      font-family: D-DIN-Bold;
      color: #FFEF2F;
      font-weight: 700;
    }
  }
  .modal-content {

    .content-wrap{
      flex-direction: column;
      align-items: center;

      .sub-title{
        font-family: PingFangSC-Semibold;
        font-size: 32rpx;
        color: #7E93B7;
        font-weight: 600;
      }
    }
    .prize-img{
      width: 256rpx;
      height: 256rpx;
      margin-bottom: 36rpx;
      .img{
        width: 100%;
      }
    }
    .btn-group {
      margin-top: 14rpx;
      margin-bottom: 20rpx;
      .btn1 {
        box-shadow: 0 15px 15px -5px rgba(184,0,0,0.10);
        border-radius: 54rpx;
      }
    }
    .main-content {
      width: 100%;
      align-items: center;
      margin-bottom: 30rpx;
      margin-top: 30rpx;
      .sub-title {
        font-size: 32rpx;
        color: #01255D;
        letter-spacing: 0;
        text-align: center;
        font-weight: 700;
        margin-bottom: 12rpx;
      }

      .title-wrap {
        margin: 0rpx auto 20rpx;
        .title {
          color: #01255D;
          font-size: 48rpx;
          letter-spacing: 0;
          text-align: center;
          line-height: 66rpx;
          font-weight: 700;
        }
        .din-num {
          color: #F02920;
          font-size: 49rpx;
          line-height: 62rpx;
        }
      }
      .cur-award {
        font-size: 32rpx;
        color: #7E93B7;
        letter-spacing: 0;
        text-align: center;
        line-height: 44rpx;
        font-weight: 700;
        .din-num {
          color: #F02920;
          font-size: 34rpx;
          line-height: 40rpx;
        }
      }
    }

    .title-wrap {
      margin: 28rpx auto;

      .title {
        color: #01255D;
      }
      .din-num {
        color: #F02920;
      }
    }
    .award-task-item-wrap {
      width: 510rpx;
      height: 152rpx;
      background: #fff;
      border-radius: 16rpx;
      box-shadow: 0 5px 20px 0 #DEE5EE;
      padding: 36rpx 26rpx 16rpx 40rpx;
      margin-top: 32rpx;
      .task-title {
        font-family: PingFangSC-Medium;
        font-weight: bold;
        font-size: 28rpx;
        color: #01255D;
        width: 440rpx;
      }
      .task-desc {
        color: #F02920;
        font-size: 24rpx;
        font-weight: 700;
        margin-top: 4rpx;
      }
    }
    .recommend-ad-content {
      margin-top: 40rpx;
      z-index: 10;
    }
    .modal-treasure-close {
      position: absolute;
      z-index: 2;
      right: 0;
      top: 0;
      padding: 32rpx;
      width: 48rpx;
      height: 48rpx;
      box-sizing: content-box;
      .close-btn {
        width: 100%;
        height: 100%;
      }
      .close-btn-bg {
        background-repeat: no-repeat;
        background-size: contain;
      }
    }
  }
  .search-words-wrapper {
    width: 450rpx;
    height: 72rpx;
    margin-bottom: 26rpx;
    margin-top: 10rpx;
    background: #EEF2F6;
    border-radius: 48rpx;
    flex-direction: row;
    align-items: center;
    padding: 0 10rpx 0 24rpx;
    .text-wrapper {
      width: 320rpx;
      height: 72rpx;
      overflow: hidden;
      margin-left: 14rpx;
      .search-text {
        line-height: 72rpx;
        font-size: 30rpx;
        color: #405A86;
        letter-spacing: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
