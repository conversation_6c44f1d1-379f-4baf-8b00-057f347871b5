import { createElement, Fragment, useEffect } from 'rax';
import './index.scss';
import View from 'rax-view';
import Text from 'rax-view';
import Image from '@/components/image';
import CloseIcon from './../assets/modal-close.png';
import ColouredRibbonImg from './images/coloured-ribbon.png';
import { checkTaskFinished, isHuiChuangAdEffectTask } from '@/pages/index/task/help';
import HighValueBg from './../assets/high-value-bg.png';
import HighBottomBg from './images/high-bottom-bg.png';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import Fact from '@/components/Fact';
import stat from '@/lib/stat';
import { dispatch, StoreState } from '@/store';
import { connect } from 'rax-redux';
import CheckedImg from './images/checked-img.png';
import config from '@/config';

let getRewardType = new Map([
  ['cash', 'cash'],
  ['coin', 'yuanbao'],
]);

export const convertCentsToYuan = (cents: number) => {
  const yuan = cents / 100;
  return yuan % 1 === 0 ? `${yuan.toFixed(0)}` : `${yuan.toFixed(2)}`;
};

export const convertCentsToPoint = (number) => {
  if (number >= 10000) {
    let result = (number / 10000).toString();
    // 去掉多余的小数位
    if (result.indexOf('.') !== -1) {
      result = parseFloat(result).toString();
    }
    return `${result}`;
  }
  return number.toString();
};

// 高价值任务弹窗
const Index = (props) => {
  const { id, highValueTask } = props;
  // 是否有子任务
  const hasPreTaskList = highValueTask?.currentTaskInfo?.preTaskList?.length > 0;
  const reward_type = highValueTask?.currentTaskInfo?.rewardItems?.[0]?.mark;
  // 素材配置
  const resourceConfig = highValueTask?.resourceConfig;
  // 获取确认按钮文本
  const getConfirmText = (task) => {
    if (task?.preTaskList?.length > 0) {
      let resulttask = task?.preTaskList?.filter((item) => {
        return !checkTaskFinished(item);
      });
      return resulttask[0]?.btnName ?? '去完成';
    }
    return task?.btnName || task?.name || '去完成';
  };

  const exposureEvent = ()=> {
    stat.exposure('resource_exposure', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      ...getStep(),
    });

    stat.exposure('task_exposure', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      taskclassify: highValueTask?.currentTaskInfo?.taskClassify,
      groupcode: highValueTask?.currentTaskInfo?.groupCode,
      ...getStep(),
    });
  }
  useEffect(() => {
    const element = document.getElementById('high-value-task-dialog') as Element & {
      $currentObserver?: IntersectionObserver;
    };
    if(!element){
      return;
    }
    const observer = new IntersectionObserver((entries: IntersectionObserverEntry[]) => {
      if (entries.length > 0) {
        const entry = entries[entries.length - 1];
        if (entry) {
          const isInElement = entry.isIntersecting;
          if(isInElement){
          exposureEvent();
          // 弹窗曝光
          dispatch.highValueTask.resourceExposure({
            taskInfo: highValueTask.currentTaskInfo,
            actionType: 'EXPOSURE',
            code: config?.taskResourceCode
          });
          // RTA商业任务曝光
          dispatch.highValueTask.rtaTaskExposure();

          if(isHuiChuangAdEffectTask(highValueTask.currentTaskInfo)){
            dispatch.ad.corpTaskExposure({
              accountId: highValueTask.currentTaskInfo.accountId,
              slotId: highValueTask.currentTaskInfo.accountId.slotId
            });
          }
        }
      }
    }});
    observer.observe(element);
    const ele = element;
    ele.$currentObserver = observer;
  }, []);

  // 获取奖励数文案
  const getAwardText = (info) => {
    let detail = Object.assign({}, info);
    switch (detail.mark) {
      case 'cash':
        return (
          <>
            {detail.randomAmount ? '最高' : '必得'}
            <Text className="amount">{convertCentsToYuan(Number(detail.amount))}</Text>
            {detail?.name}
          </>
        );
      case 'coin':
        return (
          <>
            {detail.randomAmount ? '最高' : '必得'}
            <Text className="amount">{convertCentsToPoint(Number(detail.amount))}</Text>
            {Number(detail.amount) >= 10000 && '万'}
            {detail?.name}
          </>
        );
      default:
        return (
          <>
            {detail.randomAmount ? '最高' : '必得'}
            <Text className="amount">{detail.amount}</Text>
            {detail?.name}
          </>
        );
    }
  };
  // 获取第几部
  const getResidueStep = () => {
    let result = highValueTask?.currentTaskInfo?.preTaskList?.filter((item) => !checkTaskFinished(item));
    return result?.length
  }
  // 获取第几部
  const getStep = () => {
    let factObj = {
      high_value_pop_style: 'ordinary',
      step: '',
    };
    if (hasPreTaskList) {
      factObj.high_value_pop_style = 'step';
      const index = highValueTask?.currentTaskInfo?.preTaskList?.findIndex((item) => !checkTaskFinished(item));
      if (index !== -1) {
        factObj.step = index === 0 ? 'one' : 'two';
      }
    }
    return factObj;
  };
  // 确认按钮
  const confirm = (event) => {
    stat.click('task_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      taskclassify: highValueTask?.currentTaskInfo?.taskClassify,
      groupcode: highValueTask?.currentTaskInfo?.groupCode,
      ...getStep(),
    });
    dispatch.highValueTask.resourceExposure({
      taskInfo: highValueTask?.currentTaskInfo,
      actionType: 'CLICK',
      code: config?.taskResourceCode
    });
    dispatch.highValueTask.dialogConfirmEvent();
    // 阻止事件冒泡
    event.stopPropagation();
  };
  // 取消按钮
  const cancel = (event) => {
    stat.click('resource_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      click_area: 'give_up',
      ...getStep(),
    });
    modal.close(id);
    dispatch.highValueTask.dialogCancelEvent();
    // 阻止事件冒泡
    event.stopPropagation();
  };
  // 关闭
  const handleClose = (event) => {
    stat.click('resource_click', {
      c: 'home',
      d: 'pop',
      resource_location: 'high_value_pop',
      reward_type: getRewardType?.get(reward_type) || reward_type,
      task_id: highValueTask?.currentTaskInfo?.id,
      task_name: highValueTask?.currentTaskInfo?.name,
      click_area: 'close',
      ...getStep(),
    });
    modal.close(id);
    dispatch.highValueTask.dialogCancelEvent();
    // 阻止事件冒泡
    event.stopPropagation();
  };
  return (
    <View>
      <View>
        <View
          style={{
            backgroundImage: `url(${
              resourceConfig?.dialogStyle?.topBgImg ?? HighValueBg
            })`,
          }}
          id="high-value-task-dialog"
          className={`modal-high-value-task ${hasPreTaskList ? '' : 'modal-high-value-task-noSchedule'}`}
        >
          {/* <View className="animation">
            <Image className="animation-circle" source={LightCircle} alt="" />
            <Image className="animation-light" source={LightPng} alt="" />
          </View> */}
          <View
            style={{
              color: resourceConfig?.dialogStyle?.containerBgColor ?? '#fff9f3',
            }}
            className={`container-bg ${hasPreTaskList ? '' : 'container-bg-noSchedule'}`}
          />
          <Image className="award-icon" source={highValueTask?.currentTaskInfo?.rewardItems?.[0]?.icon} alt="" />
          <Image className="coloured-ribbon" source={ColouredRibbonImg} alt="" />
          <View className="title">{highValueTask?.resourceConfig?.dialogTitle}</View>
          <View className="subheading">{getAwardText(highValueTask?.currentTaskInfo?.rewardItems?.[0])}</View>
          <View className="lose-time">今日失效</View>
          {hasPreTaskList && (
            <View className="schedule">
              <View className="task-explain">
                只需完成{getResidueStep()}步即可领取福利
              </View>
              <View className="task-detail">
                <View className="schedule-continer">
                  {highValueTask?.currentTaskInfo?.preTaskList?.map((item, index) => {
                    return (
                      <Fact
                        key={index}
                        c="home"
                        d="pop"
                        expoLogkey="task_exposure"
                        expoExtra={{
                          resource_location: 'high_value_pop',
                          reward_type: getRewardType?.get(highValueTask?.currentTaskInfo?.rewardItems?.[0]?.mark),
                          task_id: item?.id,
                          task_name: item?.name,
                          taskclassify: item?.taskClassify,
                          groupcode: item?.groupCode,
                          ...getStep(),
                        }}
                      >
                        <View className="schedule-item">
                          <View className="schedule-item-left">
                            <View className={`circle ${checkTaskFinished(item) ? 'circle-checked' : ''}`}>
                              {index + 1}
                              {checkTaskFinished(item) && <Image className="checked-img" source={CheckedImg} alt="" />}
                            </View>
                            {highValueTask?.currentTaskInfo?.preTaskList?.length !== index + 1 && (
                              <div className={`line ${checkTaskFinished(item) ? 'line-checked' : ''}`} />
                            )}
                          </View>
                          <View
                            className={`schedule-item-text ${
                              checkTaskFinished(item) ? 'schedule-item-text-checked' : ''
                            }`}
                          >
                            {item.name}
                          </View>
                        </View>
                      </Fact>
                    );
                  })}
                </View>
              </View>
            </View>
          )}
          <View
            style={{
              backgroundImage: `url(${
                resourceConfig?.dialogStyle?.bottomBgImg ?? HighBottomBg
              })`,
            }}
            className="btn-conainer"
          >
            <View onClick={(e)=>confirm(e)} className="confirm">
              {getConfirmText(highValueTask?.currentTaskInfo) || '去完成'}
            </View>
            <View onClick={(e)=>cancel(e)} className="cancel">
              {highValueTask?.resourceConfig?.dialogCancelBtnText}
            </View>
          </View>
          <View className="close-btn" onClick={(e)=> handleClose(e)}>
            <Image source={CloseIcon} className="close-icon" alt="" />
          </View>
        </View>
      </View>
    </View>
  );
};

const mapState = (state: StoreState) => ({
  highValueTask: state.highValueTask,
});

const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(Index);
