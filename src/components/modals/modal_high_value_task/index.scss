.modal-high-value-task {
  position: relative;
  box-sizing: border-box;
  width: 594rpx;
  height: 972rpx;
  border-radius: 44rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-image: url(./../assets/high-value-bg.png);
  uc-perf-stat-ignore: image;
  background-repeat: no-repeat;
  background-size: 100% 316rpx;
  background-position: top;
  box-sizing: border-box;

  img {
    uc-perf-stat-ignore: image;
  }
  .animation {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 100vw;
    height: 85%;
    z-index: -1;
    .animation-circle {
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: circleFadeIn 0.67s forwards;
    }
    .animation-light {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      animation: lightFadeInOpacity 0.67s forwards, lightFadeInRotate 2s linear infinite;
    }
  }
  .container-bg {
    position: absolute;
    top: 312rpx;
    width: 100%;
    height: 640rpx;
    background: #fff9f3;
    z-index: -1;
    border-bottom-left-radius: 40rpx;
    border-bottom-right-radius: 40rpx;
  }
  .container-bg-noSchedule {
    height: 423rpx;
  }
  .award-icon {
    width: 200rpx;
    height: 200rpx;
    background-size: cover;
    position: absolute;
    top: -86rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .coloured-ribbon {
    width: 665rpx;
    height: 225rpx;
    background-size: cover;
    position: absolute;
    top: -80rpx;
    left: 50%;
    transform: translateX(-50%);
  }
  .title {
    margin-top: 150rpx;
    font-family: FZLanTingYuanS-Bold;
    font-size: 42rpx;
    color: #8b553a;
    letter-spacing: 0;
    font-weight: 400;
    line-height: 49rpx;
  }
  .subheading {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    margin-top: 13rpx;
    margin-bottom: 9rpx;
    font-family: FZLanTingYuanS-Bold;
    font-size: 58rpx;
    letter-spacing: 0;
    font-weight: 400;
    background: linear-gradient(to bottom, #ff8605, #ff2c00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    vertical-align: baseline;
    .amount {
      margin: 0 4rpx;
      font-size: 84rpx;
    }
  }
  .lose-time {
    margin-bottom: 48rpx;
    width: 284rpx;
    text-align: center;
    background-image: url(./images/snaplines.png);
    uc-perf-stat-ignore: image;
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: center center;
    font-family: PingFangSC-Regular;
    font-size: 28rpx;
    color: #916031;
    letter-spacing: 0;
    text-align: center;
    font-weight: 400;
    line-height: 40rpx;
  }
  .schedule {
    .task-explain {
      font-family: PingFangSC-Semibold;
      font-size: 32rpx;
      color: #8b553a;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      line-height: 45rpx;
      margin-bottom: 16rpx;
    }
    .task-detail {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 427rpx;
      min-height: 125rpx;
      background: #fcf7e8;
      border: 2rpx solid #fee0af;
      border-radius: 20rpx;
      .schedule-continer {
        .schedule-item {
          display: flex;
          flex-direction: row;
          .schedule-item-left {
            display: flex;
            flex-direction: column;
            align-items: center;
            .circle {
              position: relative;
              width: 23rpx;
              height: 23rpx;
              background-image: linear-gradient(270deg, #ff6d05 0%, #ff4600 100%),
                linear-gradient(-66deg, #ff9309 21%, #ff7609 97%);
              border-radius: 50%;
              font-family: D-DIN-Bold;
              font-size: 16rpx;
              color: #ffffff;
              letter-spacing: -0.45rpx;
              text-align: right;
              line-height: 23rpx;
              font-weight: 700;
              text-align: center;
              .checked-img {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-size: cover;
                z-index: 1;
              }
            }
            .line {
              width: 3rpx;
              height: 20rpx;
              background-image: linear-gradient(270deg, #ff6d05 0%, #ff4600 100%),
                linear-gradient(-11deg, #ff9309 21%, #ff7609 97%);
            }
            .circle-checked {
              background: #D9B58E;
            }
            .line-checked {
              background: #D9B58E;
            }
          }
          .schedule-item-text {
            margin-left: 16rpx;
            font-family: PingFangSC-Semibold;
            font-size: 24rpx;
            line-height: 24rpx;
            color: #8b553a;
            letter-spacing: 0;
            font-weight: 600;
          }
          .schedule-item-text-checked {
            color: #CE9E70;
          }
        }
      }
    }
  }
  .btn-conainer {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 378rpx;
    background-image: url(./images/high-bottom-bg.png);
    uc-perf-stat-ignore: image;
    background-repeat: no-repeat;
    background-size: 100% 378rpx;
    background-position: bottom;
    z-index: 10;
    .confirm {
      margin: 0 auto;
      margin-top: 115rpx;
      margin-bottom: 22rpx;
      width: 427rpx;
      height: 91rpx;
      background-image: linear-gradient(-86deg, #ffdf97 0%, #fefdf0 91%);
      box-shadow: 4rpx 7rpx 28rpx 0 rgba(251, 0, 0, 0.88);
      border-radius: 48rpx;
      font-family: PingFangSC-Semibold;
      font-size: 36rpx;
      color: #ff0303;
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      line-height: 91rpx;
    }
    .cancel {
      margin: 0 auto;
      width: 427rpx;
      height: 91rpx;
      border: 4rpx solid rgba(255, 255, 255, 0.35);
      border-radius: 48rpx;
      font-family: PingFangSC-Semibold;
      font-size: 36rpx;
      color: rgba(255, 255, 255, 0.6);
      letter-spacing: 0;
      text-align: center;
      font-weight: 600;
      line-height: 91rpx;
    }
  }
  .close-btn {
    width: 72rpx;
    height: 72rpx;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    bottom: -112rpx;
    .close-icon {
      width: 100%;
    }
  }
}

.modal-high-value-task-noSchedule{
    height: 745rpx;
}

@keyframes circleFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes lightFadeInOpacity {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.6;
  }
}

@keyframes lightFadeInRotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
