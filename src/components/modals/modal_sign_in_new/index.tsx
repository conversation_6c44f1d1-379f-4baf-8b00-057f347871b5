import {createElement} from 'rax';
import ModalCommon from '../modal_common';
import modal from '@/components/modals/modal';

import SigninNew from '@/pages/index/SigninNew';
import {RewardItem} from "@/store/models/task/types";


interface IProps {
  id: string;
  isVoluntarily: boolean;
  rewardItem: RewardItem | null
}

type ITotalProps = IProps;

class ModalSignIn extends ModalCommon<ITotalProps> {

  static defaultProps = {
    wrapperClass: 'modal-sign-in_new',
  };

  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  getCloseBtnStyle(): Rax.CSSProperties {
    return {
      top: '20rpx',
    };
  }

  getBottomDescText() {
    return '';
  }

  getBottomDescStyle() {
    return {
      'color:': '#7E93B7',
    };
  }

  getContentStyle() {
    return {
      paddingTop: '84rpx',
    };
  }

  onCancel = () => {
    modal.close(this.props.id);
  };

  renderBottomDesc(): JSX.Element | null {
    return null;
  }

  renderBtnGroup(): JSX.Element | null {
    return null
  }

  renderMain() {
   return <SigninNew rewardItem={this.props.rewardItem} isVoluntarily={this.props.isVoluntarily} />
  }
}

export default ModalSignIn

