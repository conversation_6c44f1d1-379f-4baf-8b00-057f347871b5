import { createElement } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import TextInput from 'rax-textinput';
import baseModal from '@ali/weex-rax-components/lib/base_modal';
import fact from '@/lib/fact';
import { TASK_EVENT_TYPE } from '@/store/models/task/types';

import './index.scss';
import { StoreState, StoreDispatch, dispatch } from '@/store';
import { connect } from 'rax-redux';
import CommonModal from '@/components/modals/modal_common';
import CloseIcon from '@/components/modals/modal_common/assets/<EMAIL>';
import {
  dealWithBindInviteCodeCodeMsg,
  dealWithBindInviteCodeBizCodeMsg,
  getBindInviteCodeFalseCode,
} from '@/utils/error';
import Toast from '@/lib/universal-toast';

interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  code?: string;
}
class ModalInviteCode extends CommonModal<IProps> {
  static defaultProps = {
    wrapperClass: 'modal-invite-code',
  };

  loading = false;

  state = {
    inviteCode: '',
  };

  componentDidMount = () => {
    const { inviteEntryTask } = this.props;
    fact.exposure('code_expo', {
      c: 'code',
      d: 'code',
      status: this.props.code ? 'qrqualified' : 'manual',
      task_id: inviteEntryTask?.id || '',
      task_name: inviteEntryTask?.name || '',
      taskclassify: inviteEntryTask?.taskClassify || '',
      groupcode: inviteEntryTask?.groupCode || '',
    })
    if (this.props.code) {
      this.setState({ inviteCode: this.props.code });
    }
  };

  renderCloseBtn(): Rax.RaxElement | null {
    return null;
  }

  handleCloseModal = () => {
    baseModal.close(this.props.id);
  };

  onConfirm = async () => {
    if (this.loading) return;
    if (this.state.inviteCode?.trim()?.length === 0) {
      Toast.show('请填写邀请码');
      return;
    };
    this.loading = true;
    const { inviteEntryTask } = this.props;
    try {
      const result = await this.props.bindInvite(this.state.inviteCode);
      console.log('绑定邀请码请求返回：', result);
      this.loading = false;
      fact.click('code_input_click', {
        c: 'code',
        d: 'code',
        code: this.state.inviteCode?.trim(),
        status: this.props.code ? 'qrqualified' : 'manual',
        success: 'true',
        task_id: inviteEntryTask?.id || '',
        task_name: inviteEntryTask?.name || '',
        taskclassify: inviteEntryTask?.taskClassify || '',
        groupcode: inviteEntryTask?.groupCode || '',
      });
      if (result?.userRewards?.length > 0) {
        if (result?.userRewards?.[0]?.rewardMark === 'cash') {
          Toast.show('任务已完成', {
            award: {
              mark: 'cash',
              amount: result?.userRewards?.[0]?.rewardValue || 0,
            }
          });
        } else {
          Toast.show('任务已完成', {
            award: {
              mark: 'coin',
              amount: result?.userRewards?.[0]?.rewardValue || 0,
            }
          });
        }
      } else {
        Toast.show('填码成功');
      }
      this.handleCloseModal();
      await dispatch.task.complete({ id: this.props.inviteEntryTask?.id, type: 'complete', params: {task: inviteEntryTask} });
      dispatch.app.updateAll();
    } catch (e) {
      this.loading = false;
      console.log(e);
      dealWithBindInviteCodeCodeMsg(e);
      dealWithBindInviteCodeBizCodeMsg(e);
      console.log('绑定邀请码出错');
      fact.click('code_input_click', {
        c: 'code',
        d: 'code',
        code: this.state.inviteCode?.trim(),
        status: this.props.code ? 'qrqualified' : 'manual',
        success: getBindInviteCodeFalseCode(e),
        task_id: inviteEntryTask?.id || '',
        task_name: inviteEntryTask?.name || '',
        taskclassify: inviteEntryTask?.taskClassify || '',
        groupcode: inviteEntryTask?.groupCode || '',
      });
    }
  };

  getContentStyle() {
    return {
      position: 'absolute',
      'margin-top': 0,
      top: '68rpx',
    };
  }

  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  getOkText() {
    return '提交';
  }

  getCancelText() {
    return null;
  }

  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    const cancelText = this.getCancelText();
    const len = [okText, cancelText].filter(i => !!i).length;
    return (
      <View className={`btn-group len-${len}`}>
        {okText && (
          <View className="btn btn1 pure-bg" onClick={this.onConfirm}>
            <Text className="btn-text btn1-text">{this.getOkText()}</Text>
          </View>
        )}
        {cancelText && (
          <View className="btn btn2 pure-bg" onClick={this.onCancel}>
            <Text className="btn-text btn2-text">{this.getCancelText()}</Text>
          </View>
        )}
      </View>
    );
  }

  handleInputChange = (e) => {
    this.setState({
      inviteCode: e.target.value
    });
  }

  handleInputBlur = () => {
    window.scrollTo(0, 0)
  }

  renderMain() {
    const { inviteCode } = this.state;
    return [
      <View className="close-icon-wrap modal-invite-code-close" onClick={this.handleCloseModal}>
        <View className="close-btn close-btn-bg" style={{ 'background-image': `url(${CloseIcon})` }} />
      </View>,
      <View className="tit-desc">
        <View className="title-wrap row">
          <Text className="title">填写邀请码</Text>
        </View>
      </View>,
      <View className="code-input-wrap">
        <TextInput
          className="invite-code-input"
          maxLength={10}
          value={inviteCode}
          onInput={this.handleInputChange}
          onBlur={this.handleInputBlur}
          placeholder={''}
          placeholderColor={'#BDC8DB'}
        />
      </View>,
      <View className="invite-code-tips">
        <Text className="tips-text">1. 下载UC极速版24小时内可填写邀请码 过时不侯</Text>
        <Text className="tips-text">2. 一个手机号只能输入一次邀请码</Text>
      </View>,
    ];
  }
}
const mapState = (state: StoreState) => {
  const { taskList } = state.task;
  const inviteEntryTask = taskList.find(item => item.event.includes(TASK_EVENT_TYPE.UCLITE_INVITE_ENTRY));
  return {
    inviteEntryTask,
  }
};

const mapDispatch = (dispatch: StoreDispatch) => ({
  bindInvite: dispatch.invite.bindInvite,
});
export default connect(mapState, mapDispatch)(ModalInviteCode);
