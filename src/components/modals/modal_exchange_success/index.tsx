import { createElement, useEffect } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import './index.scss';
import ucapi from '@/utils/ucapi';
import { getGiftUseUrl } from '@/utils/index';

import modal from '@ali/weex-rax-components/lib/base_modal';
import { MODAL_ID } from '../index';
import { IEquityGiftItem } from '@/store/models/rightsGift/typings';
import fact from '@/lib/fact';
import '@/components/ModalBtn/index.scss';

interface IProps {
  data: IEquityGiftItem,
  resourceCode: string;
}
const Index = (props: IProps)=> {
  const { data, resourceCode } = props;

  useEffect(()=>{
    fact.exposure('exchange_final_exposure', {
      c: 'list',
      d: 'pop',
      exchange_name: data?.name,
      code: 'coin',
      count: data.assetConsumeList?.[0]?.amount || '',
      resource_location: resourceCode,
      planid: data?.planId,
    })
  }, [])

  const handleConfirm = ()=> {
    fact.click('exchange_final__click', {
      c: 'list',
      d: 'pop',
      exchange_name: data?.name,
      code: 'coin',
      count: data.assetConsumeList?.[0]?.amount || '',
      resource_location: resourceCode,
      planid: data?.planId,
      click_position: data?.useUrl ? '去看看' : '我知道了',
      planId: data.planId,
    })
    modal.close(MODAL_ID.EXCHANGE_SUCCESS);
    if (data?.useUrl) {
      ucapi.base.openURL({url: getGiftUseUrl(data.useUrl)})
    }
  }

  const handleClose = () => {
    fact.click('exchange_final__click', {
      c: 'list',
      d: 'pop',
      exchange_name: data?.name,
      exchange_type: data?.factName || '',
      count: data.assetConsumeList?.[0]?.amount || '',
      resource_location: resourceCode,
      click_position: '关闭',
      planId: data.planId,
    })
    modal.close(MODAL_ID.EXCHANGE_SUCCESS);
  }

  return (
    <View className="exchange-success-modal">
      <View className="right-close" onClick={handleClose} />
      <View className="title">兑换成功</View>
      <View className="content-text row">
        <Text className="award-name">{data?.name || ''}</Text>
        权益已兑换成功，并为你自动设置，可在
        <Text className="award-desc">{data?.useExplain || ''}</Text>
        中查看
      </View>
      <View className="modal-confirm-btn flex-center" onClick={handleConfirm}>{data?.useUrl ? '去看看' : '我知道了'}</View>
    </View>
  )
}

export default Index
