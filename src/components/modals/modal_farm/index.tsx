import { createElement } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import { StoreState } from '@/store';
import './index.scss';
import CommonModal from '@/components/modals/modal_common';
import { connect } from 'rax-redux';
import fact from '@/lib/fact';
import TopImg from './images/top-img.png';
import RightArrowsImg from './images/right-arrows.png';
import { execWithLock } from '@/utils/lock';
import { taskActionHandler } from '@/pages/index/task/help';
import modal from '@ali/weex-rax-components/lib/base_modal/index';
import Text from 'rax-text';
interface IProps extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {
  id: string;
  desc: string;
  taskInfo: any;
}
class ModalFarm extends CommonModal<IProps> {
  topImg = TopImg;
  modalTopHeight = '248rpx';
  // 曝光
  logExposure = () => {
    const { taskInfo } = this.props;
    fact.exposureAsync('center_pop', {
      c: 'task',
      d: 'center',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
      award_amount: taskInfo?.rewardItems[0]?.amount || '',
    });
  };
  // 关闭按钮点击
  logCancelClk = () => {
    const { taskInfo } = this.props;
    fact.click('task_pop_click', {
      c: 'task',
      d: 'button',
      task_id: taskInfo?.id,
      task_name: taskInfo?.name,
      click_pop: 0,
      taskclassify: taskInfo?.taskClassify || '',
      groupcode: taskInfo?.groupCode || '',
    });
  };
  // 完成任务跳转到芭芭农场
  toFarm(taskInfo) {
    return async () => {
      const { id } = this.props
      fact.click('task_pop_click', {
        c: 'task',
        d: 'button',
        task_id: taskInfo?.id,
        task_name: taskInfo?.name,
        click_pop: 1,
        taskclassify: taskInfo?.taskClassify || '',
        groupcode: taskInfo?.groupCode || '',
        award_amount: taskInfo?.rewardItems[0]?.amount || '',
      });
      await execWithLock(
        'finish_task_lock',
        async () => {
          console.log('taskActionHandler', taskInfo);
          await taskActionHandler(taskInfo);
          modal.close(id);
        },
        3000,
      );
    };
  }
  // 关闭按钮位置
  getCloseBtnStyle(): Rax.CSSProperties {
    return {
      top: '146rpx',
    };
  }
  renderBtnGroup() {
    return null;
  }
  renderMain() {
    const { taskInfo, app } = this.props;
    const { modalFarmText, modalFarmIamge } = app;
    this.topImg = modalFarmIamge;
    return (
      <View className="modal—farm-rendermain">
        <View className="modal—farm-rendermain-text">{taskInfo?.name}</View>
        <View className="modal—farm-rendermain-btn" onClick={this.toFarm(taskInfo)}>
          <Text className='modal—farm-btn-text'>
            {modalFarmText ? modalFarmText : '施肥可得'}
          </Text>
          <Text className="gold-color">{taskInfo?.rewardItems[0]?.amount}</Text>
          <Text className='modal—farm-btn-text'>
            元宝
          </Text>
          <Image source={RightArrowsImg} className="modal—farm-rendermain-arrows" />
        </View>
      </View>
    );
  }
}

const mapState = (state: StoreState) => ({
  app: state.app,
});
const mapDispatch = () => ({});
export default connect(mapState, mapDispatch)(ModalFarm);
