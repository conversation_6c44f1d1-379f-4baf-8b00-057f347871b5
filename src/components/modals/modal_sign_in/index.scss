.modal-sign-in {
  .modal-top {
    margin-top: -160rpx;
  }
  .modal-sign-in-main {
    z-index: 1;
    .title-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      .title {
        font-family: PingFang-SC-Heavy;
        font-size: 48rpx;
        color: #01255D;
        letter-spacing: 2rpx;
        text-align: center;
        font-weight: bold;
      }
      .amount-text {
        font-family: PingFangSC-Semibold;
        font-size: 48rpx;
        color: #F02920;
        letter-spacing: 0rpx;
        text-align: center;
        font-weight: bold;
      }
    }

    .desc-wrapper {
      margin: 12rpx auto;
      .desc-text {
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #7E93B7;
        letter-spacing: 0;
        text-align: center;
        // font-weight: bold;
      }
    }

    .sign-day-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      flex-wrap: wrap;
      margin-top: 34rpx;
      margin-bottom: 16rpx;

      .sign-day-container {
        position: relative;
        margin: 6rpx;
        padding: 4rpx;
        background-color: transparent;

        .continusous-sign-tips {
          position: absolute;
          transform: rotate(-4deg);
          uc-perf-stat-ignore: image;
          width: 84rpx;
          height: 84rpx;
          right: -34rpx;
        }

        .sign-day-item {
          width: 120rpx;
          // height: 140rpx;
          height: 152rpx;
          // margin: 10rpx;
          // background-image: url('./assets//bg.png');
          background-size: cover;
          uc-perf-stat-ignore: image;
          background-repeat: no-repeat;
          background-position: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          // border: 2rpx solid transparent;
          position: relative;
          background-color: transparent;
          border-radius: 28rpx;
          box-shadow: 0 30rpx 30rpx -10rpx rgba(184,0,0,0.10);

          .day-text {
            font-family: PingFangSC-Semibold;
            font-size: 16rpx;
            // color: #01255D;
            color: #FFFFFF;
            letter-spacing: 0;
            text-align: center;
            font-weight: bold;
            position: absolute;
            top: 2rpx;
            left: 20rpx;
          }

          .prize-icon-wrapper {
            position: relative;
            width: 64rpx;
            height: 64rpx;

            .prize-icon {
              width: 64rpx;
              height: 64rpx;
            }
            .complete-icon {
              position: absolute;
              width: 32rpx;
              height: 32rpx;
              right: 0;
              bottom: 0;
            }
          }

          .prize-amount-wrapper {
            // position: relative;
            position: absolute;
            bottom: 18rpx;
            text-align: center;
            width: 100%;
            // height: 42rpx;
            // line-height: 42rpx;
            display: flex;
            flex-direction: row;
            // align-items: center;
            justify-content: center;

            .rmb-symbol {
              // margin-top: -4rpx;
              margin-right: 4rpx;
              font-family: D-DIN-Bold;
              // font-size: 16rpx;
              font-size: 22rpx;
              // color: #F02920;
              color: #FFFFFF;
              letter-spacing: 0;
              text-align: center;
              font-weight: bold;
            }
            .coin-text {
              margin-top: 6rpx;
              margin-left: 2rpx;
              font-family: D-DIN-Bold;
              font-size: 16rpx;
              color: #FFFFFF;
              letter-spacing: 0;
              text-align: center;
              font-weight: bold;
            }
            .prize-amount {
              font-family: D-DIN-Bold;
              // font-size: 24rpx;
              font-size: 26rpx;
              // color: #F02920;
              color: #FFFFFF;
              letter-spacing: 0;
              text-align: center;
              font-weight: bold;
            }
          }
        }
      }

      .today {
        // border: 2rpx solid #8e939c;
        border: 6rpx solid #FB4A50;
        // border-radius: 16rpx;
        border-radius: 40rpx;
        animation: signscale 1.5s ease infinite;
      }
      .disable-animation {
        animation: none;
      }
    }
  }
}

@keyframes signscale {
  0% {
    transform: scale(1);
  }
  20% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  80% {
    transform: scale(1);
  }
  100% {
    transform: scale(1);
  }
}
