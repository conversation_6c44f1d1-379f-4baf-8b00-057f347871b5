import {createElement} from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import Image from 'rax-image';
import {connect} from 'rax-redux';
import {dispatch, store, StoreDispatch, StoreState} from '@/store';
import ModalCommon, {ModalProps} from '../modal_common';
import modal from '@/components/modals/modal';
import {SignInfo, TASK_EVENT_TYPE, TASK_STATUS, RewardItem, TaskInfo} from '@/store/models/task/types';
import {getSignInfo} from '@/store/models/task/helper';
import Toast from '@/lib/universal-toast';
import {getRealAmount, getSignDateStr, getSignInTotalCash} from '@/utils/signin_helper';
import ucapi from '@/utils/ucapi';
import {rewardDesc, taskActionHandler, checkTaskFinished} from '@/pages/index/task/help';

import Coin0Icon from './assets/coin_0.png';
import Coin1Icon from './assets/coin_1.png';
import RedPacket0Icon from './assets/red_packet_0.png';
import RedPacket1Icon from './assets/red_packet_1.png';
import ContinuousSignTipsIcon from './assets/<EMAIL>';
import BigCoinIcon from './assets/<EMAIL>';
import useSignData from '@/pages/index/SigninNew/useSingData';
import useAdplay from '@/pages/index/SigninNew/useAdplay';
import './index.scss';
import fact from '@/lib/fact';
import {execWithLock} from "@/utils/lock";
import Fact from '@/components/Fact';
import logoutCheck from '@/utils/logoutCheck';

export interface IProps extends ModalProps {
  signAward: RewardItem,
  isVoluntarily: boolean,
}

type ITotalProps = ReturnType<typeof mapState> & IProps;

interface IState {
  loading: boolean;
}

class ModalSignIn extends ModalCommon<ITotalProps> {
  static defaultProps = {
    wrapperClass: 'modal-common modal-sign-in',
  };
  topImg;
  state: IState = {
    loading: false,
  };

  constructor(props) {
    super(props);
    this.topImg = props.topImg;
  }

  componentDidMount() {
    const sign = getSignInfo();
    const { adTask, signList } = this.props;
    const { isLogin } = store.getState()?.user; 
    fact.exposure('resource_exposure', {
      c: 'pop',
      d: 'sign',
      day: sign?.title?.split('')[1] as string,
      task_id: sign?.id || '',
      task_name: sign?.name || '',
      taskclassify: sign?.taskClassify || '',
      groupcode: sign?.groupCode || '',
      award_amount: sign?.rewardItems[0]?.amount || '',
      resource_location: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nusign' : 'sign',
      is_login: isLogin ? '1' : '0'
    });
    // 区分新老用户曝光
    fact.exposure('fuli_expo', {
      c: 'pop',
      d: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nusign' : 'sign',
      day: sign?.title?.split('')[1] as string,
      video: this.needToVideo() && adTask?.rewardItems?.[0] ? 1 : 0,
      task_id: sign?.id || '',
      task_name: sign?.name || '',
      taskclassify: sign?.taskClassify || '',
      groupcode: sign?.groupCode || '',
      award_amount: this.needToVideo() && adTask?.rewardItems[0]?.amount || '',
      task_progress: this.needToVideo() && adTask?.dayTimes?.progress || '',
    });
    signList?.map((signTask)=> {
      fact.click('signin_click', {
        c: 'pop',
        d: 'signin',
        day: signTask?.title?.split('')[1] as string,
        task_id: signTask?.id || '',
        task_name: signTask?.name || '',
        taskclassify: signTask?.taskClassify || '',
        groupcode: signTask?.groupCode || '',
        award_amount: signTask?.rewardItems[0]?.amount || '',
      });
    })
  }

  logConfirmClk = () => {

  };

  logCancelClk = () => {

  };

  needToVideo = () => {
    const { signList, adTask, signAward } = this.props
    const { isLogin } = store.getState()?.user; 
    // 未登录下都不出套娃任务
    if (!isLogin) {
      return false
    }
    if (signAward) return true
    return adTask && !(signList[0]?.isSignDay && signList[0]?.event === TASK_EVENT_TYPE.UCLITE_SIGN)
  }

  getOkText() {
    // const { adTask } = this.props
    const { isLogin } = store.getState()?.user; 
    // 新用户签到第一天 不显示视频推荐
    if (this.needToVideo()) {
      const [amount,,adPlayDone] = useAdplay();
      if (adPlayDone || !amount) return '好的'
      return '看激励视频再领' + amount + '元宝'
    } else {
      return isLogin ? '领取' : '登录领取'
    }
  }

  getCancelText() {
    const sign = getSignInfo();
    if (this.props.signAward || checkTaskFinished(sign as TaskInfo)) {
      return ''
    }
    return this.needToVideo() ? '直接签到' : '';
  }

  renderTopOrnament(): JSX.Element | null {
    return null;
  }

  getCloseBtnStyle(): Rax.CSSProperties {
    return {
      top: '20rpx',
    };
  }

  getBottomDescText() {
    return '';
  }

  getBottomDescStyle() {
    return {
      'color:': '#7E93B7',
    };
  }

  getContentStyle() {
    return {
      paddingTop: '84rpx',
    };
  }

  onConfirm = async (e?: any) => {
    if (!this.props.signAward) {
      this.handleSignIn(null, 'confirm')
    }
    // 签到完再跳去视频
    if (this.needToVideo()) {
      modal.close(this.props.id)
      const [,,adplayDone] = useAdplay()
      if (!adplayDone) {
        const sign = getSignInfo();
        if (this.props.signAward) {
          fact.click('resource_click', {
            c: 'pop',
            d: 'sign',
            day: sign?.title?.split('')[1] as string,
            task_id: sign?.id || '',
            task_name: sign?.name || '',
            taskclassify: sign?.taskClassify || '',
            groupcode: sign?.groupCode || '',
            award_amount: sign?.rewardItems[0]?.amount || '',
            resource_location: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nusign' : 'sign',
            click_area: 'task_matryoshka',
          });
        }
        fact.click('video_click', {
          c: 'pop',
          d: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nuvideo' : 'video',
          day: sign?.title?.split('')[1] as string,
          task_id: sign?.id || '',
          task_name: sign?.name || '',
          taskclassify: sign?.taskClassify || '',
          groupcode: sign?.groupCode || '',
          award_amount: sign?.rewardItems[0]?.amount || '',
        });

        if (logoutCheck()) {
          return
        }
        setTimeout(() => {
          this.finishTask()
        }, 1200)
      }
    }
  }

  onCancel = () => {
    const sign = getSignInfo();
    fact.click('resource_click', {
      c: 'pop',
      d: 'sign',
      day: sign?.title?.split('')[1] as string,
      task_id: sign?.id || '',
      task_name: sign?.name || '',
      taskclassify: sign?.taskClassify || '',
      groupcode: sign?.groupCode || '',
      award_amount: sign?.rewardItems[0]?.amount || '',
      resource_location: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nusign' : 'sign',
      click_area: 'close',
    });
    modal.close(this.props.id);
  };

  getFactClickArea = (clickType = 'cancel') => {
    // 点击看视频按钮 (第一个按钮) 
    if (this.needToVideo() && clickType !== 'cancel') {
      return 'task_matryoshka';
    }
    // 点击直接签到 (第二个按钮) 
    if (this.getCancelText() && clickType === 'cancel') {
      return 'directly_claim';
    }
    const { isLogin } = store.getState()?.user; 
    const sign = getSignInfo();
    // 好的 (老用户自动签到) 
    if (isLogin && this.props.signAward && sign?.event !== TASK_EVENT_TYPE.UCLITE_SIGN ) {
      return 'ok';
    } 
    return isLogin ? 'button_claim' : 'button_loginclaim';
  }

  handleSignIn = async (e: any, clickType = 'cancel') => {    
    // 获取当前签到任务
    const sign = getSignInfo();
    fact.click('resource_click', {
      c: 'pop',
      d: 'sign',
      day: sign?.title?.split('')[1] as string,
      task_id: sign?.id || '',
      task_name: sign?.name || '',
      taskclassify: sign?.taskClassify || '',
      groupcode: sign?.groupCode || '',
      award_amount: sign?.rewardItems[0]?.amount || '',
      resource_location: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nusign' : 'sign',
      click_area: this.getFactClickArea(clickType),
    });

    // 区分新老用户签到点击打点
    fact.click('fuli_click', {
      c: 'pop',
      d: sign?.event === TASK_EVENT_TYPE.UCLITE_SIGN ? 'nusign' : 'sign',
      day: sign?.title?.split('')[1] as string,
      task_id: sign?.id || '',
      task_name: sign?.name || '',
      taskclassify: sign?.taskClassify || '',
      groupcode: sign?.groupCode || '',
      award_amount: sign?.rewardItems[0]?.amount || '',
    });
    if (logoutCheck() || checkTaskFinished(sign as TaskInfo)) {
      return
    }
    if (this.state.loading) return;
    this.setState({ loading: true });
    const prizes = await dispatch.task.signIn(sign as SignInfo);
    if (prizes) {
      const winPrizes = prizes.filter(prize => prize.win);
      if (winPrizes.length > 0) {
        Toast.show('签到完成', {
          award: winPrizes[0].rewardItem,
        });
        // postmessage页面广播通知信息流卡片签到状态变更
        ucapi.base.postmessage({
          data: {
            id: 'signStatusChange'
          }
        });
        modal.close(this.props.id);
      } else {
        Toast.show('签到失败，请稍后再试');
      }
    } else {
      Toast.show('签到失败，请稍后再试');
    }
    this.setState({ loading: false });
  };

  finishTask = async () => {
    const { adTask } = this.props;
    if (!adTask) return;
    await execWithLock(
      'finish_video_task',
      async () => {
        console.log('modalTaskActionHandler', adTask);
        await taskActionHandler(adTask);
      },
      3000,
    );
  };

  renderBottomDesc(): JSX.Element | null {
    return null;
  }

  renderBtnGroup(): JSX.Element | null {
    const okText = this.getOkText();
    const cancelText = this.getCancelText();
    const len = [okText, cancelText].filter((i) => !!i).length;
    const style = {
      boxShadow: '0 30rpx 30rpx -10rpx rgba(184,0,0,0.10)',
      borderRadius: '59rpx',
      marginBottom: '20rpx',
    };
    return (
      <View className={`btn-group len-${len}`}>
        {okText && (
          <View className='btn btn1 pure-bg' style={style} onClick={this.onConfirm}>
            <Text className='btn-text btn1-text'>{this.getOkText()}</Text>
          </View>
        )}
        {cancelText && (
          <View className='btn btn2 pure-bg' onClick={this.handleSignIn}>
            <Text className='btn-text btn2-text'>{this.getCancelText()}</Text>
          </View>
        )}
      </View>
    );
  }

  getSignDayBg = (sign: SignInfo, isLastDay: boolean) => {
    if (sign.state === TASK_STATUS.TASK_CONFIRMED) {
      // 签到完成
      if (sign.rewardItems[0]?.mark?.indexOf('cash') > -1) {
        // 现金
        return RedPacket1Icon;
      }
      return Coin1Icon;
    } else {
      // 未签到
      if (sign.event === TASK_EVENT_TYPE.UCLITE_SIGN_NM && isLastDay) {
        return BigCoinIcon;
      }
      if (sign.rewardItems[0]?.mark?.indexOf('cash') > -1) {
        return RedPacket0Icon;
      }
      return Coin0Icon;
    }
  };

  getPrizeUnit = (rewardItems) => {
    return rewardItems?.[0]?.mark?.indexOf('cash') > -1 ? '元' : '元宝';
  }

  renderMain() {
    const { signList, signAward} = this.props;
    const { firstDayStr, lastDayStr } = getSignDateStr(signList);
    const signToday = getSignInfo();
    const [ ,toDayAward] = useSignData(store.getState().task.newNewSignList, this.props.isVoluntarily)
    const titleAward = this.props.isVoluntarily ? toDayAward?.replace('最高', '') : toDayAward
    return (
      <View className='modal-sign-in-main'>
        <View className='title-wrapper' x-if={signList.length && signList[0].event === TASK_EVENT_TYPE.UCLITE_SIGN}>
          <Text className='title'>7天签到必得</Text>
          <Text className='amount-text'>{getSignInTotalCash(signList)}元</Text>
        </View>
        <View className='title-wrapper' x-else>
          <Text className='title'>
            {/* { signAward ? '今天签到成功' : `今天签到领${getRealAmount(signToday?.rewardItems[0])}${this.getPrizeUnit(signToday?.rewardItems)}`}
             */}
             {this.props.isVoluntarily ? '签到成功' : '明日签到'}
          </Text>
          {/* <Text x-if={signAward} className='amount-text'>+{getRealAmount(signToday?.rewardItems[0])}{this.getPrizeUnit(signToday?.rewardItems)}</Text> */}
          <Text x-if={signAward || titleAward} className='amount-text'>+{titleAward}</Text>
        </View>
        <View className='desc-wrapper'>
          <Text className='desc-text' x-if={signList.length && signList[0].event === TASK_EVENT_TYPE.UCLITE_SIGN}>新用户专享 仅限{firstDayStr}～{lastDayStr}</Text>
          <Text className='desc-text' x-else>7天连续签到必得大额元宝</Text>
        </View>
        <View className='sign-day-wrapper'>
          {
            signList.map((item, idx) => {
              return (
                <Fact
                key={item?.id + idx}
                c="pop"
                d="signin"
                expoLogkey="signin_exposure"
                noUseClick
                expoExtra={{
                  task_id: item?.id,
                  module: 'pop',
                  task_name: item?.name,
                  taskclassify: item?.taskClassify || '',
                  groupcode: item?.groupCode || '',
                  day: item?.title?.split('')[1] as string,
                  award_amount: item?.rewardItems[0]?.amount || '',
                }}
              >
                <View className={`sign-day-container ${item.isSignDay ? 'today' : ''} ${signAward ? 'disable-animation' : ''}`}>
                  <View className='sign-day-item' style={{ backgroundImage: `url(${this.getSignDayBg(item, idx+1 === signList.length || false)})` }}
                        key={item.id}>
                    <Text className='day-text'>{item.isSignDay ? '今天' : item.title}</Text>
                    <View className='prize-amount-wrapper'>
                      <Text className='prize-amount'>{getRealAmount(item.rewardItems[0])}</Text>
                      <Text x-if={item.rewardItems[0]?.mark?.indexOf('cash') > -1} className='rmb-symbol'>元</Text>
                      <Text x-else className='coin-text'>元宝</Text>
                    </View>
                  </View>
                  <Image x-if={signList[0].event === TASK_EVENT_TYPE.UCLITE_SIGN_NM && (idx+1 === signList.length)} className='continusous-sign-tips' source={{ uri: ContinuousSignTipsIcon }} />
                </View>
              </Fact>
                
              );
            })
          }
        </View>
      </View>
    );
  }
}

const mapState = (state: StoreState) => {
  const adTask = state.task.taskList.find(task => {
    return (task.event === TASK_EVENT_TYPE.VIDEO_AD_NEW || task.event === TASK_EVENT_TYPE.UCLITE_VIDEO_AD) && task.state === TASK_STATUS.TASK_DOING;
  });
  return {
    signList: state.task.signin,
    adTask
  };
};
const mapDispatch = (dispatch: StoreDispatch) => ({});

export default connect(mapState, mapDispatch)(ModalSignIn);
