import { createElement, Component } from 'rax';
import { IStoreDispatch, IStoreState } from '@/store';
import { connect } from 'rax-redux';
import View from 'rax-view';
import Text from 'rax-text';

import './index.scss';

class Title extends Component<Props> {
  componentDidMount() {
  }

  render() {
    return <View>
      <Text>{this.props.title}</Text>
    </View>;
  }
}


const mapState = (state: IStoreState) => ({
  title: state.app.title,
});

const mapDispatch = (dispatch: IStoreDispatch) => ({
  dispatch,
});

interface Props extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch> {}

export default connect(mapState, mapDispatch)(Title);
