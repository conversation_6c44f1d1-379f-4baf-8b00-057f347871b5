import { Component } from 'rax';
import { ssrIsSkeletonMode } from '@/lib/render-utils/ssr';
import { renderAction, perfMark } from '@/lib/render-utils/perf';

interface Props<IFirstData> {
    // 首屏数据
    firstData?: IFirstData;
}

class BasicPage<IFirstData> extends Component<Props<IFirstData>> {
    static getInitialProps: any;
    renderContent: () => any = () => null;
    customSkeleton: () => any;

    constructor(props) {
        super(props);
    }

    componentDidMount() {
        perfMark(renderAction.hydrated)
    }

    // GUIDANCE：根据当前页面首屏相关数据，自行选择对应的store进行更新，
    // 入参为getFirstData所返回的数据
    handleFirstData = async (useFirstData?: (data) => any) => {
        let firstData = this.props.firstData;
        if (!firstData && window.__CSRFirstDataPms__) {
            // 如果没有firstData，并写有CSR请求的Promise的时候，此处变成异步
            try {
                firstData = await window.__CSRFirstDataPms__;
            } catch(err) {
              console.error('[BasicPage] await window.__CSRFirstDataPms__ error ', err);
            }
        }

        if (useFirstData) {
            useFirstData(firstData)
        }
    }

    render() {
        if (ssrIsSkeletonMode() && this.customSkeleton) {
            return this.customSkeleton();
        }
        return this.renderContent();
    }
}

export default BasicPage;
