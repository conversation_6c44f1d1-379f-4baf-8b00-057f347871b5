import { createElement, Component } from 'rax';

const STATUS = {
  LOADING: 0,
  READY: 1,
  ERROR: 2,
};

interface IProps {
  LoadingComponent?: any;
  ErrorComponent?: any;
  innerRef?: any;
  resolve?: () => Promise<any>;
}

class AsyncComponent extends Component<IProps> {

  El = null;

  state = {
    status: STATUS.LOADING,
  };

  constructor(props) {
    super(props);

    props.resolve()
      .then(ret => {
        this.El = ret && ret.default || ret;
        if (this.El) {
          this.setState({ status: STATUS.READY });
        } else {
          throw new Error('[AsyncComponent] unexpected Component chunk');
        }
      })
      .catch(() => {
        this.setState({ status: STATUS.ERROR });
      });
  }

  render() {
    const { El } = this; // tslint:disable-line:no-this-assignment
    const { status } = this.state;
    const { LoadingComponent, ErrorComponent, innerRef, children, ...props } = this.props;

    if (El) {
      return <El {...props} ref={innerRef}>{children}</El>;
    }

    if (LoadingComponent && status === STATUS.LOADING) {
      return <LoadingComponent {...props} />;
    }

    if (ErrorComponent && status === STATUS.ERROR) {
      return <ErrorComponent {...props} />;
    }

    return null;
  }
}

export default AsyncComponent;
