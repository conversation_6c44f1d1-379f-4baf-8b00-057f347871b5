import { createElement, Component } from 'rax';
import Image from 'rax-image';
import { IStoreDispatch, IStoreState } from '@/store';
import { connect } from 'rax-redux';

import './index.scss';

class Title extends Component<Props> {
  render(){
    const { uri, avatar } = this.props;
    const source = { uri: avatar || uri };
    return <Image className="logo" source={source} />;
  };
}

const mapState = (state: IStoreState) => ({
  avatar: state.app.avatar,
});

const mapDispatch = (dispatch: IStoreDispatch) => ({
  dispatch,
});

interface LogoProps {
  uri: string;
}

interface Props extends ReturnType<typeof mapState>, ReturnType<typeof mapDispatch>, LogoProps {}

export default connect(mapState, mapDispatch)(Title);
