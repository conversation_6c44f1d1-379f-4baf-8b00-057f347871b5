import { createElement, useEffect, useState, useRef, useCallback, useMemo } from 'rax';
import View from 'rax-view';
import Image from '@/components/image';
import './index.scss';
import { bindObserver, unbindObserver } from '@/lib/utils/help';

const arrowLeftIcon = 'https://img.alicdn.com/imgextra/i2/O1CN012eZdNH216HkEK11BS_!!6000000006935-55-tps-60-63.svg';
const arrowRightIcon = 'https://img.alicdn.com/imgextra/i3/O1CN01nnhnTH1Ldz1Vd7QH3_!!6000000001323-55-tps-60-63.svg';
interface CarouselProps {
  imgs: Array<{
    img: string;
    link?: string;
  }>;
  interval?: number;
  autoplay?: boolean;
  onImageClick?: (link: string) => void;
  onChange?: (current: number) => void;
  className?: string;
  indicatorPosition?: 'left' | 'center' | 'right';
  showIndicator?: boolean;
  showArrows?: boolean;
}

export function Carousel({
  imgs,
  interval = 3000,
  autoplay = true,
  onImageClick,
  onChange,
  className = '',
  indicatorPosition = 'center',
  showIndicator = true,
  showArrows = true
}: CarouselProps) {
  if (!imgs.length) return null;
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentIndex, setCurrentIndex] = useState(1);
  const [containerHeight, setContainerHeight] = useState<number>(0);

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    // 图片加载完成后添加 loaded 类并更新高度
    e.currentTarget.classList.add('loaded');
    
    // 只有当这个图片是当前显示的图片时，才更新容器高度
    const currentImg = containerRef.current?.querySelector(`.carousel-item:nth-child(${currentIndex + 1}) img`);
    if (currentImg === e.currentTarget) {
      setContainerHeight(e.currentTarget.height);
    }
  };

  // 单图渲染

  const renderOneImg = useMemo(() => {
    return (
      <View className={`carousel ${className}`}>
        <View ref={containerRef} className="carousel-container" style={{ height: containerHeight ? `${containerHeight}px` : undefined }}>
          <View className="carousel-wrapper" style={{ width: '100%' }}>
            <View 
              className="carousel-item" 
              style={{ width: '100%' }}
              onClick={() => onImageClick?.(imgs[0]?.link || '')}
            >
              <Image 
                source={imgs[0].img}
                className="carousel-image"
                onLoad={handleImageLoad}
              />
            </View>
          </View>
        </View>
      </View>
    );
  }, [imgs, className, onImageClick]);

  if (imgs.length === 1) {
    return renderOneImg
  }

  // 监听当前图片变化时更新高度
  useEffect(() => {
    const currentImg = containerRef.current?.querySelector(`.carousel-item:nth-child(${currentIndex + 1}) img`);
    if (currentImg && (currentImg as HTMLImageElement).complete) {
      setContainerHeight((currentImg as HTMLImageElement).height);
    }
  }, [currentIndex]);

  const extendedImgs = useRef([imgs[imgs.length - 1], ...imgs, imgs[0]]);
  const [isAnimating, setIsAnimating] = useState(false);
  const touchStartX = useRef(0);
  const touchStartY = useRef(0);
  const timerRef = useRef<number>();
  const isMovingRef = useRef(false);

  const handleTransitionEnd = useCallback(() => {
    if (!isAnimating) {
      return
    };
    
    setIsAnimating(false);
    requestAnimationFrame(() => {
      if (currentIndex >= imgs.length + 1) {
        setCurrentIndex(1);
      } else if (currentIndex <= 0) {
        setCurrentIndex(imgs.length);
      }
    });
    // 更新高度
    const currentImg = containerRef.current?.querySelector(`.carousel-item:nth-child(${currentIndex + 1}) img`);
    if (currentImg && (currentImg as HTMLImageElement).complete) {
      setContainerHeight((currentImg as HTMLImageElement).height);
    }
  }, [currentIndex, imgs.length, isAnimating]);

  const startAutoPlay = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    if (!autoplay || imgs.length <= 1) return;

    timerRef.current = window.setInterval(() => {
      setCurrentIndex(prev => prev + 1);
      setIsAnimating(true);
    }, interval);
  }, [autoplay, interval, imgs.length]);

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (isAnimating) return;
    
    touchStartX.current = e.touches[0].clientX;
    touchStartY.current = e.touches[0].clientY;
    isMovingRef.current = true;

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  }, [isAnimating]);

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isMovingRef.current || isAnimating) return;

    const touchEndX = e.touches[0].clientX;
    const touchEndY = e.touches[0].clientY;
    
    const deltaX = touchEndX - touchStartX.current;
    const deltaY = touchEndY - touchStartY.current;

    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      e.preventDefault();
    }
  }, [isAnimating]);

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!isMovingRef.current || isAnimating) return;
    
    isMovingRef.current = false;
    const touchEndX = e.changedTouches[0].clientX;
    const deltaX = touchEndX - touchStartX.current;
    const minSwipeDistance = 50;

    if (Math.abs(deltaX) > minSwipeDistance) {
      requestAnimationFrame(() => {
        setCurrentIndex(prev => deltaX > 0 ? prev - 1 : prev + 1);
        setIsAnimating(true);
      });
    }

    startAutoPlay();
  }, [isAnimating, startAutoPlay]);

  useEffect(() => {
    const container = document.getElementById('carousel-container');
    if (container) {
      bindObserver(container, () => {
        startAutoPlay();
        unbindObserver(container);
      });
    }
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startAutoPlay]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('touchstart', handleTouchStart, { passive: true });
    container.addEventListener('touchmove', handleTouchMove, { passive: false });
    container.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener('touchstart', handleTouchStart);
      container.removeEventListener('touchmove', handleTouchMove);
      container.removeEventListener('touchend', handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd]);

  const actualIndex = ((currentIndex - 1 + imgs.length) % imgs.length);

  useEffect(() => {
    onChange?.(actualIndex);
  }, [actualIndex, onChange]);

  // 使用 transform3d 提升动画性能
  const translateX = -currentIndex * (100 / extendedImgs.current.length);
  const transform = `translate3d(${translateX}%, 0, 0)`;

  const handlePrev = useCallback(() => {
    if (isAnimating) return;
    setCurrentIndex(prev => prev - 1);
    setIsAnimating(true);
  }, [isAnimating]);

  const handleNext = useCallback(() => {
    if (isAnimating) return;
    setCurrentIndex(prev => prev + 1);
    setIsAnimating(true);
  }, [isAnimating]);

  return (
    <View className={`carousel ${className}`} id="carousel-container">
      <View 
        ref={containerRef}
        className="carousel-container"
        style={{ height: containerHeight ? `${containerHeight}px` : undefined }}
      >
        {showArrows && (
          <>
            <View className="carousel-arrow carousel-arrow-left" onClick={handlePrev}>
            <Image 
                source={arrowLeftIcon}
                className="carousel-image"
              />
            </View>
            <View className="carousel-arrow carousel-arrow-right" onClick={handleNext}>
            <Image 
                source={arrowRightIcon}
                className="carousel-image"
              />
            </View>
          </>
        )}
        
        <View 
          className="carousel-wrapper"
          style={{ 
            transform,
            transition: isAnimating ? 'transform 0.3s cubic-bezier(0.165, 0.84, 0.44, 1)' : 'none',
            width: `${extendedImgs.current.length * 100}%`,
            willChange: 'transform'
          }}
          onTransitionEnd={handleTransitionEnd}
        >
          {extendedImgs.current.map((item, index) => (
            <View 
              key={index}
              className="carousel-item"
              onClick={() => onImageClick?.(item.link || '')}
              style={{ 
                width: `${100 / extendedImgs.current.length}%`,
                willChange: 'transform'
              }}
            >
              <Image 
                source={item.img}
                className="carousel-image"
              />
            </View>
          ))}
        </View>
      </View>

      {showIndicator && imgs.length > 1 && (
        <View className={`carousel-indicators ${indicatorPosition}`}>
          {imgs.map((_, index) => (
            <View
              key={index}
              className={`indicator ${index === actualIndex ? 'active' : ''}`}
              onClick={() => {
                setCurrentIndex(index + 1);
                setIsAnimating(true);
              }}
            />
          ))}
        </View>
      )}
    </View>
  );
} 