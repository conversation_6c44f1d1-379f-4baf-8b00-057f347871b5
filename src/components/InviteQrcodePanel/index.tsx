import { createElement, Component, RefObject, createRef } from 'rax';
import View from 'rax-view';
import Text from 'rax-text';
import { StoreState } from '@/store';
import { connect } from 'rax-redux';
import Image from '@/components/image';
import QRCode from 'rax-qrcode';

import event from '@/utils/event';
import fact from '@/lib/fact';

import './panel.scss';

import ICON_CLOSE from './assets/<EMAIL>';
import TITLE_IMG from './assets/<EMAIL>';
import INVITE_STEP1_IMG from './assets/<EMAIL>';
import INVITE_STEP2_IMG from './assets/<EMAIL>';

class InviteQrcodePanel extends Component<IProps, IState> {

  $inner: RefObject<HTMLDivElement> = createRef();

  state: IState = {
    show: false,
  }

  togglePanel(show: boolean) {
    if (this.$inner.current) {
      if (show) {
        this.$inner.current.classList.add('visible');
      } else {
        this.$inner.current.classList.remove('visible');
      }
    }
  }

  onWrapClick = () => {
    if (this.state.show) {
      this.togglePanel(false);
      setTimeout(() => {
        this.setState({ show: false });
      }, 300);
    }
  }

  onPanelClick = (e) => {
    e.stopPropagation();
    e.preventDefault();
  }

  listenPanelShow = () => {
    fact.exposure('fuli_expo', {
      c: 'qrshare_invite',
      d: 'expo',
    });
    this.setState({ show: true });
  }

  componentDidUpdate(_, prevState: IState) {
    if (prevState.show !== this.state.show) {
      if (this.state.show) {
        setTimeout(() => {
          // 等背景出现再动画
          this.togglePanel(true);
        }, 10)
      }
    }
  }

  componentDidMount() {
    event.on('showInviteQrcodePanel', this.listenPanelShow);
  }

  componentWillUnmount() {
    event.off('showInviteQrcodePanel', this.listenPanelShow);
  }

  render() {
    const { inviteCode = '' } = this.props;
    if (!this.state.show) return null;
    return (
      <View className="piggy-level-panel-wrap" onClick={this.onWrapClick}>
        <View className="inner-mask" />
        <View className="panel-inner" ref={this.$inner} onClick={this.onPanelClick}>
          <View className="close-wrap" onClick={this.onWrapClick}>
            <Image source={ICON_CLOSE} className="close-icon" style={{ width: 48, height: 48}} />
          </View>
          <View className="panel-body">
            <Image source={TITLE_IMG} className="title-img" />
            <View className="qrcode">
              <QRCode
                data={`https://broccoli.uc.cn/apps/Y_d_gZyAX/routes/FLWGEPg1T?uc_param_str=dsdnfrpfbivesscpgimibtbmnijblauputogpintnwktprchmt&uc_biz_str=S%3Acustom%7CC%3Afull_screen%7COPT%3AIMMERSIVE%401&code=${inviteCode}`}
                style={{ width: 380, height: 380 }}
              />
            </View>
            <View className="invite-step-wrap">
              <View className="step1-wrap">
                <Image source={INVITE_STEP1_IMG} className="step1-img" />
                <View className="step1-text">
                  去应用商店下载<Text className="step1-app-name">UC极速版</Text>
                </View>
              </View>
              <View className="step2-wrap">
                <Image source={INVITE_STEP2_IMG} className="step2-img" />
                <View className="step2-text">
                  <View className="step2-app-name"><Text className="step2-inlne-text">用</Text>UC极速版</View>
                  <Text className="step2-other-text">扫码接受邀请</Text>
                </View>
              </View>
            </View>
          </View>
        </View>
      </View>
    )
  }
}

interface IState {
  show: boolean;
}

const mapState = (state: StoreState) => ({
  inviteCode: state.invite.inviteCode,
});
const mapDispatch = (dispatch) => ({

});
type IProps = ReturnType<typeof mapState> & ReturnType<typeof mapDispatch> & {

};
export default connect(mapState, mapDispatch)(InviteQrcodePanel)
