.piggy-level-panel-wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  // width: 100vw;
  // height: 100vh;
  // padding-top: 462rpx;
  padding-top: 25vh;
  z-index: 200;
  align-items: center;

  .inner-mask {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,.5);
  }

  .panel-inner {
    position: relative;
    z-index: 1;
    width: 750rpx;
    height: 100%;
    align-items: center;
    background-image: linear-gradient(180deg, #FFF7F1 0%, #FFF2E6 100%);
    border-radius: 24px 24px 0 0;
    // background-position: 0 0, 0 324rpx;
    background-repeat: no-repeat, no-repeat;
    background-size: contain;
    transform: translateY(100%);
    opacity: 1;
    transition: transform .3s ease-in-out;

    &.visible {
      transform: translateY(0);
    }

    .close-wrap {
      position: absolute;
      right: 0;
      top: 16rpx;
      right: 16rpx;
      padding: 24rpx;
      z-index: 1;
    }
  }

  .panel-body {
    // flex: 1;
    height: 100%;
    overflow-y: auto;
    padding-bottom: 60rpx;
    border-radius: 12rpx;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;

    .title-img {
      width: 550rpx;
      height: 136rpx;
      margin-top: 88rpx;
    }

    .qrcode {
      width: 448rpx;
      height: 448rpx;
      background: #FFFFFF;
      border-radius: 48rpx;
      border: 4rpx solid #8096B3;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin: 28rpx auto;
    }

    .invite-step-wrap {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 28rpx;

      .step1-wrap,
      .step2-wrap {
        width: 252rpx;
        margin: 20rpx 43rpx;

        .step1-img,
        .step2-img {
          width: 251rpx;
          height: 136rpx;
        }
        .step1-text,
        .step2-text {
          display: inline;
          font-family: PingFangSC-Regular;
          font-size: 28rpx;
          color: #01255D;
          letter-spacing: 0;
          text-align: center;
          line-height: 34rpx;
          font-weight: 400;
          margin-top: 19rpx;
          .step1-app-name {
            font-size: 28rpx;
            color: #F03931;
          }
          .step2-app-name {
            color: #F03931;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            .step2-inlne-text {
              display: inline;
              color: #01255D;
            }
          }
          .step2-other-text {
            font-size: 28rpx;
            color: #01255D;
          }
        }
      }
    }
  }
}

@media screen and (max-height: 667px) {
  .piggy-level-panel-wrap {
    padding-top: 15vh;
  }
}
