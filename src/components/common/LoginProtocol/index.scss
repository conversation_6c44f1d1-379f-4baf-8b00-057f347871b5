.login-panel-comp {
  width: 100%;
  height: 100vh;
  position: fixed;
  left: 0;
  bottom: 0;
  z-index: 99999;
  .panel-mask {
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.5;
    position: absolute;
    top: 0;
    left: 0;
  }
  .operation-content {
    padding-bottom: 132rpx;
    width: 100%;
    background-color: #fff;
    position: absolute;
    bottom: 0;
    transform: translateY(0);
    background: #ffffff;
    border-radius: 48rpx 48rpx 0 0;
    z-index: 9999;
    .close {
      padding: 30rpx 30rpx 0 0;
      display: flex;
      flex-direction: row;
      justify-content: end;
      .close-img {
        width: 32rpx;
        width: 32rpx;
        background-size: cover;
      }
    }
    .title {
      font-family: PingFangSC-Semibold;
      font-size: 36rpx;
      color: #12161a;
      text-align: center;
      font-weight: 700;
      line-height: 50rpx;
    }
    .protocol {
      margin-top: 36rpx;
      margin-bottom: 50rpx;
      font-family: PingFangSC-Regular;
      font-size: 24rpx;
      color: #859199;
      text-align: center;
      line-height: 34rpx;
      .protocol-view {
        display: flex;
        flex-direction: row;
        justify-content: center;
        .protocol-text {
          color: #194975;
        }
      }
    }
    .button {
      margin: 0 auto;
      width: 622rpx;
      height: 88rpx;
      font-family: PingFangSC-Medium;
      font-size: 34rpx;
      color: #ffffff;
      font-weight: 500;
      line-height: 88rpx;
      background: #12161A;
      border-radius: 20rpx;
      text-align: center;
    }
    .agree {
      color: #ffffff;
      background: #12161A;
    }
    .disagree {
      margin-top: 32rpx;
      background: #F5F6F7;
      color: #12161A;
    }
  }
  .panel-appear {
    animation: panelOpen 0.3s ease-in forwards;
  }
}

@keyframes panelOpen {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}
