import { createElement, useEffect, useState } from 'rax';
import View from 'rax-view';
import Text from 'rax-view';
import Image from '@/components/image';
import ucapi from '@/utils/ucapi';
import EventEmitter from 'eventemitter3';
import stat from '@/lib/stat';
import './index.scss';
import closeImg from '@/components/common/LoginProtocol/images/close.png';
import { store } from '@/store';

export const event = new EventEmitter();

// 登录协议
const Index = () => {
  const [showPanle, setShowPanel] = useState(false);
  const app = store?.getState()?.app;
  useEffect(() => {
    event.on('OpenLoginProtocolPanel', openPanel);
    event.on('HideLoginProtocolPanel', closePanel);
  }, []);
  const openPanel = () => {
    stat.exposure('privacy_pop_show', {
      c: 'privacy',
      d: 'pop',
      source: 'log_pop',
    });
    setShowPanel(true);
  };
  const closePanel = (position) => {
    stat.click('privacy_pop_click', {
      c: 'privacy',
      d: 'pop',
      source: 'log_pop',
      click_position: position || '',
    });
    setShowPanel(false);
  };
  const userAgreement = () => {
    const { userAgreement } = app || {};
    if (!userAgreement) return;
    ucapi.base.openURL({ url: userAgreement });
  };
  const privacyPolicy = () => {
    const { privacyPolicy } = app || {};
    if (!privacyPolicy) return;
    ucapi.base.openURL({ url: privacyPolicy });
  };
  const submitLogin = () => {
    stat.click('privacy_pop_click', {
      c: 'privacy',
      d: 'pop',
      source: 'log_pop',
      click_position: 'agree',
    });
    ucapi?.account?.openLoginWindow('taobao');
    setShowPanel(false);
  };
  return (
    <View className="login-panel-comp" style={{ display: showPanle ? 'block' : 'none' }}>
      <View onClick={() => closePanel('close')} className="panel-mask" />
      <View className={`operation-content ${showPanle ? 'panel-appear' : ''}`}>
        <View className="close">
          <Image className="close-img" onClick={() => closePanel('close')} source={closeImg} alt="" />
        </View>
        <View className="title">请先同意用户协议与隐私条款</View>
        <View className="protocol">
          <View>为了保障你的合法权益，请你先阅读并同意 </View>
          <View className="protocol-view">
            <Text className="protocol-text" onClick={userAgreement}>《用户协议》</Text>
            <Text className="protocol-text" onClick={privacyPolicy}>《隐私政策》</Text>
          </View>
        </View>
        <View onClick={submitLogin} className="button agree">
          同意并登录
        </View>
        <View onClick={() => closePanel('disagree')} className="button disagree">
          不同意
        </View>
      </View>
    </View>
  );
};

export default Index;
