export enum LOCALSTORAGE_KEY {
  /** 小说福利模块折叠开关 */
  UC_PIGGY_NOVEL = 'uc_piggy_novel',
  /** 网盘福利模块折叠开关 */
  UC_PIGGY_CLOUDDRIVE = 'uc_piggy_clouddrive',
  /** 标签人群福利模块折叠开关 */
  UC_PIGGY_TAG_PERSON = 'uc_piggy_tag_person',
  /** 拦截挽留code */
  UC_PIGGY_BACK_INTERCEPT = 'uc_piggy_back_intercept',
  /** 每日签到弹窗时间戳 */
  UC_LITE_WELFARE_NEW_SIGN_DIALOG_TS  = 'uc-lite-welfare-new-sign-dialog-ts',
  /** 第二次弹窗时间戳 + 访问次数 */
  UC_PIGGY_SECOND_DIALOG_TS_VISIT  = 'uc_piggy_second_dialog_ts_visit',
}
