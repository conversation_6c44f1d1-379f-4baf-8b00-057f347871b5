import { createElement } from 'rax';
import { Root, Data, Style, Script } from 'rax-document';
import wormholeData from '@/lib/wormhole-data';

function Document({ data }) {
  console.log('render document ')
  return (
    <html>
      <head>
        <meta charset="utf-8" />
        <meta name="wpk-bid" content="45w4uv8x-nrs4k62g" />
        <meta name="wpk-rel" content={MAIN_VERSION} />
        <meta name="ut-on" />
        <meta name="exposure-on" />
        <meta name="multiple-page"/>
        <meta name="nightmode" content="disable" />
        <meta name="customizetype" content="reader" />
        <meta content="disable" name="transparentmode" />
        <meta name="screen-orientation" content="portrait" />
        <meta
          name="viewport"
          content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,user-scalable=no,viewport-fit=cover"
        />
        <script dangerouslySetInnerHTML={{__html: `
          window.__t0 = Date.now();
          window.__documentStartTime = window?.performance?.now();
          console.log('__documentStartTime -> ', window.__documentStartTime);
          try {
            window.__broTestId = $broTestId;
            window.__broTestDataId = $broTestDataId;
            console.log('broTestId', window.__broTestId, ' broTestDataId', window.__broTestDataId);
          } catch(err) {
            console.warn(err);
          }
          `}}
        >
        </script>
        <Style />
      </head>
      <body data-theme="welfare-theme">
        <script dangerouslySetInnerHTML={{ __html: `window.__wh_data__ = ${JSON.stringify(wormholeData) || '{}'}` }} />
        <Root />
        <Data />
        {/* 兼容流式csr */}
        <script dangerouslySetInnerHTML={{ __html: `window.__INITIAL_PROPS__ = {}; `}}></script>

        <Script />
      </body>
    </html>
  );
}

export default Document;
